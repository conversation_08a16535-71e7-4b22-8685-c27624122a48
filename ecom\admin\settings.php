<?php
require_once('inc/header.php');
require_once('inc/sidebar.php');

// Handle form submission
if(isset($_POST['form1'])) {
    $valid = 1;
    
    // Validate installation fee (must be a positive number)
    $installation_fee = $_POST['installation_fee'];
    if(!is_numeric($installation_fee) || $installation_fee < 0) {
        $valid = 0;
        $error_message = 'Installation fee must be a positive number';
    }
    
    if($valid == 1) {
        // Update installation fee
        $statement = $pdo->prepare("INSERT INTO tbl_settings (setting_name, setting_value, setting_description) 
                                  VALUES ('installation_fee', ?, 'Default installation fee in TSH') 
                                  ON DUPLICATE KEY UPDATE setting_value = ?");
        $statement->execute(array($installation_fee, $installation_fee));
        
        $success_message = 'Settings updated successfully.';
    }
}

// Get current settings
try {
    // First, check if the settings table exists
    $tableExists = false;
    $stmt = $pdo->query("SHOW TABLES LIKE 'tbl_settings'");
    if($stmt->rowCount() > 0) {
        $tableExists = true;
    }
    
    // Create the table if it doesn't exist
    if(!$tableExists) {
        $pdo->exec("CREATE TABLE IF NOT EXISTS tbl_settings (
            setting_id INT AUTO_INCREMENT PRIMARY KEY,
            setting_name VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // Insert default installation fee
        $stmt = $pdo->prepare("INSERT INTO tbl_settings (setting_name, setting_value, setting_description) 
                              VALUES ('installation_fee', '15000', 'Default installation fee in TSH')");
        $stmt->execute();
    }
    
    // Get the current installation fee
    $stmt = $pdo->prepare("SELECT setting_value FROM tbl_settings WHERE setting_name = 'installation_fee'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $installation_fee = $result ? $result['setting_value'] : '15000';
} catch (PDOException $e) {
    $error_message = "Database error: " . $e->getMessage();
}
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Settings</h1>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <?php if(isset($error_message)): ?>
            <div class="callout callout-danger">
                <p><?php echo $error_message; ?></p>
            </div>
            <?php endif; ?>
            
            <?php if(isset($success_message)): ?>
            <div class="callout callout-success">
                <p><?php echo $success_message; ?></p>
            </div>
            <?php endif; ?>
            
            <form class="form-horizontal" action="" method="post">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="form-group">
                            <label for="installation_fee" class="col-sm-3 control-label">Installation Fee (TSH) *</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" name="installation_fee" value="<?php echo $installation_fee; ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"></label>
                            <div class="col-sm-6">
                                <button type="submit" class="btn btn-success pull-left" name="form1">Update</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<?php require_once('inc/footer.php'); ?>
