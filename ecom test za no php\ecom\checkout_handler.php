<?php
// Start session and enable error reporting
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include necessary files
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Set content type to JSON
header('Content-Type: application/json');

// Verify user is logged in
if (!isset($_SESSION['customer'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'User not logged in',
        'redirect' => 'login.php'
    ]);
    exit;
}

// Verify cart exists
if (empty($_SESSION['cart'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Cart is empty',
        'redirect' => 'cart.php'
    ]);
    exit;
}

// Get JSON input
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// If JSON parsing failed, try POST data
if (json_last_error() !== JSON_ERROR_NONE) {
    $data = $_POST;
}

// Log request data
error_log("CHECKOUT REQUEST: " . json_encode($data));
error_log("SESSION DATA: " . json_encode($_SESSION));

// Process the checkout data
try {
    // Get customer data
    $customer = $_SESSION['customer'];
    $user_id = $customer['cust_id']; // This might be called user_id or customer_id in the database

    // Fetch complete customer details
    $stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id = ?");
    $stmt->execute([$user_id]);
    $customerData = $stmt->fetch(PDO::FETCH_ASSOC);

    // Prepare full address
    $addressParts = [
        $customerData['cust_address_street'] ?? '',
        $customerData['cust_address_city'] ?? '',
        $customerData['cust_address_region'] ?? '',
        $customerData['cust_address_zip'] ?? '',
        $customerData['cust_country'] ?? ''
    ];
    $address = implode(", ", array_filter($addressParts));

    // Get totals from data or session
    if (isset($data['products_subtotal']) && isset($data['final_total'])) {
        $total_items = floatval($data['products_subtotal']);
        $shipping_fee = floatval($data['shipping_fee']);
        $installation_fee_total = floatval($data['installation_fee']);
        $grand_total = floatval($data['final_total']);

        // Update session with these values
        $_SESSION['products_subtotal'] = $total_items;
        $_SESSION['shipping_fee'] = $shipping_fee;
        $_SESSION['installation_fee'] = $installation_fee_total;
        $_SESSION['final_total'] = $grand_total;

        error_log("Using totals from request data: products=$total_items, shipping=$shipping_fee, installation=$installation_fee_total, total=$grand_total");
    } else {
        // Get from session
        $total_items = floatval($_SESSION['products_subtotal'] ?? 0);
        $shipping_fee = floatval($_SESSION['shipping_fee'] ?? 0);
        $installation_fee_total = floatval($_SESSION['installation_fee'] ?? 0);
        $grand_total = floatval($_SESSION['final_total'] ?? 0);

        error_log("Using totals from session: products=$total_items, shipping=$shipping_fee, installation=$installation_fee_total, total=$grand_total");
    }

    // If totals are still zero, calculate from cart
    if ($total_items == 0 || $grand_total == 0) {
        $calculated_items_total = 0;
        $calculated_installation_total = 0;

        foreach ($_SESSION['cart'] as $item) {
            $price = floatval($item['price'] ?? 0);
            $quantity = intval($item['quantity'] ?? 1);
            $calculated_items_total += $price * $quantity;

            if (isset($item['installation']) && $item['installation'] == 1) {
                // Get product-specific installation fee
                $product_id = $item['product_id'] ?? $item['id'] ?? null;
                if ($product_id) {
                    try {
                        $stmt = $pdo->prepare("SELECT installation_fee FROM tbl_product WHERE p_id = ?");
                        $stmt->execute([$product_id]);
                        $product_fee = $stmt->fetchColumn();

                        // Use product-specific fee if available, otherwise use default
                        $installation_fee = (is_numeric($product_fee) && $product_fee > 0) ? $product_fee : 15000;
                        $calculated_installation_total += $installation_fee;
                        error_log("Using installation fee for product $product_id: $installation_fee");
                    } catch (PDOException $e) {
                        error_log("Error fetching installation fee for product $product_id: " . $e->getMessage());
                        $calculated_installation_total += 15000; // Fallback to default
                    }
                } else {
                    $calculated_installation_total += 15000; // Default installation fee
                }
            }
        }

        if ($calculated_items_total > 0) {
            $total_items = $calculated_items_total;
            $installation_fee_total = $calculated_installation_total;
            $grand_total = $total_items + $shipping_fee + $installation_fee_total;

            $_SESSION['products_subtotal'] = $total_items;
            $_SESSION['installation_fee'] = $installation_fee_total;
            $_SESSION['final_total'] = $grand_total;
        }
    }

    // Get shipping country info - prioritize cookie over session for better persistence
    $shipping_country_id = null;
    $shipping_country = '';

    // First try to get from cookie (most reliable for persistence)
    if (isset($_COOKIE['selectedCountryId']) && !empty($_COOKIE['selectedCountryId'])) {
        $shipping_country_id = $_COOKIE['selectedCountryId'];
        error_log("Found shipping country ID in cookie: $shipping_country_id");

        // Try to get the country name from the database
        try {
            $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
            $stmt->execute([$shipping_country_id]);
            $country = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($country) {
                $shipping_country = $country['country_name'];
                // Update session with these values for consistency
                $_SESSION['shipping_country_id'] = $shipping_country_id;
                $_SESSION['shipping_country'] = $shipping_country;
                error_log("Updated session with shipping country from cookie: $shipping_country (ID: $shipping_country_id)");
            }
        } catch (PDOException $e) {
            error_log("Error fetching country name from cookie ID: " . $e->getMessage());
        }
    }

    // If not found in cookie, try session
    if (empty($shipping_country_id) || empty($shipping_country)) {
        $shipping_country_id = $_SESSION['shipping_country_id'] ?? null;
        $shipping_country = $_SESSION['shipping_country'] ?? '';
        error_log("Using shipping country from session: $shipping_country (ID: $shipping_country_id)");
    }

    // If country is missing, try to get from data, POST, or cookie
    if (empty($shipping_country)) {
        // Try to get from data
        if (isset($data['country_id']) && !empty($data['country_id'])) {
            $shipping_country_id = $data['country_id'];
            error_log("Using country ID from request data: $shipping_country_id");

            // Get country name from database
            $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
            $stmt->execute([$shipping_country_id]);
            $country = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($country) {
                $shipping_country = $country['country_name'];
                $_SESSION['shipping_country'] = $shipping_country;
                $_SESSION['shipping_country_id'] = $shipping_country_id;
                error_log("Found country name: $shipping_country");
            }
        }
        // Try to get from POST
        else if (isset($_POST['country_id']) && !empty($_POST['country_id'])) {
            $shipping_country_id = $_POST['country_id'];
            error_log("Using country ID from POST: $shipping_country_id");

            // Get country name from database
            $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
            $stmt->execute([$shipping_country_id]);
            $country = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($country) {
                $shipping_country = $country['country_name'];
                $_SESSION['shipping_country'] = $shipping_country;
                $_SESSION['shipping_country_id'] = $shipping_country_id;
                error_log("Found country name: $shipping_country");
            }
        }
        // Try to get from cookie
        else if (isset($_COOKIE['selectedCountryId']) && !empty($_COOKIE['selectedCountryId'])) {
            $shipping_country_id = $_COOKIE['selectedCountryId'];
            error_log("Using country ID from cookie: $shipping_country_id");

            // Get country name from database
            $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
            $stmt->execute([$shipping_country_id]);
            $country = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($country) {
                $shipping_country = $country['country_name'];
                $_SESSION['shipping_country'] = $shipping_country;
                $_SESSION['shipping_country_id'] = $shipping_country_id;
                error_log("Found country name: $shipping_country");
            }
        }

        // If still empty, use default
        if (empty($shipping_country)) {
            $shipping_country = "Tanzania";
            $shipping_country_id = "1"; // Assuming Tanzania is ID 1
            $_SESSION['shipping_country'] = $shipping_country;
            $_SESSION['shipping_country_id'] = $shipping_country_id;
        }
    }

    // Generate transaction reference
    $tx_ref = "ORDER_" . time() . "_" . bin2hex(random_bytes(4));

    // Process cart items
    $verifiedCart = [];

    foreach ($_SESSION['cart'] as $item) {
        if (empty($item['product_id']) && empty($item['id'])) {
            continue;
        }

        // Use product_id or id
        $product_id = $item['product_id'] ?? $item['id'] ?? null;
        if (!$product_id) continue;

        // Fetch product info
        $stmt = $pdo->prepare("SELECT p_name, p_current_price, p_is_active FROM tbl_product WHERE p_id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        // Skip inactive products
        if (!$product || $product['p_is_active'] != 1) {
            continue;
        }

        // Get price and quantity
        $currentPrice = floatval($item['price'] ?? $product['p_current_price'] ?? 0);
        $quantity = max(1, intval($item['quantity'] ?? 1));

        // Calculate subtotal
        $itemSubtotal = $currentPrice * $quantity;

        // Installation fee
        $unitInstallFee = 0;
        if (!empty($item['installation']) && $item['installation'] == 1) {
            // Get product-specific installation fee
            try {
                $stmt = $pdo->prepare("SELECT installation_fee FROM tbl_product WHERE p_id = ?");
                $stmt->execute([$product_id]);
                $product_fee = $stmt->fetchColumn();

                // Use product-specific fee if available, otherwise use default
                $unitInstallFee = (is_numeric($product_fee) && $product_fee > 0) ? $product_fee : 15000;
                error_log("Using installation fee for product $product_id: $unitInstallFee");
            } catch (PDOException $e) {
                error_log("Error fetching installation fee for product $product_id: " . $e->getMessage());
                $unitInstallFee = 15000; // Fallback to default
            }
        }
        $itemInstallFee = $unitInstallFee * $quantity;

        // Add to verified cart
        $verifiedCart[] = [
            'product_id' => $product_id,
            'name' => $product['p_name'],
            'unit_price' => $currentPrice,
            'quantity' => $quantity,
            'variation_id' => $item['variation_id'] ?? null,
            'variation_price' => null,
            'color_id' => $item['color_id'] ?? null,
            'installation_fee' => $itemInstallFee,
            'subtotal' => $itemSubtotal,
            'total' => $itemSubtotal + $itemInstallFee,
            'variation_name' => $item['variation_name'] ?? null
        ];
    }

    // If no valid items, return error
    if (empty($verifiedCart)) {
        echo json_encode([
            'status' => 'error',
            'message' => 'No valid items in cart',
            'redirect' => 'cart.php'
        ]);
        exit;
    }

    // Begin database transaction
    $pdo->beginTransaction();

    // Check the structure of the orders table
    try {
        // First, try to get the table structure
        $stmt = $pdo->query("DESCRIBE orders");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Check if shipping_country exists
        $has_shipping_country = in_array('shipping_country', $columns);

        // Check if it's user_id or customer_id
        $user_id_column = 'user_id';
        if (in_array('customer_id', $columns)) {
            $user_id_column = 'customer_id';
        }

        error_log("Orders table structure: " . json_encode($columns));
        error_log("Has shipping_country: " . ($has_shipping_country ? 'Yes' : 'No'));
        error_log("User ID column: " . $user_id_column);

        // Prepare the SQL based on the table structure
        if ($has_shipping_country) {
            $sql = "
                INSERT INTO orders (
                    tx_ref, $user_id_column, firstname, lastname, email, phone, address,
                    total_amount, shipping_fee, installation_fee_total, currency, payment_status,
                    shipping_country, shipping_country_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";

            $params = [
                $tx_ref,
                $user_id,
                $customer['cust_fname'],
                $customer['cust_lname'],
                $customer['cust_email'],
                $customerData['cust_phone'] ?? '',
                $address,
                $grand_total,
                $shipping_fee,
                $installation_fee_total,
                'TZS', // Currency code
                'pending',
                $shipping_country,
                $shipping_country_id
            ];
        } else {
            $sql = "
                INSERT INTO orders (
                    tx_ref, $user_id_column, firstname, lastname, email, phone, address,
                    total_amount, shipping_fee, installation_fee_total, currency, payment_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";

            $params = [
                $tx_ref,
                $user_id,
                $customer['cust_fname'],
                $customer['cust_lname'],
                $customer['cust_email'],
                $customerData['cust_phone'] ?? '',
                $address,
                $grand_total,
                $shipping_fee,
                $installation_fee_total,
                'TZS', // Currency code
                'pending'
            ];
        }

        // Execute the query
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
    } catch (PDOException $e) {
        // Log the error for debugging
        error_log("Error in orders table query: " . $e->getMessage());

        // Try a fallback approach with minimal columns
        try {
            error_log("Trying fallback query with minimal columns");

            // Get the table structure to see what columns are available
            $stmt = $pdo->query("SHOW COLUMNS FROM orders");
            $available_columns = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $available_columns[] = $row['Field'];
            }

            error_log("Available columns: " . json_encode($available_columns));

            // Build a query with only the columns we know exist
            $columns = [];
            $placeholders = [];
            $values = [];

            // Required columns
            $column_map = [
                'tx_ref' => $tx_ref,
                'user_id' => $user_id,
                'customer_id' => $user_id,
                'firstname' => $customer['cust_fname'],
                'lastname' => $customer['cust_lname'],
                'email' => $customer['cust_email'],
                'phone' => $customerData['cust_phone'] ?? '',
                'address' => $address,
                'total_amount' => $grand_total,
                'shipping_fee' => $shipping_fee,
                'installation_fee_total' => $installation_fee_total,
                'currency' => 'TZS',
                'payment_status' => 'pending'
            ];

            // Add columns that exist in the table
            foreach ($column_map as $column => $value) {
                if (in_array($column, $available_columns)) {
                    $columns[] = $column;
                    $placeholders[] = '?';
                    $values[] = $value;
                }
            }

            // Build and execute the query
            $sql = "INSERT INTO orders (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
            error_log("Fallback SQL: " . $sql);
            error_log("Fallback values: " . json_encode($values));

            $stmt = $pdo->prepare($sql);
            $stmt->execute($values);
        } catch (PDOException $e2) {
            // If the fallback also fails, log and rethrow
            error_log("Fallback query also failed: " . $e2->getMessage());
            throw $e2;
        }
    }

    $order_id = $pdo->lastInsertId();

    // Insert order items
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, product_id, variation_id, variation_name, product_name,
            color_id, quantity, unit_price, variation_price,
            installation_fee, subtotal, total
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    foreach ($verifiedCart as $item) {
        $stmt->execute([
            $order_id,
            $item['product_id'],
            $item['variation_id'],
            $item['variation_name'],
            $item['name'],
            $item['color_id'],
            $item['quantity'],
            $item['unit_price'],
            $item['variation_price'],
            $item['installation_fee'],
            $item['subtotal'],
            $item['total']
        ]);
    }

    // Commit transaction
    $pdo->commit();

    // Return success response with payment URL
    echo json_encode([
        'status' => 'success',
        'message' => 'Order created successfully',
        'order_id' => $order_id,
        'tx_ref' => $tx_ref,
        'redirect' => 'payment.php?tx_ref=' . $tx_ref
    ]);

} catch (Exception $e) {
    // Rollback transaction if an error occurred
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // Log the error
    error_log("CHECKOUT ERROR: " . $e->getMessage());

    // Return error response
    echo json_encode([
        'status' => 'error',
        'message' => 'An error occurred during checkout: ' . $e->getMessage(),
        'error_details' => $e->getTraceAsString()
    ]);
}
