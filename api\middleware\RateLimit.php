<?php
/**
 * Rate Limiting Middleware
 * Implements rate limiting using database storage with file fallback
 */

class RateLimitMiddleware {

    private static $storage_path = __DIR__ . '/../storage/rate_limits/';
    private static $use_database = true;

    /**
     * Check rate limit for IP address
     */
    public static function check($ip = null, $limit = null, $window = null) {
        $ip = $ip ?: self::getClientIP();
        $limit = $limit ?: RATE_LIMIT_REQUESTS;
        $window = $window ?: RATE_LIMIT_WINDOW;

        // Check if IP should bypass rate limiting
        if (defined('RATE_LIMIT_BYPASS_IPS') && in_array($ip, RATE_LIMIT_BYPASS_IPS)) {
            return; // Skip rate limiting for bypass IPs
        }

        if (self::$use_database) {
            try {
                self::checkDatabaseRateLimit($ip, $limit, $window);
                return;
            } catch (Exception $e) {
                // Fall back to file-based rate limiting
                self::$use_database = false;
                error_log("Rate limit database error, falling back to file storage: " . $e->getMessage());
            }
        }

        self::checkFileRateLimit($ip, $limit, $window);
    }

    /**
     * Database-based rate limiting
     */
    private static function checkDatabaseRateLimit($ip, $limit, $window) {
        global $pdo;
        $db = new Database($pdo);

        $current_time = time();
        $window_start = $current_time - $window;

        // Clean old entries
        $db->delete('api_rate_limits', 'window_start < ?', [date('Y-m-d H:i:s', $window_start)]);

        // Get current rate limit record
        $rate_limit = $db->fetchOne(
            "SELECT * FROM api_rate_limits WHERE ip_address = ?",
            [$ip]
        );

        if ($rate_limit) {
            $window_start_time = strtotime($rate_limit['window_start']);

            // Check if we're still in the same window
            if (($current_time - $window_start_time) < $window) {
                if ($rate_limit['requests'] >= $limit) {
                    $reset_time = $window_start_time + $window;
                    self::sendRateLimitHeaders($limit, 0, $reset_time);
                    Response::rateLimitExceeded("Rate limit exceeded. Maximum {$limit} requests per hour allowed.");
                }

                // Increment request count
                $new_count = $rate_limit['requests'] + 1;
                $db->update('api_rate_limits',
                    ['requests' => $new_count, 'last_request' => date('Y-m-d H:i:s')],
                    'id = ?',
                    [$rate_limit['id']]
                );

                $remaining = max(0, $limit - $new_count);
                $reset_time = $window_start_time + $window;
                self::sendRateLimitHeaders($limit, $remaining, $reset_time);
            } else {
                // New window, reset counter
                $db->update('api_rate_limits', [
                    'requests' => 1,
                    'window_start' => date('Y-m-d H:i:s'),
                    'last_request' => date('Y-m-d H:i:s')
                ], 'id = ?', [$rate_limit['id']]);

                self::sendRateLimitHeaders($limit, $limit - 1, $current_time + $window);
            }
        } else {
            // First request from this IP
            $db->insert('api_rate_limits', [
                'ip_address' => $ip,
                'requests' => 1,
                'window_start' => date('Y-m-d H:i:s'),
                'last_request' => date('Y-m-d H:i:s')
            ]);

            self::sendRateLimitHeaders($limit, $limit - 1, $current_time + $window);
        }
    }

    /**
     * File-based rate limiting (fallback)
     */
    private static function checkFileRateLimit($ip, $limit, $window) {
        // Create storage directory if it doesn't exist
        if (!is_dir(self::$storage_path)) {
            mkdir(self::$storage_path, 0755, true);
        }

        $file_path = self::$storage_path . md5($ip) . '.json';
        $current_time = time();

        // Load existing data
        $data = [];
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            $data = json_decode($content, true) ?: [];
        }

        // Clean old entries
        $data = array_filter($data, function($timestamp) use ($current_time, $window) {
            return ($current_time - $timestamp) < $window;
        });

        // Check if limit exceeded
        if (count($data) >= $limit) {
            self::sendRateLimitHeaders($limit, 0, $current_time + $window);
            Response::rateLimitExceeded("Rate limit exceeded. Maximum {$limit} requests per hour allowed.");
        }

        // Add current request
        $data[] = $current_time;

        // Save updated data
        file_put_contents($file_path, json_encode($data));

        // Add rate limit headers
        $remaining = max(0, $limit - count($data));
        $reset_time = $current_time + $window;
        self::sendRateLimitHeaders($limit, $remaining, $reset_time);
    }

    /**
     * Send rate limit headers
     */
    private static function sendRateLimitHeaders($limit, $remaining, $reset_time) {
        header("X-RateLimit-Limit: {$limit}");
        header("X-RateLimit-Remaining: {$remaining}");
        header("X-RateLimit-Reset: {$reset_time}");
    }

    /**
     * Get client IP address
     */
    private static function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Clean up old rate limit files
     */
    public static function cleanup() {
        if (!is_dir(self::$storage_path)) {
            return;
        }

        $files = glob(self::$storage_path . '*.json');
        $current_time = time();

        foreach ($files as $file) {
            $modified_time = filemtime($file);
            // Delete files older than 2 hours
            if (($current_time - $modified_time) > 7200) {
                unlink($file);
            }
        }
    }
}
