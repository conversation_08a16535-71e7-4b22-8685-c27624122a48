<?php
// Include session configuration before starting session
include("session_config.php");
session_start();

// Include necessary files
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

// Set content type for JSON response
header('Content-Type: application/json');

// Verify user is logged in using persistent login system
if (!isUserLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Verify cart exists
if (empty($_SESSION['cart'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Cart is empty']);
    exit;
}

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Constants
define('CURRENCY_CODE', 'TZS');
define('CURRENCY_SYMBOL', 'TSH');

// Get customer data
$customer = $_SESSION['customer'];
$user_id  = $customer['cust_id'];

// Fetch complete customer details
$stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id = ?");
$stmt->execute([$user_id]);
$customerData = $stmt->fetch(PDO::FETCH_ASSOC);

// Prepare full address
$addressParts = [
    $customerData['cust_address_street'] ?? '',
    $customerData['cust_address_city']   ?? '',
    $customerData['cust_address_region'] ?? '',
    $customerData['cust_address_zip']    ?? '',
    $customerData['cust_country']        ?? ''
];
$address = implode(", ", array_filter($addressParts));

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

// Check if we have totals in POST data
$has_post_totals = isset($input['products_subtotal']) && isset($input['final_total']);

// Initialize totals from POST data if available, otherwise from session
if ($has_post_totals) {
    $total_items = floatval($input['products_subtotal']);
    $shipping_fee = floatval($input['shipping_fee']);
    $installation_fee_total = floatval($input['installation_fee']);
    $grand_total = floatval($input['final_total']);

    // Also update the session with these values
    $_SESSION['products_subtotal'] = $total_items;
    $_SESSION['shipping_fee'] = $shipping_fee;
    $_SESSION['installation_fee'] = $installation_fee_total;
    $_SESSION['final_total'] = $grand_total;

    // If country_id is provided, update shipping country
    if (isset($input['country_id']) && !empty($input['country_id'])) {
        $country_id = $input['country_id'];
        $_SESSION['shipping_country_id'] = $country_id;

        // Try to get the country name from the database
        try {
            $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
            $stmt->execute([$country_id]);
            $country = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($country) {
                $_SESSION['shipping_country'] = $country['country_name'];
            }
        } catch (PDOException $e) {
            error_log("Error fetching country name from POST data: " . $e->getMessage());
        }
    }
} else {
    // Initialize totals from session
    $total_items = floatval($_SESSION['products_subtotal'] ?? 0.0);
    $shipping_fee = floatval($_SESSION['shipping_fee'] ?? 0.0);
    $installation_fee_total = floatval($_SESSION['installation_fee'] ?? 0.0);
    $grand_total = floatval($_SESSION['final_total'] ?? 0.0);
}

// If any of the required values are missing, try to calculate them from the cart
if ($total_items == 0 || $grand_total == 0) {
    // Calculate totals from cart items
    $calculated_items_total = 0;
    $calculated_installation_total = 0;

    if (isset($_SESSION['cart']) && is_array($_SESSION['cart']) && !empty($_SESSION['cart'])) {
        foreach ($_SESSION['cart'] as $item) {
            $price = floatval($item['price'] ?? 0);
            $quantity = intval($item['quantity'] ?? 1);
            $calculated_items_total += $price * $quantity;

            // Add installation fee if selected
            if (isset($item['installation']) && $item['installation'] == 1) {
                // Get product-specific installation fee
                $product_id = $item['product_id'] ?? null;
                if ($product_id) {
                    try {
                        $stmt = $pdo->prepare("SELECT installation_fee FROM tbl_product WHERE p_id = ?");
                        $stmt->execute([$product_id]);
                        $product_fee = $stmt->fetchColumn();

                        // Use product-specific fee if available, otherwise use default
                        $installation_fee = (is_numeric($product_fee) && $product_fee > 0) ? $product_fee : 15000;
                        $calculated_installation_total += $installation_fee;
                    } catch (PDOException $e) {
                        error_log("Error fetching installation fee for product $product_id: " . $e->getMessage());
                        $calculated_installation_total += 15000; // Fallback to default
                    }
                } else {
                    $calculated_installation_total += 15000; // Default installation fee
                }
            }
        }

        // If we calculated non-zero values, use them
        if ($calculated_items_total > 0) {
            $total_items = $calculated_items_total;
            $installation_fee_total = $calculated_installation_total;
            $grand_total = $total_items + $shipping_fee + $installation_fee_total;

            // Store the calculated values in session
            $_SESSION['products_subtotal'] = $total_items;
            $_SESSION['installation_fee'] = $installation_fee_total;
            $_SESSION['final_total'] = $grand_total;
        }
    }
}

// Verify the totals
$calculated_total = $total_items + $shipping_fee + $installation_fee_total;
if (abs($calculated_total - $grand_total) > 0.01) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Order total verification failed']);
    exit;
}

$verifiedCart      = [];
$price_discrepancy = false;
$inactiveProducts  = [];

// Process each cart item (same logic as start_checkout.php)
foreach ($_SESSION['cart'] as $item) {
    if (empty($item['product_id'])) {
        continue;
    }

    // Fetch product base info including active status
    $stmt = $pdo->prepare("SELECT p_name, p_current_price, p_is_active FROM tbl_product WHERE p_id = ?");
    $stmt->execute([$item['product_id']]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if product is active; if not, collect its name and skip adding to verified cart
    if (!$product || $product['p_is_active'] != 1) {
        $inactiveProducts[] = $product ? $product['p_name'] : "Product ID {$item['product_id']}";
        continue;
    }

    // Session price & quantity
    $currentPrice = floatval($item['price']);
    $quantity     = max(1, (int)$item['quantity']);

    // Subtotal for items
    $itemSubtotal = $currentPrice * $quantity;

    // Installation fee per unit if selected
    $unitInstallFee = (
        !empty($item['installation']) && $item['installation'] == 1
    ) ? floatval($item['installation_fee'] ?? 0) : 0.0;
    $itemInstallFee = $unitInstallFee * $quantity;

    // Verify variation price against DB if any
    $variation_price = null;
    if (!empty($item['variation_id'])) {
        try {
            $stmt = $pdo->prepare("
                SELECT price_adjustment
                FROM tbl_product_variations
                WHERE variation_id = ? AND product_id = ?
            ");
            $stmt->execute([$item['variation_id'], $item['product_id']]);
            $variation = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($variation) {
                $variation_price = floatval($variation['price_adjustment']);
            }
        } catch (PDOException $e) {
            error_log("Error fetching variation price: " . $e->getMessage());
        }
    }

    // Build verified cart item
    $verifiedCart[] = [
        'product_id'      => $item['product_id'],
        'variation_id'    => $item['variation_id'] ?? null,
        'variation_name'  => $item['variation_name'] ?? null,
        'variation_price' => $variation_price,
        'name'            => $product['p_name'],
        'color_id'        => $item['color_id'] ?? null,
        'quantity'        => $quantity,
        'unit_price'      => $currentPrice,
        'installation_fee'=> $unitInstallFee,
        'subtotal'        => $itemSubtotal,
        'total'           => $itemSubtotal + $itemInstallFee
    ];
}

// If there are inactive products, return error
if (!empty($inactiveProducts)) {
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'message' => 'Some products are no longer available: ' . implode(", ", $inactiveProducts)
    ]);
    exit;
}

// Generate transaction reference
$tx_ref = "WHATSAPP_" . time() . "_" . bin2hex(random_bytes(4));

// Get shipping country and fee from session
$shipping_country_id = $_SESSION['shipping_country_id'] ?? null;
$shipping_country = $_SESSION['shipping_country'] ?? '';

// If shipping_country_id is set but shipping_country is not, fetch the country name from the database
if ($shipping_country_id && empty($shipping_country)) {
    try {
        $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
        $stmt->execute([$shipping_country_id]);
        $country = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($country) {
            $shipping_country = $country['country_name'];
            $_SESSION['shipping_country'] = $shipping_country;
        }
    } catch (PDOException $e) {
        error_log("Error fetching country name: " . $e->getMessage());
    }
}

// Set default if still empty
if (empty($shipping_country)) {
    $shipping_country = "Tanzania";
    $shipping_country_id = "1"; // Assuming Tanzania is ID 1
}

// Begin DB transaction
$pdo->beginTransaction();
try {
    // Insert into orders with 'whatsapp' payment status
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            tx_ref, user_id, firstname, lastname, email, phone, address,
            total_amount, shipping_fee, installation_fee_total, currency, payment_status,
            shipping_country, shipping_country_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $tx_ref,
        $user_id,
        $customer['cust_fname'],
        $customer['cust_lname'],
        $customer['cust_email'],
        $customerData['cust_phone'] ?? '',
        $address,
        $grand_total,
        $shipping_fee,
        $installation_fee_total,
        CURRENCY_CODE,
        'whatsapp', // Special status for WhatsApp orders
        $shipping_country,
        $shipping_country_id
    ]);
    $order_id = $pdo->lastInsertId();

    // Insert each order item
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, product_id, variation_id, variation_name, product_name,
            color_id, quantity, unit_price, variation_price,
            installation_fee, subtotal, total
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    foreach ($verifiedCart as $item) {
        $stmt->execute([
            $order_id,
            $item['product_id'],
            $item['variation_id'],
            $item['variation_name'],
            $item['name'],
            $item['color_id'],
            $item['quantity'],
            $item['unit_price'],
            $item['variation_price'],
            $item['installation_fee'],
            $item['subtotal'],
            $item['total']
        ]);
    }

    $pdo->commit();

    // Prepare order data for WhatsApp message
    $orderData = [
        'order_id' => $order_id,
        'tx_ref' => $tx_ref,
        'customer' => [
            'name' => $customer['cust_fname'] . ' ' . $customer['cust_lname'],
            'email' => $customer['cust_email'],
            'phone' => $customerData['cust_phone'] ?? '',
            'address' => $address
        ],
        'items' => [],
        'totals' => [
            'products_subtotal' => $total_items,
            'shipping_fee' => $shipping_fee,
            'installation_fee' => $installation_fee_total,
            'grand_total' => $grand_total,
            'currency' => CURRENCY_CODE,
            'currency_symbol' => CURRENCY_SYMBOL
        ],
        'shipping' => [
            'country' => $shipping_country,
            'country_id' => $shipping_country_id
        ]
    ];

    // Add item details for WhatsApp message
    foreach ($verifiedCart as $item) {
        $itemData = [
            'name' => $item['name'],
            'quantity' => $item['quantity'],
            'unit_price' => $item['unit_price'],
            'subtotal' => $item['subtotal'],
            'total' => $item['total']
        ];

        // Add variation if exists
        if (!empty($item['variation_name'])) {
            $itemData['variation'] = $item['variation_name'];
        }

        // Add color if exists
        if (!empty($item['color_id'])) {
            try {
                $stmt = $pdo->prepare("SELECT color_name FROM tbl_color WHERE color_id = ?");
                $stmt->execute([$item['color_id']]);
                $color = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($color) {
                    $itemData['color'] = $color['color_name'];
                }
            } catch (PDOException $e) {
                error_log("Error fetching color name: " . $e->getMessage());
            }
        }

        // Add installation fee if applicable
        if ($item['installation_fee'] > 0) {
            $itemData['installation_fee'] = $item['installation_fee'];
        }

        $orderData['items'][] = $itemData;
    }

    // Clear some session data but keep cart for potential retry
    unset(
        $_SESSION['shipping_fee'],
        $_SESSION['shipping_country'],
        $_SESSION['shipping_country_id'],
        $_SESSION['final_total']
    );

    // Return success response with order data
    echo json_encode([
        'success' => true,
        'message' => 'Order saved successfully',
        'order_data' => $orderData
    ]);

} catch (Exception $e) {
    $pdo->rollBack();
    error_log("WhatsApp order processing failed: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Order processing failed. Please try again.'
    ]);
}
