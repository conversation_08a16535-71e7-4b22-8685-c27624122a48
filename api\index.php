<?php
/**
 * API Router
 * Main entry point for all API requests
 */

// Start output buffering to prevent any accidental output
ob_start();

// Suppress all errors to prevent JSON corruption
error_reporting(0);
ini_set('display_errors', 0);

// Set JSON header immediately
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include configuration
require_once __DIR__ . '/config/config.php';

// Apply CORS middleware (simplified)
try {
    if (class_exists('CORSMiddleware')) {
        CORSMiddleware::handle();
    }
} catch (Exception $e) {
    // Continue without CORS if it fails
}

// Apply rate limiting (simplified)
try {
    if (class_exists('RateLimitMiddleware')) {
        RateLimitMiddleware::check();
    }
} catch (Exception $e) {
    // Continue without rate limiting if it fails
}

// Get request method and input
$method = $_SERVER['REQUEST_METHOD'];
$input = [];

// Get input data based on method
if ($method === 'POST' || $method === 'PUT') {
    $content_type = $_SERVER['CONTENT_TYPE'] ?? '';
    if (strpos($content_type, 'application/json') !== false) {
        $json = file_get_contents('php://input');
        $input = json_decode($json, true) ?: [];
    } else {
        $input = $_POST;
    }
}

// Parse the request path
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove common base paths
$base_patterns = [
    '/ecom/api/v1',
    '/api/v1',
    dirname($_SERVER['SCRIPT_NAME']) . '/v1'
];

$clean_path = $path;
foreach ($base_patterns as $pattern) {
    if (strpos($path, $pattern) === 0) {
        $clean_path = substr($path, strlen($pattern));
        break;
    }
}

$clean_path = trim($clean_path, '/');

// Split into segments
$segments = $clean_path ? explode('/', $clean_path) : [];
$endpoint = $segments[0] ?? '';

// Route to appropriate endpoint
try {
    switch ($endpoint) {
        case 'auth':
            require_once __DIR__ . '/endpoints/auth.php';
            break;

        case 'products':
            require_once __DIR__ . '/endpoints/products.php';
            break;

        case 'categories':
            require_once __DIR__ . '/endpoints/categories.php';
            break;

        case 'cart':
            require_once __DIR__ . '/endpoints/cart.php';
            break;

        case 'orders':
            require_once __DIR__ . '/endpoints/orders.php';
            break;

        case 'users':
            require_once __DIR__ . '/endpoints/users.php';
            break;

        case 'admin':
            require_once __DIR__ . '/endpoints/admin.php';
            break;

        case 'search':
            require_once __DIR__ . '/endpoints/search.php';
            break;

        case 'wishlist':
            require_once __DIR__ . '/endpoints/wishlist.php';
            break;

        case 'shipping':
            require_once __DIR__ . '/endpoints/shipping.php';
            break;

        case 'settings':
            require_once __DIR__ . '/endpoints/settings.php';
            break;

        case '':
            // API root - return API information
            $script_dir = dirname($_SERVER['SCRIPT_NAME']);
            Response::success([
                'name' => 'Ecommerce API',
                'version' => API_VERSION,
                'endpoints' => [
                    'auth' => 'Authentication endpoints',
                    'products' => 'Product catalog',
                    'categories' => 'Product categories',
                    'cart' => 'Shopping cart management',
                    'orders' => 'Order management',
                    'users' => 'User profile management',
                    'search' => 'Product search',
                    'wishlist' => 'User wishlist',
                    'shipping' => 'Shipping information',
                    'admin' => 'Admin endpoints'
                ],
                'documentation' => $script_dir . '/v1/docs'
            ], 'Welcome to the Ecommerce API');
            break;

        case 'docs':
            require_once __DIR__ . '/docs/index.php';
            break;

        default:
            Response::notFound('Endpoint not found');
    }

} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    error_log("API Error: " . $e->getMessage());

    // Send clean JSON error response
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'An unexpected error occurred',
        'timestamp' => date('c'),
        'api_version' => API_VERSION,
        'error_code' => 'INTERNAL_ERROR'
    ]);
    exit();
}

// Clear output buffer and ensure clean response
ob_end_clean();
