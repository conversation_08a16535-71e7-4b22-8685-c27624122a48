<?php
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Verify user is logged in
if (!isset($_SESSION['customer'])) {
    header("Location: login.php");
    exit;
}

/* Verify cart exists*/
if (empty($_SESSION['cart'])) {
    header("Location: cart.php");
    exit;
}   

// Constants
define('CURRENCY_CODE', 'TZS');

// Get customer data
$customer = $_SESSION['customer'];
$user_id  = $customer['cust_id'];

// Fetch complete customer details
$stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id = ?");
$stmt->execute([$user_id]);
$customerData = $stmt->fetch(PDO::FETCH_ASSOC);

// Prepare full address
$addressParts = [
    $customerData['cust_address_street'] ?? '',
    $customerData['cust_address_city']   ?? '',
    $customerData['cust_address_region'] ?? '',
    $customerData['cust_address_zip']    ?? '',
    $customerData['cust_country']        ?? ''
];
$address = implode(", ", array_filter($addressParts));

// Initialize totals from session
$total_items = floatval($_SESSION['products_subtotal'] ?? 0.0);
$shipping_fee = floatval($_SESSION['shipping_fee'] ?? 0.0);
$installation_fee_total = floatval($_SESSION['installation_fee'] ?? 0.0);
$grand_total = floatval($_SESSION['final_total'] ?? 0.0);

// Verify the totals
$calculated_total = $total_items + $shipping_fee + $installation_fee_total;
if (abs($calculated_total - $grand_total) > 0.01) {
    error_log("Total mismatch in checkout: Session total: $grand_total, Calculated: $calculated_total");
    $_SESSION['error_message'] = "Order total verification failed. Please try again.";
    header("Location: cart.php");
    exit;
}

$verifiedCart            = [];
$price_discrepancy       = false;

// Process each cart item
foreach ($_SESSION['cart'] as $item) {
    if (empty($item['product_id'])) {
        continue;
    }  

    // Fetch product base info
    $stmt = $pdo->prepare("SELECT p_name, p_current_price FROM tbl_product WHERE p_id = ?");
    $stmt->execute([$item['product_id']]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$product) {
        continue;
    }

    // Session price & quantity
    $currentPrice = floatval($item['price']);
    $quantity     = max(1, (int)$item['quantity']);

    // Subtotal for items
    $itemSubtotal = $currentPrice * $quantity;

    // Installation fee per unit if selected
    $unitInstallFee = (
        !empty($item['installation']) && $item['installation'] == 1
    ) ? floatval($item['installation_fee'] ?? 0) : 0.0;
    $itemInstallFee = $unitInstallFee * $quantity;

    // Verify variation price against DB if any
    $variation_price = null;
    if (!empty($item['variation_id'])) {
        try {
            $stmt = $pdo->prepare("
                SELECT price_adjustment
                FROM tbl_product_variations
                WHERE variation_id = ? AND product_id = ?
            ");
            $stmt->execute([$item['variation_id'], $item['product_id']]);
            $var = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($var) {
                $dbPrice = $product['p_current_price'] + floatval($var['price_adjustment']);
                if (abs($dbPrice - $currentPrice) > 0.01) {
                    $price_discrepancy = true;
                    error_log("Price discrepancy for product {$item['product_id']}: session {$currentPrice} vs db {$dbPrice}");
                }
                $variation_price = $dbPrice;
            }
        } catch (PDOException $e) {
            error_log("Variation lookup error: " . $e->getMessage());
        }
    } else {
        // Verify base price for non-variation
        if (abs($product['p_current_price'] - $currentPrice) > 0.01) {
            $price_discrepancy = true;
            error_log("Price discrepancy for product {$item['product_id']}: session {$currentPrice} vs db {$product['p_current_price']}");
        }
    }

    // Build verified cart entry
    $itemTotal = $itemSubtotal + $itemInstallFee;
    $verifiedCart[] = [
        'product_id'       => $item['product_id'],
        'name'             => $product['p_name'],
        'unit_price'       => $currentPrice,
        'quantity'         => $quantity,
        'variation_id'     => $item['variation_id'] ?? null,
        'variation_price'  => $variation_price,
        'color_id'         => $item['color_id'] ?? null,
        'installation_fee' => $itemInstallFee,
        'subtotal'         => $itemSubtotal,
        'total'            => $itemTotal,
        'variation_name'   => $item['variation_name'] ?? null
    ];
}

// Generate transaction reference
$tx_ref = "ORDER_" . time() . "_" . bin2hex(random_bytes(4));

// Begin DB transaction
$pdo->beginTransaction();
try {
    // Insert into orders
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            tx_ref, user_id, firstname, lastname, email, phone, address,
            total_amount, shipping_fee, installation_fee_total, currency, payment_status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $tx_ref,
        $user_id,
        $customer['cust_fname'],
        $customer['cust_lname'],
        $customer['cust_email'],
        $customerData['cust_phone'] ?? '',
        $address,
        $grand_total,
        $shipping_fee,
        $installation_fee_total,
        CURRENCY_CODE,
        'pending'
    ]);
    $order_id = $pdo->lastInsertId();

    // Insert each order item
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, product_id, variation_id, variation_name, product_name,
            color_id, quantity, unit_price, variation_price,
            installation_fee, subtotal, total
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    foreach ($verifiedCart as $item) {
        $stmt->execute([
            $order_id,
            $item['product_id'],
            $item['variation_id'],
            $item['variation_name'],
            $item['name'],
            $item['color_id'],
            $item['quantity'],
            $item['unit_price'],
            $item['variation_price'],
            $item['installation_fee'],
            $item['subtotal'],
            $item['total']
        ]);
    }

    $pdo->commit();

    /*
    // Clear cart
    unset($_SESSION['cart'], $_SESSION['shipping_fee']); */

} catch (Exception $e) {
    $pdo->rollBack();
    error_log("Order processing failed: " . $e->getMessage());
    $_SESSION['error_message'] = "Order processing failed. Please try again.";
    header("Location: cart.php");
    exit;
}

// Log if any price discrepancies were found
if ($price_discrepancy) {
    error_log("Price discrepancies detected in order $tx_ref");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Processing Payment</title>
    <script src="https://checkout.flutterwave.com/v3.js"></script>
</head>
<body>
<script>
function makePayment() {
    FlutterwaveCheckout({
        public_key: "FLWPUBK_TEST-02b9b5fc6406bd4a41c3ff141cc45e93-X",
        tx_ref: "<?= $tx_ref ?>",
        amount: <?= number_format($grand_total, 2, '.', '') ?>,
        currency: "<?= CURRENCY_CODE ?>",
        payment_options: "card, mobilemoney, ussd, banktransfer, account, applepay",
        customer: {
            email: "<?= $customer['cust_email'] ?>",
            phone_number: "<?= $customerData['cust_phone'] ?? '' ?>",
            name: "<?= $customer['cust_fname'] . ' ' . $customer['cust_lname'] ?>"
        },
        callback: function(data) {
            window.location.href = "payment_verify.php?tx_ref=" + data.tx_ref;
        },
        onclose: function() {
            alert("Payment cancelled - your cart has been saved");
            window.location.href = "cart.php";
        },
        customizations: {
            title: "SMART LIFE Order",
            description: "Payment for your order #<?= $tx_ref ?>",
            logo: "https://yourdomain.com/logo.png"
        }
    });
}
makePayment();
</script>
</body>
</html>


