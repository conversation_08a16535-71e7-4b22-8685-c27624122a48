<?php require_once('header.php'); ?>

<?php
// Check for success or error messages from URL parameters
$success_message = isset($_GET['success']) ? urldecode($_GET['success']) : '';
$error_message = isset($_GET['error']) ? urldecode($_GET['error']) : '';
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Contact Messages</h1>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">Messages from Contact Form</h3>
                </div>
                <div class="box-body table-responsive">
                    <table id="example1" class="table table-bordered table-hover table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Subject</th>
                                <th>Message</th>
                                <th>Status</th>
                                <th>Received Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $i=0;
                            $statement = $pdo->prepare("SELECT * FROM tbl_contact_message ORDER BY created_at DESC");
                            $statement->execute();
                            $result = $statement->fetchAll(PDO::FETCH_ASSOC);
                            foreach ($result as $row) {
                                $i++;
                                ?>
                                <tr class="<?php echo ($row['status'] == 0) ? 'bg-r' : 'bg-g'; ?>">
                                    <td><?php echo $i; ?></td>
                                    <td><?php echo htmlspecialchars($row['name']); ?></td>
                                    <td><?php echo htmlspecialchars($row['email']); ?></td>
                                    <td><?php echo htmlspecialchars($row['subject']); ?></td>
                                    <td><?php echo htmlspecialchars(strip_tags(substr($row['message'], 0, 100))) . (strlen($row['message']) > 100 ? '...' : ''); ?></td>
                                    <td><?php echo $row['status'] ? '<span class="label label-success">Read</span>' : '<span class="label label-danger">Unread</span>'; ?></td>
                                    <td><?php echo $row['created_at']; ?></td>
                                    <td>
                                        <a href="#" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#messageModal<?php echo $row['id']; ?>">Read More</a>
                                        <a href="contact-message-status.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-xs">Mark as <?php echo $row['status'] ? 'Unread' : 'Read'; ?></a>
                                        <a href="#" class="btn btn-danger btn-xs" data-href="contact-message-delete.php?id=<?php echo $row['id']; ?>" data-toggle="modal" data-target="#confirm-delete">Delete</a>
                                    </td>
                                </tr>
                                <!-- Modal for full message -->
                                <div class="modal fade" id="messageModal<?php echo $row['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="messageModalLabel<?php echo $row['id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                                <h4 class="modal-title" id="messageModalLabel<?php echo $row['id']; ?>">Message from: <?php echo htmlspecialchars($row['name']); ?></h4>
                                            </div>
                                            <div class="modal-body">
                                                <p><strong>Email:</strong> <?php echo htmlspecialchars($row['email']); ?></p>
                                                <p><strong>Subject:</strong> <?php echo htmlspecialchars($row['subject']); ?></p>
                                                <p><strong>Received Date:</strong> <?php echo $row['created_at']; ?></p>
                                                <p><strong>Status:</strong> <?php echo $row['status'] ? 'Read' : 'Unread'; ?></p>
                                                <hr>
                                                <p><strong>Message:</strong></p>
                                                <div style="border: 1px solid #ddd; padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;">
                                                    <?php echo nl2br(htmlspecialchars(strip_tags($row['message']))); ?>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal for delete confirmation -->
<div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="confirmDeleteLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="confirmDeleteLabel">Delete Contact Message</h4>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this contact message?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <a class="btn btn-danger btn-ok">Delete</a>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $('#confirm-delete').on('show.bs.modal', function(e) {
        $(this).find('.btn-ok').attr('href', $(e.relatedTarget).data('href'));
    });
    
    // SweetAlert for success and error messages
    <?php if($success_message != ''): ?>
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: '<?php echo $success_message; ?>',
            timer: 2000,
            showConfirmButton: false
        });
    <?php endif; ?>
    
    <?php if($error_message != ''): ?>
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: '<?php echo $error_message; ?>',
            timer: 2000,
            showConfirmButton: false
        });
    <?php endif; ?>
</script>

<?php require_once('footer.php'); ?>
