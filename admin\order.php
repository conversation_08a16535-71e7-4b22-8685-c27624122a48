<?php require_once('header.php'); ?>

<?php
$error_message = '';
if(isset($_POST['form1'])) {
    $valid = 1;
    if(empty($_POST['subject_text'])) {
        $valid = 0;
        $error_message .= 'Subject can not be empty\n';
    }
    if(empty($_POST['message_text'])) {
        $valid = 0;
        $error_message .= 'Message can not be empty\n';
    }
    if($valid == 1) {
        $subject_text = strip_tags($_POST['subject_text']);
        $message_text = strip_tags($_POST['message_text']);

        // Getting Customer Email Address
        $statement = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id=?");
        $statement->execute(array($_POST['user_id']));
        $result = $statement->fetchAll(PDO::FETCH_ASSOC);
        foreach ($result as $row) {
            $cust_email = $row['cust_email'];
        }

        // Getting Admin Email Address
        $statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
        $statement->execute();
        $result = $statement->fetchAll(PDO::FETCH_ASSOC);
        foreach ($result as $row) {
            $admin_email = $row['contact_email'];
        }

        $order_detail = '';
        $statement = $pdo->prepare("SELECT * FROM orders WHERE id=?");
        $statement->execute(array($_POST['order_id']));
        $result = $statement->fetchAll(PDO::FETCH_ASSOC);
        foreach ($result as $row) {
            $order_detail .= '
Customer Name: '.$row['firstname'].' '.$row['lastname'].'<br>
Customer Email: '.$row['email'].'<br>
Phone: '.$row['phone'].'<br>
Address: '.$row['address'].'<br>
Order Date: '.$row['created_at'].'<br>
Transaction Reference: '.$row['tx_ref'].'<br>
Total Amount: '.$row['total_amount'].' '.$row['currency'].'<br>
Shipping Fee: '.$row['shipping_fee'].' '.$row['currency'].'<br>
Installation Fee: '.$row['installation_fee_total'].' '.$row['currency'].'<br>
Payment Status: '.$row['payment_status'].'<br>
            ';
        }

        // Get order items
        $statement = $pdo->prepare("SELECT
            oi.*,
            tc.color_name,
            tc.color_code
        FROM order_items oi
        LEFT JOIN tbl_color tc ON oi.color_id = tc.color_id
        WHERE oi.order_id=?");
        $statement->execute(array($_POST['order_id']));
        $result = $statement->fetchAll(PDO::FETCH_ASSOC);
        foreach ($result as $row) {
            $order_detail .= '
<br><b><u>Product Item</u></b><br>
Product Name: '.$row['product_name'].'<br>';

            if($row['variation_name']) {
                $order_detail .= 'Variation: '.$row['variation_name'].'<br>';
            }

            if($row['color_name']) {
                $order_detail .= 'Color: '.$row['color_name'].' <span style="display:inline-block;width:15px;height:15px;background-color:'.$row['color_code'].';border:1px solid #ccc;vertical-align:middle;"></span><br>';
            }

            $order_detail .= '
Quantity: '.$row['quantity'].'<br>
Unit Price: '.$row['unit_price'].'<br>';

            if($row['installation_fee'] > 0) {
                $order_detail .= 'Installation Fee: '.$row['installation_fee'].'<br>';
            }

            $order_detail .= 'Subtotal: '.$row['subtotal'].'<br>';
        }

        // Check if tbl_customer_message table exists
        try {
            $check_table = $pdo->query("SHOW TABLES LIKE 'tbl_customer_message'");
            $table_exists = $check_table->rowCount() > 0;
            
            if($table_exists) {
                $statement = $pdo->prepare("INSERT INTO tbl_customer_message (subject,message,order_detail,user_id) VALUES (?,?,?,?)");
                $statement->execute(array($subject_text,$message_text,$order_detail,$_POST['user_id']));
            } else {
                // Create the table if it doesn't exist
                $pdo->exec("CREATE TABLE IF NOT EXISTS tbl_customer_message (
                    customer_message_id INT(11) NOT NULL AUTO_INCREMENT,
                    subject VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    order_detail TEXT NOT NULL,
                    user_id INT(11) NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (customer_message_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
                
                // Now insert the data
                $statement = $pdo->prepare("INSERT INTO tbl_customer_message (subject,message,order_detail,user_id) VALUES (?,?,?,?)");
                $statement->execute(array($subject_text,$message_text,$order_detail,$_POST['user_id']));
            }
        } catch(PDOException $e) {
            // Silently continue if there's an error with the database operation
            // We'll still send the email even if we can't store the message in the database
        }

        // sending email
        $to_customer = $cust_email;
        $message = '
<html><body>
<h3>Message: </h3>
'.$message_text.'
<h3>Order Details: </h3>
'.$order_detail.'
</body></html>
';

        // Attempt to send email to customer using PHPMailer
        $mail_sent = false;
        try {
            // Include PHPMailer library
            require_once 'vendor/PHPMailer/src/Exception.php';
            require_once 'vendor/PHPMailer/src/PHPMailer.php';
            require_once 'vendor/PHPMailer/src/SMTP.php';
            
            // Create a new PHPMailer instance
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            
            // Disable debug output
            // $mail->SMTPDebug = PHPMailer\PHPMailer\SMTP::DEBUG_SERVER;
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = 'mail.smartlifetz.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'W;.7Ya5kQb5I8h';
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port = 465;
            
            // Additional settings to help with connection issues
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );
            
            // Set timeout
            $mail->Timeout = 60;
            
            // Set sender name
            $mail->setFrom('<EMAIL>', 'Smart Life Support');
            
            // Recipients
            $mail->addAddress($to_customer);
            $mail->addReplyTo($admin_email, 'Smart Home');
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject_text;
            $mail->Body    = $message;
            
            // Send the email
            $mail->send();
            $mail_sent = true;
        } catch (Exception $e) {
            // Catch any exceptions that might occur
            $mail_sent = false;
            // Log the error for debugging
            error_log("Message could not be sent. Mailer Error: {$mail->ErrorInfo}");
        }
        
        // Store the email content in the database for reference
        try {
            $pdo->exec("CREATE TABLE IF NOT EXISTS tbl_email_log (
                id INT(11) NOT NULL AUTO_INCREMENT,
                recipient VARCHAR(255) NOT NULL,
                subject VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                sent_status TINYINT(1) NOT NULL DEFAULT 0,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
            
            $stmt = $pdo->prepare("INSERT INTO tbl_email_log (recipient, subject, message, sent_status) VALUES (?, ?, ?, ?)");
            $stmt->execute(array($to_customer, $subject_text, $message, $mail_sent ? 1 : 0));
        } catch (PDOException $e) {
            // Silently continue if there's an error with the database operation
        }
        
        if ($mail_sent) {
            $success_message = 'Your email to customer is sent successfully.';
        } else {
            // Email could not be sent, but we've saved it to the database
            $success_message = 'Message saved successfully. Note: Email delivery may be delayed due to mail server configuration.';
        }
    }
}
?>
<?php
if($error_message != '') {
    echo "<script>alert('".$error_message."')</script>";
}
if($success_message != '') {
    echo "<script>alert('".$success_message."')</script>";
}
?>

<section class="content-header">
	<div class="content-header-left">
		<h1>View Orders</h1>
	</div>
</section>

<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">
        <div class="box-body table-responsive">
          <table id="example1" class="table table-bordered table-hover table-striped">
			<thead>
			    <tr>
			        <th>#</th>
                    <th>Customer</th>
			        <th>Order Details</th>
                    <th>Shipping Area</th>
                    <th>Payment Information</th>
                    <th>Total Amount</th>
                    <th>Payment Status</th>
                    <th>Verification Status</th>
                    <th>Action</th>
			    </tr>
			</thead>
            <tbody>
            	<?php
            	$i=0;
            	$statement = $pdo->prepare("SELECT * FROM orders ORDER by id DESC");
            	$statement->execute();
            	$result = $statement->fetchAll(PDO::FETCH_ASSOC);
            	foreach ($result as $row) {
            		$i++;
            		?>
					<tr class="<?php if($row['payment_status']=='failed'){echo 'bg-danger';}elseif($row['payment_status']=='success'){echo 'bg-success';}else{echo '';} ?>" style="line-height: 1.2;">
	                    <td><?php echo $i; ?></td>
	                    <td>
                            <b>Name:</b><br> <?php echo $row['firstname'].' '.$row['lastname']; ?><br>
                            <b>Email:</b><br> <?php echo $row['email']; ?><br>
                            <b>Phone:</b><br> <?php echo $row['phone']; ?><br><br>
                            <a href="#" data-toggle="modal" data-target="#model-<?php echo $i; ?>"class="btn btn-warning btn-xs" style="width:100%;margin-bottom:4px;">Send Message</a>
                            <div id="model-<?php echo $i; ?>" class="modal fade" role="dialog">
								<div class="modal-dialog">
									<div class="modal-content">
										<div class="modal-header">
											<button type="button" class="close" data-dismiss="modal">&times;</button>
											<h4 class="modal-title" style="font-weight: bold;">Send Message</h4>
										</div>
										<div class="modal-body" style="font-size: 14px">
											<form action="" method="post">
                                                <input type="hidden" name="user_id" value="<?php echo $row['user_id']; ?>">
                                                <input type="hidden" name="order_id" value="<?php echo $row['id']; ?>">
												<table class="table table-bordered">
													<tr>
														<td>Subject</td>
														<td>
                                                            <input type="text" name="subject_text" class="form-control" style="width: 100%;">
														</td>
													</tr>
                                                    <tr>
                                                        <td>Message</td>
                                                        <td>
                                                            <textarea name="message_text" class="form-control" cols="30" rows="10" style="width:100%;height: 200px;"></textarea>
                                                        </td>
                                                    </tr>
													<tr>
														<td></td>
														<td><input type="submit" value="Send Message" name="form1"></td>
													</tr>
												</table>
											</form>
										</div>
										<div class="modal-footer">
											<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
										</div>
									</div>
								</div>
							</div>
                        </td>
                        <td>
                            <button type="button" class="btn btn-info btn-xs" data-toggle="modal" data-target="#orderDetails-<?php echo $i; ?>">
                                View Details
                            </button>
                            <div id="orderDetails-<?php echo $i; ?>" class="modal fade" role="dialog">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            <h4 class="modal-title" style="font-weight: bold;">Order Details</h4>
                                        </div>
                                        <div class="modal-body" style="font-size: 14px">
                                            <?php
                                            $statement1 = $pdo->prepare("SELECT
                                                oi.*,
                                                tc.color_name,
                                                tc.color_code
                                            FROM order_items oi
                                            LEFT JOIN tbl_color tc ON oi.color_id = tc.color_id
                                            WHERE oi.order_id=?");
                                            $statement1->execute(array($row['id']));
                                            $result1 = $statement1->fetchAll(PDO::FETCH_ASSOC);
                                            foreach ($result1 as $row1) {
                                                echo '<b>Product:</b> '.$row1['product_name'];
                                                if($row1['variation_name']) {
                                                    echo '<br>(<b>Variation:</b> '.$row1['variation_name'].')';
                                                }
                                                echo '<br>(<b>Quantity:</b> '.$row1['quantity'];
                                                echo ', <b>Unit Price:</b> '.$row1['unit_price'].')';
                                                if($row1['color_name']) {
                                                    echo '<br>(<b>Color:</b> '.$row1['color_name'];
                                                    if($row1['color_code']) {
                                                        echo ' <span style="display:inline-block;width:15px;height:15px;background-color:'.$row1['color_code'].';border:1px solid #ccc;vertical-align:middle;"></span>';
                                                    }
                                                    echo ')';
                                                }
                                                if($row1['installation_fee'] > 0) {
                                                    echo '<br>(<b>Installation Fee:</b> '.$row1['installation_fee'].')';
                                                }
                                                echo '<br><br>';
                                            }
                                            ?>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td><?php echo $row['shipping_country']; ?></td>
                        <td>
                            <b>Transaction Ref:</b><br><?php echo $row['tx_ref']; ?><br>
                            <b>Order Date:</b><br><?php echo $row['created_at']; ?>
                        </td>
                        <td>
                            <b>Total:</b> <?php echo $row['total_amount'].' '.$row['currency']; ?><br>
                            <b>Shipping Fee:</b> <?php echo $row['shipping_fee'].' '.$row['currency']; ?><br>
                            <?php if($row['installation_fee_total'] > 0): ?>
                            <b>Installation Fee:</b> <?php echo $row['installation_fee_total'].' '.$row['currency']; ?><br>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div style="margin-bottom:5px;">
                                <?php if($row['payment_status']=='pending'): ?>
                                    <span class="label label-warning" style="font-size:11px;padding:3px 7px;border-radius:3px;">
                                        <i class="fa fa-clock-o"></i> Pending
                                    </span>
                                <?php elseif($row['payment_status']=='success'): ?>
                                    <span class="label label-success" style="font-size:11px;padding:3px 7px;border-radius:3px;">
                                        <i class="fa fa-check"></i> Success
                                    </span>
                                <?php else: ?>
                                    <span class="label label-danger" style="font-size:11px;padding:3px 7px;border-radius:3px;">
                                        <i class="fa fa-times"></i> Failed
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="btn-group btn-group-xs" style="width:100%;">
                                <button type="button" class="btn <?php if($row['payment_status']=='success') echo 'btn-success active'; else echo 'btn-default'; ?>" style="width:33%;" onclick="window.location='order-payment-status.php?id=<?php echo $row['id']; ?>&status=success'">
                                    <i class="fa fa-check"></i>
                                </button>
                                <button type="button" class="btn <?php if($row['payment_status']=='pending') echo 'btn-warning active'; else echo 'btn-default'; ?>" style="width:33%;" onclick="window.location='order-payment-status.php?id=<?php echo $row['id']; ?>&status=pending'">
                                    <i class="fa fa-clock-o"></i>
                                </button>
                                <button type="button" class="btn <?php if($row['payment_status']=='failed') echo 'btn-danger active'; else echo 'btn-default'; ?>" style="width:34%;" onclick="window.location='order-payment-status.php?id=<?php echo $row['id']; ?>&status=failed'">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </td>
                        <td>
                            <?php if($row['payment_status'] == 'success'): ?>
                                <div style="margin-bottom:5px;">
                                    <?php if($row['verification_status']=='verified'): ?>
                                        <span class="label label-success" style="font-size:11px;padding:3px 7px;border-radius:3px;">
                                            <i class="fa fa-check"></i> Verified
                                        </span>
                                    <?php elseif($row['verification_status']=='waiting'): ?>
                                        <span class="label label-warning" style="font-size:11px;padding:3px 7px;border-radius:3px;">
                                            <i class="fa fa-clock-o"></i> Waiting
                                        </span>
                                    <?php else: ?>
                                        <span class="label label-danger" style="font-size:11px;padding:3px 7px;border-radius:3px;">
                                            <i class="fa fa-times"></i> Not Verified
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="btn-group btn-group-xs" style="width:100%;">
                                    <button type="button" class="btn <?php if($row['verification_status']=='verified') echo 'btn-success active'; else echo 'btn-default'; ?>" style="width:33%;" onclick="window.location='order-verification-status.php?id=<?php echo $row['id']; ?>&status=verified'">
                                        <i class="fa fa-check"></i>
                                    </button>
                                    <button type="button" class="btn <?php if($row['verification_status']=='waiting') echo 'btn-warning active'; else echo 'btn-default'; ?>" style="width:33%;" onclick="window.location='order-verification-status.php?id=<?php echo $row['id']; ?>&status=waiting'">
                                        <i class="fa fa-clock-o"></i>
                                    </button>
                                    <button type="button" class="btn <?php if($row['verification_status']=='not_verified') echo 'btn-danger active'; else echo 'btn-default'; ?>" style="width:34%;" onclick="window.location='order-verification-status.php?id=<?php echo $row['id']; ?>&status=not_verified'">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            <?php else: ?>
                                <span class="label label-default">N/A</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="javascript:void(0)" class="btn btn-danger btn-xs" data-href="orders-delete.php?id=<?php echo $row['id']; ?>" data-toggle="modal" data-target="#confirm-delete">Delete</a>
                        </td>
                    </tr>
            		<?php
            	}
            	?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</section>

<div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">Delete Confirmation</h4>
            </div>
            <div class="modal-body">
                Sure you want to delete this item?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <a class="btn btn-danger btn-ok">Delete</a>
            </div>
        </div>
    </div>
</div>

<?php require_once('footer.php'); ?>
