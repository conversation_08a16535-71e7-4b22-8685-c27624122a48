# SMART LIFE eCommerce Platform Documentation

## 1. Project Overview

### Introduction
SMART LIFE is an eCommerce platform specializing in smart home automation products. The platform offers a comprehensive shopping experience with features including product browsing, user registration with OTP verification, shopping cart management, secure checkout, and order tracking.

### Project Structure
The project follows a modular structure:
- `/admin/` - Admin panel and backend management
- `/ecom/` - Customer-facing frontend
- `/assets/` - Static assets (images, etc.)
- `/vendor/` - Third-party libraries

### Technology Stack
- **Backend**: PHP 7.4+
- **Database**: MySQL (MariaDB 10.4+)
- **Frontend**: HTML5, CSS3, JavaScript
- **Libraries**: PHPMailer, Flutterwave Payment Gateway
- **Server**: Apache (XAMPP)

## 2. Database Structure

### Key Tables
- `tbl_customer` - User accounts and profiles
  - Contains customer information (name, email, password, address)
  - Tracks registration date and last login
  - Stores customer status (active/inactive)

- `tbl_product` - Product information
  - Stores product details (name, price, description)
  - Tracks inventory with `p_qty` field
  - Contains installation fee information
  - Links to categories via `tcat_id` and `mcat_id`

- `tbl_color` - Product color options
  - Stores color name and color code (hex)
  - Referenced by `order_items.color_id`

- `tbl_top_category`, `tbl_mid_category`, `tbl_end_category` - Product categorization
  - Hierarchical category structure
  - Controls menu display with `show_on_menu` flag

- `orders` - Order information
  - Stores transaction reference (`tx_ref`)
  - Contains customer details and shipping information
  - Tracks payment status and verification status
  - Stores total amounts, shipping fees, and installation fees

- `order_items` - Individual items in orders
  - Links to products and variations
  - Stores quantity, price, and color information
  - Tracks installation fees per item

- `tbl_settings` - System configuration
  - Stores site-wide settings
  - Contains payment gateway credentials
  - Manages default installation fees

### Relationships
- Products belong to categories (top, mid, and end categories)
- Products have colors stored in `tbl_color`
- Orders contain multiple order items
- Customers place orders
- Product variations are linked to base products

## 3. User Management

### Authentication
- Secure login system with password hashing (BCRYPT)
- Session-based authentication
- Password protection

### Registration Process
1. User submits registration form
2. System validates input
3. OTP is generated and sent to user's email
4. User verifies email by entering OTP
5. Account is created and user is automatically logged in

### OTP Verification
- 6-digit numeric OTP
- 10-minute expiration
- Resend functionality with cooldown

## 4. Product Management

### Product Structure
Products are organized in a hierarchical category system:
- Top Categories (e.g., SMART DOORBELL)
- Mid Categories
- End Categories

### Product Attributes
- Basic information (name, price, description)
- Stock quantity
- Featured status
- Installation fee
- Color options
- Product images

## 5. Shopping Cart & Checkout

### Cart Functionality (`cart.php`)
- Add products to cart with color and variation selection
- Update product quantities
- Remove items from cart
- Session-based cart storage for guests
- Database-synchronized cart for logged-in users
- Installation service option per product
- Real-time subtotal and total calculation

### Cart Implementation
- Client-side storage using localStorage
- Server-side session storage (`$_SESSION['cart']`)
- Synchronization between client and server (`sync_cart.php`)
- Cart item unique keys based on product ID, variation ID, and color ID

### Checkout Process
1. Cart review and final confirmation (`cart.php`)
   - Review items, quantities, and prices
   - Apply shipping options
   - Calculate final totals including installation fees

2. Checkout initiation (`start_checkout.php`)
   - Verify product availability and prices
   - Create order record in database
   - Generate transaction reference

3. Payment processing (`payment.php`)
   - Integration with Flutterwave checkout
   - Multiple payment method options
   - Secure transaction handling

4. Payment verification (`payment_verify.php`)
   - Verify payment status with payment gateway
   - Update order status in database
   - Redirect to success or failure page

5. Order confirmation (`order_success.php`)
   - Display order summary
   - Provide tracking information
   - Clear cart after successful purchase

### Payment Integration
- Flutterwave payment gateway
  - Test mode configuration for development
  - Production mode for live transactions
  - Public/private key management

- Supported Payment Methods
  - Credit/debit cards
  - Mobile money
  - Bank transfers
  - USSD
  - Apple Pay

- Transaction Security
  - Secure API communication
  - Transaction verification
  - Error handling and logging

## 6. Order Management

### Order Processing
1. Order creation
2. Payment verification
3. Order fulfillment
4. Shipping
5. Delivery

### Order Statuses
- Pending
- Paid
- Processing
- Shipped
- Delivered
- Cancelled

## 7. Admin Panel

### Access and Security
- Admin authentication via `admin/login.php`
- Role-based access control (Super Admin, Admin)
- Session management and timeout
- CSRF protection for all forms

### Dashboard (`admin/index.php`)
- Sales overview and statistics
- Recent orders with status indicators
- Low stock alerts
- Quick access to key management functions

### Product Management
- Add products (`admin/product-add.php`)
  - Set basic information, pricing, and inventory
  - Upload product images
  - Configure installation fees
  - Assign categories and colors

- Edit products (`admin/product-edit.php`)
  - Update all product attributes
  - Manage product images
  - Change stock levels

- Product listing (`admin/product.php`)
  - View all products with filtering options
  - Bulk actions for multiple products
  - Quick status changes

- SKU Management (`admin/sku-management.php`)
  - Manage product variations
  - Set pricing for different variants
  - Track inventory at the SKU level

### Category Management
- Three-level category hierarchy:
  - Top categories (`admin/top-category.php`)
  - Mid categories (`admin/mid-category.php`)
  - End categories (`admin/end-category.php`)
- Category visibility control
- Category relationship management

### Order Management
- Order listing (`admin/order.php`)
  - View all orders with filtering
  - Search by customer, date, or status
  - Export order data

- Order processing
  - Change payment status (`admin/order-payment-status.php`)
  - Update verification status (`admin/order-verification-status.php`)
  - Generate shipping labels (`admin/generate-shipping-label.php`)
  - Track shipping (`admin/shipping-tracking.php`)

### Customer Management
- Customer listing (`admin/customer.php`)
  - View all registered customers
  - Filter and search functionality
  - Account status management

- Customer details
  - View customer information
  - Order history
  - Account activation/deactivation

- Customer communication
  - View customer messages
  - Respond to inquiries
  - Send notifications

### Settings and Configuration
- Site settings (`admin/settings.php`)
  - General site configuration
  - Payment gateway settings
  - Email templates
  - Default fees and charges

## 8. Security Features

### Authentication Security
- Password hashing with BCRYPT for customer accounts
- MD5 hashing for admin accounts (note: consider upgrading to BCRYPT)
- Session protection with regeneration on login
- CSRF protection for all forms using `CSRF_Protect.php`
- Login attempt monitoring
- Session timeout management

### Data Protection
- Input validation and sanitization
  - Email validation with `filter_input()`
  - Form input sanitization with `strip_tags()`
  - Type checking and conversion

- SQL Injection Prevention
  - Prepared statements for all database queries
  - Parameter binding for user inputs
  - PDO for database access with error mode set to exceptions

- XSS Prevention
  - Output escaping with `htmlspecialchars()`
  - Content Security Policy considerations
  - Proper encoding of user-generated content

### Error Handling
- Custom error logging
- Production vs. development error display
- Graceful error recovery
- User-friendly error messages

### File Upload Security
- File type validation
- File size restrictions
- Secure file naming with randomization
- Storage in protected directories

### OTP Security
- Time-limited OTP codes (10-minute expiration)
- Secure OTP generation
- Rate limiting for OTP requests
- Secure storage in session

## 9. Front-end Architecture

### Key Templates
- `index.php` - Homepage with featured products, categories, and promotional content
- `product_detail.php` - Detailed product information, images, and add-to-cart functionality
- `cart.php` - Shopping cart with quantity management and checkout options
- `login.php`/`register.php` - User authentication forms with validation
- `all_products.php` - Complete product catalog with filtering and sorting
- `verify_otp.php` - OTP verification during registration
- `payment.php` - Payment processing interface
- `order_success.php` - Order confirmation page

### CSS Organization
- `style.css` - Main styles for layout, typography, and common components
- `auth.css` - Styles for login and registration forms
- `cart.css` - Shopping cart and checkout process styling
- `product.css` - Product listing and detail page styles

### Responsive Design
- Mobile-first approach
- Responsive navigation menu
- Adaptive product grid layout
- Touch-friendly interface elements

### JavaScript Functionality
- Cart management (add, update, remove items)
- Product filtering by category and attributes
- Form validation for user inputs
- Payment processing integration
- OTP countdown timer
- Toast notifications for user feedback
- Dynamic shipping calculation

## 10. Third-party Integrations

### Flutterwave Payment
- Test mode configuration
- Multiple payment methods
- Transaction verification

### Email Service
- PHPMailer integration
- OTP delivery
- Order notifications

## 11. Installation & Setup

### System Requirements
- **Web Server**: Apache 2.4+ with mod_rewrite enabled
- **PHP**: Version 7.4+ (recommended 8.0+)
- **Database**: MySQL 5.7+ or MariaDB 10.4+
- **Extensions**: PDO, GD, cURL, mbstring, fileinfo
- **Mail Server**: SMTP access for sending emails
- **Storage**: Minimum 500MB for application files and product images
- **Memory**: 256MB+ PHP memory limit recommended

### Installation Steps
1. **Server Setup**
   - Install XAMPP, WAMP, or equivalent for local development
   - Ensure Apache and MySQL services are running
   - Enable required PHP extensions

2. **Application Deployment**
   - Clone or extract repository to web server directory (e.g., `c:\xampp\htdocs\ecom\`)
   - Set appropriate file permissions:
     - Read/execute for most directories
     - Read/write for upload directories and cache

3. **Database Configuration**
   - Create a new database named `ecommerceweb`
   - Import database schema from `ecommerceweb.sql`
   - Configure database connection in `admin/inc/config.php`:
     ```php
     $dbhost = 'localhost';
     $dbname = 'ecommerceweb';
     $dbuser = 'root';
     $dbpass = '';
     ```

4. **Email Configuration**
   - Configure email settings for OTP and notifications
   - Update SMTP settings in the appropriate files
   - Test email functionality

5. **Payment Gateway Setup**
   - Create a Flutterwave developer account
   - Obtain API keys (test mode for development)
   - Configure keys in the application settings
   - Test payment flow in sandbox mode

6. **Final Configuration**
   - Set up site settings in admin panel
   - Configure default installation fees
   - Set up shipping countries and rates
   - Create initial product categories

7. **Security Hardening**
   - Update default admin credentials
   - Secure sensitive directories
   - Configure proper error reporting
   - Set up SSL if deploying to production

## 12. Maintenance & Troubleshooting

### Common Issues and Solutions

#### Payment Gateway Issues
- **Problem**: Payment gateway not connecting
  - **Solution**: Verify API keys and check Flutterwave dashboard for service status
  - **Files to check**: `payment.php`, `payment_verify.php`

- **Problem**: Payment verification failures
  - **Solution**: Check transaction logs in Flutterwave dashboard and verify webhook configuration
  - **Files to check**: `payment_verify.php`

#### Email Delivery Problems
- **Problem**: OTP emails not being sent
  - **Solution**: Verify SMTP settings, check spam filters, ensure proper email configuration
  - **Files to check**: `functions.php` (sendEmailWithPHPMailer function)

- **Problem**: Email formatting issues
  - **Solution**: Test email templates with different email clients, fix HTML/CSS compatibility issues
  - **Files to check**: Email template sections in registration and order processing files

#### Session Management
- **Problem**: Users being logged out unexpectedly
  - **Solution**: Check session timeout settings, verify session storage configuration
  - **Files to check**: PHP configuration, session handling in authentication files

- **Problem**: Cart items disappearing
  - **Solution**: Debug session storage, check for conflicts between localStorage and session storage
  - **Files to check**: `cart.php`, `sync_cart.php`, JavaScript cart handling

#### Database Issues
- **Problem**: Database connection errors
  - **Solution**: Verify database credentials, check database server status
  - **Files to check**: `admin/inc/config.php`

- **Problem**: Slow queries
  - **Solution**: Optimize database indexes, review query performance
  - **Files to check**: Complex queries in product listing and order management

### Regular Maintenance Tasks

#### Daily/Weekly Tasks
- Monitor error logs for unexpected issues
- Check for failed payment transactions
- Review new customer registrations
- Monitor inventory levels for popular products

#### Monthly Tasks
- Database backups
- Clean up temporary files and old logs
- Review and update product information
- Check for security updates

#### Quarterly Tasks
- Full system backup
- Performance optimization
  - Database query optimization
  - Image optimization
  - Cache configuration
- Security audit
  - Check for outdated dependencies
  - Review access controls
  - Test authentication systems

#### Annual Tasks
- Comprehensive security review
- Update SSL certificates
- Review and update privacy policies
- Major version upgrades of dependencies

## 13. Future Enhancements

### Potential Improvements

#### Security Enhancements
- Upgrade admin authentication from MD5 to BCRYPT or Argon2
- Implement two-factor authentication for admin accounts
- Add CAPTCHA for login attempts to prevent brute force attacks
- Implement more comprehensive input validation

#### User Experience
- Develop a mobile app for iOS and Android
- Implement wishlist functionality
- Add product comparison features
- Enhance product search with filters and sorting options
- Implement product reviews and ratings

#### Business Features
- Customer loyalty program with points and rewards
- Subscription-based purchasing options
- Bulk order discounts
- Affiliate marketing system
- Gift cards and promotional codes

#### Technical Improvements
- Implement caching for improved performance
- Migrate to a modern PHP framework (Laravel, Symfony)
- Develop a comprehensive API for third-party integrations
- Implement WebSockets for real-time notifications
- Add multi-language support

#### Analytics and Reporting
- Enhanced sales reporting and analytics
- Customer behavior tracking
- Inventory forecasting
- Marketing campaign effectiveness tracking
- Custom report generation

## 14. Conclusion

The SMART LIFE eCommerce platform provides a comprehensive solution for selling smart home automation products online. The system includes all essential eCommerce functionality including product management, user authentication with OTP verification, shopping cart, secure checkout with Flutterwave integration, and order management.

The platform is built with a focus on security, usability, and maintainability. The modular architecture allows for easy extension and customization to meet specific business requirements.

By following the documentation provided here, developers can understand the system architecture, maintain the existing codebase, and implement new features as needed.

---

*Documentation created for SMART LIFE eCommerce Platform*
*Last updated: June 12, 2024*
