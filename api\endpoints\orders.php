<?php
/**
 * Orders Endpoints
 * Handles order management operations
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path and ID
$sub_path = $segments[1] ?? '';
$order_id = $segments[2] ?? null;

switch ($method) {
    case 'GET':
        if ($order_id) {
            handleGetOrder($db, $order_id);
        } else {
            handleGetOrders($db);
        }
        break;
        
    case 'POST':
        if ($sub_path === 'create') {
            handleCreateOrder($db, $input);
        } elseif ($sub_path === 'payment-verify') {
            handleVerifyPayment($db, $input);
        } else {
            Response::notFound('Order endpoint not found');
        }
        break;
        
    case 'PUT':
        AuthMiddleware::requireAdmin();
        if (!$order_id) {
            Response::error('Order ID is required', 400);
        }
        handleUpdateOrder($db, $order_id, $input);
        break;
        
    default:
        Response::methodNotAllowed(['GET', 'POST', 'PUT']);
}

/**
 * Get user orders with pagination
 */
function handleGetOrders($db) {
    $user = AuthMiddleware::requireAuth();
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
    $status = $_GET['status'] ?? null;
    
    // Build WHERE clause
    $where_conditions = ['user_id = ?'];
    $params = [$user['user_id']];
    
    if ($status) {
        $where_conditions[] = 'payment_status = ?';
        $params[] = $status;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            id,
            tx_ref,
            firstname,
            lastname,
            email,
            phone,
            address,
            total_amount,
            shipping_fee,
            installation_fee_total,
            currency,
            payment_status,
            verification_status,
            shipping_status,
            tracking_number,
            carrier,
            estimated_delivery,
            created_at,
            updated_at
        FROM orders
        WHERE {$where_clause}
        ORDER BY created_at DESC
    ";
    
    $result = $db->paginate($sql, $params, $page, $limit);
    
    // Format orders
    $orders = array_map('formatOrder', $result['data']);
    
    Response::paginated($orders, $result['total'], $page, $limit, 'Orders retrieved successfully');
}

/**
 * Get single order with items
 */
function handleGetOrder($db, $order_id) {
    $user = AuthMiddleware::requireAuth();
    
    // Get order details
    $order = $db->fetchOne(
        "SELECT * FROM orders WHERE id = ? AND user_id = ?",
        [$order_id, $user['user_id']]
    );
    
    if (!$order) {
        Response::notFound('Order not found');
    }
    
    // Get order items
    $items = $db->fetchAll(
        "SELECT * FROM order_items WHERE order_id = ? ORDER BY id",
        [$order_id]
    );
    
    // Get shipping history
    $shipping_history = $db->fetchAll(
        "SELECT * FROM shipping_history WHERE order_id = ? ORDER BY created_at DESC",
        [$order_id]
    );
    
    $formatted_order = formatOrder($order);
    $formatted_order['items'] = array_map('formatOrderItem', $items);
    $formatted_order['shipping_history'] = array_map('formatShippingHistory', $shipping_history);
    
    Response::success($formatted_order, 'Order details retrieved successfully');
}

/**
 * Create new order
 */
function handleCreateOrder($db, $input) {
    $user = AuthMiddleware::requireAuth();
    
    $validator = new Validator($input);
    $validator->required('items')
             ->required('shipping_address')
             ->required('shipping_country_id')->integer('shipping_country_id')
             ->required('phone');
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    $items = $input['items'];
    $shipping_address = $input['shipping_address'];
    $shipping_country_id = $input['shipping_country_id'];
    $phone = $input['phone'];
    
    if (empty($items)) {
        Response::error('Order must contain at least one item', 400);
    }
    
    try {
        $db->beginTransaction();
        
        // Calculate totals
        $subtotal = 0;
        $installation_total = 0;
        $order_items = [];
        
        foreach ($items as $item) {
            // Validate item
            $item_validator = new Validator($item);
            $item_validator->required('product_id')->integer('product_id')
                           ->required('quantity')->integer('quantity')->min('quantity', 1);
            
            if ($item_validator->fails()) {
                throw new Exception('Invalid item data: ' . $item_validator->getFirstError());
            }
            
            // Get product details
            $product = $db->fetchOne(
                "SELECT * FROM tbl_product WHERE p_id = ? AND p_is_active = 1",
                [$item['product_id']]
            );
            
            if (!$product) {
                throw new Exception("Product {$item['product_id']} not found");
            }
            
            $unit_price = $product['p_current_price'];
            $variation_name = null;
            $variation_price = null;
            
            // Handle variations
            if (!empty($item['variation_id'])) {
                $variation = $db->fetchOne(
                    "SELECT * FROM tbl_product_variation WHERE variation_id = ? AND p_id = ?",
                    [$item['variation_id'], $item['product_id']]
                );
                
                if ($variation) {
                    $variation_name = $variation['variation_name'];
                    $variation_price = $variation['variation_price'];
                    if ($variation_price) {
                        $unit_price = $variation_price;
                    }
                }
            }
            
            $quantity = $item['quantity'];
            $item_subtotal = $unit_price * $quantity;
            $installation_fee = ($item['installation'] ?? false) ? 
                ($product['installation_fee'] ?? DEFAULT_INSTALLATION_FEE) : 0;
            
            $order_items[] = [
                'product_id' => $item['product_id'],
                'variation_id' => $item['variation_id'] ?? null,
                'variation_name' => $variation_name,
                'variation_price' => $variation_price,
                'product_name' => $product['p_name'],
                'quantity' => $quantity,
                'unit_price' => $unit_price,
                'subtotal' => $item_subtotal,
                'total' => $item_subtotal + $installation_fee,
                'color_id' => $item['color_id'] ?? null,
                'installation_fee' => $installation_fee
            ];
            
            $subtotal += $item_subtotal;
            $installation_total += $installation_fee;
        }
        
        // Get shipping cost
        $shipping_cost = $db->fetchOne(
            "SELECT amount FROM tbl_shipping_cost WHERE country_id = ?",
            [$shipping_country_id]
        );
        
        $shipping_fee = $shipping_cost ? $shipping_cost['amount'] : 0;
        
        // Get shipping country name
        $country = $db->fetchOne(
            "SELECT country_name FROM tbl_country WHERE country_id = ?",
            [$shipping_country_id]
        );
        
        $shipping_country = $country ? $country['country_name'] : '';
        
        $total_amount = $subtotal + $shipping_fee + $installation_total;
        
        // Generate transaction reference
        $tx_ref = 'ORDER_' . time() . '_' . substr(md5(uniqid()), 0, 8);
        
        // Create order
        $order_data = [
            'tx_ref' => $tx_ref,
            'user_id' => $user['user_id'],
            'firstname' => $user['first_name'],
            'lastname' => $user['last_name'],
            'email' => $user['email'],
            'phone' => $phone,
            'address' => $shipping_address,
            'shipping_country_id' => $shipping_country_id,
            'total_amount' => $total_amount,
            'shipping_fee' => $shipping_fee,
            'shipping_country' => $shipping_country,
            'installation_fee_total' => $installation_total,
            'currency' => DEFAULT_CURRENCY,
            'payment_status' => 'pending',
            'verification_status' => 'waiting',
            'shipping_status' => 'processing',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $order_id = $db->insert('orders', $order_data);
        
        // Insert order items
        foreach ($order_items as $item) {
            $item['order_id'] = $order_id;
            $db->insert('order_items', $item);
        }
        
        $db->commit();
        
        // Clear cart after successful order
        session_start();
        $_SESSION['cart'] = [];
        
        Response::success([
            'order_id' => (int)$order_id,
            'tx_ref' => $tx_ref,
            'total_amount' => (float)$total_amount,
            'currency' => DEFAULT_CURRENCY,
            'payment_url' => "/api/v1/orders/{$order_id}/payment"
        ], 'Order created successfully', 201);
        
    } catch (Exception $e) {
        $db->rollback();
        Response::error($e->getMessage(), 400);
    }
}

/**
 * Update order status (admin only)
 */
function handleUpdateOrder($db, $order_id, $input) {
    // Check if order exists
    $order = $db->fetchOne("SELECT * FROM orders WHERE id = ?", [$order_id]);
    
    if (!$order) {
        Response::notFound('Order not found');
    }
    
    $allowed_fields = [
        'payment_status', 'verification_status', 'shipping_status',
        'tracking_number', 'carrier', 'estimated_delivery', 'shipping_notes'
    ];
    
    $update_data = [];
    foreach ($allowed_fields as $field) {
        if (isset($input[$field])) {
            $update_data[$field] = $input[$field];
        }
    }
    
    if (empty($update_data)) {
        Response::error('No valid fields to update', 400);
    }
    
    $update_data['updated_at'] = date('Y-m-d H:i:s');
    
    $db->update('orders', $update_data, 'id = ?', [$order_id]);
    
    Response::success(null, 'Order updated successfully');
}

/**
 * Verify payment status
 */
function handleVerifyPayment($db, $input) {
    $validator = new Validator($input);
    $validator->required('tx_ref');
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    $tx_ref = $input['tx_ref'];
    
    // Get order
    $order = $db->fetchOne("SELECT * FROM orders WHERE tx_ref = ?", [$tx_ref]);
    
    if (!$order) {
        Response::notFound('Order not found');
    }
    
    // Here you would integrate with your payment provider to verify the payment
    // For now, we'll just return the current status
    
    Response::success([
        'order_id' => (int)$order['id'],
        'tx_ref' => $order['tx_ref'],
        'payment_status' => $order['payment_status'],
        'total_amount' => (float)$order['total_amount'],
        'currency' => $order['currency']
    ], 'Payment status retrieved successfully');
}

/**
 * Format order data for API response
 */
function formatOrder($order) {
    return [
        'id' => (int)$order['id'],
        'tx_ref' => $order['tx_ref'],
        'customer_name' => trim($order['firstname'] . ' ' . $order['lastname']),
        'email' => $order['email'],
        'phone' => $order['phone'],
        'address' => $order['address'],
        'shipping_country' => $order['shipping_country'],
        'total_amount' => (float)$order['total_amount'],
        'shipping_fee' => (float)$order['shipping_fee'],
        'installation_fee_total' => (float)$order['installation_fee_total'],
        'currency' => $order['currency'],
        'payment_status' => $order['payment_status'],
        'verification_status' => $order['verification_status'],
        'shipping_status' => $order['shipping_status'],
        'tracking_number' => $order['tracking_number'],
        'carrier' => $order['carrier'],
        'estimated_delivery' => $order['estimated_delivery'],
        'created_at' => $order['created_at'],
        'updated_at' => $order['updated_at']
    ];
}

/**
 * Format order item data
 */
function formatOrderItem($item) {
    return [
        'id' => (int)$item['id'],
        'product_id' => (int)$item['product_id'],
        'variation_id' => $item['variation_id'] ? (int)$item['variation_id'] : null,
        'variation_name' => $item['variation_name'],
        'product_name' => $item['product_name'],
        'quantity' => (int)$item['quantity'],
        'unit_price' => (float)$item['unit_price'],
        'subtotal' => (float)$item['subtotal'],
        'total' => (float)$item['total'],
        'installation_fee' => (float)$item['installation_fee']
    ];
}

/**
 * Format shipping history data
 */
function formatShippingHistory($history) {
    return [
        'id' => (int)$history['id'],
        'location' => $history['location'],
        'status' => $history['status'],
        'description' => $history['description'],
        'created_at' => $history['created_at']
    ];
}
?>
