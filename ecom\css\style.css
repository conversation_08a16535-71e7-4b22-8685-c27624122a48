@import url("https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700&display=swap");

:root {
  --primary: #ffffff;
  --secondary: #00c2ff;
  --accent: #081725;
  --dark: #333333;
  --light: #f8f9fa;
  --gray: #6c757d;
  --text: #2d2d2d;
  --overlay: rgba(255, 255, 255, 0.7);
  --glass: rgba(255, 255, 255, 0.8);
  --shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  --border: #eeeeee;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Sora", sans-serif;
  background-color: var(--primary);
  color: var(--text);
  overflow-x: hidden;
  line-height: 1.6;
}

h1,
h2,
h3,
h4 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--dark);
}

p {
  font-weight: 300;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

img {
  max-width: 100%;
  display: block;
}

.container {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

section {
  padding: 120px 0;
  position: relative;
}

.btn {
  display: inline-flex;
  align-items: center;
  padding: 12px 32px;
  background-color: var(--secondary);
  color: var(--primary);
  border: none;
  font-weight: 500;
  text-decoration: none;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-size: 0.85rem;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 1;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: var(--dark);
  z-index: -1;
  transition: width 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.btn:hover {
  color: var(--primary);
}

.btn:hover::before {
  width: 100%;
}

.btn-icon {
  margin-left: 10px;
  transition: transform 0.3s ease;
}

.btn:hover .btn-icon {
  transform: translateX(5px);
}

.add-to-cart {
  background-color: var(--secondary);
  color: var(--primary);
  padding: 8px 15px;
  text-decoration: none;
  display: inline-block;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.product-card:hover .add-to-cart {
  bottom: 0;
  background-color: var(--accent);
}
.cart-icon {
  position: relative;
  margin-left: 20px;
  display: inline-block;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--accent);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Header Styles */
header {
  position: fixed;
  width: 100%;
  z-index: 1000;
  transition: all 0.4s ease;
  padding: 30px 0;
}

header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--dark);
  letter-spacing: 2px;
}

.logo span {
  color: var(--secondary);
}

.nav-links {
  display: flex;
  gap: 40px;
}

.nav-links a {
  color: var(--dark);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: 1px;
  position: relative;
  padding: 5px 0;
  transition: color 0.3s ease;
}

.nav-links a::before {
  content: "";
  position: absolute;
  width: 100%;
  transform: scaleX(0);
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--secondary);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
}

.nav-links a:hover {
  color: var(--secondary);
}

.nav-links a:hover::before {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.mobile-menu {
  display: none;
  cursor: pointer;
  z-index: 1001;
}

.menu-btn {
  width: 30px;
  height: 20px;
  position: relative;
}

.menu-btn span {
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: var(--dark);
  transition: all 0.3s ease;
  left: 0;
}

.menu-btn span:nth-child(1) {
  top: 0;
}

.menu-btn span:nth-child(2) {
  top: 9px;
  width: 80%;
}

.menu-btn span:nth-child(3) {
  bottom: 0;
}

.menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Hero Section */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  background-color: var(--light);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(248, 249, 250, 0.9) 100%
  );
  z-index: 1;
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.6;
}

.hero-content {
  position: relative;
  z-index: 8;
  max-width: 500px;
}

.hero-tag {
  display: inline-block;
  padding: 5px 15px;
  background-color: var(--accent);
  color: var(--primary);
  font-size: 0.8rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  border-radius: 3px;
}

.hero-title {
  font-size: 5.5rem;
  line-height: 1;
  margin-bottom: 30px;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeUp 0.8s forwards 0.2s;
}

.hero-title span {
  color: var(--secondary);
  display: block;
}

.hero-subtitle {
  font-size: 2.2rem;
  margin-bottom: 40px;
  max-width: 600px;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeUp 0.8s forwards 0.4s;
}

.hero-btn {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeUp 0.8s forwards 0.6s;
}

.scroll-indicator {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-80%);
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  animation: fadeIn 1s forwards 1s;
}

.scroll-indicator p {
  font-size: 0.8rem;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 10px;
  color: var(--dark);
}

.scroll-arrow {
  width: 30px;
  height: 60px;
  border: 2px solid var(--secondary);
  border-radius: 15px;
  position: relative;
}

.scroll-arrow::before {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--secondary);
  left: 50%;
  top: 10px;
  transform: translateX(-50%);
  animation: scrollDown 2s infinite;
}

/* About Section */
.about {
  background-color: var(--primary);
  overflow: hidden;
}

.section-header {
  margin-bottom: 60px;
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 3rem;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: "";
  position: absolute;
  width: 60px;
  height: 3px;
  background-color: var(--secondary);
  bottom: -10px;
  left: 0;
}

.about-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.about-img {
  position: relative;
  height: 600px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.about-img img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  transition: transform 0.7s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.about-img:hover img {
  transform: scale(1.05);
}

.about-img::before {
  content: "";
  position: absolute;
  top: -20px;
  left: -20px;
  width: 100px;
  height: 100px;
  border-top: 2px solid var(--secondary);
  border-left: 2px solid var(--secondary);
  z-index: 1;
}

.about-img::after {
  content: "";
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  border-bottom: 2px solid var(--secondary);
  border-right: 2px solid var(--secondary);
  z-index: 1;
}

.about-content h3 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--secondary);
}

.features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-top: 40px;
}

.feature {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.feature-icon {
  font-size: 1.5rem;
  color: var(--secondary);
  flex-shrink: 0;
}

.feature-content h4 {
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.feature-content p {
  font-size: 0.9rem;
  margin-bottom: 0;
}

/* Products Section */
.products {
  background-color: var(--light);
  position: relative;
  overflow: hidden;
}

.products-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  justify-content: space-between;
}

.product-card {
  flex: 1 0 calc(33.333% - 20px); /* Three items per row */
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow);
  transition: transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1),
    box-shadow 0.5s ease;
  max-width: calc(33.333% - 20px);
  min-width: 250px;
  opacity: 0;
  transform: translateY(50px);
}

.product-card:nth-child(1) {
  animation: fadeUp 0.8s forwards 0.2s;
}

.product-card:nth-child(2) {
  animation: fadeUp 0.8s forwards 0.4s;
}

.product-card:nth-child(3) {
  animation: fadeUp 0.8s forwards 0.6s;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.product-img {
  height: 300px;
  overflow: hidden;
}

.product-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.product-card:hover .product-img img {
  transform: scale(1.1);
}

.product-content {
  padding: 30px;
  position: relative;
}

.product-tag {
  display: inline-block;
  padding: 5px 10px;
  background-color: var(--secondary);
  color: var(--primary);
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: absolute;
  top: -15px;
  left: 30px;
  border-radius: 3px;
}

.product-title {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.product-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.product-price {
  font-weight: 600;
  font-size: 1.2rem;
  color: var(--secondary);
}

.product-rating {
  display: flex;
  align-items: center;
  color: var(--secondary);
}

/* Gallery Section */
.gallery {
  background-color: var(--primary);
  overflow: hidden;
}

.gallery-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: 300px;
  gap: 20px;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: var(--shadow);
  opacity: 0;
  animation: fadeIn 0.8s forwards;
}

.gallery-item:nth-child(1) {
  grid-column: span 2;
  grid-row: span 2;
  animation-delay: 0.2s;
}

.gallery-item:nth-child(2) {
  animation-delay: 0.3s;
}

.gallery-item:nth-child(3) {
  animation-delay: 0.4s;
}

.gallery-item:nth-child(4) {
  animation-delay: 0.5s;
}

.gallery-item:nth-child(5) {
  animation-delay: 0.6s;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(139, 90, 43, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-icon {
  font-size: 2rem;
  color: var(--primary);
}

/* Contact Section */
.contact {
  background-color: var(--light);
  position: relative;
}

.contact::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 40%;
  height: 100%;
  background: url("/api/placeholder/800/1200") center/cover no-repeat;
  opacity: 0.1;
}

.contact-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  position: relative;
  z-index: 1;
}

.contact-form {
  background-color: var(--primary);
  padding: 50px;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.form-group {
  margin-bottom: 25px;
}

.form-control {
  width: 100%;
  padding: 15px;
  background-color: var(--light);
  border: 1px solid var(--border);
  border-radius: 4px;
  color: var(--text);
  font-family: inherit;
  font-size: 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--secondary);
  box-shadow: 0 0 0 3px rgba(139, 90, 43, 0.1);
}

.form-control::placeholder {
  color: var(--gray);
}

textarea.form-control {
  height: 120px;
  resize: none;
}

.contact-info h3 {
  font-size: 1.5rem;
  margin-bottom: 30px;
  color: var(--secondary);
}

.info-list {
  list-style: none;
  margin-bottom: 40px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
}

.info-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(139, 90, 43, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 1.2rem;
  color: var(--secondary);
  flex-shrink: 0;
}

.info-content h4 {
  margin-bottom: 5px;
  font-size: 1.1rem;
}

.info-content p,
.info-content a {
  color: var(--gray);
  text-decoration: none;
  transition: color 0.3s ease;
}

.info-content a:hover {
  color: var(--secondary);
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark);
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: var(--secondary);
  color: var(--primary);
  transform: translateY(-5px);
}

/* Footer */
footer {
  background-color: var(--dark);
  color: var(--primary);
  padding: 80px 0 30px;
  position: relative;
}

.footer-top {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  margin-bottom: 60px;
}

.footer-logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 20px;
  letter-spacing: 2px;
}

.footer-logo span {
  color: var(--secondary);
}

.footer-col p {
  color: #bbbbbb;
  margin-bottom: 20px;
}

.footer-col h4 {
  font-size: 1.2rem;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 10px;
  color: var(--primary);
}

.footer-col h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background-color: var(--secondary);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #bbbbbb;
  text-decoration: none;
  transition: color 0.3s ease;
  display: inline-block;
  position: relative;
}

.footer-links a::before {
  content: "→";
  position: absolute;
  left: -20px;
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-links a:hover {
  color: var(--secondary);
  transform: translateX(20px);
}

.footer-links a:hover::before {
  opacity: 1;
}

.newsletter-form {
  display: flex;
}

.newsletter-input {
  flex-grow: 1;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--primary);
  font-family: inherit;
}

.newsletter-input:focus {
  outline: none;
}

.newsletter-btn {
  background-color: var(--secondary);
  color: var(--primary);
  border: none;
  padding: 0 15px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.newsletter-btn:hover {
  background-color: var(--primary);
  color: var(--dark);
}

.footer-bottom {
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  color: #bbbbbb;
  font-size: 0.9rem;
}

.footer-nav {
  display: flex;
  gap: 20px;
}

.footer-nav a {
  color: #bbbbbb;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-nav a:hover {
  color: var(--secondary);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scrollDown {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  75% {
    opacity: 0;
    transform: translateX(-50%) translateY(35px);
  }
  76% {
    opacity: 0;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Media Queries */
@media (max-width: 1200px) {
  .hero-title {
    font-size: 1.5rem;
  }

  .product-card {
    flex: 1 0 calc(33.333% - 20px);
    max-width: calc(33.333% - 20px);
  }

  .gallery-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .hero-title {
    font-size: 2rem;
  }

  .about-container,
  .contact-container {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .about-img {
    height: 400px;
  }

  .footer-top {
    grid-template-columns: repeat(2, 1fr);
  }

  .product-card {
    flex: 1 0 calc(50% - 20px);
    max-width: calc(50% - 20px);
  }

  .gallery-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .gallery-item:nth-child(1) {
    grid-column: span 1;
    grid-row: span 1;
  }
}

/* Mobile Menu Styles */
.mobile-menu-trigger {
  display: none;
  cursor: pointer;
}

.menu-icon {
  width: 30px;
  height: 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.menu-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--dark);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 50%;
  height: 100vh;
  background-color: #ffffff;
  z-index: 1000;
  transition: right 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: -5px 0 30px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-menu.active {
  right: 0;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark);
}

.mobile-logo span {
  color: var(--secondary);
}

.mobile-close {
  cursor: pointer;
  color: var(--dark);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.mobile-close:hover {
  background-color: rgba(0, 0, 0, 0.06);
  color: var(--secondary);
}

.mobile-search {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
}

.mobile-search input {
  flex: 1;
  border: none;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 0.95rem;
  color: var(--dark);
  outline: none;
}

.mobile-search input:focus {
  box-shadow: 0 0 0 2px rgba(0, 194, 255, 0.2);
}

.mobile-search-btn {
  background: none;
  border: none;
  padding: 10px;
  margin-left: 5px;
  cursor: pointer;
  color: var(--dark);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.mobile-search-btn:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--secondary);
}

.mobile-search-suggestions {
  position: absolute;
  top: 100%;
  left: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  z-index: 1000;
}

.mobile-search-suggestions.active {
  display: block;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.suggestion-item .icon {
  margin-right: 10px;
  color: var(--secondary);
}

.suggestion-item .name {
  flex: 1;
  font-size: 0.9rem;
  color: var(--dark);
}

.suggestion-item .type {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.4);
  margin-left: 10px;
}

.suggestion-item .highlight {
  background-color: rgba(255, 230, 0, 0.2);
  font-weight: 500;
}

.mobile-nav {
  flex: 1;
  padding: 10px 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* Firefox */
}

/* Custom scrollbar for Webkit browsers */
.mobile-nav::-webkit-scrollbar {
  width: 5px;
}

.mobile-nav::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-nav::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.mobile-nav::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.mobile-nav-item {
  display: block;
  padding: 15px 20px;
  color: var(--dark);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.mobile-nav-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--secondary);
}

/* Mobile Categories */
.mobile-category-wrapper {
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.mobile-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  font-weight: 500;
  color: var(--dark);
  transition: all 0.3s ease;
}

.mobile-category-header:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--secondary);
}

.mobile-category-icon {
  transition: transform 0.3s ease;
}

.mobile-category-header.active .mobile-category-icon {
  transform: rotate(180deg);
}

.mobile-category-content {
  display: none;
  background-color: rgba(0, 0, 0, 0.01);
}

.mobile-category-content.active {
  display: block;
}

.mobile-category-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.mobile-category-item:last-child {
  border-bottom: none;
}

.mobile-category-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px 12px 30px;
  cursor: pointer;
}

.mobile-category-item-header a {
  color: var(--dark);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  flex: 1;
  transition: color 0.3s ease;
}

.mobile-category-item-header a:hover {
  color: var(--secondary);
}

.mobile-subcategory-icon {
  transition: transform 0.3s ease;
  color: var(--dark);
  opacity: 0.7;
}

.mobile-category-item-header.active .mobile-subcategory-icon {
  transform: rotate(90deg);
  color: var(--secondary);
  opacity: 1;
}

.mobile-subcategory-content {
  display: none;
  padding: 5px 0;
  background-color: rgba(0, 0, 0, 0.02);
}

.mobile-subcategory-content.active {
  display: block;
}

.mobile-subcategory-item {
  display: block;
  padding: 10px 20px 10px 40px;
  color: var(--dark);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  position: relative;
}

.mobile-subcategory-item:before {
  content: "•";
  position: absolute;
  left: 25px;
  color: var(--secondary);
  opacity: 0.7;
}

.mobile-subcategory-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--secondary);
}

.mobile-cart {
  padding: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-cart-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: var(--secondary);
  color: white;
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.mobile-cart-btn:hover {
  background-color: var(--accent);
  transform: translateY(-2px);
}

.mobile-cart-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-cart-text {
  flex: 1;
}

.mobile-cart-count {
  background-color: white;
  color: var(--secondary);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: auto;
}

/* Fixed cart counter in header */
.cart-icon .cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--secondary);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
}

@media (max-width: 768px) {
  header {
    padding: 20px 0;
  }

  .mobile-menu-trigger {
    display: block;
  }

  .nav-links {
    display: none;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  section {
    padding: 80px 0;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .features {
    grid-template-columns: 1fr;
  }

  .gallery-container {
    grid-template-columns: 1fr;
  }

  .products-grid {
    justify-content: center;
  }

  .product-card {
    flex: 1 0 100%;
    max-width: 320px;
  }

  .footer-top {
    grid-template-columns: 1fr;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .mobile-menu {
    width: 80%;
  }

  .mobile-logo {
    font-size: 1.3rem;
  }

  .mobile-nav-item,
  .mobile-category-header {
    padding: 12px 15px;
    font-size: 0.95rem;
  }

  .mobile-category-item-header {
    padding: 10px 15px 10px 25px;
  }

  .mobile-category-item-header a {
    font-size: 0.9rem;
  }

  .mobile-subcategory-item {
    padding: 8px 15px 8px 35px;
    font-size: 0.85rem;
  }

  .mobile-subcategory-item:before {
    left: 20px;
  }
}

/* --- Best Deals Section Styles --- */
.best-deals {
  background-color: #f9f9f9;
  padding: 60px 0;
}

.deals-container {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  justify-content: center;
  margin: 30px 0;
}

.deal-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 280px;
  position: relative;
  transition: transform 0.3s;
}

.deal-card:hover {
  transform: translateY(-10px);
}

.discount-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(45deg, #ff4d4d, #ff1a1a);
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  z-index: 2;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}

.deal-img {
  height: 200px;
  overflow: hidden;
}

.deal-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.deal-card:hover .deal-img img {
  transform: scale(1.05);
}

.deal-content {
  padding: 20px;
}

.deal-title {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
}

.price-container {
  margin: 10px 0;
}

.old-price {
  text-decoration: line-through;
  color: #999;
  font-size: 0.9rem;
  margin-right: 10px;
}

.current-price {
  color: #ff4d4d;
  font-weight: bold;
  font-size: 1.3rem;
}

.deal-description {
  color: #666;
  font-size: 0.9rem;
  margin: 15px 0;
}

.deal-card .add-to-cart {
  background: linear-gradient(45deg, #ff4d4d, #ff1a1a);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  width: 100%;
  font-weight: bold;
  transition: all 0.3s;
}

.deal-card .add-to-cart:hover {
  background: linear-gradient(45deg, #ff1a1a, #e60000);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .deals-container {
    flex-direction: column;
    align-items: center;
  }

  .deal-card {
    width: 90%;
    max-width: 350px;
  }
}
