<?php
/**
 * Direct Endpoint Test
 * Tests API endpoints directly without routing
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>Direct Endpoint Test</h1>";

// Test 1: Settings endpoint
echo "<h2>1. Testing Settings Endpoint</h2>";
try {
    $segments = ['settings', 'app'];
    $method = 'GET';
    $input = [];
    
    ob_start();
    include __DIR__ . '/endpoints/settings.php';
    $output = ob_get_clean();
    
    echo "<h3>Settings Response:</h3>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Settings error: " . $e->getMessage() . "</p>";
}

// Test 2: Products endpoint
echo "<h2>2. Testing Products Endpoint</h2>";
try {
    $segments = ['products'];
    $method = 'GET';
    $input = [];
    $_GET['limit'] = 5;
    
    ob_start();
    include __DIR__ . '/endpoints/products.php';
    $output = ob_get_clean();
    
    echo "<h3>Products Response:</h3>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Products error: " . $e->getMessage() . "</p>";
}

// Test 3: Categories endpoint
echo "<h2>3. Testing Categories Endpoint</h2>";
try {
    $segments = ['categories'];
    $method = 'GET';
    $input = [];
    
    ob_start();
    include __DIR__ . '/endpoints/categories.php';
    $output = ob_get_clean();
    
    echo "<h3>Categories Response:</h3>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Categories error: " . $e->getMessage() . "</p>";
}

// Test 4: Manual JSON response
echo "<h2>4. Manual JSON Response Test</h2>";

$response_data = [
    'status' => 'success',
    'message' => 'API is working correctly',
    'timestamp' => date('c'),
    'api_version' => API_VERSION,
    'data' => [
        'test' => true,
        'endpoints_tested' => ['settings', 'products', 'categories'],
        'database_connected' => true
    ]
];

echo "<h3>Sample JSON Response:</h3>";
echo "<pre style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
echo json_encode($response_data, JSON_PRETTY_PRINT);
echo "</pre>";

// Test 5: Database connectivity
echo "<h2>5. Database Test</h2>";
try {
    $product_count = $pdo->query("SELECT COUNT(*) FROM tbl_product")->fetchColumn();
    $customer_count = $pdo->query("SELECT COUNT(*) FROM tbl_customer")->fetchColumn();
    
    echo "<p style='color: green;'>✓ Database connected successfully</p>";
    echo "<p>Products: {$product_count}</p>";
    echo "<p>Customers: {$customer_count}</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>If you see JSON responses above, the API endpoints are working!</strong></p>";
echo "<p>The issue is likely with URL rewriting or the main routing logic.</p>";

// Test 6: URL Rewriting Test
echo "<h2>6. URL Rewriting Test</h2>";
echo "<p>Try these direct URLs:</p>";
echo "<ul>";
echo "<li><a href='/ecom/api/index.php?endpoint=settings&action=app' target='_blank'>Settings via GET parameter</a></li>";
echo "<li><a href='/ecom/api/v1/settings/app' target='_blank'>Settings via URL rewriting</a></li>";
echo "</ul>";
?>
