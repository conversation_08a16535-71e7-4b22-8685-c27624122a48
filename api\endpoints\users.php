<?php
/**
 * Users Endpoints
 * Handles user profile and account management
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path
$sub_path = $segments[1] ?? '';

switch ($method) {
    case 'GET':
        switch ($sub_path) {
            case 'profile':
                handleGetProfile($db);
                break;
                
            case 'orders':
                handleGetUserOrders($db);
                break;
                
            case 'stats':
                handleGetUserStats($db);
                break;
                
            default:
                Response::notFound('User endpoint not found');
        }
        break;
        
    case 'PUT':
        switch ($sub_path) {
            case 'profile':
                handleUpdateProfile($db, $input);
                break;
                
            case 'password':
                handleChangePassword($db, $input);
                break;
                
            default:
                Response::notFound('User endpoint not found');
        }
        break;
        
    case 'POST':
        switch ($sub_path) {
            case 'upload-photo':
                handleUploadPhoto($db, $_FILES);
                break;
                
            default:
                Response::notFound('User endpoint not found');
        }
        break;
        
    default:
        Response::methodNotAllowed(['GET', 'PUT', 'POST']);
}

/**
 * Get user profile
 */
function handleGetProfile($db) {
    $user = AuthMiddleware::requireAuth();
    
    // Get fresh user data from database
    $user_data = $db->fetchOne(
        "SELECT * FROM tbl_customer WHERE cust_id = ?",
        [$user['user_id']]
    );
    
    if (!$user_data) {
        Response::notFound('User not found');
    }
    
    $profile = [
        'id' => (int)$user_data['cust_id'],
        'first_name' => $user_data['cust_fname'],
        'last_name' => $user_data['cust_lname'],
        'email' => $user_data['cust_email'],
        'phone' => $user_data['cust_phone'],
        'address' => [
            'street' => $user_data['cust_address_street'],
            'city' => $user_data['cust_address_city'],
            'region' => $user_data['cust_address_region'],
            'zip_code' => $user_data['cust_address_zip'],
            'country' => $user_data['cust_country']
        ],
        'photo' => $user_data['cust_photo'] ? 
            '/assets/uploads/customers/' . $user_data['cust_photo'] : null,
        'status' => (int)$user_data['cust_status'],
        'created_at' => $user_data['cust_created_at'],
        'last_login' => $user_data['cust_last_login']
    ];
    
    Response::success($profile, 'Profile retrieved successfully');
}

/**
 * Update user profile
 */
function handleUpdateProfile($db, $input) {
    $user = AuthMiddleware::requireAuth();
    
    $validator = new Validator($input);
    $validator->maxLength('first_name', 100)
             ->maxLength('last_name', 100)
             ->email('email')
             ->phone('phone')
             ->maxLength('address_street', 255)
             ->maxLength('address_city', 100)
             ->maxLength('address_region', 100)
             ->maxLength('address_zip', 20)
             ->maxLength('country', 100);
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    // Check if email is being changed and if it already exists
    if (isset($input['email'])) {
        $existing_user = $db->fetchOne(
            "SELECT cust_id FROM tbl_customer WHERE cust_email = ? AND cust_id != ?",
            [$input['email'], $user['user_id']]
        );
        
        if ($existing_user) {
            Response::error('Email address already exists', 409, 'EMAIL_EXISTS');
        }
    }
    
    // Build update data
    $update_data = [];
    $field_mapping = [
        'first_name' => 'cust_fname',
        'last_name' => 'cust_lname',
        'email' => 'cust_email',
        'phone' => 'cust_phone',
        'address_street' => 'cust_address_street',
        'address_city' => 'cust_address_city',
        'address_region' => 'cust_address_region',
        'address_zip' => 'cust_address_zip',
        'country' => 'cust_country'
    ];
    
    foreach ($field_mapping as $input_field => $db_field) {
        if (isset($input[$input_field])) {
            $update_data[$db_field] = $input[$input_field];
        }
    }
    
    if (empty($update_data)) {
        Response::error('No data to update', 400);
    }
    
    // Update user
    $db->update('tbl_customer', $update_data, 'cust_id = ?', [$user['user_id']]);
    
    Response::success(null, 'Profile updated successfully');
}

/**
 * Change user password
 */
function handleChangePassword($db, $input) {
    $user = AuthMiddleware::requireAuth();
    
    $validator = new Validator($input);
    $validator->required('current_password')
             ->required('new_password')->minLength('new_password', 6)
             ->required('confirm_password');
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    if ($input['new_password'] !== $input['confirm_password']) {
        Response::error('New password and confirmation do not match', 400, 'PASSWORD_MISMATCH');
    }
    
    // Get current user data
    $user_data = $db->fetchOne(
        "SELECT cust_password FROM tbl_customer WHERE cust_id = ?",
        [$user['user_id']]
    );
    
    if (!$user_data) {
        Response::notFound('User not found');
    }
    
    // Verify current password
    if (!Auth::verifyPassword($input['current_password'], $user_data['cust_password'])) {
        Response::error('Current password is incorrect', 400, 'INVALID_PASSWORD');
    }
    
    // Hash new password
    $new_password_hash = Auth::hashPassword($input['new_password']);
    
    // Update password
    $db->update(
        'tbl_customer',
        ['cust_password' => $new_password_hash],
        'cust_id = ?',
        [$user['user_id']]
    );
    
    Response::success(null, 'Password changed successfully');
}

/**
 * Get user orders summary
 */
function handleGetUserOrders($db) {
    $user = AuthMiddleware::requireAuth();
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? 10), 20);
    
    $sql = "
        SELECT 
            id,
            tx_ref,
            total_amount,
            currency,
            payment_status,
            shipping_status,
            created_at
        FROM orders
        WHERE user_id = ?
        ORDER BY created_at DESC
    ";
    
    $result = $db->paginate($sql, [$user['user_id']], $page, $limit);
    
    $orders = array_map(function($order) {
        return [
            'id' => (int)$order['id'],
            'tx_ref' => $order['tx_ref'],
            'total_amount' => (float)$order['total_amount'],
            'currency' => $order['currency'],
            'payment_status' => $order['payment_status'],
            'shipping_status' => $order['shipping_status'],
            'created_at' => $order['created_at']
        ];
    }, $result['data']);
    
    Response::paginated($orders, $result['total'], $page, $limit, 'User orders retrieved successfully');
}

/**
 * Get user statistics
 */
function handleGetUserStats($db) {
    $user = AuthMiddleware::requireAuth();
    
    // Get order statistics
    $order_stats = $db->fetchOne(
        "SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN payment_status = 'success' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_orders,
            COALESCE(SUM(CASE WHEN payment_status = 'success' THEN total_amount ELSE 0 END), 0) as total_spent
         FROM orders 
         WHERE user_id = ?",
        [$user['user_id']]
    );
    
    // Get recent orders
    $recent_orders = $db->fetchAll(
        "SELECT id, tx_ref, total_amount, payment_status, created_at 
         FROM orders 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 5",
        [$user['user_id']]
    );
    
    // Get favorite categories (based on order history)
    $favorite_categories = $db->fetchAll(
        "SELECT 
            tc.tcat_name as category_name,
            COUNT(*) as order_count
         FROM order_items oi
         JOIN orders o ON oi.order_id = o.id
         JOIN tbl_product p ON oi.product_id = p.p_id
         JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
         WHERE o.user_id = ? AND o.payment_status = 'success'
         GROUP BY tc.tcat_id, tc.tcat_name
         ORDER BY order_count DESC
         LIMIT 3",
        [$user['user_id']]
    );
    
    $stats = [
        'orders' => [
            'total' => (int)$order_stats['total_orders'],
            'completed' => (int)$order_stats['completed_orders'],
            'pending' => (int)$order_stats['pending_orders'],
            'failed' => (int)$order_stats['failed_orders']
        ],
        'total_spent' => (float)$order_stats['total_spent'],
        'currency' => DEFAULT_CURRENCY,
        'recent_orders' => array_map(function($order) {
            return [
                'id' => (int)$order['id'],
                'tx_ref' => $order['tx_ref'],
                'total_amount' => (float)$order['total_amount'],
                'payment_status' => $order['payment_status'],
                'created_at' => $order['created_at']
            ];
        }, $recent_orders),
        'favorite_categories' => array_map(function($category) {
            return [
                'name' => $category['category_name'],
                'order_count' => (int)$category['order_count']
            ];
        }, $favorite_categories)
    ];
    
    Response::success($stats, 'User statistics retrieved successfully');
}

/**
 * Upload user profile photo
 */
function handleUploadPhoto($db, $files) {
    $user = AuthMiddleware::requireAuth();
    
    if (!isset($files['photo']) || $files['photo']['error'] !== UPLOAD_ERR_OK) {
        Response::error('No valid photo uploaded', 400, 'UPLOAD_ERROR');
    }
    
    $file = $files['photo'];
    
    // Validate file type
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowed_types)) {
        Response::error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed', 400, 'INVALID_FILE_TYPE');
    }
    
    // Validate file size (5MB max)
    if ($file['size'] > MAX_FILE_SIZE) {
        Response::error('File too large. Maximum size is 5MB', 400, 'FILE_TOO_LARGE');
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'cust_' . $user['user_id'] . '_' . uniqid() . '.' . $extension;
    
    // Upload directory
    $upload_dir = __DIR__ . '/../../assets/uploads/customers/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $upload_path = $upload_dir . $filename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        Response::error('Failed to upload file', 500, 'UPLOAD_FAILED');
    }
    
    // Delete old photo if exists
    $old_photo = $db->fetchOne(
        "SELECT cust_photo FROM tbl_customer WHERE cust_id = ?",
        [$user['user_id']]
    )['cust_photo'];
    
    if ($old_photo && file_exists($upload_dir . $old_photo)) {
        unlink($upload_dir . $old_photo);
    }
    
    // Update database
    $db->update(
        'tbl_customer',
        ['cust_photo' => $filename],
        'cust_id = ?',
        [$user['user_id']]
    );
    
    Response::success([
        'photo_url' => '/assets/uploads/customers/' . $filename
    ], 'Photo uploaded successfully');
}
?>
