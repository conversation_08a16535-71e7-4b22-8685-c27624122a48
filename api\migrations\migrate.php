<?php
/**
 * Database Migration Script
 * Safely executes database migrations for the API
 */

// Include configuration
require_once __DIR__ . '/../config/config.php';

// Only allow CLI execution for security
if (php_sapi_name() !== 'cli' && !isset($_GET['web_install'])) {
    http_response_code(403);
    echo json_encode(['error' => 'This script should be run from command line or installation wizard']);
    exit;
}

echo "Starting database migration...\n";

try {
    // Check if tables already exist
    $existing_tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }

    $migrations_run = 0;

    // 1. Create wishlist table
    if (!in_array('tbl_wishlist', $existing_tables)) {
        echo "Creating tbl_wishlist table...\n";
        $pdo->exec("
            CREATE TABLE `tbl_wishlist` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `cust_id` int(11) NOT NULL,
              `p_id` int(11) NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_wishlist` (`cust_id`, `p_id`),
              KEY `idx_customer` (`cust_id`),
              KEY `idx_product` (`p_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $migrations_run++;
    }

    // 2. Create orders table
    if (!in_array('orders', $existing_tables)) {
        echo "Creating orders table...\n";
        $pdo->exec("
            CREATE TABLE `orders` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `tx_ref` varchar(100) NOT NULL UNIQUE,
              `user_id` int(11) NOT NULL,
              `firstname` varchar(100) NOT NULL,
              `lastname` varchar(100) NOT NULL,
              `email` varchar(255) NOT NULL,
              `phone` varchar(20) NOT NULL,
              `address` text NOT NULL,
              `shipping_country_id` int(11) DEFAULT NULL,
              `shipping_country` varchar(100) DEFAULT NULL,
              `total_amount` decimal(10,2) NOT NULL,
              `shipping_fee` decimal(10,2) DEFAULT 0.00,
              `installation_fee_total` decimal(10,2) DEFAULT 0.00,
              `currency` varchar(3) DEFAULT 'TZS',
              `payment_status` enum('pending','success','failed') DEFAULT 'pending',
              `verification_status` enum('waiting','verified','rejected') DEFAULT 'waiting',
              `shipping_status` enum('processing','shipped','delivered','cancelled') DEFAULT 'processing',
              `tracking_number` varchar(100) DEFAULT NULL,
              `carrier` varchar(100) DEFAULT NULL,
              `estimated_delivery` date DEFAULT NULL,
              `shipping_notes` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_user` (`user_id`),
              KEY `idx_payment_status` (`payment_status`),
              KEY `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $migrations_run++;
    }

    // 3. Create order_items table
    if (!in_array('order_items', $existing_tables)) {
        echo "Creating order_items table...\n";
        $pdo->exec("
            CREATE TABLE `order_items` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `order_id` int(11) NOT NULL,
              `product_id` int(11) NOT NULL,
              `variation_id` int(11) DEFAULT NULL,
              `variation_name` varchar(255) DEFAULT NULL,
              `variation_price` decimal(10,2) DEFAULT NULL,
              `product_name` varchar(255) NOT NULL,
              `quantity` int(11) NOT NULL,
              `unit_price` decimal(10,2) NOT NULL,
              `subtotal` decimal(10,2) NOT NULL,
              `total` decimal(10,2) NOT NULL,
              `color_id` int(11) DEFAULT NULL,
              `installation_fee` decimal(10,2) DEFAULT 0.00,
              PRIMARY KEY (`id`),
              KEY `idx_order` (`order_id`),
              KEY `idx_product` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $migrations_run++;
    }

    // 4. Create shipping_history table
    if (!in_array('shipping_history', $existing_tables)) {
        echo "Creating shipping_history table...\n";
        $pdo->exec("
            CREATE TABLE `shipping_history` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `order_id` int(11) NOT NULL,
              `location` varchar(255) NOT NULL,
              `status` varchar(100) NOT NULL,
              `description` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_order` (`order_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $migrations_run++;
    }

    // 5. Create API logs table
    if (!in_array('api_logs', $existing_tables)) {
        echo "Creating api_logs table...\n";
        $pdo->exec("
            CREATE TABLE `api_logs` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `endpoint` varchar(255) NOT NULL,
              `method` varchar(10) NOT NULL,
              `ip_address` varchar(45) NOT NULL,
              `user_id` int(11) DEFAULT NULL,
              `request_data` text DEFAULT NULL,
              `response_code` int(11) NOT NULL,
              `response_time` decimal(8,3) DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_endpoint` (`endpoint`),
              KEY `idx_ip` (`ip_address`),
              KEY `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $migrations_run++;
    }

    // 6. Create API rate limits table
    if (!in_array('api_rate_limits', $existing_tables)) {
        echo "Creating api_rate_limits table...\n";
        $pdo->exec("
            CREATE TABLE `api_rate_limits` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `ip_address` varchar(45) NOT NULL,
              `requests` int(11) NOT NULL DEFAULT 1,
              `window_start` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `last_request` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_ip` (`ip_address`),
              KEY `idx_window_start` (`window_start`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $migrations_run++;
    }

    // 7. Add columns to existing tables if they don't exist
    echo "Checking for missing columns...\n";

    // Check if installation_fee column exists in tbl_product
    $columns = $pdo->query("SHOW COLUMNS FROM tbl_product LIKE 'installation_fee'")->fetchAll();
    if (empty($columns)) {
        echo "Adding installation_fee column to tbl_product...\n";
        $pdo->exec("ALTER TABLE `tbl_product` ADD COLUMN `installation_fee` int(11) DEFAULT 15000 AFTER `p_total_view`");
        $migrations_run++;
    }

    // Check if last_login column exists in tbl_customer
    $columns = $pdo->query("SHOW COLUMNS FROM tbl_customer LIKE 'cust_last_login'")->fetchAll();
    if (empty($columns)) {
        echo "Adding cust_last_login column to tbl_customer...\n";
        $pdo->exec("ALTER TABLE `tbl_customer` ADD COLUMN `cust_last_login` timestamp NULL DEFAULT NULL AFTER `cust_created_at`");
        $migrations_run++;
    }

    // 8. Add indexes for better performance
    echo "Adding database indexes...\n";

    $indexes_to_add = [
        "CREATE INDEX IF NOT EXISTS `idx_product_active` ON `tbl_product` (`p_is_active`)",
        "CREATE INDEX IF NOT EXISTS `idx_product_featured` ON `tbl_product` (`p_is_featured`)",
        "CREATE INDEX IF NOT EXISTS `idx_product_category` ON `tbl_product` (`tcat_id`)",
        "CREATE INDEX IF NOT EXISTS `idx_product_subcategory` ON `tbl_product` (`mcat_id`)",
        "CREATE INDEX IF NOT EXISTS `idx_product_views` ON `tbl_product` (`p_total_view`)",
        "CREATE INDEX IF NOT EXISTS `idx_customer_email` ON `tbl_customer` (`cust_email`)",
        "CREATE INDEX IF NOT EXISTS `idx_customer_status` ON `tbl_customer` (`cust_status`)"
    ];

    foreach ($indexes_to_add as $index_sql) {
        try {
            $pdo->exec($index_sql);
        } catch (PDOException $e) {
            // Index might already exist, continue
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                echo "Warning: " . $e->getMessage() . "\n";
            }
        }
    }

    // 9. Insert sample data if tables are empty
    echo "Checking for sample data...\n";

    // Check if countries exist
    $country_count = $pdo->query("SELECT COUNT(*) FROM tbl_country")->fetchColumn();
    if ($country_count == 0) {
        echo "Adding sample countries...\n";
        $countries = ['Tanzania', 'Kenya', 'Uganda', 'Rwanda', 'Burundi'];
        foreach ($countries as $country) {
            $pdo->prepare("INSERT IGNORE INTO `tbl_country` (`country_name`) VALUES (?)")->execute([$country]);
        }
        $migrations_run++;
    }

    // Check if shipping costs exist
    $shipping_count = $pdo->query("SELECT COUNT(*) FROM tbl_shipping_cost")->fetchColumn();
    if ($shipping_count == 0) {
        echo "Adding sample shipping costs...\n";
        $shipping_data = [
            ['Tanzania', 5000, 3],
            ['Kenya', 15000, 7],
            ['Uganda', 20000, 10],
            ['Rwanda', 25000, 14],
            ['Burundi', 25000, 14]
        ];

        foreach ($shipping_data as $data) {
            $stmt = $pdo->prepare("SELECT country_id FROM tbl_country WHERE country_name = ?");
            $stmt->execute([$data[0]]);
            $country = $stmt->fetch();
            if ($country) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO `tbl_shipping_cost` (`country_id`, `amount`, `estimated_days`, `description`) VALUES (?, ?, ?, 'Standard shipping')");
                $stmt->execute([$country['country_id'], $data[1], $data[2]]);
            }
        }
        $migrations_run++;
    }

    echo "\nMigration completed successfully!\n";
    echo "Total migrations run: {$migrations_run}\n";

    return ['success' => true, 'migrations_run' => $migrations_run];

} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    return ['success' => false, 'error' => $e->getMessage()];
}
