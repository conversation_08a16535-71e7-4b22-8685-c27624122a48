<?php
// Include session configuration before starting session
include("session_config.php");
session_start();
header('Content-Type: application/json');

// Buffer output to catch any errors
ob_start();

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = json_decode(file_get_contents('php://input'), true);

        if (isset($data['cart'])) {
            // Log the incoming cart data for debugging
            error_log("Syncing cart session with JSON data: " . json_encode($data['cart']));

            // Log the current session cart before clearing
            error_log("Current session cart before sync: " . json_encode($_SESSION['cart'] ?? []));

            // Clear existing cart first
            $_SESSION['cart'] = [];

            // If cart data is not empty, update the session
            if (!empty($data['cart'])) {
                $_SESSION['cart'] = $data['cart'];
                error_log("Cart updated in session. Items: " . count($_SESSION['cart']));

                // Log the keys of the cart items for debugging
                $keys = array_keys($_SESSION['cart']);
                error_log("Cart keys after update: " . json_encode($keys));
            } else {
                error_log("Cart cleared in session.");
            }

            echo json_encode(['status' => 'success']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Invalid cart data']);
        }
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
    }
} catch (Exception $e) {
    error_log("Error in sync_cart.php: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => 'Server error']);
}

// Clean output buffer
ob_end_flush();
?>