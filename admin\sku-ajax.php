<?php
// Enable comprehensive error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'sku_ajax_errors.log');

require_once('inc/config.php');

// Debug function for AJAX
function debugAjaxLog($message, $data = null) {
    $logMessage = "[" . date('Y-m-d H:i:s') . "] AJAX: " . $message;
    if ($data !== null) {
        $logMessage .= " | Data: " . print_r($data, true);
    }
    error_log($logMessage, 3, 'sku_ajax_debug.log');
}

// Test database connection
try {
    if (!isset($pdo) || !$pdo) {
        throw new Exception("Database connection not established");
    }

    // Test query
    $test = $pdo->query("SELECT 1");
    if (!$test) {
        throw new Exception("Database query test failed");
    }
    debugAjaxLog("Database connection successful");
} catch (Exception $e) {
    debugAjaxLog("Database connection error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Check if user is logged in
if(!isset($_SESSION['user'])) {
    debugAjaxLog("Unauthorized access attempt");
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? '';
debugAjaxLog("AJAX action requested: " . $action, $_GET);

try {
    switch($action) {
        case 'get_product_details':
            getProductDetails();
            break;
        case 'check_sku_duplicate':
            checkSKUDuplicate();
            break;
        case 'delete_sku':
            deleteSKU();
            break;
        case 'update_sku':
            updateSKU();
            break;
        case 'bulk_action':
            handleBulkAction();
            break;
        case 'update_stock':
            updateStock();
            break;
        default:
            debugAjaxLog("Invalid action requested: " . $action);
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action: ' . $action]);
    }
} catch (Exception $e) {
    debugAjaxLog("Unexpected error in AJAX handler: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Unexpected error: ' . $e->getMessage()]);
}

function getProductDetails() {
    global $pdo;

    try {
        $productId = $_GET['product_id'] ?? 0;
        debugAjaxLog("getProductDetails called for product ID: " . $productId);

        if(!$productId || !is_numeric($productId)) {
            debugAjaxLog("Invalid product ID provided: " . $productId);
            echo json_encode(['error' => 'Valid Product ID required']);
            return;
        }

        // Get basic product information
        try {
            $statement = $pdo->prepare("SELECT p_name, p_current_price, p_qty FROM tbl_product WHERE p_id = ?");
            $statement->execute([$productId]);
            $product = $statement->fetch(PDO::FETCH_ASSOC);

            if(!$product) {
                debugAjaxLog("Product not found for ID: " . $productId);
                echo json_encode(['error' => 'Product not found']);
                return;
            }
            debugAjaxLog("Product found", $product);
        } catch(PDOException $e) {
            debugAjaxLog("Database error getting product: " . $e->getMessage());
            echo json_encode(['error' => 'Database error getting product: ' . $e->getMessage()]);
            return;
        }

        // Get available colors for this product
        try {
            $colorStatement = $pdo->prepare("
                SELECT c.color_id, c.color_name, c.color_code
                FROM tbl_product_color pc
                JOIN tbl_color c ON pc.color_id = c.color_id
                WHERE pc.p_id = ?
                ORDER BY c.color_name ASC
            ");
            $colorStatement->execute([$productId]);
            $availableColors = $colorStatement->fetchAll(PDO::FETCH_ASSOC);
            debugAjaxLog("Colors found: " . count($availableColors));
        } catch(PDOException $e) {
            debugAjaxLog("Database error getting colors: " . $e->getMessage());
            $availableColors = [];
        }

        // Get available sizes for this product
        try {
            $sizeStatement = $pdo->prepare("
                SELECT s.size_id, s.size_name
                FROM tbl_product_size ps
                JOIN tbl_size s ON ps.size_id = s.size_id
                WHERE ps.p_id = ?
                ORDER BY s.size_name ASC
            ");
            $sizeStatement->execute([$productId]);
            $availableSizes = $sizeStatement->fetchAll(PDO::FETCH_ASSOC);
            debugAjaxLog("Sizes found: " . count($availableSizes));
        } catch(PDOException $e) {
            debugAjaxLog("Database error getting sizes: " . $e->getMessage());
            $availableSizes = [];
        }

        // Get existing product variations with full details
        try {
            $variationStatement = $pdo->prepare("
                SELECT
                    pv.variation_id,
                    pv.variation_name,
                    pv.variation_description,
                    pv.variation_price,
                    pv.variation_qty,
                    pv.variation_sku,
                    c.color_id,
                    c.color_name,
                    c.color_code,
                    s.size_id,
                    s.size_name
                FROM tbl_product_variation pv
                LEFT JOIN tbl_color c ON pv.variation_color = c.color_id
                LEFT JOIN tbl_size s ON pv.variation_size = s.size_id
                WHERE pv.p_id = ?
                ORDER BY c.color_name ASC, s.size_name ASC
            ");
            $variationStatement->execute([$productId]);
            $existingVariations = $variationStatement->fetchAll(PDO::FETCH_ASSOC);
            debugAjaxLog("Variations found: " . count($existingVariations));
        } catch(PDOException $e) {
            debugAjaxLog("Database error getting variations: " . $e->getMessage());
            $existingVariations = [];
        }

        // Get existing SKU variants for this product (from our SKU table)
        try {
            $skuVariantStatement = $pdo->prepare("
                SELECT DISTINCT variant_details
                FROM tbl_sku
                WHERE product_id = ? AND variant_details IS NOT NULL AND variant_details != ''
                ORDER BY variant_details ASC
            ");
            $skuVariantStatement->execute([$productId]);
            $existingSkuVariants = $skuVariantStatement->fetchAll(PDO::FETCH_COLUMN);
            debugAjaxLog("SKU variants found: " . count($existingSkuVariants));
        } catch(PDOException $e) {
            debugAjaxLog("Database error getting SKU variants: " . $e->getMessage());
            $existingSkuVariants = [];
        }

        $response = [
            'success' => true,
            'product' => $product,
            'available_colors' => $availableColors,
            'available_sizes' => $availableSizes,
            'existing_variations' => $existingVariations,
            'existing_sku_variants' => $existingSkuVariants
        ];

        debugAjaxLog("Sending successful response", $response);
        echo json_encode($response);

    } catch(Exception $e) {
        debugAjaxLog("Unexpected error in getProductDetails: " . $e->getMessage());
        echo json_encode(['error' => 'Unexpected error: ' . $e->getMessage()]);
    }
}

function checkSKUDuplicate() {
    global $pdo;

    $skuCode = $_GET['sku_code'] ?? '';
    $excludeId = $_GET['exclude_id'] ?? 0;

    if(!$skuCode) {
        echo json_encode(['error' => 'SKU code required']);
        return;
    }

    $sql = "SELECT COUNT(*) FROM tbl_sku WHERE sku_code = ?";
    $params = [$skuCode];

    if($excludeId) {
        $sql .= " AND sku_id != ?";
        $params[] = $excludeId;
    }

    $statement = $pdo->prepare($sql);
    $statement->execute($params);
    $count = $statement->fetchColumn();

    echo json_encode([
        'success' => true,
        'exists' => $count > 0
    ]);
}

function deleteSKU() {
    global $pdo;
    
    $skuId = $_POST['sku_id'] ?? 0;
    
    if(!$skuId) {
        echo json_encode(['error' => 'SKU ID required']);
        return;
    }
    
    try {
        $statement = $pdo->prepare("DELETE FROM tbl_sku WHERE sku_id = ?");
        $statement->execute([$skuId]);
        
        if($statement->rowCount() > 0) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['error' => 'SKU not found']);
        }
    } catch(PDOException $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}

function updateSKU() {
    global $pdo;

    try {
        $skuId = $_POST['sku_id'] ?? 0;
        $productId = $_POST['product_id'] ?? 0;
        $variantDetails = $_POST['variant_details'] ?? '';
        $price = $_POST['price'] ?? 0;
        $costPrice = $_POST['cost_price'] ?? '';
        $quantity = $_POST['quantity'] ?? 0;
        $reorderLevel = $_POST['reorder_level'] ?? '';
        $barcode = $_POST['barcode'] ?? '';
        $status = $_POST['status'] ?? 1;

        debugAjaxLog("updateSKU called", $_POST);

        // Handle empty decimal values properly
        $costPrice = (!empty($costPrice) && is_numeric($costPrice)) ? $costPrice : 0;
        $reorderLevel = (!empty($reorderLevel) && is_numeric($reorderLevel)) ? $reorderLevel : 5;

        if(!$skuId || !$productId || $price <= 0) {
            debugAjaxLog("updateSKU validation failed", [
                'skuId' => $skuId,
                'productId' => $productId,
                'price' => $price
            ]);
            echo json_encode(['error' => 'Required fields missing or invalid']);
            return;
        }

        $updateData = [
            $productId,
            $variantDetails,
            $price,
            $costPrice,
            $quantity,
            $reorderLevel,
            $barcode,
            $status,
            $skuId
        ];

        debugAjaxLog("updateSKU data prepared", $updateData);

        $statement = $pdo->prepare("UPDATE tbl_sku SET
            product_id = ?,
            variant_details = ?,
            price = ?,
            cost_price = ?,
            quantity = ?,
            reorder_level = ?,
            barcode = ?,
            status = ?,
            updated_at = NOW()
            WHERE sku_id = ?");

        $result = $statement->execute($updateData);

        if($result && $statement->rowCount() > 0) {
            debugAjaxLog("updateSKU successful", ['affected_rows' => $statement->rowCount()]);
            echo json_encode(['success' => true, 'message' => 'SKU updated successfully']);
        } else {
            debugAjaxLog("updateSKU no changes", ['rowCount' => $statement->rowCount()]);
            echo json_encode(['error' => 'No changes made or SKU not found']);
        }
    } catch(PDOException $e) {
        debugAjaxLog("updateSKU database error", ['error' => $e->getMessage()]);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    } catch(Exception $e) {
        debugAjaxLog("updateSKU unexpected error", ['error' => $e->getMessage()]);
        echo json_encode(['error' => 'Unexpected error: ' . $e->getMessage()]);
    }
}

function handleBulkAction() {
    global $pdo;
    
    $action = $_POST['bulk_action'] ?? '';
    $skuIds = $_POST['sku_ids'] ?? [];
    
    if(!$action || empty($skuIds)) {
        echo json_encode(['error' => 'Action and SKU IDs required']);
        return;
    }
    
    try {
        switch($action) {
            case 'delete':
                $statement = $pdo->prepare("DELETE FROM tbl_sku WHERE sku_id IN (" . implode(',', array_fill(0, count($skuIds), '?')) . ")");
                $statement->execute($skuIds);
                $message = count($skuIds) . ' SKU(s) deleted successfully';
                break;
                
            case 'activate':
                $statement = $pdo->prepare("UPDATE tbl_sku SET status = 1, updated_at = NOW() WHERE sku_id IN (" . implode(',', array_fill(0, count($skuIds), '?')) . ")");
                $statement->execute($skuIds);
                $message = count($skuIds) . ' SKU(s) activated successfully';
                break;
                
            case 'deactivate':
                $statement = $pdo->prepare("UPDATE tbl_sku SET status = 0, updated_at = NOW() WHERE sku_id IN (" . implode(',', array_fill(0, count($skuIds), '?')) . ")");
                $statement->execute($skuIds);
                $message = count($skuIds) . ' SKU(s) deactivated successfully';
                break;
                
            default:
                echo json_encode(['error' => 'Invalid bulk action']);
                return;
        }
        
        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
    } catch(PDOException $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}

function updateStock() {
    global $pdo;
    
    $skuId = $_POST['sku_id'] ?? 0;
    $quantity = $_POST['quantity'] ?? 0;
    
    if(!$skuId || $quantity < 0) {
        echo json_encode(['error' => 'Invalid SKU ID or quantity']);
        return;
    }
    
    try {
        $statement = $pdo->prepare("UPDATE tbl_sku SET quantity = ?, updated_at = NOW() WHERE sku_id = ?");
        $statement->execute([$quantity, $skuId]);
        
        if($statement->rowCount() > 0) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['error' => 'SKU not found']);
        }
    } catch(PDOException $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
