<?php
/**
 * API Configuration File
 * Contains database connection and API settings
 */

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Africa/Dar_es_Salaam');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'ecommerceweb');
define('DB_USER', 'root');
define('DB_PASS', '');

// JWT Configuration
define('JWT_SECRET', 'aa0292d037077359cdb1e7146aecfbb0c256e9fcb29e4ce22644d356d7ec4189');
define('JWT_ALGORITHM', 'HS256');
define('JWT_EXPIRY', 86400); // 24 hours in seconds

// API Configuration
define('API_VERSION', 'v1');
define('API_BASE_URL', '/api/v1');

// CORS settings
define('ALLOWED_ORIGINS', ['*']); // Allow all origins for development

// Rate limiting (more lenient for development)
define('RATE_LIMIT_REQUESTS', 1000); // Increased for testing
define('RATE_LIMIT_WINDOW', 3600); // 1 hour
define('RATE_LIMIT_BYPASS_IPS', ['127.0.0.1', '::1']); // Bypass for localhost

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Pagination defaults
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// Currency
define('DEFAULT_CURRENCY', 'TZS');

// Installation fee default
define('DEFAULT_INSTALLATION_FEE', 15000);

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed',
        'error_code' => 'DB_CONNECTION_ERROR'
    ]);
    exit;
}

// Include required libraries
// Try to load Composer autoloader, fallback to manual includes
if (file_exists(__DIR__ . '/../../vendor/autoload.php')) {
    require_once __DIR__ . '/../../vendor/autoload.php';
}

// Include API utilities
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Auth.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../utils/Database.php';
require_once __DIR__ . '/../middleware/CORS.php';
require_once __DIR__ . '/../middleware/RateLimit.php';
require_once __DIR__ . '/../middleware/Auth.php';
