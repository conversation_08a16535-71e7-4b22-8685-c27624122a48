<?php
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Verify user is logged in
if (!isset($_SESSION['customer'])) {
    header("Location: login.php");
    exit;
}

/* Verify cart exists*/
if (empty($_SESSION['cart'])) {
    header("Location: cart.php");
    exit;
}

// Constants
define('CURRENCY_CODE', 'TZS');

// Get customer data
$customer = $_SESSION['customer'];
$user_id  = $customer['cust_id'];

// Fetch complete customer details
$stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id = ?");
$stmt->execute([$user_id]);
$customerData = $stmt->fetch(PDO::FETCH_ASSOC);

// Prepare full address
$addressParts = [
    $customerData['cust_address_street'] ?? '',
    $customerData['cust_address_city']   ?? '',
    $customerData['cust_address_region'] ?? '',
    $customerData['cust_address_zip']    ?? '',
    $customerData['cust_country']        ?? ''
];
$address = implode(", ", array_filter($addressParts));

// Log all session data for debugging
error_log("SESSION DATA: " . json_encode($_SESSION));

// Log POST data if available
if (!empty($_POST)) {
    error_log("POST DATA: " . json_encode($_POST));
}

// Check if we have totals in POST data
$has_post_totals = isset($_POST['products_subtotal']) && isset($_POST['final_total']);

// Initialize totals from POST data if available, otherwise from session
if ($has_post_totals) {
    $total_items = floatval($_POST['products_subtotal']);
    $shipping_fee = floatval($_POST['shipping_fee']);
    $installation_fee_total = floatval($_POST['installation_fee']);
    $grand_total = floatval($_POST['final_total']);

    // Also update the session with these values
    $_SESSION['products_subtotal'] = $total_items;
    $_SESSION['shipping_fee'] = $shipping_fee;
    $_SESSION['installation_fee'] = $installation_fee_total;
    $_SESSION['final_total'] = $grand_total;

    // If country_id is provided, update shipping country
    if (isset($_POST['country_id']) && !empty($_POST['country_id'])) {
        $country_id = $_POST['country_id'];
        $_SESSION['shipping_country_id'] = $country_id;

        // Try to get the country name from the database
        try {
            $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
            $stmt->execute([$country_id]);
            $country = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($country) {
                $_SESSION['shipping_country'] = $country['country_name'];
            }
        } catch (PDOException $e) {
            error_log("Error fetching country name from POST data: " . $e->getMessage());
        }
    }
} else {
    // Initialize totals from session
    $total_items = floatval($_SESSION['products_subtotal'] ?? 0.0);
    $shipping_fee = floatval($_SESSION['shipping_fee'] ?? 0.0);
    $installation_fee_total = floatval($_SESSION['installation_fee'] ?? 0.0);
    $grand_total = floatval($_SESSION['final_total'] ?? 0.0);
}

// Log the extracted values
error_log("EXTRACTED VALUES: products_subtotal=$total_items, shipping_fee=$shipping_fee, installation_fee=$installation_fee_total, final_total=$grand_total");

// If any of the required values are missing, try to calculate them from the cart
if ($total_items == 0 || $grand_total == 0) {
    error_log("Missing total values, attempting to calculate from cart");

    // Calculate totals from cart items
    $calculated_items_total = 0;
    $calculated_installation_total = 0;

    if (isset($_SESSION['cart']) && is_array($_SESSION['cart']) && !empty($_SESSION['cart'])) {
        foreach ($_SESSION['cart'] as $item) {
            $price = floatval($item['price'] ?? 0);
            $quantity = intval($item['quantity'] ?? 1);
            $calculated_items_total += $price * $quantity;

            // Add installation fee if selected
            if (isset($item['installation']) && $item['installation'] == 1) {
                // Get product-specific installation fee
                $product_id = $item['product_id'] ?? null;
                if ($product_id) {
                    try {
                        $stmt = $pdo->prepare("SELECT installation_fee FROM tbl_product WHERE p_id = ?");
                        $stmt->execute([$product_id]);
                        $product_fee = $stmt->fetchColumn();

                        // Use product-specific fee if available, otherwise use default
                        $installation_fee = (is_numeric($product_fee) && $product_fee > 0) ? $product_fee : 15000;
                        $calculated_installation_total += $installation_fee;
                        error_log("Using installation fee for product $product_id: $installation_fee");
                    } catch (PDOException $e) {
                        error_log("Error fetching installation fee for product $product_id: " . $e->getMessage());
                        $calculated_installation_total += 15000; // Fallback to default
                    }
                } else {
                    $calculated_installation_total += 15000; // Default installation fee
                }
            }
        }

        // If we calculated non-zero values, use them
        if ($calculated_items_total > 0) {
            $total_items = $calculated_items_total;
            $installation_fee_total = $calculated_installation_total;
            $grand_total = $total_items + $shipping_fee + $installation_fee_total;

            // Store the calculated values in session
            $_SESSION['products_subtotal'] = $total_items;
            $_SESSION['installation_fee'] = $installation_fee_total;
            $_SESSION['final_total'] = $grand_total;

            error_log("CALCULATED VALUES: products_subtotal=$total_items, installation_fee=$installation_fee_total, final_total=$grand_total");
        }
    }
}

// Verify the totals
$calculated_total = $total_items + $shipping_fee + $installation_fee_total;
if (abs($calculated_total - $grand_total) > 0.01) {
    error_log("Total mismatch in checkout: Session total: $grand_total, Calculated: $calculated_total");
    $_SESSION['error_message'] = "Order total verification failed. Please try again.";
    header("Location: cart.php");
    exit;
}

$verifiedCart      = [];
$price_discrepancy = false;
$inactiveProducts  = [];

// Process each cart item
foreach ($_SESSION['cart'] as $item) {
    if (empty($item['product_id'])) {
        continue;
    }

    // Fetch product base info including active status
    $stmt = $pdo->prepare("SELECT p_name, p_current_price, p_is_active FROM tbl_product WHERE p_id = ?");
    $stmt->execute([$item['product_id']]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if product is active; if not, collect its name and skip adding to verified cart
    if (!$product || $product['p_is_active'] != 1) {
        $inactiveProducts[] = $product ? $product['p_name'] : "Product ID {$item['product_id']}";
        continue;
    }

    // Session price & quantity
    $currentPrice = floatval($item['price']);
    $quantity     = max(1, (int)$item['quantity']);

    // Subtotal for items
    $itemSubtotal = $currentPrice * $quantity;

    // Installation fee per unit if selected
    $unitInstallFee = (
        !empty($item['installation']) && $item['installation'] == 1
    ) ? floatval($item['installation_fee'] ?? 0) : 0.0;
    $itemInstallFee = $unitInstallFee * $quantity;

    // Verify variation price against DB if any
    $variation_price = null;
    if (!empty($item['variation_id'])) {
        try {
            $stmt = $pdo->prepare("
                SELECT price_adjustment
                FROM tbl_product_variations
                WHERE variation_id = ? AND product_id = ?
            ");
            $stmt->execute([$item['variation_id'], $item['product_id']]);
            $var = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($var) {
                $dbPrice = $product['p_current_price'] + floatval($var['price_adjustment']);
                if (abs($dbPrice - $currentPrice) > 0.01) {
                    $price_discrepancy = true;
                    error_log("Price discrepancy for product {$item['product_id']}: session {$currentPrice} vs db {$dbPrice}");
                }
                $variation_price = $dbPrice;
            }
        } catch (PDOException $e) {
            error_log("Variation lookup error: " . $e->getMessage());
        }
    } else {
        // Verify base price for non-variation
        if (abs($product['p_current_price'] - $currentPrice) > 0.01) {
            $price_discrepancy = true;
            error_log("Price discrepancy for product {$item['product_id']}: session {$currentPrice} vs db {$product['p_current_price']}");
        }
    }

    // Build verified cart entry
    $itemTotal = $itemSubtotal + $itemInstallFee;
    $verifiedCart[] = [
        'product_id'       => $item['product_id'],
        'name'             => $product['p_name'],
        'unit_price'       => $currentPrice,
        'quantity'         => $quantity,
        'variation_id'     => $item['variation_id'] ?? null,
        'variation_price'  => $variation_price,
        'color_id'         => $item['color_id'] ?? null,
        'installation_fee' => $itemInstallFee,
        'subtotal'         => $itemSubtotal,
        'total'            => $itemTotal,
        'variation_name'   => $item['variation_name'] ?? null
    ];
}

// If there are inactive products, remove them from the session cart
if (!empty($inactiveProducts)) {
    foreach ($_SESSION['cart'] as $key => $item) {
        if (empty($item['product_id'])) {
            unset($_SESSION['cart'][$key]);
            continue;
        }
        $stmt = $pdo->prepare("SELECT p_is_active FROM tbl_product WHERE p_id = ?");
        $stmt->execute([$item['product_id']]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$product || $product['p_is_active'] != 1) {
            unset($_SESSION['cart'][$key]);
        }
    }
    // Reset array keys and set error message for the user.
    $_SESSION['cart'] = array_values($_SESSION['cart']);
    $_SESSION['error_message'] = "The following products were removed from your cart because they are no longer available: " . implode(", ", $inactiveProducts);
    header("Location: cart.php");
    exit;
}

// Generate transaction reference
$tx_ref = "ORDER_" . time() . "_" . bin2hex(random_bytes(4));

// Get shipping country and fee from session
$shipping_country_id = $_SESSION['shipping_country_id'] ?? null;
$shipping_fee = floatval($_SESSION['shipping_fee'] ?? 0.0);

// If shipping_country_id is set but shipping_country is not, fetch the country name from the database
if ($shipping_country_id && empty($_SESSION['shipping_country'])) {
    try {
        $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
        $stmt->execute([$shipping_country_id]);
        $country = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($country) {
            $_SESSION['shipping_country'] = $country['country_name'];
        }
    } catch (PDOException $e) {
        error_log("Error fetching country name: " . $e->getMessage());
    }
}

$shipping_country = $_SESSION['shipping_country'] ?? '';

// If shipping_country is still empty but we have a country ID in localStorage, try to use that
if (empty($shipping_country) && isset($_COOKIE['selectedCountryId'])) {
    $selectedCountryId = $_COOKIE['selectedCountryId'];
    try {
        $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
        $stmt->execute([$selectedCountryId]);
        $country = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($country) {
            $shipping_country = $country['country_name'];
            $shipping_country_id = $selectedCountryId;
            $_SESSION['shipping_country'] = $shipping_country;
            $_SESSION['shipping_country_id'] = $shipping_country_id;
        }
    } catch (PDOException $e) {
        error_log("Error fetching country name from cookie: " . $e->getMessage());
    }
}

// Log shipping information for debugging
error_log("Shipping info: Country ID: $shipping_country_id, Country: $shipping_country, Fee: $shipping_fee");

// Verify shipping country is selected
if (empty($shipping_country)) {
    // As a last resort, try to get the country name from the database using the country ID from localStorage
    $country_id_from_js = null;

    // Check if we have a country ID in the request
    if (isset($_COOKIE['selectedCountryId']) && !empty($_COOKIE['selectedCountryId'])) {
        $country_id_from_js = $_COOKIE['selectedCountryId'];
        error_log("Found country ID in cookie: $country_id_from_js");
    }
    // Try to get it from localStorage via JavaScript
    else {
        error_log("No country ID in cookie, trying to get it from localStorage");
        // We'll set a default country ID and name as a fallback
        $shipping_country = "Tanzania";
        $shipping_country_id = "1"; // Assuming Tanzania is ID 1
        $_SESSION['shipping_country'] = $shipping_country;
        $_SESSION['shipping_country_id'] = $shipping_country_id;
        error_log("Set default shipping country: $shipping_country (ID: $shipping_country_id)");
    }
}

// Begin DB transaction
$pdo->beginTransaction();
try {
    // Enable error reporting for debugging
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    // Insert into orders
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            tx_ref, user_id, firstname, lastname, email, phone, address,
            total_amount, shipping_fee, installation_fee_total, currency, payment_status,
            shipping_country, shipping_country_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $tx_ref,
        $user_id,
        $customer['cust_fname'],
        $customer['cust_lname'],
        $customer['cust_email'],
        $customerData['cust_phone'] ?? '',
        $address,
        $grand_total,
        $shipping_fee,
        $installation_fee_total,
        CURRENCY_CODE,
        'pending',
        $shipping_country,
        $shipping_country_id
    ]);
    $order_id = $pdo->lastInsertId();

    // Insert each order item
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, product_id, variation_id, variation_name, product_name,
            color_id, quantity, unit_price, variation_price,
            installation_fee, subtotal, total
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    foreach ($verifiedCart as $item) {
        $stmt->execute([
            $order_id,
            $item['product_id'],
            $item['variation_id'],
            $item['variation_name'],
            $item['name'],
            $item['color_id'],
            $item['quantity'],
            $item['unit_price'],
            $item['variation_price'],
            $item['installation_fee'],
            $item['subtotal'],
            $item['total']
        ]);
    }

    $pdo->commit();

    /*
    // Clear cart
    unset($_SESSION['cart'], $_SESSION['shipping_fee']); */
    unset(
       // $_SESSION['cart'],
        $_SESSION['shipping_fee'],
        $_SESSION['shipping_country'],
        $_SESSION['shipping_country_id'],
        $_SESSION['final_total']

    );



} catch (Exception $e) {
    $pdo->rollBack();
    error_log("Order processing failed: " . $e->getMessage());
    $_SESSION['error_message'] = "Order processing failed. Please try again.";
    header("Location: cart.php");
    exit;
}

// Log if any price discrepancies were found
if ($price_discrepancy) {
    error_log("Price discrepancies detected in order $tx_ref");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Processing Payment</title>
    <script src="https://checkout.flutterwave.com/v3.js"></script>
</head>
<body>
<script>
function makePayment() {
    FlutterwaveCheckout({
        public_key: "FLWPUBK_TEST-02b9b5fc6406bd4a41c3ff141cc45e93-X",
        tx_ref: "<?= $tx_ref ?>",
        amount: <?= number_format($grand_total, 2, '.', '') ?>,
        currency: "<?= CURRENCY_CODE ?>",
        payment_options: "card, mobilemoney, ussd, banktransfer, account, applepay",
        customer: {
            email: "<?= $customer['cust_email'] ?>",
            phone_number: "<?= $customerData['cust_phone'] ?? '' ?>",
            name: "<?= $customer['cust_fname'] . ' ' . $customer['cust_lname'] ?>"
        },
        callback: function(data) {
            window.location.href = "payment_verify.php?tx_ref=" + data.tx_ref;
                // Clear localStorage shipping data
    localStorage.removeItem('selectedCountryId');
    localStorage.removeItem('shippingFee');
        },
        onclose: function() {
            alert("Payment cancelled - your cart has been saved");
            window.location.href = "cart.php";
        },
        customizations: {
            title: "SMART LIFE Order",
            description: "Payment for your order #<?= $tx_ref ?>",
            logo: "https://smartlifetz.com/assets/uploads/logo.png"
        }
    });
}
makePayment();
</script>
</body>
</html>
