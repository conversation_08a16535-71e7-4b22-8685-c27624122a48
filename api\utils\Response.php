<?php
/**
 * API Response Utility Class
 * Standardizes API responses across all endpoints
 */

class Response {

    /**
     * Send a successful response
     */
    public static function success($data = null, $message = 'Success', $code = 200) {
        http_response_code($code);

        $response = [
            'status' => 'success',
            'message' => $message,
            'timestamp' => date('c'),
            'api_version' => API_VERSION
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        header('Content-Type: application/json');
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Send an error response
     */
    public static function error($message = 'An error occurred', $code = 400, $error_code = null, $details = null) {
        http_response_code($code);

        $response = [
            'status' => 'error',
            'message' => $message,
            'timestamp' => date('c'),
            'api_version' => API_VERSION
        ];

        if ($error_code) {
            $response['error_code'] = $error_code;
        }

        if ($details) {
            $response['details'] = $details;
        }

        header('Content-Type: application/json');
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Send a paginated response
     */
    public static function paginated($data, $total, $page, $limit, $message = 'Success') {
        $total_pages = ceil($total / $limit);

        $pagination = [
            'current_page' => (int)$page,
            'per_page' => (int)$limit,
            'total_items' => (int)$total,
            'total_pages' => (int)$total_pages,
            'has_next' => $page < $total_pages,
            'has_prev' => $page > 1
        ];

        self::success([
            'items' => $data,
            'pagination' => $pagination
        ], $message);
    }

    /**
     * Send validation error response
     */
    public static function validationError($errors) {
        self::error('Validation failed', 422, 'VALIDATION_ERROR', $errors);
    }

    /**
     * Send unauthorized response
     */
    public static function unauthorized($message = 'Unauthorized access') {
        self::error($message, 401, 'UNAUTHORIZED');
    }

    /**
     * Send forbidden response
     */
    public static function forbidden($message = 'Access forbidden') {
        self::error($message, 403, 'FORBIDDEN');
    }

    /**
     * Send not found response
     */
    public static function notFound($message = 'Resource not found') {
        self::error($message, 404, 'NOT_FOUND');
    }

    /**
     * Send method not allowed response
     */
    public static function methodNotAllowed($allowed_methods = []) {
        $message = 'Method not allowed';
        if (!empty($allowed_methods)) {
            $message .= '. Allowed methods: ' . implode(', ', $allowed_methods);
        }
        self::error($message, 405, 'METHOD_NOT_ALLOWED');
    }

    /**
     * Send internal server error response
     */
    public static function serverError($message = 'Internal server error') {
        self::error($message, 500, 'INTERNAL_ERROR');
    }

    /**
     * Send rate limit exceeded response
     */
    public static function rateLimitExceeded($message = 'Rate limit exceeded') {
        self::error($message, 429, 'RATE_LIMIT_EXCEEDED');
    }
}
