<?php
// Include database configuration
include("inc/config.php");

try {
    // Create settings table if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS tbl_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        setting_name VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        setting_description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Insert default installation fee
    $stmt = $pdo->prepare("INSERT INTO tbl_settings (setting_name, setting_value, setting_description) 
                          VALUES ('installation_fee', '15000', 'Default installation fee in TSH') 
                          ON DUPLICATE KEY UPDATE setting_value = '15000'");
    $stmt->execute();
    
    echo "Settings table created and default installation fee set successfully.";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
