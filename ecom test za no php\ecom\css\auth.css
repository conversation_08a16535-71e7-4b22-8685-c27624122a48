/* Example css/auth.css - Based on style.css variables */
.auth-container {
    max-width: 450px;
    margin: 0 auto; /* Remove top margin */
    padding: 40px;
    background-color: var(--primary); /* Typically white */
    border-radius: 8px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border); /* Subtle border */
    position: relative; /* Ensure proper stacking context */
    z-index: 1; /* Ensure it's above any background elements */
}

.auth-container h2 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--dark);
    font-weight: 600; /* Match h-tag styling */
}


.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500; /* Sora font weight */
    color: var(--gray);
    font-size: 0.9rem;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="tel"],
.form-group input[type="file"] { /* Add file input styling */
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 1rem;
    font-family: "Sora", sans-serif; /* Use main font */
    box-sizing: border-box; /* Include padding in width */
}
.form-group input[type="file"] {
    padding: 8px; /* Adjust file input padding */
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary);
    box-shadow: 0 0 0 3px rgba(0, 194, 255, 0.15); /* Match secondary color */
}

.form-row {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping on small screens */
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    min-width: calc(50% - 10px); /* Adjust for gap */
    margin-bottom: 0;
}

.auth-container .btn { /* Style auth buttons like site buttons */
    width: 100%;
    margin-top: 15px;
    padding: 12px 32px; /* Match style.css .btn */
    /* Inherits font, color, transition from style.css */
}

.auth-container p { /* Style the "Don't have an account?" text */
    text-align: center;
    margin-top: 25px;
    color: var(--gray);
    font-size: 0.9rem;
}

.auth-container p a {
    color: var(--secondary);
    text-decoration: none;
    font-weight: 500;
}

.auth-container p a:hover {
    text-decoration: underline;
}

/* Error/Success Messages Styling */
.error-message, .success-message {
    padding: 12px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-size: 0.9rem;
}
.error-message {
    background-color: #ffe8e8; /* Light red */
    color: #a30000; /* Dark red */
    border: 1px solid #f5c6cb;
}
 .success-message {
    background-color: #e8f5e9; /* Light green */
    color: #155724; /* Dark green */
    border: 1px solid #c3e6cb;
}

/* css/auth.css */

  /* Mobile Menu Styles */
  .mobile-menu {
    display: none;
    margin-right: auto; /* Push to the right */
}

.menu-btn {
    width: 30px;
    height: 20px;
    position: relative;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.menu-btn span {
    display: block;
    width: 100%;
    height: 3px;
    background-color: #333;
    transition: all 0.3s ease;
}

.menu-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-btn.active span:nth-child(2) {
    opacity: 0;
}

.menu-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

@media (max-width: 768px) {
    .mobile-menu {
        display: block;
    }

    .nav-links {
        position: fixed;
        top: 70px;
        right: -50%; /* Start off-screen to the right */
        width: 50%; /* Take half the screen */
        height: calc(100vh - 70px);
        background-color: #fff;
        flex-direction: column;
        align-items: flex-start;
        padding: 20px;
        transition: all 0.3s ease;
        z-index: 999;
        box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        right: 0; /* Slide in from right */
    }

    .nav-links a {
        padding: 12px 0;
        width: 100%;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .cart-icon {
        margin-top: 15px;
        padding-left: 0;
    }

    /* Overlay for the rest of the screen */
    .nav-overlay {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: rgba(0,0,0,0.5);
        z-index: 998;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .nav-links.active ~ .nav-overlay {
        opacity: 1;
        pointer-events: auto;
    }
}
/* Override mobile nav behavior ONLY on auth pages */
@media (max-width: 768px) { /* Apply only on mobile viewports */

    body.auth-page .nav-links {
        /* Override default positioning */
        right: auto; /* Remove the default right positioning */
        left: -100%; /* Position off-screen left */

        /* Override transition */
        transition: left 0.5s cubic-bezier(0.77, 0, 0.175, 1);

        /* Override box-shadow */
        box-shadow: 5px 0 30px rgba(0, 0, 0, 0.1); /* Shadow on the right */
    }

    body.auth-page .nav-links.active {
        /* Override default active state */
        right: auto; /* Remove the default right positioning */
        left: 0; /* Slide in from left */
    }



} /* End Media Query */

/* Optional: Adjust main padding specifically for auth pages if needed */
/* body.auth-page main { padding-top: 100px; } */
/* Add Padding Below Fixed Header */
main {
    /* Significantly increase padding to prevent content from being hidden under header */
    padding-top: 200px !important; /* Use !important to override any other styles */
    /* Keep existing padding or add others if needed */
    padding-bottom: 40px; /* Example bottom padding */
    flex: 1; /* Ensure main takes up available space (if using flex for body) */
  }

/* Ensure header is visible with background */
body.auth-page header {
    background-color: white !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    z-index: 9999 !important;
}
/* Responsive */
@media (max-width: 768px) {
    .form-row .form-group {
       min-width: 100%; /* Stack fields */
       margin-bottom: 20px; /* Add margin when stacked */
    }
    .form-row {
        margin-bottom: 0; /* Remove margin from row itself */
        gap: 0;
    }
     .form-row .form-group:last-child {
        margin-bottom: 0;
    }
}
@media (max-width: 500px) {
    .auth-container {
        margin: 30px auto;
        padding: 25px;
    }
}