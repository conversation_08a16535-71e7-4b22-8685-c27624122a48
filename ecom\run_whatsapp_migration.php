<?php
// WhatsApp Migration Runner
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type for JSON response
header('Content-Type: application/json');

try {
    // Include database config
    include("../admin/inc/config.php");
    
    if (!isset($pdo)) {
        throw new Exception("Database connection not available");
    }
    
    // Check current payment_status column
    $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_status'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$column) {
        throw new Exception("payment_status column not found in orders table");
    }
    
    $currentType = $column['Type'];
    $hasWhatsApp = strpos($currentType, 'whatsapp') !== false;
    
    if ($hasWhatsApp) {
        echo json_encode([
            'success' => true,
            'message' => 'WhatsApp enum already exists',
            'current_type' => $currentType,
            'migration_needed' => false
        ]);
        exit;
    }
    
    // Run the migration
    $migrationSQL = "ALTER TABLE orders MODIFY COLUMN payment_status ENUM('pending', 'success', 'failed', 'whatsapp') DEFAULT 'pending'";
    
    $result = $pdo->exec($migrationSQL);
    
    // Verify the migration
    $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_status'");
    $newColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    $newType = $newColumn['Type'];
    $migrationSuccess = strpos($newType, 'whatsapp') !== false;
    
    if ($migrationSuccess) {
        echo json_encode([
            'success' => true,
            'message' => 'Migration completed successfully',
            'old_type' => $currentType,
            'new_type' => $newType,
            'migration_needed' => true,
            'migration_completed' => true
        ]);
    } else {
        throw new Exception("Migration failed - whatsapp enum not found after migration");
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Migration failed: ' . $e->getMessage(),
        'error' => $e->getMessage()
    ]);
}
?>
