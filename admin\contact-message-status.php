<?php
require_once('header.php');

if(isset($_GET['id'])) {
    $id = $_GET['id'];
    // Get current status and toggle it
    $statement = $pdo->prepare("SELECT status FROM tbl_contact_message WHERE id=?");
    $statement->execute(array($id));
    $result = $statement->fetch(PDO::FETCH_ASSOC);
    
    if($result) {
        $new_status = $result['status'] ? 0 : 1;
        $statement = $pdo->prepare("UPDATE tbl_contact_message SET status=? WHERE id=?");
        $statement->execute(array($new_status, $id));
        
        $success_message = 'Contact message status updated successfully.';
        header('Location: contact-messages.php?success=' . urlencode($success_message));
        exit;
    } else {
        $error_message = 'Message not found.';
        header('Location: contact-messages.php?error=' . urlencode($error_message));
        exit;
    }
} else {
    $error_message = 'Invalid request.';
    header('Location: contact-messages.php?error=' . urlencode($error_message));
    exit;
}
?>
