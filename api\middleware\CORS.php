<?php
/**
 * CORS Middleware
 * Handles Cross-Origin Resource Sharing headers
 */

class CORSMiddleware {

    /**
     * Handle CORS headers
     */
    public static function handle() {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';

        // If '*' is in allowed origins, allow all. Otherwise, check specific origin
        if (in_array('*', ALLOWED_ORIGINS)) {
            header("Access-Control-Allow-Origin: *");
        } elseif (in_array($origin, ALLOWED_ORIGINS)) {
            header("Access-Control-Allow-Origin: {$origin}");
        }

        header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
        header("Access-Control-Allow-Credentials: true");
        header("Access-Control-Max-Age: 86400"); // 24 hours

        // Handle preflight OPTIONS request
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
}
