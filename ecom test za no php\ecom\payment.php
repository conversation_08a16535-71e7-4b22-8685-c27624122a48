<?php
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Verify user is logged in
if (!isset($_SESSION['customer'])) {
    header("Location: login.php");
    exit;
}

// Verify tx_ref is provided
if (!isset($_GET['tx_ref'])) {
    header("Location: cart.php");
    exit;
}

$tx_ref = $_GET['tx_ref'];

// Fetch order details
try {
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE tx_ref = ?");
    $stmt->execute([$tx_ref]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    // Log order details for debugging
    error_log("Order details: " . json_encode($order));
} catch (PDOException $e) {
    error_log("Error fetching order: " . $e->getMessage());
    $_SESSION['error_message'] = "Error retrieving order details.";
    header("Location: cart.php");
    exit;
}

if (!$order) {
    $_SESSION['error_message'] = "Invalid order reference.";
    header("Location: cart.php");
    exit;
}

// Get customer data
$customer = $_SESSION['customer'];

// Constants
define('CURRENCY_CODE', 'TZS');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - SMART LIFE</title>
    <link rel="icon" href="/ecom4/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css" />
    <script src="https://checkout.flutterwave.com/v3.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
        }
        .payment-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .payment-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .payment-header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }
        .payment-details {
            margin-bottom: 30px;
        }
        .payment-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .payment-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 2px solid #eee;
        }
        .payment-actions {
            text-align: center;
            margin-top: 30px;
        }
        .payment-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .payment-button:hover {
            background-color: #0056b3;
        }
        .cancel-button {
            background-color: #6c757d;
            margin-left: 10px;
        }
        .cancel-button:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="payment-header">
            <h1><i class="fas fa-credit-card"></i> Complete Your Payment</h1>
            <p>Order Reference: <?php echo htmlspecialchars($tx_ref); ?></p>
        </div>

        <div class="payment-details">
            <?php
            // Get the total amount
            $total_amount = isset($order['total_amount']) ? $order['total_amount'] : 0;

            // Get the shipping fee
            $shipping_fee = isset($order['shipping_fee']) ? $order['shipping_fee'] : 0;

            // Get the installation fee
            $installation_fee = isset($order['installation_fee_total']) ? $order['installation_fee_total'] : 0;

            // Calculate the products subtotal
            $products_subtotal = $total_amount - $shipping_fee - $installation_fee;
            ?>
            <div class="payment-row">
                <span>Products Subtotal:</span>
                <span>TSH <?php echo number_format($products_subtotal, 0); ?></span>
            </div>
            <div class="payment-row">
                <span>Shipping Fee:</span>
                <span>TSH <?php echo number_format($shipping_fee, 0); ?></span>
            </div>
            <?php if ($installation_fee > 0): ?>
            <div class="payment-row">
                <span>Installation Fee:</span>
                <span>TSH <?php echo number_format($installation_fee, 0); ?></span>
            </div>
            <?php endif; ?>
            <div class="payment-row">
                <span>Total Amount:</span>
                <span>TSH <?php echo number_format($total_amount, 0); ?></span>
            </div>
        </div>

        <div class="payment-actions">
            <button id="payButton" class="payment-button">Pay Now</button>
            <a href="cart.php" class="payment-button cancel-button">Cancel</a>
        </div>
    </div>

    <script>
        document.getElementById('payButton').addEventListener('click', function() {
            makePayment();
        });

        function makePayment() {
            // Get the total amount
            const totalAmount = <?php echo json_encode($total_amount); ?>;

            // Get customer details
            const customerEmail = <?php echo json_encode($customer['cust_email'] ?? ''); ?>;
            const customerPhone = <?php echo json_encode($order['phone'] ?? ''); ?>;
            const customerName = <?php echo json_encode(($order['firstname'] ?? '') . ' ' . ($order['lastname'] ?? '')); ?>;

            console.log("Payment details:", {
                tx_ref: <?php echo json_encode($tx_ref); ?>,
                amount: totalAmount,
                customer: {
                    email: customerEmail,
                    phone: customerPhone,
                    name: customerName
                }
            });

            // Add debug logging
            if (!customerEmail || !customerPhone || !customerName) {
                console.error("Missing required customer information:", {
                    email: customerEmail,
                    phone: customerPhone,
                    name: customerName
                });
                alert("Please ensure all customer information is provided");
                return;
            }

            if (!totalAmount || totalAmount <= 0) {
                console.error("Invalid amount:", totalAmount);
                alert("Invalid payment amount");
                return;
            }

            FlutterwaveCheckout({
                public_key: "FLWPUBK_TEST-02b9b5fc6406bd4a41c3ff141cc45e93-X",
                tx_ref: "<?php echo $tx_ref; ?>",
                amount: totalAmount,
                currency: "<?php echo CURRENCY_CODE; ?>",
                payment_options: "card, mobilemoney, ussd, banktransfer, account, applepay",
                customer: {
                    email: customerEmail,
                    phone_number: customerPhone,
                    name: customerName
                },
                callback: function(data) {
                    // Ensure we have a valid transaction reference
                    if (data && data.tx_ref) {
                        window.location.href = "payment_verify.php?tx_ref=" + data.tx_ref;
                        // Clear localStorage shipping data
                        localStorage.removeItem('selectedCountryId');
                        localStorage.removeItem('shippingFee');
                    } else {
                        alert("Payment verification failed. Please contact support.");
                        window.location.href = "cart.php";
                    }
                },
                onclose: function() {
                    alert("Payment cancelled - your order has been saved");
                    window.location.href = "cart.php";
                },
                customizations: {
                    title: "SMART LIFE Order",
                    description: "Payment for your order #<?php echo $tx_ref; ?>",
                    logo: "https://smartlifetz.com/assets/uploads/logo.png"
                },
                meta: {
                    order_id: "<?php echo $tx_ref; ?>"
                }
            });
        }
    </script>
</body>
</html>
