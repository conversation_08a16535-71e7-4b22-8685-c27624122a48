<?php
/**
 * Clear Rate Limits
 * Clears all rate limiting data for testing
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>Clear Rate Limits</h1>";

try {
    // Clear database rate limits
    $deleted_db = $pdo->exec("DELETE FROM api_rate_limits");
    echo "<p style='color: green;'>✓ Cleared {$deleted_db} database rate limit entries</p>";
    
    // Clear file-based rate limits
    $rate_limit_dir = __DIR__ . '/storage/rate_limits/';
    $deleted_files = 0;
    
    if (is_dir($rate_limit_dir)) {
        $files = glob($rate_limit_dir . '*.json');
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted_files++;
            }
        }
    }
    
    echo "<p style='color: green;'>✓ Cleared {$deleted_files} rate limit files</p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ Rate Limits Cleared Successfully!</h2>";
    echo "<p>All rate limiting data has been cleared. You can now test the API without rate limit restrictions.</p>";
    echo "<p><strong>Note:</strong> Localhost IPs (127.0.0.1, ::1) are now bypassed automatically.</p>";
    echo "</div>";
    
    echo "<h2>Test the API Now</h2>";
    echo "<ul>";
    echo "<li><a href='/ecom/api/test.php'>Run API Test</a></li>";
    echo "<li><a href='/ecom/api/final_test.php'>Run Final Test</a></li>";
    echo "<li><a href='/ecom/api/v1/'>Test API Root</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><small>You can delete this file after clearing rate limits.</small></p>";
?>
