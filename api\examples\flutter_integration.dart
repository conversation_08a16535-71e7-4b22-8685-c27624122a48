// Flutter API Integration Example
// This file shows how to integrate with the Ecommerce API from a Flutter app

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class EcommerceApiService {
  static const String baseUrl = 'https://yourdomain.com/ecom/api/v1';
  // For local development: 'http://localhost/ecom/api/v1'
  // For production: 'https://yourdomain.com/ecom/api/v1'

  // Get stored JWT token
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('jwt_token');
  }

  // Save JWT token
  Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('jwt_token', token);
  }

  // Remove JWT token
  Future<void> removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('jwt_token');
  }

  // Get headers with authentication
  Future<Map<String, String>> getHeaders() async {
    final token = await getToken();
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Handle API response
  Map<String, dynamic> handleResponse(http.Response response) {
    final data = json.decode(response.body);

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw ApiException(
        message: data['message'] ?? 'Unknown error',
        statusCode: response.statusCode,
        errorCode: data['error_code'],
      );
    }
  }

  // Authentication Methods

  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: await getHeaders(),
      body: json.encode({
        'email': email,
        'password': password,
      }),
    );

    final data = handleResponse(response);

    // Save token for future requests
    if (data['data']['token'] != null) {
      await saveToken(data['data']['token']);
    }

    return data;
  }

  Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: await getHeaders(),
      body: json.encode(userData),
    );

    final data = handleResponse(response);

    // Save token for future requests
    if (data['data']['token'] != null) {
      await saveToken(data['data']['token']);
    }

    return data;
  }

  Future<void> logout() async {
    await http.post(
      Uri.parse('$baseUrl/auth/logout'),
      headers: await getHeaders(),
    );

    await removeToken();
  }

  // Product Methods

  Future<Map<String, dynamic>> getProducts({
    int page = 1,
    int limit = 20,
    String? search,
    int? categoryId,
    double? minPrice,
    double? maxPrice,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (search != null) queryParams['search'] = search;
    if (categoryId != null) queryParams['category_id'] = categoryId.toString();
    if (minPrice != null) queryParams['min_price'] = minPrice.toString();
    if (maxPrice != null) queryParams['max_price'] = maxPrice.toString();

    final uri = Uri.parse('$baseUrl/products').replace(queryParameters: queryParams);

    final response = await http.get(uri, headers: await getHeaders());
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> getProduct(int productId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/products/$productId'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> getFeaturedProducts({int limit = 10}) async {
    final response = await http.get(
      Uri.parse('$baseUrl/products/featured?limit=$limit'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  // Cart Methods

  Future<Map<String, dynamic>> getCart() async {
    final response = await http.get(
      Uri.parse('$baseUrl/cart'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> addToCart({
    required int productId,
    required int quantity,
    int? variationId,
    int? colorId,
    bool installation = false,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/cart/add'),
      headers: await getHeaders(),
      body: json.encode({
        'product_id': productId,
        'quantity': quantity,
        'variation_id': variationId,
        'color_id': colorId,
        'installation': installation,
      }),
    );
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> updateCartItem(String cartKey, int quantity) async {
    final response = await http.put(
      Uri.parse('$baseUrl/cart/$cartKey'),
      headers: await getHeaders(),
      body: json.encode({'quantity': quantity}),
    );
    return handleResponse(response);
  }

  Future<void> removeFromCart(String cartKey) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/cart/$cartKey'),
      headers: await getHeaders(),
    );
    handleResponse(response);
  }

  // Order Methods

  Future<Map<String, dynamic>> createOrder({
    required List<Map<String, dynamic>> items,
    required String shippingAddress,
    required int shippingCountryId,
    required String phone,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/orders/create'),
      headers: await getHeaders(),
      body: json.encode({
        'items': items,
        'shipping_address': shippingAddress,
        'shipping_country_id': shippingCountryId,
        'phone': phone,
      }),
    );
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> getOrders({int page = 1, int limit = 10}) async {
    final response = await http.get(
      Uri.parse('$baseUrl/orders?page=$page&limit=$limit'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> getOrder(int orderId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/orders/$orderId'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  // User Methods

  Future<Map<String, dynamic>> getUserProfile() async {
    final response = await http.get(
      Uri.parse('$baseUrl/users/profile'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> profileData) async {
    final response = await http.put(
      Uri.parse('$baseUrl/users/profile'),
      headers: await getHeaders(),
      body: json.encode(profileData),
    );
    return handleResponse(response);
  }

  // Search Methods

  Future<Map<String, dynamic>> searchProducts(String query, {
    int page = 1,
    int limit = 20,
    String sort = 'relevance',
  }) async {
    final response = await http.get(
      Uri.parse('$baseUrl/search/products?q=$query&page=$page&limit=$limit&sort=$sort'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  Future<List<dynamic>> getSearchSuggestions(String query) async {
    final response = await http.get(
      Uri.parse('$baseUrl/search/suggestions?q=$query'),
      headers: await getHeaders(),
    );
    final data = handleResponse(response);
    return data['data'] ?? [];
  }

  // Wishlist Methods

  Future<Map<String, dynamic>> getWishlist({int page = 1, int limit = 20}) async {
    final response = await http.get(
      Uri.parse('$baseUrl/wishlist?page=$page&limit=$limit'),
      headers: await getHeaders(),
    );
    return handleResponse(response);
  }

  Future<Map<String, dynamic>> toggleWishlist(int productId) async {
    final response = await http.post(
      Uri.parse('$baseUrl/wishlist/toggle'),
      headers: await getHeaders(),
      body: json.encode({'product_id': productId}),
    );
    return handleResponse(response);
  }
}

// Custom exception class for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;
  final String? errorCode;

  ApiException({
    required this.message,
    required this.statusCode,
    this.errorCode,
  });

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode, Code: $errorCode)';
  }
}

// Usage Example:
/*
void main() async {
  final apiService = EcommerceApiService();

  try {
    // Login
    final loginResult = await apiService.login('<EMAIL>', 'password');
    print('Login successful: ${loginResult['data']['user']['first_name']}');

    // Get products
    final products = await apiService.getProducts(page: 1, limit: 10);
    print('Found ${products['data']['pagination']['total_items']} products');

    // Add to cart
    await apiService.addToCart(productId: 1, quantity: 2);
    print('Added to cart successfully');

  } catch (e) {
    print('Error: $e');
  }
}
*/
