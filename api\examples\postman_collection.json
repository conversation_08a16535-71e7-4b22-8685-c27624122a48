{"info": {"name": "Ecommerce API", "description": "Complete API collection for the Ecommerce system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost/ecom/api/v1", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('jwt_token', response.data.token);", "    }", "}"]}}]}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"phone\": \"+255712345678\",\n  \"address\": \"123 Main Street\",\n  \"city\": \"Dar es Salaam\",\n  \"region\": \"Dar es Salaam\",\n  \"country\": \"Tanzania\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}}}]}, {"name": "Products", "item": [{"name": "Get Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products?page=1&limit=10", "host": ["{{base_url}}"], "path": ["products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Product Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/1", "host": ["{{base_url}}"], "path": ["products", "1"]}}}, {"name": "Get Featured Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/featured?limit=5", "host": ["{{base_url}}"], "path": ["products", "featured"], "query": [{"key": "limit", "value": "5"}]}}}, {"name": "Search Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products?search=laptop&min_price=100&max_price=1000", "host": ["{{base_url}}"], "path": ["products"], "query": [{"key": "search", "value": "laptop"}, {"key": "min_price", "value": "100"}, {"key": "max_price", "value": "1000"}]}}}]}, {"name": "Categories", "item": [{"name": "Get Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}, {"name": "Get Category Tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/tree", "host": ["{{base_url}}"], "path": ["categories", "tree"]}}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/cart", "host": ["{{base_url}}"], "path": ["cart"]}}}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 2,\n  \"installation\": true\n}"}, "url": {"raw": "{{base_url}}/cart/add", "host": ["{{base_url}}"], "path": ["cart", "add"]}}}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 3\n}"}, "url": {"raw": "{{base_url}}/cart/1-0-0", "host": ["{{base_url}}"], "path": ["cart", "1-0-0"]}}}]}, {"name": "Orders", "item": [{"name": "Get Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders", "host": ["{{base_url}}"], "path": ["orders"]}}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"product_id\": 1,\n      \"quantity\": 2,\n      \"installation\": true\n    }\n  ],\n  \"shipping_address\": \"123 Main Street, Dar es Salaam\",\n  \"shipping_country_id\": 1,\n  \"phone\": \"+255712345678\"\n}"}, "url": {"raw": "{{base_url}}/orders/create", "host": ["{{base_url}}"], "path": ["orders", "create"]}}}]}, {"name": "User Profile", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"phone\": \"+255712345679\"\n}"}, "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}}]}, {"name": "Wishlist", "item": [{"name": "Get Wishlist", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/wishlist", "host": ["{{base_url}}"], "path": ["wishlist"]}}}, {"name": "Toggle Wishlist", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1\n}"}, "url": {"raw": "{{base_url}}/wishlist/toggle", "host": ["{{base_url}}"], "path": ["wishlist", "toggle"]}}}]}, {"name": "Settings", "item": [{"name": "Get App Settings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/settings/app", "host": ["{{base_url}}"], "path": ["settings", "app"]}}}, {"name": "Get Shipping Countries", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/shipping/countries", "host": ["{{base_url}}"], "path": ["shipping", "countries"]}}}]}]}