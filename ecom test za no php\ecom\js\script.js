// Sticky Header
window.addEventListener("scroll", function () {
  const header = document.querySelector("header");
  header.classList.toggle("scrolled", window.scrollY > 50);
});

// Mobile Menu Variables
const mobileMenuTrigger = document.querySelector(".mobile-menu-trigger");
const mobileMenu = document.querySelector(".mobile-menu");
const mobileOverlay = document.querySelector(".mobile-overlay");
const mobileClose = document.querySelector(".mobile-close");
const mobileCategoryHeader = document.querySelector(".mobile-category-header");
const mobileCategoryContent = document.querySelector(".mobile-category-content");
const mobileCategoryItems = document.querySelectorAll(".mobile-category-item-header");
const mobileSearchInput = document.getElementById("mobileSearchInput");
const mobileSearchButton = document.getElementById("mobileSearchButton");
const mobileSearchSuggestions = document.getElementById("mobileSearchSuggestions");

// Function to open mobile menu
function openMobileMenu() {
  mobileMenu.classList.add("active");
  mobileOverlay.classList.add("active");
  document.body.style.overflow = "hidden"; // Prevent scrolling
}

// Function to close mobile menu
function closeMobileMenu() {
  mobileMenu.classList.remove("active");
  mobileOverlay.classList.remove("active");
  document.body.style.overflow = ""; // Enable scrolling
}

// Mobile Menu Toggle
mobileMenuTrigger.addEventListener("click", openMobileMenu);

// Mobile Menu Close
mobileClose.addEventListener("click", closeMobileMenu);

// Mobile Overlay Click
mobileOverlay.addEventListener("click", closeMobileMenu);

// Mobile Category Toggle
if (mobileCategoryHeader) {
  mobileCategoryHeader.addEventListener("click", function() {
    this.classList.toggle("active");
    mobileCategoryContent.classList.toggle("active");
  });
}

// Mobile Subcategory Toggle
mobileCategoryItems.forEach(item => {
  item.addEventListener("click", function(e) {
    // Only toggle if clicking on the arrow icon or the header itself (not the link)
    if (e.target.tagName !== 'A') {
      e.preventDefault();

      // Toggle active class
      this.classList.toggle("active");

      // Find subcategory content
      const subcategoryContent = this.nextElementSibling;
      if (subcategoryContent && subcategoryContent.classList.contains("mobile-subcategory-content")) {
        subcategoryContent.classList.toggle("active");
      }
    }
  });
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.mobile-nav-item, .mobile-subcategory-item').forEach(link => {
  link.addEventListener('click', function() {
    closeMobileMenu();
  });
});

// Smooth Scrolling
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    const targetId = this.getAttribute("href");
    if (targetId === "#") return;

    const targetElement = document.querySelector(targetId);
    if (targetElement) {
      e.preventDefault();

      window.scrollTo({
        top: targetElement.offsetTop - 80,
        behavior: "smooth",
      });

      // Close mobile menu if open
      closeMobileMenu();
    }
  });
});

// Mobile Search Functionality
if (mobileSearchInput && mobileSearchSuggestions) {
  let searchTimeout;

  // Function to highlight matching text in search results
  function highlightText(text, term) {
    if (!term) return text;
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
  }

  // Function to fetch search suggestions
  async function fetchSearchSuggestions(searchTerm) {
    if (searchTerm.length < 2) {
      mobileSearchSuggestions.classList.remove('active');
      return;
    }

    try {
      const response = await fetch(`search_suggestions.php?q=${encodeURIComponent(searchTerm)}`);
      const data = await response.json();

      if (data.length > 0) {
        mobileSearchSuggestions.innerHTML = data.map(item => `
          <div class="suggestion-item" data-type="${item.type}" data-id="${item.id}">
            <div class="icon">${item.type === 'product' ? '🛍️' : '📁'}</div>
            <div class="name">${highlightText(item.name, searchTerm)}</div>
            <div class="type">${item.type}</div>
          </div>
        `).join('');

        mobileSearchSuggestions.classList.add('active');

        // Add click event to suggestion items
        document.querySelectorAll('.suggestion-item').forEach(item => {
          item.addEventListener('click', function() {
            const type = this.getAttribute('data-type');
            const id = this.getAttribute('data-id');

            if (type === 'product') {
              window.location.href = `product_detail.php?id=${id}`;
            } else if (type === 'category') {
              window.location.href = `category.php?id=${id}`;
            }

            // Close mobile menu
            closeMobileMenu();
          });
        });
      } else {
        mobileSearchSuggestions.innerHTML = '<div class="suggestion-item">No results found</div>';
        mobileSearchSuggestions.classList.add('active');
      }
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
    }
  }

  // Input event for search
  mobileSearchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      fetchSearchSuggestions(this.value.trim());
    }, 300); // Debounce for 300ms
  });

  // Search button click
  if (mobileSearchButton) {
    mobileSearchButton.addEventListener('click', function() {
      fetchSearchSuggestions(mobileSearchInput.value.trim());
    });
  }

  // Close suggestions when clicking outside
  document.addEventListener('click', function(e) {
    if (!mobileSearchInput.contains(e.target) && !mobileSearchSuggestions.contains(e.target) && !mobileSearchButton.contains(e.target)) {
      mobileSearchSuggestions.classList.remove('active');
    }
  });
}

// Update cart count on page load
function updateCartCount() {
  const cartCountElements = document.querySelectorAll('.cart-count');

  fetch('get_cart_count.php')
    .then(response => response.text())
    .then(count => {
      cartCountElements.forEach(element => {
        element.textContent = count;
      });
    })
    .catch(error => {
      console.error('Error updating cart count:', error);
    });
}

// Function to check screen width and close mobile menu if needed
function checkScreenWidth() {
  const windowWidth = window.innerWidth;
  if (windowWidth > 768 && mobileMenu && mobileMenu.classList.contains('active')) {
    closeMobileMenu();
  }
}

// Call the function on page load
document.addEventListener('DOMContentLoaded', function() {
  updateCartCount();

  const productCards = document.querySelectorAll(".product-card");
  observeElements(productCards, "animated");

  // Check screen width initially
  checkScreenWidth();
});

// Add resize event listener to close mobile menu when switching to wide screen
window.addEventListener('resize', checkScreenWidth);

// Animation observer
const observeElements = (elements, className) => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add(className);
        }
      });
    },
    { threshold: 0.1 }
  );

  elements.forEach((element) => {
    observer.observe(element);
  });
};

  $(document).ready(function() {

    // --- Function to update the header cart count ---
    // CHECK: Ensure the URL 'get_cart_count.php' is correct.
    // CHECK: Ensure the selector '#header-cart-count' matches your header cart count element ID.
    // CHECK: Add selectors for any other places the count is displayed (e.g., '.cart-count-display').
    function updateHeaderCartCount() {
        $.ajax({
            url: 'get_cart_count.php',
            type: 'GET',
            success: function(response) {
                $('#header-cart-count').text(response); // Update header count
                $('.cart-count-display').text(response); // Update other counts if they exist
                console.log("Cart count updated to: " + response); // For debugging
            },
            error: function() {
                console.error("Error fetching cart count.");
            }
        });
    }

    // --- Handle click on delete button ---
    // CHECK: Ensure the selector '.cart-table' matches the class or ID of your main cart table
    //        or a container element around it for event delegation.
    $('.cart-table').on('click', '.delete-cart-item', function(e) {
        e.preventDefault();

        var $button = $(this);
        // CHECK: Ensure 'data-cart-index' matches the attribute name used in cart.php
        var cartIndex = $button.data('cart-index');

        if (!confirm("Are you sure you want to remove this item?")) {
             return;
        }

        // CHECK: Ensure the URL 'update_cart_session.php' is correct.
        $.ajax({
            url: 'update_cart_session.php',
            type: 'POST',
            data: {
                action: 'delete',
                index: cartIndex
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Remove row: CHECK closest('tr') targets the correct table row
                    $button.closest('tr').fadeOut(300, function() { $(this).remove(); });

                    // Update header count by calling the function above
                    updateHeaderCartCount();

                    // Update totals: CHECK selectors like '#cart-subtotal', '#cart-total'
                    // and ensure the response includes formatted totals as expected.
                     if(response.new_subtotal_formatted !== undefined) {
                          $('#cart-subtotal').text(response.new_subtotal_formatted);
                     }
                     if(response.new_total_formatted !== undefined) {
                          $('#cart-total').text(response.new_total_formatted);
                     }

                     // Handle empty cart: CHECK selectors and logic if needed.
                     if (response.new_count !== undefined && response.new_count == 0) {
                         $('.cart-table tbody').html('<tr><td colspan="6" class="text-center">Your cart is empty.</td></tr>');
                         // Maybe hide checkout button, etc.
                         $('.checkout-button-container').hide(); // Example
                     }

                } else {
                    alert('Error removing item: ' + (response.message || 'Unknown error'));
                    console.error("Server error:", response);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX Error:", textStatus, errorThrown, jqXHR.responseText);
                alert('An error occurred while removing the item. Please check the console (F12) and try again.');
            }
        });
    });

    // Optional: Call updateHeaderCartCount once on page load if it's not already being updated correctly
    // updateHeaderCartCount();

}); // end document ready

