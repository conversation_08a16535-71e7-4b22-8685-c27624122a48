<?php
/**
 * Test Order Flow Authentication
 * This script tests the complete order flow authentication
 */

// Include session configuration before starting session
include("session_config.php");
session_start();

// Include database connection and functions
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

// Check if user is logged in
$is_logged_in = isUserLoggedIn();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Flow Authentication Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
            cursor: pointer;
            border: none;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        .test-section { 
            border: 1px solid #ddd; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 5px; 
        }
        .code { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            margin: 10px 0;
        }
        .test-result { 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 3px; 
            border-left: 4px solid #007bff; 
            background: #f8f9fa; 
        }
        .test-result.pass { border-left-color: #28a745; background: #d4edda; }
        .test-result.fail { border-left-color: #dc3545; background: #f8d7da; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Order Flow Authentication Test</h1>
        
        <div class="test-section">
            <h2>Authentication Status</h2>
            <?php if ($is_logged_in): ?>
                <div class="status success">✅ User is logged in</div>
                <p><strong>Customer:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_fname'] . ' ' . $_SESSION['customer']['cust_lname']) ?></p>
                <p><strong>Email:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_email']) ?></p>
                <p><strong>Customer ID:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_id']) ?></p>
            <?php else: ?>
                <div class="status error">❌ User is not logged in</div>
                <p>You need to be logged in to test the order flow.</p>
                <a href="login.php" class="btn">Login</a>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>Order Flow Pages Test</h2>
            <p>This section tests all order-related pages for authentication issues.</p>
            
            <table>
                <thead>
                    <tr>
                        <th>Page</th>
                        <th>Expected Behavior</th>
                        <th>Test Action</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>cart.php</strong></td>
                        <td>Should work for all users</td>
                        <td><a href="cart.php" class="btn btn-success" target="_blank">Test Cart</a></td>
                        <td>✅ Always accessible</td>
                    </tr>
                    <tr>
                        <td><strong>checkout_handler.php</strong></td>
                        <td><?= $is_logged_in ? 'Should process checkout' : 'Should return "User not logged in"' ?></td>
                        <td><button class="btn btn-warning" onclick="testCheckoutHandler()">Test Checkout Handler</button></td>
                        <td id="checkout-handler-status">⏳ Click to test</td>
                    </tr>
                    <tr>
                        <td><strong>start_checkout.php</strong></td>
                        <td><?= $is_logged_in ? 'Should start payment process' : 'Should redirect to login' ?></td>
                        <td><a href="start_checkout.php" class="btn btn-warning" target="_blank">Test Start Checkout</a></td>
                        <td><?= $is_logged_in ? '✅ Should work' : '⚠️ Will redirect to login' ?></td>
                    </tr>
                    <tr>
                        <td><strong>payment.php</strong></td>
                        <td><?= $is_logged_in ? 'Should show payment page' : 'Should redirect to login' ?></td>
                        <td><a href="payment.php?tx_ref=TEST_123" class="btn btn-warning" target="_blank">Test Payment</a></td>
                        <td><?= $is_logged_in ? '✅ Should work' : '⚠️ Will redirect to login' ?></td>
                    </tr>
                    <tr>
                        <td><strong>payment_verify.php</strong></td>
                        <td>Should verify payment status</td>
                        <td><a href="payment_verify.php?tx_ref=TEST_123" class="btn btn-warning" target="_blank">Test Payment Verify</a></td>
                        <td>✅ Should work for all</td>
                    </tr>
                    <tr>
                        <td><strong>order_success.php</strong></td>
                        <td><?= $is_logged_in ? 'Should show order details' : 'Should redirect to index' ?></td>
                        <td><a href="order_success.php?order_id=123" class="btn btn-warning" target="_blank">Test Order Success</a></td>
                        <td><?= $is_logged_in ? '✅ Should work' : '⚠️ Will redirect to index' ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h2>Session Data</h2>
            <div class="code">
                <strong>Session ID:</strong> <?= htmlspecialchars(session_id()) ?><br>
                <strong>Customer Session:</strong> <?= isset($_SESSION['customer']) ? 'Set' : 'Not set' ?><br>
                <strong>Remember Token Cookie:</strong> <?= isset($_COOKIE['smartlife_remember']) ? 'Present' : 'Not present' ?><br>
                <strong>Cart Session:</strong> <?= isset($_SESSION['cart']) ? 'Set (' . count($_SESSION['cart']) . ' items)' : 'Not set' ?>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="test-results">
                <p>Run tests to see results here.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Quick Actions</h2>
            <?php if ($is_logged_in): ?>
                <a href="cart.php" class="btn">Go to Cart</a>
                <a href="dashboard.php" class="btn">Dashboard</a>
                <a href="logout.php" class="btn btn-danger">Logout</a>
            <?php else: ?>
                <a href="login.php" class="btn btn-success">Login</a>
                <a href="registration.php" class="btn">Register</a>
            <?php endif; ?>
            <a href="test_auth_status.php" class="btn">Auth Status Test</a>
            <a href="test_checkout_flow.php" class="btn">Checkout Flow Test</a>
        </div>
    </div>
    
    <script>
        function addTestResult(test, result, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${result}`;
            resultDiv.innerHTML = `<strong>${test}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function testCheckoutHandler() {
            const statusCell = document.getElementById('checkout-handler-status');
            statusCell.innerHTML = '⏳ Testing...';
            
            // Create a test cart item
            const testCart = [{
                product_id: 1,
                name: 'Test Product',
                price: 1000,
                quantity: 1,
                variation_id: null,
                color_id: null,
                installation: false
            }];
            
            // Test data
            const testData = {
                products_subtotal: 1000,
                shipping_fee: 0,
                installation_fee: 0,
                final_total: 1000,
                country_id: 1,
                cart: testCart
            };
            
            fetch('checkout_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Checkout handler response:', data);
                
                if (data.status === 'error' && data.message === 'User not logged in') {
                    statusCell.innerHTML = '❌ User not logged in (as expected)';
                    statusCell.className = 'error';
                    addTestResult('Checkout Handler', 'fail', 'User not logged in - authentication working correctly');
                } else if (data.status === 'success') {
                    statusCell.innerHTML = '✅ Checkout successful';
                    statusCell.className = 'success';
                    addTestResult('Checkout Handler', 'pass', 'Checkout processed successfully');
                } else {
                    statusCell.innerHTML = '⚠️ Unexpected response: ' + data.message;
                    statusCell.className = 'warning';
                    addTestResult('Checkout Handler', 'fail', 'Unexpected response: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error testing checkout handler:', error);
                statusCell.innerHTML = '❌ Test failed: ' + error.message;
                statusCell.className = 'error';
                addTestResult('Checkout Handler', 'fail', 'Test failed: ' + error.message);
            });
        }
        
        // Log current authentication status
        console.log('Order Flow Test Results:');
        console.log('- PHP logged in:', <?php echo json_encode($is_logged_in); ?>);
        console.log('- Session customer:', <?php echo json_encode(isset($_SESSION['customer'])); ?>);
        console.log('- Remember cookie:', <?php echo json_encode(isset($_COOKIE['smartlife_remember'])); ?>);
    </script>
</body>
</html>
