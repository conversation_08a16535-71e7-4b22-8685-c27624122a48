<?php
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Check if user is logged in
if (!isset($_SESSION['customer']) || empty($_SESSION['customer']['cust_id'])) {
    header('Location: login.php');
    exit;
}

$customer = $_SESSION['customer'];
$customer_id = $customer['cust_id'];

// Fetch complete customer data
try {
    $stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id = ?");
    $stmt->execute([$customer_id]);
    $customerData = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching customer data: " . $e->getMessage());
    $customerData = $customer;
}

// Fetch order statistics
try {
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_orders,
            SUM(CASE WHEN payment_status = 'success' THEN total_amount ELSE 0 END) as total_spent,
            SUM(CASE WHEN payment_status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN payment_status = 'failed' THEN 1 ELSE 0 END) as failed_orders
        FROM orders
        WHERE user_id = ?
    ");
    $stmt->execute([$customer_id]);
    $orderStats = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching order stats: " . $e->getMessage());
    $orderStats = ['total_orders' => 0, 'total_spent' => 0, 'pending_orders' => 0, 'failed_orders' => 0];
}

// Fetch recent orders
try {
    $stmt = $pdo->prepare("
        SELECT o.*,
               GROUP_CONCAT(oi.product_name SEPARATOR ', ') as products
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.user_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$customer_id]);
    $recentOrders = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching recent orders: " . $e->getMessage());
    $recentOrders = [];
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'retry_order':
            $order_id = $_POST['order_id'];
            try {
                $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ? AND user_id = ?");
                $stmt->execute([$order_id, $customer_id]);
                $order = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($order && in_array($order['payment_status'], ['failed', 'pending'])) {
                    // Generate new tx_ref for retry
                    $new_tx_ref = 'RETRY_' . time() . '_' . $order_id;

                    $stmt = $pdo->prepare("UPDATE orders SET tx_ref = ?, payment_status = 'pending', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$new_tx_ref, $order_id]);

                    echo json_encode(['success' => true, 'tx_ref' => $new_tx_ref, 'order_id' => $order_id]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Order cannot be retried']);
                }
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Database error']);
            }
            exit;

        case 'update_profile':
            $fname = trim($_POST['fname']);
            $lname = trim($_POST['lname']);
            $phone = trim($_POST['phone']);
            $address = trim($_POST['address']);
            $city = trim($_POST['city']);
            $region = trim($_POST['region']);
            $zip = trim($_POST['zip']);
            $country = trim($_POST['country']);

            try {
                $stmt = $pdo->prepare("
                    UPDATE tbl_customer
                    SET cust_fname = ?, cust_lname = ?, cust_phone = ?,
                        cust_address_street = ?, cust_address_city = ?,
                        cust_address_region = ?, cust_address_zip = ?, cust_country = ?
                    WHERE cust_id = ?
                ");
                $stmt->execute([$fname, $lname, $phone, $address, $city, $region, $zip, $country, $customer_id]);

                // Update session data
                $_SESSION['customer']['cust_fname'] = $fname;
                $_SESSION['customer']['cust_lname'] = $lname;

                echo json_encode(['success' => true, 'message' => 'Profile updated successfully']);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Failed to update profile']);
            }
            exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - SMART LIFE 2030</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Exo+2:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <style>
        :root {
            /* 2030 Futuristic Color Palette */
            --primary-neon: #00ffff;
            --secondary-neon: #ff00ff;
            --accent-neon: #ffff00;
            --success-neon: #00ff00;
            --warning-neon: #ff8800;
            --danger-neon: #ff0040;

            /* Dark Theme */
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --bg-glass: rgba(255, 255, 255, 0.05);
            --bg-glass-hover: rgba(255, 255, 255, 0.1);

            /* Text Colors */
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --text-muted: #6c7b8a;

            /* Gradients */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

            /* Shadows & Effects */
            --shadow-neon: 0 0 20px rgba(0, 255, 255, 0.3);
            --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-elevated: 0 20px 40px rgba(0, 0, 0, 0.4);

            /* Borders */
            --border-neon: 1px solid rgba(0, 255, 255, 0.3);
            --border-glass: 1px solid rgba(255, 255, 255, 0.1);

            /* Animations */
            --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Exo 2', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 0, 0.05) 0%, transparent 50%);
            animation: backgroundPulse 10s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes backgroundPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.7; }
        }

        /* Glassmorphism Container */
        .glass-container {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: var(--border-glass);
            border-radius: 20px;
            box-shadow: var(--shadow-glass);
            transition: var(--transition-smooth);
        }

        .glass-container:hover {
            background: var(--bg-glass-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-elevated);
        }

        /* Header */
        .dashboard-header {
            padding: 2rem;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-bottom: var(--border-glass);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-family: 'Orbitron', monospace;
            font-size: 1.8rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: var(--shadow-neon);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 600;
            box-shadow: var(--shadow-neon);
        }

        .theme-toggle {
            background: var(--bg-glass);
            border: var(--border-neon);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition-smooth);
            font-size: 1rem;
        }

        .theme-toggle:hover {
            background: var(--bg-glass-hover);
            box-shadow: var(--shadow-neon);
        }

        /* Main Container */
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
            min-height: calc(100vh - 120px);
        }

        /* Sidebar */
        .sidebar {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: var(--border-glass);
            border-radius: 20px;
            padding: 2rem;
            height: fit-content;
            position: sticky;
            top: 140px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 12px;
            transition: var(--transition-smooth);
            font-weight: 500;
        }

        .nav-link:hover,
        .nav-link.active {
            background: var(--bg-glass-hover);
            color: var(--primary-neon);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
        }

        .nav-link i {
            font-size: 1.2rem;
            width: 20px;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: var(--border-glass);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: var(--transition-smooth);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--gradient-primary);
        }

        .stat-card:nth-child(2)::before {
            background: var(--gradient-success);
        }

        .stat-card:nth-child(3)::before {
            background: var(--gradient-warning);
        }

        .stat-card:nth-child(4)::before {
            background: var(--gradient-danger);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-elevated);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-card:nth-child(2) .stat-icon {
            background: var(--gradient-success);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-card:nth-child(3) .stat-icon {
            background: var(--gradient-warning);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-card:nth-child(4) .stat-icon {
            background: var(--gradient-danger);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            font-family: 'Orbitron', monospace;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo">SMART LIFE 2030</div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($customerData['cust_fname'], 0, 1) . substr($customerData['cust_lname'], 0, 1)); ?>
                </div>
                <div>
                    <div style="font-weight: 600;"><?php echo htmlspecialchars($customerData['cust_fname'] . ' ' . $customerData['cust_lname']); ?></div>
                    <div style="font-size: 0.8rem; color: var(--text-muted);"><?php echo htmlspecialchars($customerData['cust_email']); ?></div>
                </div>
                <button class="theme-toggle" onclick="toggleTheme()">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Dashboard Container -->
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" data-section="overview">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Overview</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="orders">
                            <i class="fas fa-shopping-bag"></i>
                            <span>Orders</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="profile">
                            <i class="fas fa-user-circle"></i>
                            <span>Profile</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="analytics">
                            <i class="fas fa-chart-line"></i>
                            <span>Analytics</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="wishlist">
                            <i class="fas fa-heart"></i>
                            <span>Wishlist</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="addresses">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Addresses</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="payments">
                            <i class="fas fa-credit-card"></i>
                            <span>Payment Methods</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="support">
                            <i class="fas fa-headset"></i>
                            <span>Support</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="security">
                            <i class="fas fa-shield-alt"></i>
                            <span>Security</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                    <li class="nav-item" style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                        <a href="index.php" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>Back to Store</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="logout.php" class="nav-link" style="color: var(--danger-neon);">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-value"><?php echo number_format($orderStats['total_orders']); ?></div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-value">TZS <?php echo number_format($orderStats['total_spent'], 0); ?></div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-value"><?php echo number_format($orderStats['pending_orders']); ?></div>
                        <div class="stat-label">Pending Orders</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-value"><?php echo number_format($orderStats['failed_orders']); ?></div>
                        <div class="stat-label">Failed Orders</div>
                    </div>
                </div>

                <!-- Quick Actions Panel -->
                <div class="glass-container" style="margin-top: 2rem; padding: 2rem;">
                    <h2 class="section-title">Quick Actions</h2>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <button class="btn btn-primary" onclick="window.location.href='index.php'">
                            <i class="fas fa-shopping-cart"></i>
                            Continue Shopping
                        </button>
                        <button class="btn btn-success" onclick="showSection('orders')">
                            <i class="fas fa-history"></i>
                            View Order History
                        </button>
                        <button class="btn btn-warning" onclick="showSection('profile')">
                            <i class="fas fa-user-edit"></i>
                            Edit Profile
                        </button>
                        <button class="btn btn-ghost" onclick="showSection('support')">
                            <i class="fas fa-headset"></i>
                            Contact Support
                        </button>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="glass-container" style="margin-top: 2rem; padding: 2rem;">
                    <h2 class="section-title">Recent Orders</h2>
                    <?php if (empty($recentOrders)): ?>
                        <div style="text-align: center; padding: 3rem; color: var(--text-muted);">
                            <i class="fas fa-shopping-bag" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <p>No orders found. Start shopping to see your orders here!</p>
                            <a href="index.php" class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-shopping-cart"></i>
                                Start Shopping
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Date</th>
                                        <th>Products</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentOrders as $order): ?>
                                        <tr>
                                            <td>
                                                <span style="font-family: 'Orbitron', monospace; font-weight: 600;">
                                                    #<?php echo htmlspecialchars($order['tx_ref']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M j, Y', strtotime($order['created_at'])); ?></td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                    <?php echo htmlspecialchars($order['products'] ?: 'No products'); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span style="font-weight: 600;">
                                                    TZS <?php echo number_format($order['total_amount'], 0); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $order['payment_status']; ?>">
                                                    <?php echo ucfirst($order['payment_status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div style="display: flex; gap: 0.5rem;">
                                                    <?php if (in_array($order['payment_status'], ['failed', 'pending'])): ?>
                                                        <button class="btn btn-warning btn-sm" onclick="retryOrder(<?php echo $order['id']; ?>)">
                                                            <i class="fas fa-redo"></i>
                                                            Retry
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-ghost btn-sm" onclick="viewOrderDetails(<?php echo $order['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                        View
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </section>

            <!-- Orders Section -->
            <section id="orders" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Order Management</h2>

                    <!-- Order Filters -->
                    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; flex-wrap: wrap;">
                        <select class="form-input" style="width: auto; min-width: 150px;" onchange="filterOrders(this.value)">
                            <option value="all">All Orders</option>
                            <option value="success">Completed</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                        </select>
                        <input type="date" class="form-input" style="width: auto;" onchange="filterOrdersByDate(this.value)">
                        <input type="text" class="form-input" placeholder="Search orders..." style="flex: 1; min-width: 200px;" oninput="searchOrders(this.value)">
                    </div>

                    <div id="orders-list">
                        <!-- Orders will be loaded here via AJAX -->
                        <div style="text-align: center; padding: 2rem;">
                            <div class="loading"></div>
                            <p style="margin-top: 1rem;">Loading orders...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Profile Management</h2>

                    <form id="profile-form" onsubmit="updateProfile(event)">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                            <div>
                                <div class="form-group">
                                    <label class="form-label">First Name</label>
                                    <input type="text" class="form-input" name="fname" value="<?php echo htmlspecialchars($customerData['cust_fname']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Last Name</label>
                                    <input type="text" class="form-input" name="lname" value="<?php echo htmlspecialchars($customerData['cust_lname']); ?>" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-input" value="<?php echo htmlspecialchars($customerData['cust_email']); ?>" disabled>
                                    <small style="color: var(--text-muted); font-size: 0.8rem;">Email cannot be changed</small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Phone</label>
                                    <input type="tel" class="form-input" name="phone" value="<?php echo htmlspecialchars($customerData['cust_phone']); ?>">
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="form-label">Street Address</label>
                                    <input type="text" class="form-input" name="address" value="<?php echo htmlspecialchars($customerData['cust_address_street']); ?>">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">City</label>
                                    <input type="text" class="form-input" name="city" value="<?php echo htmlspecialchars($customerData['cust_address_city']); ?>">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Region/State</label>
                                    <input type="text" class="form-input" name="region" value="<?php echo htmlspecialchars($customerData['cust_address_region']); ?>">
                                </div>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                    <div class="form-group">
                                        <label class="form-label">ZIP Code</label>
                                        <input type="text" class="form-input" name="zip" value="<?php echo htmlspecialchars($customerData['cust_address_zip']); ?>">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Country</label>
                                        <input type="text" class="form-input" name="country" value="<?php echo htmlspecialchars($customerData['cust_country']); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 2rem; text-align: center;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Personal Analytics</h2>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem;">
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Spending Over Time</h3>
                            <canvas id="spendingChart" width="400" height="200"></canvas>
                        </div>
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Order Status Distribution</h3>
                            <canvas id="statusChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <div style="margin-top: 2rem;">
                        <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Shopping Insights</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-calendar"></i></div>
                                <div class="stat-value"><?php echo date('M j, Y', strtotime($customerData['cust_created_at'])); ?></div>
                                <div class="stat-label">Member Since</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-trophy"></i></div>
                                <div class="stat-value">Gold</div>
                                <div class="stat-label">Loyalty Status</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-star"></i></div>
                                <div class="stat-value">1,250</div>
                                <div class="stat-label">Loyalty Points</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Wishlist Section -->
            <section id="wishlist" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">My Wishlist</h2>

                    <div style="text-align: center; padding: 3rem; color: var(--text-muted);">
                        <i class="fas fa-heart" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>Your wishlist is empty. Start adding products you love!</p>
                        <a href="index.php" class="btn btn-primary" style="margin-top: 1rem;">
                            <i class="fas fa-shopping-cart"></i>
                            Browse Products
                        </a>
                    </div>
                </div>
            </section>

            <!-- Addresses Section -->
            <section id="addresses" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Address Book</h2>

                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                        <p style="color: var(--text-secondary);">Manage your shipping addresses</p>
                        <button class="btn btn-primary" onclick="addNewAddress()">
                            <i class="fas fa-plus"></i>
                            Add New Address
                        </button>
                    </div>

                    <div class="address-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                        <!-- Default Address -->
                        <div class="glass-container" style="padding: 1.5rem; position: relative;">
                            <div style="position: absolute; top: 1rem; right: 1rem;">
                                <span class="status-badge status-success">Default</span>
                            </div>
                            <h4 style="margin-bottom: 1rem; color: var(--text-primary);">
                                <?php echo htmlspecialchars($customerData['cust_fname'] . ' ' . $customerData['cust_lname']); ?>
                            </h4>
                            <p style="color: var(--text-secondary); line-height: 1.6;">
                                <?php echo htmlspecialchars($customerData['cust_address_street']); ?><br>
                                <?php echo htmlspecialchars($customerData['cust_address_city'] . ', ' . $customerData['cust_address_region']); ?><br>
                                <?php echo htmlspecialchars($customerData['cust_address_zip'] . ', ' . $customerData['cust_country']); ?>
                            </p>
                            <div style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                                <button class="btn btn-ghost btn-sm" onclick="editAddress('default')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Payment Methods Section -->
            <section id="payments" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Payment Methods</h2>

                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                        <p style="color: var(--text-secondary);">Manage your payment methods</p>
                        <button class="btn btn-primary" onclick="addPaymentMethod()">
                            <i class="fas fa-plus"></i>
                            Add Payment Method
                        </button>
                    </div>

                    <div style="text-align: center; padding: 3rem; color: var(--text-muted);">
                        <i class="fas fa-credit-card" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <p>No payment methods saved. Add a payment method for faster checkout!</p>
                    </div>
                </div>
            </section>

            <!-- Support Section -->
            <section id="support" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Customer Support</h2>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Contact Support</h3>
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <button class="btn btn-primary" onclick="startLiveChat()">
                                    <i class="fas fa-comments"></i>
                                    Start Live Chat
                                </button>
                                <button class="btn btn-success" onclick="window.location.href='mailto:<EMAIL>'">
                                    <i class="fas fa-envelope"></i>
                                    Email Support
                                </button>
                                <button class="btn btn-warning" onclick="window.location.href='tel:+255123456789'">
                                    <i class="fas fa-phone"></i>
                                    Call Support
                                </button>
                            </div>
                        </div>
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Quick Help</h3>
                            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                <a href="#" class="nav-link" onclick="showFAQ()">
                                    <i class="fas fa-question-circle"></i>
                                    <span>Frequently Asked Questions</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showOrderHelp()">
                                    <i class="fas fa-truck"></i>
                                    <span>Order & Shipping Help</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showReturnPolicy()">
                                    <i class="fas fa-undo"></i>
                                    <span>Return Policy</span>
                                </a>
                                <a href="#" class="nav-link" onclick="showWarranty()">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Warranty Information</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Security Section -->
            <section id="security" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Security Settings</h2>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Password & Authentication</h3>
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <button class="btn btn-primary" onclick="changePassword()">
                                    <i class="fas fa-key"></i>
                                    Change Password
                                </button>
                                <button class="btn btn-success" onclick="enable2FA()">
                                    <i class="fas fa-mobile-alt"></i>
                                    Enable 2FA
                                </button>
                                <button class="btn btn-warning" onclick="viewLoginHistory()">
                                    <i class="fas fa-history"></i>
                                    Login History
                                </button>
                            </div>
                        </div>
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Account Security</h3>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <p><i class="fas fa-check-circle" style="color: var(--success-neon);"></i> Account verified</p>
                                <p><i class="fas fa-check-circle" style="color: var(--success-neon);"></i> Email verified</p>
                                <p><i class="fas fa-times-circle" style="color: var(--danger-neon);"></i> 2FA not enabled</p>
                                <p><i class="fas fa-check-circle" style="color: var(--success-neon);"></i> Strong password</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="content-section">
                <div class="glass-container" style="padding: 2rem;">
                    <h2 class="section-title">Account Settings</h2>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Notifications</h3>
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <label style="display: flex; align-items: center; gap: 1rem; cursor: pointer;">
                                    <input type="checkbox" checked style="transform: scale(1.2);">
                                    <span>Order updates</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 1rem; cursor: pointer;">
                                    <input type="checkbox" checked style="transform: scale(1.2);">
                                    <span>Promotional emails</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 1rem; cursor: pointer;">
                                    <input type="checkbox" style="transform: scale(1.2);">
                                    <span>SMS notifications</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 1rem; cursor: pointer;">
                                    <input type="checkbox" checked style="transform: scale(1.2);">
                                    <span>Newsletter</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <h3 style="margin-bottom: 1rem; color: var(--text-secondary);">Preferences</h3>
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label">Language</label>
                                    <select class="form-input">
                                        <option>English</option>
                                        <option>Swahili</option>
                                        <option>French</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Currency</label>
                                    <select class="form-input">
                                        <option>TZS - Tanzanian Shilling</option>
                                        <option>USD - US Dollar</option>
                                        <option>EUR - Euro</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Time Zone</label>
                                    <select class="form-input">
                                        <option>Africa/Dar_es_Salaam</option>
                                        <option>UTC</option>
                                        <option>America/New_York</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 2rem; text-align: center;">
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i>
                            Save Settings
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Toast Notification Container -->
    <div id="toast-container"></div>

    <!-- Additional CSS for responsive design and animations -->
    <style>
        /* Additional styles for better UX */
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            background: var(--bg-glass);
            border: var(--border-glass);
            border-radius: 12px;
            color: var(--text-primary);
            font-family: inherit;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-neon);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .data-table th {
            background: var(--bg-glass);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .data-table tr:hover {
            background: var(--bg-glass-hover);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-success {
            background: rgba(0, 255, 0, 0.2);
            color: var(--success-neon);
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .status-pending {
            background: rgba(255, 136, 0, 0.2);
            color: var(--warning-neon);
            border: 1px solid rgba(255, 136, 0, 0.3);
        }

        .status-failed {
            background: rgba(255, 0, 64, 0.2);
            color: var(--danger-neon);
            border: 1px solid rgba(255, 0, 64, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .sidebar {
                position: static;
                order: 2;
            }

            .main-content {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-container {
                padding: 0.5rem;
            }

            .content-section {
                padding: 1rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-neon);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Notification Toast */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: var(--border-glass);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            color: var(--text-primary);
            box-shadow: var(--shadow-elevated);
            transform: translateX(400px);
            transition: var(--transition-smooth);
            z-index: 1000;
            max-width: 350px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-left: 4px solid var(--success-neon);
        }

        .toast.error {
            border-left: 4px solid var(--danger-neon);
        }

        .toast.warning {
            border-left: 4px solid var(--warning-neon);
        }

        .toast.info {
            border-left: 4px solid var(--primary-neon);
        }
    </style>

    <!-- JavaScript for Dashboard Functionality -->
    <script>
        // Global variables
        let currentTheme = 'dark';
        let currentSection = 'overview';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            initializeCharts();
            showWelcomeMessage();

            // Auto-save theme preference
            const savedTheme = localStorage.getItem('dashboard-theme');
            if (savedTheme) {
                currentTheme = savedTheme;
                applyTheme();
            }
        });

        // Navigation functionality
        function initializeNavigation() {
            const navLinks = document.querySelectorAll('.nav-link[data-section]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.getAttribute('data-section');
                    showSection(section);
                });
            });
        }

        function showSection(sectionName) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Show target section
            const targetSection = document.getElementById(sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionName;
            }

            // Update navigation
            const navLinks = document.querySelectorAll('.nav-link[data-section]');
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('data-section') === sectionName) {
                    link.classList.add('active');
                }
            });

            // Load section-specific data
            loadSectionData(sectionName);
        }

        function loadSectionData(section) {
            switch(section) {
                case 'orders':
                    loadOrders();
                    break;
                case 'analytics':
                    initializeCharts();
                    break;
            }
        }

        // Theme toggle functionality
        function toggleTheme() {
            currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
            applyTheme();
            localStorage.setItem('dashboard-theme', currentTheme);
            showToast('Theme changed to ' + currentTheme + ' mode', 'info');
        }

        function applyTheme() {
            const root = document.documentElement;
            const themeToggle = document.querySelector('.theme-toggle i');

            if (currentTheme === 'light') {
                root.style.setProperty('--bg-primary', '#f8f9fa');
                root.style.setProperty('--bg-secondary', '#ffffff');
                root.style.setProperty('--bg-glass', 'rgba(0, 0, 0, 0.05)');
                root.style.setProperty('--bg-glass-hover', 'rgba(0, 0, 0, 0.1)');
                root.style.setProperty('--text-primary', '#2d2d2d');
                root.style.setProperty('--text-secondary', '#6c757d');
                root.style.setProperty('--text-muted', '#9ca3af');
                themeToggle.className = 'fas fa-sun';
            } else {
                root.style.setProperty('--bg-primary', '#0a0a0f');
                root.style.setProperty('--bg-secondary', '#1a1a2e');
                root.style.setProperty('--bg-glass', 'rgba(255, 255, 255, 0.05)');
                root.style.setProperty('--bg-glass-hover', 'rgba(255, 255, 255, 0.1)');
                root.style.setProperty('--text-primary', '#ffffff');
                root.style.setProperty('--text-secondary', '#b8c5d6');
                root.style.setProperty('--text-muted', '#6c7b8a');
                themeToggle.className = 'fas fa-moon';
            }
        }

        // Toast notification system
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-${getToastIcon(type)}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Hide toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, duration);
        }

        function getToastIcon(type) {
            switch(type) {
                case 'success': return 'check-circle';
                case 'error': return 'exclamation-circle';
                case 'warning': return 'exclamation-triangle';
                default: return 'info-circle';
            }
        }

        // Order management functions
        function retryOrder(orderId) {
            showToast('Retrying order...', 'info');

            fetch('dashboard.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=retry_order&order_id=${orderId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Order retry initiated successfully!', 'success');
                    // Redirect to payment
                    setTimeout(() => {
                        window.location.href = `payment.php?retry=1&order_id=${data.order_id}&tx_ref=${data.tx_ref}`;
                    }, 1500);
                } else {
                    showToast(data.message || 'Failed to retry order', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred while retrying the order', 'error');
            });
        }

        function viewOrderDetails(orderId) {
            showToast('Loading order details...', 'info');
            // Implementation for viewing order details
            // This could open a modal or navigate to a detailed view
        }

        function loadOrders() {
            const ordersList = document.getElementById('orders-list');
            ordersList.innerHTML = '<div style="text-align: center; padding: 2rem;"><div class="loading"></div><p style="margin-top: 1rem;">Loading orders...</p></div>';

            // Simulate loading orders (replace with actual AJAX call)
            setTimeout(() => {
                ordersList.innerHTML = '<p style="text-align: center; color: var(--text-muted);">Orders loaded successfully!</p>';
            }, 1000);
        }

        // Profile management
        function updateProfile(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            formData.append('action', 'update_profile');

            showToast('Updating profile...', 'info');

            fetch('dashboard.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Profile updated successfully!', 'success');
                } else {
                    showToast(data.message || 'Failed to update profile', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred while updating profile', 'error');
            });
        }

        // Charts initialization
        function initializeCharts() {
            if (currentSection !== 'analytics') return;

            // Spending Chart
            const spendingCtx = document.getElementById('spendingChart');
            if (spendingCtx) {
                new Chart(spendingCtx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Spending (TZS)',
                            data: [12000, 19000, 3000, 5000, 2000, 30000],
                            borderColor: '#00ffff',
                            backgroundColor: 'rgba(0, 255, 255, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#b8c5d6'
                                }
                            }
                        },
                        scales: {
                            y: {
                                ticks: {
                                    color: '#b8c5d6'
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#b8c5d6'
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            // Status Chart
            const statusCtx = document.getElementById('statusChart');
            if (statusCtx) {
                new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Completed', 'Pending', 'Failed'],
                        datasets: [{
                            data: [<?php echo $orderStats['total_orders'] - $orderStats['pending_orders'] - $orderStats['failed_orders']; ?>,
                                   <?php echo $orderStats['pending_orders']; ?>,
                                   <?php echo $orderStats['failed_orders']; ?>],
                            backgroundColor: ['#00ff00', '#ff8800', '#ff0040'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#b8c5d6'
                                }
                            }
                        }
                    }
                });
            }
        }

        // Utility functions
        function showWelcomeMessage() {
            setTimeout(() => {
                showToast('Welcome to your futuristic dashboard!', 'success', 4000);
            }, 1000);
        }

        // Placeholder functions for future features
        function filterOrders(status) {
            showToast(`Filtering orders by: ${status}`, 'info');
        }

        function filterOrdersByDate(date) {
            showToast(`Filtering orders by date: ${date}`, 'info');
        }

        function searchOrders(query) {
            if (query.length > 2) {
                showToast(`Searching orders: ${query}`, 'info');
            }
        }

        function addNewAddress() {
            showToast('Add new address feature coming soon!', 'info');
        }

        function editAddress(addressId) {
            showToast('Edit address feature coming soon!', 'info');
        }

        function addPaymentMethod() {
            showToast('Add payment method feature coming soon!', 'info');
        }

        function startLiveChat() {
            showToast('Connecting to live chat...', 'info');
        }

        function showFAQ() {
            showToast('Loading FAQ...', 'info');
        }

        function showOrderHelp() {
            showToast('Loading order help...', 'info');
        }

        function showReturnPolicy() {
            showToast('Loading return policy...', 'info');
        }

        function showWarranty() {
            showToast('Loading warranty information...', 'info');
        }

        function changePassword() {
            showToast('Change password feature coming soon!', 'info');
        }

        function enable2FA() {
            showToast('2FA setup feature coming soon!', 'info');
        }

        function viewLoginHistory() {
            showToast('Loading login history...', 'info');
        }

        function saveSettings() {
            showToast('Settings saved successfully!', 'success');
        }

        // Voice search functionality (placeholder)
        function initVoiceSearch() {
            if ('webkitSpeechRecognition' in window) {
                const recognition = new webkitSpeechRecognition();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'en-US';

                recognition.onresult = function(event) {
                    const command = event.results[0][0].transcript.toLowerCase();
                    handleVoiceCommand(command);
                };

                recognition.onerror = function(event) {
                    showToast('Voice recognition error', 'error');
                };

                return recognition;
            }
            return null;
        }

        function handleVoiceCommand(command) {
            if (command.includes('orders')) {
                showSection('orders');
            } else if (command.includes('profile')) {
                showSection('profile');
            } else if (command.includes('analytics')) {
                showSection('analytics');
            } else {
                showToast(`Voice command not recognized: ${command}`, 'warning');
            }
        }

        // Progressive Web App features
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed');
                    });
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        showSection('overview');
                        break;
                    case '2':
                        e.preventDefault();
                        showSection('orders');
                        break;
                    case '3':
                        e.preventDefault();
                        showSection('profile');
                        break;
                    case 't':
                        e.preventDefault();
                        toggleTheme();
                        break;
                }
            }
        });
    </script>
</body>
</html>