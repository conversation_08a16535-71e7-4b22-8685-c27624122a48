name: lint php documentation

permissions:
  contents: read

on:
  push:
    branches:
      - 'main'
  pull_request:
    branches:
      - 'main'

jobs:
    lint-docs:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - name: lint php documentation
              uses: sudo-bot/action-doctum@dev
              with:
                  config-file: scripts/doctum.php
                  method: "parse"
                  cli-args: "--output-format=github --no-ansi --no-progress -v --ignore-parse-errors"
