<?php
/**
 * Authentication Endpoints
 * Handles user login, registration, password reset, etc.
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path
$sub_path = $segments[1] ?? '';

switch ($method) {
    case 'POST':
        switch ($sub_path) {
            case 'login':
                handleLogin($db, $input);
                break;

            case 'register':
                handleRegister($db, $input);
                break;

            case 'forgot-password':
                handleForgotPassword($db, $input);
                break;

            case 'reset-password':
                handleResetPassword($db, $input);
                break;

            case 'verify-otp':
                handleVerifyOTP($db, $input);
                break;

            case 'resend-otp':
                handleResendOTP($db, $input);
                break;

            case 'refresh':
                handleRefreshToken($db, $input);
                break;

            case 'logout':
                handleLogout();
                break;

            default:
                Response::notFound('Auth endpoint not found');
        }
        break;

    case 'GET':
        switch ($sub_path) {
            case 'me':
                handleGetCurrentUser($db);
                break;

            default:
                Response::notFound('Auth endpoint not found');
        }
        break;

    default:
        Response::methodNotAllowed(['POST', 'GET']);
}

/**
 * Handle user login
 */
function handleLogin($db, $input) {
    try {
        error_log("🔐 Login attempt for: " . ($input['email'] ?? 'no email'));

        // Validate input
        $validator = new Validator($input);
        $validator->required('email')->email('email')
                 ->required('password')->minLength('password', 6);

        if ($validator->fails()) {
            error_log("❌ Validation failed: " . json_encode($validator->getErrors()));
            Response::validationError($validator->getErrors());
        }

        $email = $input['email'];
        $password = $input['password'];
        $remember_me = $input['remember_me'] ?? false;

        error_log("🔍 Looking for user: " . $email);

        // Find user
        $user = $db->fetchOne(
            "SELECT * FROM tbl_customer WHERE cust_email = ? AND cust_status = 1",
            [$email]
        );

        error_log("👤 User found: " . ($user ? "YES (ID: " . $user['cust_id'] . ")" : "NO"));

        if (!$user || !Auth::verifyPassword($password, $user['cust_password'])) {
            error_log("❌ Authentication failed for: " . $email);
            Response::error('Invalid email or password', 401, 'INVALID_CREDENTIALS');
        }

        error_log("✅ Authentication successful for: " . $email);
    } catch (Exception $e) {
        error_log("💥 Exception in handleLogin: " . $e->getMessage());
        error_log("📍 Stack trace: " . $e->getTraceAsString());
        Response::error('An unexpected error occurred', 500, 'INTERNAL_ERROR');
    }

    // Update last login
    $db->update('tbl_customer',
        ['cust_last_login' => date('Y-m-d H:i:s')],
        'cust_id = :cust_id',
        ['cust_id' => $user['cust_id']]
    );

    // Generate token
    $token = Auth::generateToken($user);

    // Prepare response data
    $response_data = [
        'token' => $token,
        'token_type' => 'Bearer',
        'expires_in' => JWT_EXPIRY,
        'user' => [
            'id' => (int)$user['cust_id'],
            'first_name' => $user['cust_fname'],
            'last_name' => $user['cust_lname'],
            'email' => $user['cust_email'],
            'phone' => $user['cust_phone'],
            'address' => $user['cust_address_street'] ?? '',
            'city' => $user['cust_address_city'] ?? '',
            'region' => $user['cust_address_region'] ?? '',
            'zip' => $user['cust_address_zip'] ?? '',
            'country' => $user['cust_country'] ?? '',
            'photo' => $user['cust_photo'],
            'is_active' => (bool)$user['cust_status'],
            'created_at' => $user['cust_created_at'],
            'last_login' => $user['cust_last_login']
        ]
    ];

    Response::success($response_data, 'Login successful');
}

/**
 * Handle user registration
 */
function handleRegister($db, $input) {
    // Validate input
    $validator = new Validator($input);
    $validator->required('first_name')->maxLength('first_name', 100)
             ->required('last_name')->maxLength('last_name', 100)
             ->required('email')->email('email')
             ->required('password')->minLength('password', 6)
             ->required('phone')->phone('phone')
             ->required('city')->maxLength('city', 100)
             ->required('region')->maxLength('region', 100)
             ->required('country')->maxLength('country', 100);

    // Address is optional
    if (isset($input['address']) && !empty($input['address'])) {
        $validator->maxLength('address', 255);
    }

    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }

    // Check if email already exists
    if ($db->exists('tbl_customer', 'cust_email = ?', [$input['email']])) {
        Response::error('Email address already exists', 409, 'EMAIL_EXISTS');
    }

    // Hash password
    $hashed_password = Auth::hashPassword($input['password']);

    // Prepare user data
    $user_data = [
        'cust_fname' => $input['first_name'],
        'cust_lname' => $input['last_name'],
        'cust_email' => $input['email'],
        'cust_password' => $hashed_password,
        'cust_phone' => $input['phone'],
        'cust_address_street' => $input['address'],
        'cust_address_city' => $input['city'],
        'cust_address_region' => $input['region'],
        'cust_address_zip' => $input['zip_code'] ?? '',
        'cust_country' => $input['country'],
        'cust_status' => 1,
        'cust_created_at' => date('Y-m-d H:i:s')
    ];

    // Insert user
    $user_id = $db->insert('tbl_customer', $user_data);

    // Get the created user
    $user = $db->fetchOne("SELECT * FROM tbl_customer WHERE cust_id = ?", [$user_id]);

    // Generate token
    $token = Auth::generateToken($user);

    // Prepare response
    $response_data = [
        'token' => $token,
        'token_type' => 'Bearer',
        'expires_in' => JWT_EXPIRY,
        'user' => [
            'id' => (int)$user['cust_id'],
            'first_name' => $user['cust_fname'],
            'last_name' => $user['cust_lname'],
            'email' => $user['cust_email'],
            'phone' => $user['cust_phone'],
            'address' => $user['cust_address_street'] ?? '',
            'city' => $user['cust_address_city'] ?? '',
            'region' => $user['cust_address_region'] ?? '',
            'zip' => $user['cust_address_zip'] ?? '',
            'country' => $user['cust_country'] ?? '',
            'photo' => $user['cust_photo'],
            'is_active' => (bool)$user['cust_status'],
            'created_at' => $user['cust_created_at'],
            'last_login' => $user['cust_last_login'] ?? null
        ]
    ];

    Response::success($response_data, 'Registration successful', 201);
}

/**
 * Handle forgot password
 */
function handleForgotPassword($db, $input) {
    $validator = new Validator($input);
    $validator->required('email')->email('email');

    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }

    $email = $input['email'];

    // Check if user exists
    $user = $db->fetchOne("SELECT * FROM tbl_customer WHERE cust_email = ?", [$email]);

    if (!$user) {
        // Don't reveal if email exists or not for security
        Response::success(null, 'If the email exists, a reset link has been sent');
    }

    // Generate reset token
    $reset_token = Auth::generateSecureToken();
    $expires_at = date('Y-m-d H:i:s', time() + 3600); // 1 hour

    // Store reset token (you might want to create a password_resets table)
    // For now, we'll use a simple approach

    // TODO: Send email with reset link
    // For now, just return success
    Response::success(['reset_token' => $reset_token], 'Password reset link sent to your email');
}

/**
 * Get current authenticated user
 */
function handleGetCurrentUser($db) {
    $user_data = AuthMiddleware::requireAuth();

    // Get fresh user data from database
    $user = $db->fetchOne("SELECT * FROM tbl_customer WHERE cust_id = ?", [$user_data['user_id']]);

    if (!$user) {
        Response::error('User not found', 404, 'USER_NOT_FOUND');
    }

    $response_data = [
        'id' => (int)$user['cust_id'],
        'first_name' => $user['cust_fname'],
        'last_name' => $user['cust_lname'],
        'email' => $user['cust_email'],
        'phone' => $user['cust_phone'],
        'address' => [
            'street' => $user['cust_address_street'],
            'city' => $user['cust_address_city'],
            'region' => $user['cust_address_region'],
            'zip_code' => $user['cust_address_zip'],
            'country' => $user['cust_country']
        ],
        'photo' => $user['cust_photo'],
        'status' => (int)$user['cust_status'],
        'created_at' => $user['cust_created_at'],
        'last_login' => $user['cust_last_login']
    ];

    Response::success($response_data, 'User data retrieved successfully');
}

/**
 * Handle logout
 */
function handleLogout() {
    // For JWT, logout is typically handled client-side by removing the token
    // But we can add token blacklisting here if needed
    Response::success(null, 'Logged out successfully');
}

/**
 * Handle OTP verification (placeholder)
 */
function handleVerifyOTP($db, $input) {
    // Implementation depends on your OTP system
    Response::success(null, 'OTP verification not implemented yet');
}

/**
 * Handle resend OTP (placeholder)
 */
function handleResendOTP($db, $input) {
    // Implementation depends on your OTP system
    Response::success(null, 'OTP resend not implemented yet');
}

/**
 * Handle token refresh (placeholder)
 */
function handleRefreshToken($db, $input) {
    // Implementation for token refresh
    Response::success(null, 'Token refresh not implemented yet');
}

/**
 * Handle password reset (placeholder)
 */
function handleResetPassword($db, $input) {
    // Implementation for password reset
    Response::success(null, 'Password reset not implemented yet');
}
?>
