<?php require_once('header.php'); ?>

<?php
$error_message = '';
$success_message = '';

// Fetch current color data
$statement = $pdo->prepare("SELECT * FROM tbl_color WHERE color_id=?");
$statement->execute(array($_REQUEST['id']));
$result = $statement->fetchAll(PDO::FETCH_ASSOC);
foreach($result as $row) {
    $color_name = $row['color_name'];
    $color_code = $row['color_code'];
    $current_color_name = $row['color_name'];
}

if(isset($_POST['form1'])) {
    $valid = 1;

    // Validate color name (auto-populated but still required)
    if(empty($_POST['color_name'])) {
        $valid = 0;
        $error_message .= "Color Name cannot be empty<br>";
    }

    // Validate color code
    if(empty($_POST['color_code'])) {
        $valid = 0;
        $error_message .= "Color Code cannot be empty<br>";
    } elseif (!preg_match('/^#[0-9a-fA-F]{6}$/', $_POST['color_code'])) {
        $valid = 0;
        $error_message .= "Color Code must be a valid hex code (e.g., #RRGGBB)<br>";
    }

    // Check for duplicate color name (excluding current record)
    if($valid == 1) {
        $statement = $pdo->prepare("SELECT * FROM tbl_color WHERE color_name=? AND color_name!=?");
        $statement->execute(array($_POST['color_name'], $current_color_name));
        $total = $statement->rowCount();
        if($total) {
            $valid = 0;
            $error_message .= 'Color name already exists<br>';
        }
    }

    if($valid == 1) {
        // Update color
        $statement = $pdo->prepare("UPDATE tbl_color SET color_name=?, color_code=? WHERE color_id=?");
        $statement->execute(array($_POST['color_name'], $_POST['color_code'], $_REQUEST['id']));

        $success_message = 'Color is updated successfully.';

        // Refresh the data after update
        $statement = $pdo->prepare("SELECT * FROM tbl_color WHERE color_id=?");
        $statement->execute(array($_REQUEST['id']));
        $result = $statement->fetchAll(PDO::FETCH_ASSOC);
        foreach($result as $row) {
            $color_name = $row['color_name'];
            $color_code = $row['color_code'];
        }
    }
}

// Check for valid id
if(!isset($_REQUEST['id'])) {
    header('location: logout.php');
    exit;
} else {
    // Check whether the id is valid or not
    $statement = $pdo->prepare("SELECT * FROM tbl_color WHERE color_id=?");
    $statement->execute(array($_REQUEST['id']));
    $total = $statement->rowCount();
    if($total == 0) {
        header('location: logout.php');
        exit;
    }
}
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Edit Color</h1>
    </div>
    <div class="content-header-right">
        <a href="color.php" class="btn btn-primary btn-sm">View All</a>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <?php if($error_message): ?>
                <div class="callout callout-danger">
                    <p><?php echo $error_message; ?></p>
                </div>
            <?php endif; ?>

            <?php if($success_message): ?>
                <div class="callout callout-success">
                    <p><?php echo $success_message; ?></p>
                </div>
            <?php endif; ?>

            <form class="form-horizontal" action="" method="post">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Color Code <span>*</span></label>
                            <div class="col-sm-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="color_code" name="color_code" value="<?php echo $color_code; ?>" placeholder="#RRGGBB">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default" id="color_picker_btn" style="padding: 6px 12px;">
                                            <i class="fa fa-eyedropper"></i> Pick Color
                                        </button>
                                    </span>
                                </div>
                                <small class="text-muted">Enter hex code or click to pick a color</small>
                                <input type="color" id="hidden_color_picker" value="<?php echo $color_code; ?>" style="display: none;">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Color Name <span>*</span></label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="color_name" name="color_name" value="<?php echo $color_name; ?>">
                                <small id="color_name_status" class="text-muted" style="display: block; margin-top: 5px;"></small>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"></label>
                            <div class="col-sm-6">
                                <button type="submit" class="btn btn-success pull-left" name="form1">Update</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <script>
                // Mapping of common hex codes to color names (fallback)
                var colorMap = {
                    "#000000": "Black",
                    "#ffffff": "White",
                    "#ff0000": "Red",
                    "#00ff00": "Lime",
                    "#0000ff": "Blue",
                    "#ffff00": "Yellow",
                    "#00ffff": "Cyan",
                    "#ff00ff": "Magenta",
                    "#c0c0c0": "Silver",
                    "#808080": "Gray",
                    "#800000": "Maroon",
                    "#808000": "Olive",
                    "#008000": "Green",
                    "#800080": "Purple",
                    "#008080": "Teal",
                    "#000080": "Navy",
                    "#ffa500": "Orange",
                    "#a52a2a": "Brown",
                    "#ff4500": "Orange Red",
                    "#da70d6": "Orchid",
                    "#ff6347": "Tomato",
                    "#4169e1": "Royal Blue",
                    "#8b4513": "Saddle Brown",
                    "#fa8072": "Salmon",
                    "#f4a460": "Sandy Brown",
                    "#2e8b57": "Sea Green",
                    "#a0522d": "Sienna",
                    "#87ceeb": "Sky Blue",
                    "#6a5acd": "Slate Blue",
                    "#708090": "Slate Gray",
                    "#fffa00": "Lemon Yellow",
                    "#00ff7f": "Spring Green",
                    "#4682b4": "Steel Blue",
                    "#d2b48c": "Tan",
                    "#d8bfd8": "Thistle",
                    "#ff7f50": "Coral",
                    "#40e0d0": "Turquoise",
                    "#ee82ee": "Violet",
                    "#f5deb3": "Wheat",
                    "#9acd32": "Yellow Green",
                    "#9932cc": "Dark Orchid",
                    "#8b0000": "Dark Red",
                    "#e9967a": "Dark Salmon",
                    "#8fbc8f": "Dark Sea Green",
                    "#483d8b": "Dark Slate Blue",
                    "#2f4f4f": "Dark Slate Gray",
                    "#00ced1": "Dark Turquoise",
                    "#9400d3": "Dark Violet",
                    "#ff1493": "Deep Pink",
                    "#00bfff": "Deep Sky Blue",
                    "#696969": "Dim Gray",
                    "#1e90ff": "Dodger Blue",
                    "#b22222": "Fire Brick",
                    "#228b22": "Forest Green",
                    "#ffd700": "Gold",
                    "#ff69b4": "Hot Pink"
                };

                const colorCodeInput = document.getElementById('color_code');
                const colorNameInput = document.getElementById('color_name');
                const colorPickerBtn = document.getElementById('color_picker_btn');
                const hiddenColorPicker = document.getElementById('hidden_color_picker');
                const statusElement = document.getElementById('color_name_status');
                const colorNameCache = {}; // Cache for API responses

                // Function to fetch color name from The Color API
                async function fetchColorName(hex) {
                    // Remove # if present and ensure lowercase
                    hex = hex.replace('#', '').toLowerCase();

                    // Show loading status
                    statusElement.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Fetching color name...';
                    statusElement.style.color = '#3498db'; // Blue color for loading

                    try {
                        // Check cache first
                        if (colorNameCache['#' + hex]) {
                            statusElement.innerHTML = '<i class="fa fa-check"></i> Color name found!';
                            statusElement.style.color = '#27ae60'; // Green color for success
                            return colorNameCache['#' + hex];
                        }

                        // Make API request
                        const response = await fetch(`https://www.thecolorapi.com/id?hex=${hex}&format=json`);

                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }

                        const data = await response.json();

                        // Get the color name from the response
                        if (data && data.name && data.name.value) {
                            // Cache the result
                            colorNameCache['#' + hex] = data.name.value;
                            statusElement.innerHTML = '<i class="fa fa-check"></i> Color name found!';
                            statusElement.style.color = '#27ae60'; // Green color for success
                            return data.name.value;
                        } else {
                            throw new Error('Color name not found in API response');
                        }
                    } catch (error) {
                        console.error('Error fetching color name:', error);
                        statusElement.innerHTML = '<i class="fa fa-info-circle"></i> Using fallback color name';
                        statusElement.style.color = '#f39c12'; // Orange color for fallback
                        // Return fallback name from local map or empty string
                        return colorMap['#' + hex] || 'Custom Color';
                    }
                }

                // Function to update the color name (with API lookup)
                async function updateColorName(hex) {
                    hex = hex.toLowerCase();

                    // First try the local map for immediate feedback
                    const localName = colorMap[hex] || 'Custom Color';
                    if (localName) {
                        colorNameInput.value = localName;
                        statusElement.innerHTML = '<i class="fa fa-database"></i> Using local color name';
                        statusElement.style.color = '#f39c12'; // Orange color for local
                    }

                    // Then try to get a more accurate name from the API
                    try {
                        const apiName = await fetchColorName(hex);
                        if (apiName) {
                            colorNameInput.value = apiName;
                        }
                    } catch (error) {
                        console.error('Error in updateColorName:', error);
                        // Keep the local name if API fails
                    }
                }

                // Initialize color name on page load
                updateColorName(colorCodeInput.value);

                // When hex code changes
                colorCodeInput.addEventListener('input', function() {
                    if (/^#[0-9a-fA-F]{6}$/.test(this.value)) {
                        updateColorName(this.value);
                        hiddenColorPicker.value = this.value;
                    } else {
                        statusElement.innerHTML = '';
                    }
                });

                // When color picker button is clicked
                colorPickerBtn.addEventListener('click', function() {
                    hiddenColorPicker.click();
                });

                // When color is selected from picker
                hiddenColorPicker.addEventListener('input', function() {
                    colorCodeInput.value = this.value;
                    updateColorName(this.value);
                });

                // Validate hex code on blur
                colorCodeInput.addEventListener('blur', function() {
                    if (!/^#[0-9a-fA-F]{6}$/.test(this.value)) {
                        alert('Please enter a valid hex color code (e.g., #RRGGBB)');
                        this.value = '<?php echo $color_code; ?>';
                        updateColorName(this.value);
                    }
                });
            </script>
        </div>
    </div>
</section>

<div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">Delete Confirmation</h4>
            </div>
            <div class="modal-body">
                Are you sure want to delete this item?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <a class="btn btn-danger btn-ok">Delete</a>
            </div>
        </div>
    </div>
</div>

<?php require_once('footer.php'); ?>