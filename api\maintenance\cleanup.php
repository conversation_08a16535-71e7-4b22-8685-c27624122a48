<?php
/**
 * API Maintenance Script
 * Performs cleanup tasks for the API system
 */

// Include configuration
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../utils/Logger.php';

// Only allow CLI execution for security
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    echo json_encode(['error' => 'This script can only be run from command line']);
    exit;
}

echo "Starting API maintenance cleanup...\n";

try {
    $db = new Database($pdo);
    
    // 1. Clean old API logs (older than 30 days)
    echo "Cleaning old API logs...\n";
    $deleted_logs = Logger::cleanOldLogs(30);
    echo "Deleted {$deleted_logs} old log entries.\n";
    
    // 2. Clean old rate limit entries
    echo "Cleaning old rate limit entries...\n";
    $cutoff_date = date('Y-m-d H:i:s', strtotime('-2 hours'));
    $deleted_rate_limits = $db->delete('api_rate_limits', 'window_start < ?', [$cutoff_date]);
    echo "Deleted {$deleted_rate_limits} old rate limit entries.\n";
    
    // 3. Clean old rate limit files
    echo "Cleaning old rate limit files...\n";
    RateLimitMiddleware::cleanup();
    echo "Rate limit files cleaned.\n";
    
    // 4. Clean expired sessions (if using file-based sessions)
    echo "Cleaning expired sessions...\n";
    $session_path = session_save_path() ?: sys_get_temp_dir();
    $session_files = glob($session_path . '/sess_*');
    $cleaned_sessions = 0;
    
    foreach ($session_files as $file) {
        if (filemtime($file) < time() - 86400) { // 24 hours old
            if (unlink($file)) {
                $cleaned_sessions++;
            }
        }
    }
    echo "Deleted {$cleaned_sessions} expired session files.\n";
    
    // 5. Optimize database tables
    echo "Optimizing database tables...\n";
    $tables = ['api_logs', 'api_rate_limits', 'orders', 'order_items', 'tbl_wishlist'];
    
    foreach ($tables as $table) {
        try {
            $db->execute("OPTIMIZE TABLE {$table}");
            echo "Optimized table: {$table}\n";
        } catch (Exception $e) {
            echo "Warning: Could not optimize table {$table}: " . $e->getMessage() . "\n";
        }
    }
    
    // 6. Generate maintenance report
    echo "\nGenerating maintenance report...\n";
    
    // Get API statistics
    $stats = Logger::getStats(7);
    
    // Get database sizes
    $db_stats = $db->fetchAll(
        "SELECT 
            table_name,
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb',
            table_rows
         FROM information_schema.TABLES 
         WHERE table_schema = ? 
         AND table_name IN ('api_logs', 'api_rate_limits', 'orders', 'order_items', 'tbl_wishlist', 'tbl_product', 'tbl_customer')
         ORDER BY (data_length + index_length) DESC",
        [DB_NAME]
    );
    
    // Get system info
    $system_info = [
        'php_version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'disk_free_space' => round(disk_free_space('.') / 1024 / 1024 / 1024, 2) . ' GB'
    ];
    
    // Create report
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'cleanup_results' => [
            'deleted_logs' => $deleted_logs,
            'deleted_rate_limits' => $deleted_rate_limits,
            'cleaned_sessions' => $cleaned_sessions
        ],
        'api_stats' => $stats,
        'database_stats' => $db_stats,
        'system_info' => $system_info
    ];
    
    // Save report
    $report_file = __DIR__ . '/reports/maintenance_' . date('Y-m-d_H-i-s') . '.json';
    $report_dir = dirname($report_file);
    
    if (!is_dir($report_dir)) {
        mkdir($report_dir, 0755, true);
    }
    
    file_put_contents($report_file, json_encode($report, JSON_PRETTY_PRINT));
    echo "Maintenance report saved to: {$report_file}\n";
    
    // Display summary
    echo "\n=== MAINTENANCE SUMMARY ===\n";
    echo "Cleanup completed successfully!\n";
    echo "- API logs cleaned: {$deleted_logs} entries\n";
    echo "- Rate limits cleaned: {$deleted_rate_limits} entries\n";
    echo "- Sessions cleaned: {$cleaned_sessions} files\n";
    
    if ($stats) {
        echo "- Total API requests (7 days): {$stats['total_requests']}\n";
        echo "- Average response time: {$stats['avg_response_time']}ms\n";
    }
    
    echo "\nMaintenance completed at: " . date('Y-m-d H:i:s') . "\n";
    
    // Log maintenance completion
    Logger::logInfo('API maintenance cleanup completed', [
        'deleted_logs' => $deleted_logs,
        'deleted_rate_limits' => $deleted_rate_limits,
        'cleaned_sessions' => $cleaned_sessions
    ]);
    
} catch (Exception $e) {
    echo "Error during maintenance: " . $e->getMessage() . "\n";
    Logger::logError('API maintenance failed', ['error' => $e->getMessage()]);
    exit(1);
}

echo "\nDone!\n";
?>
