<?php
/**
 * Cleanup script for expired remember tokens
 * This script can be run manually or via cron job to clean up expired tokens
 */

// Include database connection
include("../admin/inc/config.php");
include("session_config.php");

// Set content type
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token Cleanup - SMART LIFE</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
        }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Remember Token Cleanup</h1>
        
        <?php
        try {
            // Check if table exists
            $stmt = $pdo->prepare("SHOW TABLES LIKE 'tbl_remember_tokens'");
            $stmt->execute();
            $table_exists = $stmt->fetch();
            
            if (!$table_exists) {
                echo "<div class='status error'>❌ Remember tokens table does not exist</div>";
                echo "<p><a href='setup_remember_tokens.php' class='btn'>Run Setup Script</a></p>";
                exit;
            }
            
            // Get current token counts
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_remember_tokens WHERE expires_at > NOW()");
            $stmt->execute();
            $active_count = $stmt->fetch()['count'];
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_remember_tokens WHERE expires_at <= NOW()");
            $stmt->execute();
            $expired_count = $stmt->fetch()['count'];
            
            echo "<div class='status info'>";
            echo "<strong>Current Status:</strong><br>";
            echo "Active tokens: $active_count<br>";
            echo "Expired tokens: $expired_count";
            echo "</div>";
            
            // Perform cleanup if requested
            if (isset($_GET['action']) && $_GET['action'] === 'cleanup') {
                if ($expired_count > 0) {
                    $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE expires_at <= NOW()");
                    $stmt->execute();
                    $deleted = $stmt->rowCount();
                    
                    echo "<div class='status success'>✅ Successfully deleted $deleted expired tokens</div>";
                    
                    // Update counts
                    $expired_count = 0;
                } else {
                    echo "<div class='status info'>ℹ️ No expired tokens to clean up</div>";
                }
            }
            
            // Show cleanup button if there are expired tokens
            if ($expired_count > 0) {
                echo "<p><a href='?action=cleanup' class='btn btn-danger'>Clean Up $expired_count Expired Tokens</a></p>";
            } else {
                echo "<div class='status success'>✅ No cleanup needed - all tokens are valid</div>";
            }
            
            // Show detailed token information
            echo "<h2>📊 Token Details</h2>";
            
            $stmt = $pdo->prepare("
                SELECT 
                    rt.customer_id,
                    c.cust_fname,
                    c.cust_lname,
                    c.cust_email,
                    rt.created_at,
                    rt.expires_at,
                    CASE 
                        WHEN rt.expires_at > NOW() THEN 'Active'
                        ELSE 'Expired'
                    END as status
                FROM tbl_remember_tokens rt
                LEFT JOIN tbl_customer c ON rt.customer_id = c.cust_id
                ORDER BY rt.created_at DESC
                LIMIT 20
            ");
            $stmt->execute();
            $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($tokens) > 0) {
                echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
                echo "<tr style='background: #f8f9fa;'>";
                echo "<th style='padding: 10px; border: 1px solid #ddd;'>Customer</th>";
                echo "<th style='padding: 10px; border: 1px solid #ddd;'>Email</th>";
                echo "<th style='padding: 10px; border: 1px solid #ddd;'>Created</th>";
                echo "<th style='padding: 10px; border: 1px solid #ddd;'>Expires</th>";
                echo "<th style='padding: 10px; border: 1px solid #ddd;'>Status</th>";
                echo "</tr>";
                
                foreach ($tokens as $token) {
                    $status_class = $token['status'] === 'Active' ? 'success' : 'error';
                    echo "<tr>";
                    echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . 
                         htmlspecialchars($token['cust_fname'] . ' ' . $token['cust_lname']) . "</td>";
                    echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . 
                         htmlspecialchars($token['cust_email']) . "</td>";
                    echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . 
                         htmlspecialchars($token['created_at']) . "</td>";
                    echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . 
                         htmlspecialchars($token['expires_at']) . "</td>";
                    echo "<td style='padding: 10px; border: 1px solid #ddd;'>";
                    echo "<span class='status $status_class' style='padding: 5px 10px; font-size: 12px;'>";
                    echo $token['status'];
                    echo "</span></td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                if (count($tokens) === 20) {
                    echo "<p><em>Showing latest 20 tokens. There may be more in the database.</em></p>";
                }
            } else {
                echo "<div class='status info'>ℹ️ No remember tokens found in database</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div class='status error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ?>
        
        <h2>🔧 Maintenance Options</h2>
        <div class="info status">
            <strong>Automatic Cleanup:</strong><br>
            If you have MySQL events enabled, expired tokens are automatically cleaned up daily.
            You can also set up a cron job to run this script periodically.
        </div>
        
        <div class="info status">
            <strong>Cron Job Example:</strong><br>
            <code>0 2 * * * /usr/bin/php /path/to/your/site/ecom/cleanup_expired_tokens.php?action=cleanup</code><br>
            <em>This runs cleanup daily at 2 AM</em>
        </div>
        
        <p>
            <a href="test_persistent_login.php" class="btn">Test Page</a>
            <a href="login.php" class="btn">Login</a>
            <a href="?refresh=1" class="btn">Refresh</a>
        </p>
    </div>
</body>
</html>
