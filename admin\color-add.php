<?php require_once('header.php'); ?>

<?php
$error_message = '';
$success_message = '';

if (isset($_POST['form1'])) {
    $valid = 1;

    // Validate color name
    if (empty($_POST['color_name'])) {
        $valid = 0;
        $error_message .= "Color Name cannot be empty<br>";
    }

    // Validate color code
    if (empty($_POST['color_code'])) {
        $valid = 0;
        $error_message .= "Color Code cannot be empty<br>";
    } elseif (!preg_match('/^#[0-9A-Fa-f]{6}$/', $_POST['color_code'])) {
        $valid = 0;
        $error_message .= "Color Code must be a valid hex (#RRGGBB)<br>";
    }

    if ($valid == 1) {
        // Duplicate check for color name
        $statement = $pdo->prepare("SELECT * FROM tbl_color WHERE color_name = ?");
        $statement->execute([$_POST['color_name']]);
        if ($statement->rowCount()) {
            $valid = 0;
            $error_message .= "Color Name already exists<br>";
        }
    }

    if ($valid == 1) {
        // Insert color name and color code into database
        $statement = $pdo->prepare("INSERT INTO tbl_color (color_name, color_code) VALUES (?, ?)");
        $statement->execute([$_POST['color_name'], $_POST['color_code']]);
        $success_message = 'Color is added successfully.';
    }
}
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Add Color</h1>
    </div>
    <div class="content-header-right">
        <a href="color.php" class="btn btn-primary btn-sm">View All</a>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <?php if ($error_message) : ?>
                <div class="callout callout-danger">
                    <p><?php echo $error_message; ?></p>
                </div>
            <?php endif; ?>

            <?php if ($success_message) : ?>
                <div class="callout callout-success">
                    <p><?php echo $success_message; ?></p>
                </div>
            <?php endif; ?>

            <form class="form-horizontal" action="" method="post">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Color Name <span>*</span></label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="color_name" name="color_name">
                                <small id="color_name_status" class="text-muted" style="display: block; margin-top: 5px;"></small>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Color Code <span>*</span></label>
                            <div class="col-sm-2">
                                <input type="color" class="form-control" id="color_picker" value="#000000" style="width:60px; padding:0;">
                            </div>
                            <div class="col-sm-2">
                                <input type="text" class="form-control" id="color_code" name="color_code" value="#000000" placeholder="#RRGGBB">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"></label>
                            <div class="col-sm-6">
                                <button type="submit" class="btn btn-success" name="form1">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>
            <script>
                // hex->name mapping (fallback)
                var colorMap = {
                    "#000000": "Black",
                    "#ffffff": "White",
                    "#ff0000": "Red",
                    "#00ff00": "Lime",
                    "#0000ff": "Blue",
                    "#ffff00": "Yellow",
                    "#00ffff": "Cyan",
                    "#ff00ff": "Magenta",
                    "#c0c0c0": "Silver",
                    "#808080": "Gray",
                    "#800000": "Maroon",
                    "#808000": "Olive",
                    "#008000": "Green",
                    "#800080": "Purple",
                    "#008080": "Teal",
                    "#000080": "Navy",
                    "#ffa500": "Orange",
                    "#a52a2a": "Brown",
                    "#ff4500": "Orange Red",
                    "#da70d6": "Orchid",
                    "#ff6347": "Tomato",
                    "#4169e1": "Royal Blue",
                    "#8b4513": "Saddle Brown",
                    "#fa8072": "Salmon",
                    "#f4a460": "Sandy Brown",
                    "#2e8b57": "Sea Green",
                    "#a0522d": "Sienna",
                    "#87ceeb": "Sky Blue",
                    "#6a5acd": "Slate Blue",
                    "#708090": "Slate Gray",
                    "#fffa00": "Lemon Yellow",
                    "#00ff7f": "Spring Green",
                    "#4682b4": "Steel Blue",
                    "#d2b48c": "Tan",
                    "#d8bfd8": "Thistle",
                    "#ff7f50": "Coral",
                    "#40e0d0": "Turquoise",
                    "#ee82ee": "Violet",
                    "#f5deb3": "Wheat",
                    "#9acd32": "Yellow Green",
                    "#9932cc": "Dark Orchid",
                    "#8b0000": "Dark Red",
                    "#e9967a": "Dark Salmon",
                    "#8fbc8f": "Dark Sea Green",
                    "#483d8b": "Dark Slate Blue",
                    "#2f4f4f": "Dark Slate Gray",
                    "#00ced1": "Dark Turquoise",
                    "#9400d3": "Dark Violet",
                    "#ff1493": "Deep Pink",
                    "#00bfff": "Deep Sky Blue",
                    "#696969": "Dim Gray",
                    "#1e90ff": "Dodger Blue",
                    "#b22222": "Fire Brick",
                    "#228b22": "Forest Green",
                    "#ffd700": "Gold",
                    "#ff69b4": "Hot Pink",
                    "#dea193": "Rose gold",
                };

                var picker = document.getElementById('color_picker');
                var hexInput = document.getElementById('color_code');
                var nameInput = document.getElementById('color_name');
                var statusElement = document.getElementById('color_name_status');
                var colorNameCache = {}; // Cache for API responses

                // Function to fetch color name from The Color API
                async function fetchColorName(hex) {
                    // Remove # if present and ensure lowercase
                    hex = hex.replace('#', '').toLowerCase();

                    // Show loading status
                    statusElement.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Fetching color name...';
                    statusElement.style.color = '#3498db'; // Blue color for loading

                    try {
                        // Check cache first
                        if (colorNameCache['#' + hex]) {
                            statusElement.innerHTML = '<i class="fa fa-check"></i> Color name found!';
                            statusElement.style.color = '#27ae60'; // Green color for success
                            return colorNameCache['#' + hex];
                        }

                        // Make API request
                        const response = await fetch(`https://www.thecolorapi.com/id?hex=${hex}&format=json`);

                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }

                        const data = await response.json();

                        // Get the color name from the response
                        if (data && data.name && data.name.value) {
                            // Cache the result
                            colorNameCache['#' + hex] = data.name.value;
                            statusElement.innerHTML = '<i class="fa fa-check"></i> Color name found!';
                            statusElement.style.color = '#27ae60'; // Green color for success
                            return data.name.value;
                        } else {
                            throw new Error('Color name not found in API response');
                        }
                    } catch (error) {
                        console.error('Error fetching color name:', error);
                        statusElement.innerHTML = '<i class="fa fa-info-circle"></i> Using fallback color name';
                        statusElement.style.color = '#f39c12'; // Orange color for fallback
                        // Return fallback name from local map or empty string
                        return colorMap['#' + hex] || '';
                    }
                }

                // Function to set the color name (with API lookup)
                async function setName(hex) {
                    hex = hex.toLowerCase();

                    // First try the local map for immediate feedback
                    const localName = colorMap[hex] || '';
                    if (localName) {
                        nameInput.value = localName;
                        statusElement.innerHTML = '<i class="fa fa-database"></i> Using local color name';
                        statusElement.style.color = '#f39c12'; // Orange color for local
                    }

                    // Then try to get a more accurate name from the API
                    try {
                        const apiName = await fetchColorName(hex);
                        if (apiName) {
                            nameInput.value = apiName;
                        }
                    } catch (error) {
                        console.error('Error in setName:', error);
                        // Keep the local name if API fails
                    }
                }

                picker.addEventListener('change', function() {
                    hexInput.value = this.value;
                    setName(this.value);
                });

                hexInput.addEventListener('input', function() {
                    var v = this.value.startsWith('#') ? this.value : '#' + this.value;
                    if (/^#[0-9A-Fa-f]{6}$/.test(v)) {
                        picker.value = v;
                        setName(v);
                    } else {
                        nameInput.value = '';
                        statusElement.innerHTML = '';
                    }
                });

                // Initialize
                setName(hexInput.value);
            </script>
        </div>
    </div>
</section>

<?php require_once('footer.php'); ?>
