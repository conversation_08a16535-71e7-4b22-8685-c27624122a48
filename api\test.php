<?php
/**
 * API Test Script
 * Simple test to verify API functionality
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>Ecommerce API Test</h1>";

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tbl_product");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✓ Database connected successfully. Found {$result['count']} products.</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test JWT functionality
echo "<h2>JWT Test</h2>";
try {
    require_once __DIR__ . '/utils/JWT.php';

    $payload = [
        'user_id' => 1,
        'email' => '<EMAIL>',
        'exp' => time() + 3600
    ];

    $token = SimpleJWT::encode($payload, JWT_SECRET);
    $decoded = SimpleJWT::decode($token, JWT_SECRET);

    if ($decoded['user_id'] == 1 && $decoded['email'] == '<EMAIL>') {
        echo "<p style='color: green;'>✓ JWT encoding/decoding working correctly.</p>";
    } else {
        echo "<p style='color: red;'>✗ JWT test failed - data mismatch.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ JWT test failed: " . $e->getMessage() . "</p>";
}

// Test API endpoints
echo "<h2>API Endpoints Test</h2>";

// Detect the correct base URL based on the current request
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$script_dir = dirname($_SERVER['SCRIPT_NAME']);
$base_url = $protocol . '://' . $host . $script_dir . '/v1';

$endpoints = [
    'GET /' => $base_url . '/',
    'GET /products' => $base_url . '/products?limit=5',
    'GET /categories' => $base_url . '/categories',
    'GET /settings/app' => $base_url . '/settings/app',
    'GET /shipping/countries' => $base_url . '/shipping/countries'
];

foreach ($endpoints as $name => $url) {
    echo "<h3>Testing: {$name}</h3>";

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Content-Type: application/json',
            'timeout' => 10
        ]
    ]);

    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        echo "<p style='color: red;'>✗ Failed to connect to {$url}</p>";
        $error = error_get_last();
        if ($error) {
            echo "<p style='color: red;'>Error: " . htmlspecialchars($error['message']) . "</p>";
        }
        continue;
    }

    // Check if response is empty
    if (empty($response)) {
        echo "<p style='color: red;'>✗ {$name} - Empty response</p>";
        continue;
    }

    $data = json_decode($response, true);
    $json_error = json_last_error();

    if ($json_error !== JSON_ERROR_NONE) {
        echo "<p style='color: red;'>✗ {$name} - Invalid JSON: " . json_last_error_msg() . "</p>";
        echo "<p style='color: red;'>Response preview: " . htmlspecialchars(substr($response, 0, 100)) . "...</p>";
        continue;
    }

    if (!is_array($data)) {
        echo "<p style='color: red;'>✗ {$name} - Response is not an array</p>";
        continue;
    }

    if (isset($data['status'])) {
        if ($data['status'] === 'success') {
            $message = $data['message'] ?? 'Success';
            echo "<p style='color: green;'>✓ {$name} - Success: {$message}</p>";
        } else {
            $message = $data['message'] ?? 'Error';
            echo "<p style='color: orange;'>⚠ {$name} - Error: {$message}</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ {$name} - Missing 'status' field in response</p>";
        echo "<p style='color: red;'>Available fields: " . implode(', ', array_keys($data)) . "</p>";
    }
}

// Test file permissions
echo "<h2>File Permissions Test</h2>";

$directories = [
    __DIR__ . '/storage',
    __DIR__ . '/storage/rate_limits'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p style='color: green;'>✓ {$dir} is writable</p>";
        } else {
            echo "<p style='color: red;'>✗ {$dir} is not writable</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ {$dir} does not exist</p>";
    }
}

// Configuration summary
echo "<h2>Configuration Summary</h2>";
echo "<ul>";
echo "<li><strong>API Version:</strong> " . API_VERSION . "</li>";
echo "<li><strong>Database:</strong> " . DB_NAME . "</li>";
echo "<li><strong>JWT Expiry:</strong> " . (JWT_EXPIRY / 3600) . " hours</li>";
echo "<li><strong>Default Currency:</strong> " . DEFAULT_CURRENCY . "</li>";
echo "<li><strong>Rate Limit:</strong> " . RATE_LIMIT_REQUESTS . " requests per hour</li>";
echo "<li><strong>Max Page Size:</strong> " . MAX_PAGE_SIZE . "</li>";
echo "</ul>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Visit <a href='{$script_dir}/v1/docs'>{$script_dir}/v1/docs</a> for complete API documentation</li>";
echo "<li>Test authentication endpoints with a REST client like Postman</li>";
echo "<li>Integrate with your Flutter mobile application</li>";
echo "<li>Monitor API usage and performance</li>";
echo "</ol>";

echo "<hr>";
echo "<p><em>API Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
