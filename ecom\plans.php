<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Life Plans & Subscriptions | SMART LIFE</title>
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00c2ff',
                        'primary-dark': '#00a8e0',
                        'primary-light': '#e0f7ff',
                    },
                    fontFamily: {
                        sans: ['Sora', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'spin-slow': 'spin 3s linear infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700;800&display=swap');

        .bg-gradient-radial {
            background-image: radial-gradient(circle at center, rgba(0, 194, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .bg-gradient-mesh {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(5deg); }
            66% { transform: translateY(5px) rotate(-5deg); }
            100% { transform: translateY(0px) rotate(0deg); }
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(0, 194, 255, 0.3); }
            to { box-shadow: 0 0 30px rgba(0, 194, 255, 0.6); }
        }

        .coming-soon-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .feature-icon {
            background: linear-gradient(135deg, #00c2ff 0%, #0099cc 100%);
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }

        .notification-badge {
            animation: pulse 2s infinite;
        }

        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00c2ff;
            border-radius: 50%;
            animation: sparkle 2s linear infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }

        .text-gradient {
            background: linear-gradient(135deg, #00c2ff 0%, #0066ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-sans overflow-x-hidden">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
                <a href="all_products.php" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>
                <a href="faq.php" class="hover:text-blue-600 transition">FAQs</a>
                <a href="plans.php" class="text-primary font-medium">Plans</a>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-[#00c2ff]">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
                <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
                <a href="all_products.php" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
                <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>
                <a href="faq.php" class="block text-gray-700 hover:text-[#00c2ff] transition">FAQs</a>
                <a href="plans.php" class="block text-[#00c2ff] font-medium">Plans</a>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <!-- Hero Section -->
    <section class="pt-32 pb-20 relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-32 h-32 bg-primary-light rounded-full filter blur-3xl opacity-30 animate-float"></div>
            <div class="absolute top-40 right-20 w-24 h-24 bg-purple-200 rounded-full filter blur-2xl opacity-40 animate-float" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-blue-100 rounded-full filter blur-3xl opacity-25 animate-float" style="animation-delay: 2s;"></div>
        </div>

        <!-- Sparkle Effects -->
        <div class="sparkle" style="top: 20%; left: 15%; animation-delay: 0s;"></div>
        <div class="sparkle" style="top: 30%; right: 20%; animation-delay: 0.5s;"></div>
        <div class="sparkle" style="bottom: 40%; left: 10%; animation-delay: 1s;"></div>
        <div class="sparkle" style="top: 60%; right: 15%; animation-delay: 1.5s;"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-4xl mx-auto">
                <!-- Coming Soon Badge -->
                <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-blue-600 text-white rounded-full text-sm font-semibold mb-8 notification-badge">
                    <i class="fas fa-rocket mr-2"></i>
                    LAUNCHING SOON
                    <span class="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">NEW</span>
                </div>

                <h1 class="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                    <span class="text-gradient">SMART LIFE</span><br>
                    <span class="text-gray-800">PLANS &</span><br>
                    <span class="text-gradient">SUBSCRIPTIONS</span>
                </h1>

                <div class="text-2xl md:text-3xl font-bold text-primary mb-8 animate-pulse">
                    COMING SOON . . .
                </div>

                <p class="text-xl md:text-2xl text-gray-600 mb-12 leading-relaxed max-w-3xl mx-auto">
                    ENJOY PEACE OF MIND WITH OUR UPCOMING <span class="font-bold text-primary">SMART LIFE CARE PLANS</span>—DESIGNED TO KEEP YOUR SMART HOME RUNNING SMOOTHLY.
                </p>

                <!-- Feature Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                    <div class="coming-soon-card rounded-2xl p-6 text-center transform hover:scale-105 transition-all duration-300">
                        <div class="feature-icon w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-tools text-2xl text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2">🔧 PRIORITY SUPPORT</h3>
                        <p class="text-sm text-gray-600">AFTER-SALES SUPPORT + WARRANTY</p>
                    </div>

                    <div class="coming-soon-card rounded-2xl p-6 text-center transform hover:scale-105 transition-all duration-300" style="animation-delay: 0.1s;">
                        <div class="feature-icon w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-calendar-check text-2xl text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2">🛠 MAINTENANCE</h3>
                        <p class="text-sm text-gray-600">SCHEDULED MAINTENANCE VISITS</p>
                    </div>

                    <div class="coming-soon-card rounded-2xl p-6 text-center transform hover:scale-105 transition-all duration-300" style="animation-delay: 0.2s;">
                        <div class="feature-icon w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-headset text-2xl text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2">📱 TROUBLESHOOTING</h3>
                        <p class="text-sm text-gray-600">REMOTE & PHYSICAL + APP ASSISTANCE</p>
                    </div>

                    <div class="coming-soon-card rounded-2xl p-6 text-center transform hover:scale-105 transition-all duration-300" style="animation-delay: 0.3s;">
                        <div class="feature-icon w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-sync-alt text-2xl text-white"></i>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2">🔁 UPDATES</h3>
                        <p class="text-sm text-gray-600">FIRMWARE UPDATES & ENHANCEMENTS</p>
                    </div>
                </div>

                <!-- Flexible Plans Section -->
                <div class="bg-gradient-to-r from-primary to-blue-600 rounded-3xl p-8 md:p-12 text-white mb-12 relative overflow-hidden">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
                    <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -ml-12 -mb-12"></div>
                    
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">FLEXIBLE PLANS FOR EVERY SPACE</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                        <div class="bg-white bg-opacity-10 rounded-xl p-6 backdrop-filter backdrop-blur-sm">
                            <i class="fas fa-home text-4xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-2">HOMES</h3>
                            <p class="text-sm opacity-90">Perfect for residential smart homes</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded-xl p-6 backdrop-filter backdrop-blur-sm">
                            <i class="fas fa-building text-4xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-2">OFFICES</h3>
                            <p class="text-sm opacity-90">Ideal for smart office environments</p>
                        </div>
                        <div class="bg-white bg-opacity-10 rounded-xl p-6 backdrop-filter backdrop-blur-sm">
                            <i class="fas fa-industry text-4xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-2">COMMERCIAL</h3>
                            <p class="text-sm opacity-90">Comprehensive commercial solutions</p>
                        </div>
                    </div>
                    <div class="text-center mt-8">
                        <p class="text-2xl font-bold">LAUNCHING SOON</p>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-wrap justify-center gap-6">
                    <button class="px-8 py-4 bg-primary hover:bg-primary-dark text-white font-bold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 animate-glow">
                        <i class="fas fa-bell mr-2"></i>
                        NOTIFY ME WHEN AVAILABLE
                    </button>
                    <a href="all_products.php" class="px-8 py-4 bg-white text-primary border-2 border-primary rounded-full font-bold shadow-lg hover:bg-primary-light transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-shopping-bag mr-2"></i>
                        SHOP PRODUCTS NOW
                    </a>
                </div>
            </div>
        </div>

        <!-- Floating Icons -->
        <div class="absolute right-10 top-1/4 text-6xl text-primary opacity-20 animate-float">
            <i class="fas fa-shield-alt"></i>
        </div>
        <div class="absolute left-10 top-1/2 text-5xl text-blue-500 opacity-20 animate-float" style="animation-delay: 1s;">
            <i class="fas fa-cog"></i>
        </div>
        <div class="absolute right-1/4 bottom-20 text-4xl text-purple-500 opacity-20 animate-float" style="animation-delay: 2s;">
            <i class="fas fa-star"></i>
        </div>
    </section>

    <!-- Newsletter Signup Section -->
    <section class="py-16 bg-gray-100">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Be the First to Know</h2>
                <p class="text-lg text-gray-600 mb-8">Get notified when our Smart Life Care Plans launch and receive exclusive early-bird pricing.</p>
                
                <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email address" 
                           class="flex-1 px-6 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button type="submit" 
                            class="px-8 py-3 bg-primary hover:bg-primary-dark text-white font-semibold rounded-full transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-paper-plane mr-2"></i>
                        NOTIFY ME
                    </button>
                </form>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function openMobileMenu() {
                mobileMenu.classList.remove('translate-x-full');
                mobileMenuBackdrop.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            function closeMobileMenuFunc() {
                mobileMenu.classList.add('translate-x-full');
                mobileMenuBackdrop.classList.add('hidden');
                document.body.style.overflow = '';
            }

            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', openMobileMenu);
            }

            if (closeMobileMenu) {
                closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
            }

            if (mobileMenuBackdrop) {
                mobileMenuBackdrop.addEventListener('click', closeMobileMenuFunc);
            }

            // Notification button functionality
            const notifyButton = document.querySelector('button[class*="animate-glow"]');
            if (notifyButton) {
                notifyButton.addEventListener('click', function() {
                    alert('Thank you for your interest! We\'ll notify you as soon as Smart Life Care Plans are available.');
                });
            }

            // Newsletter form functionality
            const newsletterForm = document.querySelector('form');
            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const email = this.querySelector('input[type="email"]').value;
                    if (email) {
                        alert('Thank you! We\'ll notify you at ' + email + ' when Smart Life Care Plans launch.');
                        this.reset();
                    }
                });
            }
        });
    </script>

</body>
</html>
