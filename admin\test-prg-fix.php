<?php
// Test script to demonstrate the Post-Redirect-Get fix
require_once('header.php');

// Function to redirect with message parameters (Post-Redirect-Get pattern)
function redirectWithMessage($type, $message) {
    $url = 'test-prg-fix.php?' . $type . '=' . urlencode($message);
    header('Location: ' . $url);
    exit();
}

// Get messages from URL parameters
$success_message = isset($_GET['success']) ? $_GET['success'] : '';
$error_message = isset($_GET['error']) ? $_GET['error'] : '';

// Handle form submission
if(isset($_POST['test_action'])) {
    $action = $_POST['test_action'];
    
    if($action === 'success') {
        redirectWithMessage('success', 'Test operation completed successfully!');
    } else {
        redirectWithMessage('error', 'Test operation failed!');
    }
}
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Post-Redirect-Get Pattern Test</h1>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            
            <!-- Success Messages -->
            <?php if($success_message): ?>
            <div class="callout callout-success">
                <h4><i class="fa fa-check-circle"></i> Success</h4>
                <p><?php echo htmlspecialchars($success_message); ?></p>
                <p><small><strong>Notice:</strong> You can refresh this page without getting a form resubmission warning!</small></p>
            </div>
            <?php endif; ?>

            <!-- Error Messages -->
            <?php if($error_message): ?>
            <div class="callout callout-danger">
                <h4><i class="fa fa-exclamation-triangle"></i> Error</h4>
                <p><?php echo htmlspecialchars($error_message); ?></p>
                <p><small><strong>Notice:</strong> You can refresh this page without getting a form resubmission warning!</small></p>
            </div>
            <?php endif; ?>

            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">Test Form Submission</h3>
                </div>
                <div class="box-body">
                    <p>This test demonstrates the Post-Redirect-Get pattern fix:</p>
                    <ol>
                        <li>Submit a form using the buttons below</li>
                        <li>After submission, you'll see a success or error message</li>
                        <li>Try refreshing the page - you won't get a "form resubmission" warning</li>
                        <li>The URL will contain the message parameters instead of POST data</li>
                    </ol>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <form method="post" action="">
                                <input type="hidden" name="test_action" value="success">
                                <button type="submit" class="btn btn-success btn-lg btn-block">
                                    <i class="fa fa-check"></i> Test Success Response
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="post" action="">
                                <input type="hidden" name="test_action" value="error">
                                <button type="submit" class="btn btn-danger btn-lg btn-block">
                                    <i class="fa fa-times"></i> Test Error Response
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="alert alert-info">
                        <h4><i class="fa fa-info-circle"></i> How the Fix Works</h4>
                        <p><strong>Before (Problem):</strong></p>
                        <ul>
                            <li>Form submits via POST to same page</li>
                            <li>Page processes data and shows result</li>
                            <li>URL still contains POST data</li>
                            <li>Refreshing triggers "form resubmission" warning</li>
                        </ul>
                        
                        <p><strong>After (Fixed with PRG Pattern):</strong></p>
                        <ul>
                            <li>Form submits via POST to same page</li>
                            <li>Page processes data</li>
                            <li>Page redirects to GET request with message parameters</li>
                            <li>URL is clean with only GET parameters</li>
                            <li>Refreshing is safe - no form resubmission warning</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once('footer.php'); ?>
