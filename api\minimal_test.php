<?php
/**
 * Minimal API Test
 * Stripped down version to test basic functionality
 */

// Include only essential files
require_once __DIR__ . '/config/config.php';

// Set JSON header
header('Content-Type: application/json');

// Get request path
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Simple path detection
if (strpos($path, '/ecom/api/v1') !== false) {
    $clean_path = str_replace('/ecom/api/v1', '', $path);
} else {
    $clean_path = $path;
}

$clean_path = trim($clean_path, '/');
$endpoint = explode('/', $clean_path)[0] ?? '';

// Simple response based on endpoint
switch ($endpoint) {
    case '':
        // API root
        $response = [
            'status' => 'success',
            'message' => 'Welcome to Ecommerce API',
            'timestamp' => date('c'),
            'api_version' => API_VERSION,
            'data' => [
                'name' => 'Ecommerce API',
                'version' => API_VERSION,
                'endpoints' => [
                    'auth', 'products', 'categories', 'cart', 
                    'orders', 'users', 'search', 'wishlist', 
                    'shipping', 'settings', 'admin'
                ]
            ]
        ];
        break;
        
    case 'settings':
        // Simple settings response
        $response = [
            'status' => 'success',
            'message' => 'App settings retrieved successfully',
            'timestamp' => date('c'),
            'api_version' => API_VERSION,
            'data' => [
                'name' => 'Ecommerce Store',
                'currency' => DEFAULT_CURRENCY,
                'installation_fee' => (int)DEFAULT_INSTALLATION_FEE,
                'version' => API_VERSION
            ]
        ];
        break;
        
    case 'products':
        // Simple products response
        try {
            $limit = $_GET['limit'] ?? 10;
            $products = $pdo->query("SELECT p_id, p_name, p_old_price, p_current_price FROM tbl_product WHERE p_is_active = 1 LIMIT " . (int)$limit)->fetchAll();
            
            $response = [
                'status' => 'success',
                'message' => 'Products retrieved successfully',
                'timestamp' => date('c'),
                'api_version' => API_VERSION,
                'data' => [
                    'products' => $products,
                    'total' => count($products)
                ]
            ];
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage(),
                'timestamp' => date('c'),
                'api_version' => API_VERSION
            ];
        }
        break;
        
    case 'categories':
        // Simple categories response
        try {
            $categories = $pdo->query("SELECT tcat_id, tcat_name FROM tbl_top_category WHERE show_on_menu = 1")->fetchAll();
            
            $response = [
                'status' => 'success',
                'message' => 'Categories retrieved successfully',
                'timestamp' => date('c'),
                'api_version' => API_VERSION,
                'data' => [
                    'categories' => $categories,
                    'total' => count($categories)
                ]
            ];
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage(),
                'timestamp' => date('c'),
                'api_version' => API_VERSION
            ];
        }
        break;
        
    default:
        $response = [
            'status' => 'error',
            'message' => 'Endpoint not found: ' . $endpoint,
            'timestamp' => date('c'),
            'api_version' => API_VERSION
        ];
        http_response_code(404);
}

// Output JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
