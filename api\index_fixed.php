<?php
/**
 * Fixed API Router
 * Simplified and more robust routing for the API
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

// Include logger
require_once __DIR__ . '/utils/Logger.php';

// Initialize logger and start timing
Logger::startTimer();

// Apply CORS middleware
CORSMiddleware::handle();

// Apply rate limiting
RateLimitMiddleware::check();

// Get request method and input
$method = $_SERVER['REQUEST_METHOD'];
$input = [];

// Get input data based on method
if ($method === 'POST' || $method === 'PUT') {
    $content_type = $_SERVER['CONTENT_TYPE'] ?? '';
    if (strpos($content_type, 'application/json') !== false) {
        $json = file_get_contents('php://input');
        $input = json_decode($json, true) ?: [];
    } else {
        $input = $_POST;
    }
}

// Parse the request path
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Debug: Show what we're working with
error_log("API Debug - Request URI: " . $request_uri);
error_log("API Debug - Parsed Path: " . $path);

// Remove common base paths
$base_patterns = [
    '/ecom/api/v1',
    '/api/v1',
    dirname($_SERVER['SCRIPT_NAME']) . '/v1'
];

$clean_path = $path;
foreach ($base_patterns as $pattern) {
    if (strpos($path, $pattern) === 0) {
        $clean_path = substr($path, strlen($pattern));
        break;
    }
}

$clean_path = trim($clean_path, '/');
error_log("API Debug - Clean Path: " . $clean_path);

// Split into segments
$segments = $clean_path ? explode('/', $clean_path) : [];
$endpoint = $segments[0] ?? '';

error_log("API Debug - Endpoint: " . $endpoint);
error_log("API Debug - Segments: " . print_r($segments, true));

try {
    // Handle different endpoints
    switch ($endpoint) {
        case '':
            // API root
            Response::success([
                'name' => 'Ecommerce API',
                'version' => API_VERSION,
                'timestamp' => date('c'),
                'endpoints' => [
                    'auth' => 'Authentication endpoints',
                    'products' => 'Product catalog',
                    'categories' => 'Product categories',
                    'cart' => 'Shopping cart',
                    'orders' => 'Order management',
                    'users' => 'User management',
                    'search' => 'Product search',
                    'wishlist' => 'User wishlist',
                    'shipping' => 'Shipping information',
                    'settings' => 'Application settings',
                    'admin' => 'Admin operations'
                ],
                'documentation' => dirname($_SERVER['SCRIPT_NAME']) . '/v1/docs'
            ], 'Welcome to Ecommerce API');
            break;

        case 'auth':
            include __DIR__ . '/endpoints/auth.php';
            break;

        case 'products':
            include __DIR__ . '/endpoints/products.php';
            break;

        case 'categories':
            include __DIR__ . '/endpoints/categories.php';
            break;

        case 'cart':
            include __DIR__ . '/endpoints/cart.php';
            break;

        case 'orders':
            include __DIR__ . '/endpoints/orders.php';
            break;

        case 'users':
            include __DIR__ . '/endpoints/users.php';
            break;

        case 'search':
            include __DIR__ . '/endpoints/search.php';
            break;

        case 'wishlist':
            include __DIR__ . '/endpoints/wishlist.php';
            break;

        case 'shipping':
            include __DIR__ . '/endpoints/shipping.php';
            break;

        case 'settings':
            include __DIR__ . '/endpoints/settings.php';
            break;

        case 'admin':
            include __DIR__ . '/endpoints/admin.php';
            break;

        case 'docs':
            // Redirect to documentation
            header('Location: ' . dirname($_SERVER['SCRIPT_NAME']) . '/docs/');
            exit;

        default:
            Response::notFound('Endpoint not found: ' . $endpoint);
    }

} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    error_log("API Error Stack: " . $e->getTraceAsString());
    
    // Log the error
    $user = Auth::getCurrentUser();
    Logger::logRequest($clean_path, $method, $user['user_id'] ?? null, $input, 500);
    
    Response::serverError('An unexpected error occurred: ' . $e->getMessage());
}

// Log successful request
$user = Auth::getCurrentUser();
Logger::logRequest($clean_path, $method, $user['user_id'] ?? null, $input, http_response_code());
?>
