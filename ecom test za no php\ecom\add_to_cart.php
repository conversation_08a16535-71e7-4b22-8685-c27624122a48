<?php
ob_start(); // Start output buffering BEFORE any output
session_start();

// --- IMPORTANT: Set JSON header EARLY ---
header('Content-Type: application/json');

// --- IMPORTANT: Suppress direct error output for AJAX ---
error_reporting(0); // Suppress default PHP errors
ini_set('display_errors', 0); // Ensure errors aren't displayed

// Include necessary files (ensure path is correct relative to this script)
require_once("../admin/inc/config.php");
// require_once("../admin/inc/functions.php"); // Include if needed

// Initialize response array
$response = [
    'status' => 'error',
    'message' => 'An unknown error occurred.',
    'cart_count' => isset($_SESSION['cart']) ? count($_SESSION['cart']) : 0, // Initial count
    'added_item' => null
];

// --- Input Validation ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    ob_end_clean(); // Clean buffer
    echo json_encode($response);
    exit();
}

// Validate Product ID
$product_id = filter_input(INPUT_POST, 'product_id', FILTER_VALIDATE_INT);
if (!$product_id || $product_id <= 0) {
    $response['message'] = 'Invalid Product ID.';
    ob_end_clean();
    echo json_encode($response);
    exit();
}

// Validate Quantity
$quantity = filter_input(INPUT_POST, 'quantity', FILTER_VALIDATE_INT);
if (!$quantity || $quantity <= 0) {
    // Allow quantity update to potentially remove item later if needed, but initial add requires > 0
    $response['message'] = 'Invalid Quantity (must be at least 1).';
     ob_end_clean();
    echo json_encode($response);
    exit();
}

// Get Variation ID (optional) - Treat '0' or empty string as null
$variation_id = filter_input(INPUT_POST, 'variation_id', FILTER_VALIDATE_INT);
if ($variation_id !== null && $variation_id <= 0) {
     $variation_id = null; // Ensure 0 or negative is treated as no variation
}

// >>> NEW: Get Color ID (optional) <<<
$color_id = filter_input(INPUT_POST, 'color_id', FILTER_VALIDATE_INT);
if ($color_id !== null && $color_id <= 0) {
    $color_id = null; // Ensure 0 or negative is treated as no color
}
// >>> END NEW <<<

// Get Installation status
$installation = filter_input(INPUT_POST, 'installation', FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
$installation_selected = ($installation === true);

// --- Database Interaction ---
try {
    $item_price = 0;
    $item_name = '';
    $variation_name = null;
    $item_stock = 0;
    $item_photo_filename = null; // Store only the filename
    $attributes = null; // Initialize attributes
    $selected_color_name = null; // For storing the name based on selected color_id
    $selected_color_code = null; // For storing the code based on selected color_id

    // Fetch base product details first (needed for name, fallback photo, base price/stock)
    $stmt_base = $pdo->prepare("
        SELECT p_name, p_current_price, p_qty, p_featured_photo
        FROM tbl_product
        WHERE p_id = ? AND p_is_active = 1
    ");
    $stmt_base->execute([$product_id]);
    $base_product_details = $stmt_base->fetch(PDO::FETCH_ASSOC);

    if (!$base_product_details) {
        throw new Exception("Product not found or is inactive.");
    }

    // Set defaults from base product
    $item_name = $base_product_details['p_name'];
    $item_price = is_numeric($base_product_details['p_current_price']) ? (float)$base_product_details['p_current_price'] : 0;
    $item_stock = (int)$base_product_details['p_qty'];
    $item_photo_filename = basename($base_product_details['p_featured_photo']); // Base photo filename

    // If a variation is selected, fetch its details and override base values
    if ($variation_id) {
        $stmt_var = $pdo->prepare("
            SELECT pv.variation_price, pv.variation_qty, pv.variation_name, pv.variation_image,
                   pv.variation_size, s.size_name, pv.variation_color as variation_linked_color_id
            FROM tbl_product_variation pv
            LEFT JOIN tbl_size s ON pv.variation_size = s.size_id
            WHERE pv.variation_id = ? AND pv.p_id = ?
        ");
        $stmt_var->execute([$variation_id, $product_id]);
        $variation_details = $stmt_var->fetch(PDO::FETCH_ASSOC);

        if (!$variation_details) {
            throw new Exception("Selected product variation not found.");
        }

        // Override with variation details
        $item_price = is_numeric($variation_details['variation_price']) ? (float)$variation_details['variation_price'] : $item_price; // Use variation price if set
        $item_stock = (int)$variation_details['variation_qty'];
        $variation_name = $variation_details['variation_name'];
        $item_photo_filename = !empty($variation_details['variation_image']) ? basename($variation_details['variation_image']) : $item_photo_filename; // Use variation image if set

        // Prepare attributes from variation (Size, potential linked color)
        $attributes = [
            'color_name' => null, // Will be overridden by explicit color choice below if available
            'color_code' => null,
            'size_name' => $variation_details['size_name'] ?? null
        ];
        $variation_linked_color_id = $variation_details['variation_linked_color_id'] ?? null;


    } else {
         // Base product - no variation name, no size, potentially a selected color
         $variation_name = null;
         $attributes = ['color_name' => null, 'color_code' => null, 'size_name' => null];
         $variation_linked_color_id = null;
    }

    // --- NEW: Fetch details for the EXPLICITLY selected color_id (if provided) ---
    // This takes precedence over any color linked implicitly via variation
    if ($color_id) {
        $stmt_color = $pdo->prepare("SELECT color_name, color_code FROM tbl_color WHERE color_id = ?");
        $stmt_color->execute([$color_id]);
        $color_details = $stmt_color->fetch(PDO::FETCH_ASSOC);
        if ($color_details) {
            $selected_color_name = $color_details['color_name'];
            $selected_color_code = $color_details['color_code'];
            // Update attributes directly
            if ($attributes) { // Check if attributes array exists (it should)
                 $attributes['color_name'] = $selected_color_name;
                 $attributes['color_code'] = $selected_color_code;
            }
        } else {
             error_log("Warning: Selected color_id {$color_id} not found in tbl_color.");
             // Keep $color_id as is, but name/code will be null
        }
    }
    // --- END NEW COLOR FETCH ---


    // --- Initialize Cart Session if not set ---
    if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }

    // --- Create unique key for cart item (using product_id, variation_id, and color_id) ---
    $cart_key = $product_id
              . '-' . ($variation_id ?? '0')
              . '-' . ($color_id ?? '0'); // <<< MODIFIED KEY

    // --- Check Stock Before Adding/Updating ---
    $qty_already_in_cart = $_SESSION['cart'][$cart_key]['quantity'] ?? 0;
    $total_requested_qty = $qty_already_in_cart + $quantity;

    if ($total_requested_qty > $item_stock) {
        $available_to_add = $item_stock - $qty_already_in_cart;
        $message = "Cannot add {$quantity} item(s). Only {$item_stock} total available";
        if ($available_to_add > 0) {
             $message .= " (you can add {$available_to_add} more).";
        } else {
            $message .= ".";
        }
         if ($qty_already_in_cart > 0) {
            $message .= " You already have {$qty_already_in_cart} in cart.";
         }
        throw new Exception($message);
    }


    // --- Prepare Item Data for Session ---
    $cart_item_data = [
        'product_id'     => $product_id,
        'variation_id'   => $variation_id, // null if base product
        'color_id'       => $color_id,     // <<< ADDED selected color_id
        'quantity'       => $quantity,     // Initial quantity being added/updated TO
        'price'          => $item_price,   // Price at time of adding
        'name'           => $item_name,
        'variation_name' => $variation_name,
        'photo'          => $item_photo_filename, // Store only filename
        'installation'   => $installation_selected,
        'color_name'     => $attributes['color_name'] ?? null, // Use fetched color name
        'color_code'     => $attributes['color_code'] ?? null, // Use fetched color code
        'size_name'      => $attributes['size_name'] ?? null
    ];


    // --- Add/Update Item in Cart Session ---
    if (isset($_SESSION['cart'][$cart_key])) {
        // Item exists, update quantity
        $_SESSION['cart'][$cart_key]['quantity'] += $quantity; // Increment quantity
        $_SESSION['cart'][$cart_key]['installation'] = $installation_selected; // Update installation status on re-add
        $cart_item_data['quantity'] = $_SESSION['cart'][$cart_key]['quantity']; // Reflect updated total quantity in response
        $response['message'] = 'Cart quantity updated.';
    } else {
        // Add new item
        $_SESSION['cart'][$cart_key] = $cart_item_data;
        $response['message'] = 'Item added to cart.';
    }

    // --- Success Response ---
    $response['status'] = 'success';
    // Calculate total quantity for cart count display
    $total_quantity_in_cart = 0;
     foreach ($_SESSION['cart'] as $item_in_cart) {
        $total_quantity_in_cart += (int)($item_in_cart['quantity'] ?? 0);
     }
    $response['cart_count'] = $total_quantity_in_cart; // Count total quantity
    // Return the specific item data that was added/updated
    $response['added_item'] = $cart_item_data;


} catch (PDOException $e) {
    error_log("Database Error in add_to_cart.php: " . $e->getMessage());
    $response['message'] = 'Database error. Please try again later.';
} catch (Exception $e) {
    $response['message'] = $e->getMessage(); // Send specific error message (e.g., stock issue)
}

// --- Final Output ---
ob_end_clean(); // Clean buffer before outputting JSON
echo json_encode($response);
exit();
?>