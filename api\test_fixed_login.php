<?php
/**
 * Test Fixed Login
 * Test the login with the user credentials that were failing
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>🔐 Testing Fixed Login</h1>";

$test_email = '<EMAIL>';
$test_password = 'changawa';

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🧪 Testing Login with Real Credentials</h2>";
echo "<p><strong>Email:</strong> {$test_email}</p>";
echo "<p><strong>Password:</strong> {$test_password}</p>";
echo "</div>";

// Helper function for API calls
function testLogin($email, $password) {
    $url = 'http://localhost/ecom/api/v1/auth/login';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'email' => $email,
        'password' => $password
    ]));
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error,
        'error' => $error,
        'http_code' => $http_code,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test 1: Login with correct credentials
echo "<h2>1. Login Test</h2>";

$result = testLogin($test_email, $test_password);

echo "<p><strong>URL:</strong> http://localhost/ecom/api/v1/auth/login</p>";
echo "<p><strong>Method:</strong> POST</p>";
echo "<p><strong>Request Body:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px;'>";
echo json_encode(['email' => $test_email, 'password' => $test_password], JSON_PRETTY_PRINT);
echo "</pre>";

echo "<p><strong>HTTP Code:</strong> {$result['http_code']}</p>";

if ($result['error']) {
    echo "<p style='color: red; font-weight: bold;'>❌ cURL Error: {$result['error']}</p>";
} else {
    echo "<p><strong>Response:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($result['response']);
    echo "</pre>";
    
    if ($result['data']) {
        $status = $result['data']['status'] ?? 'unknown';
        $message = $result['data']['message'] ?? 'No message';
        
        if ($status === 'success') {
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✅ LOGIN SUCCESSFUL!</p>";
            
            if (isset($result['data']['data']['token'])) {
                $token = $result['data']['data']['token'];
                echo "<p style='color: green;'>✅ JWT Token received</p>";
                echo "<p><strong>Token:</strong> " . substr($token, 0, 50) . "...</p>";
                
                // Test the token by getting current user
                echo "<h2>2. Testing Token with Get Current User</h2>";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://localhost/ecom/api/v1/auth/me');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $token
                ]);
                
                $me_response = curl_exec($ch);
                $me_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $me_error = curl_error($ch);
                curl_close($ch);
                
                echo "<p><strong>Get User HTTP Code:</strong> {$me_http_code}</p>";
                
                if (!$me_error) {
                    echo "<p><strong>Get User Response:</strong></p>";
                    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
                    echo htmlspecialchars($me_response);
                    echo "</pre>";
                    
                    $me_data = json_decode($me_response, true);
                    if ($me_data && $me_data['status'] === 'success') {
                        echo "<p style='color: green; font-weight: bold;'>✅ Token validation successful!</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Token validation failed</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Get User cURL Error: {$me_error}</p>";
                }
            }
            
            if (isset($result['data']['data']['user'])) {
                $user = $result['data']['data']['user'];
                echo "<p><strong>User Data:</strong></p>";
                echo "<ul>";
                echo "<li><strong>ID:</strong> {$user['id']}</li>";
                echo "<li><strong>Name:</strong> {$user['first_name']} {$user['last_name']}</li>";
                echo "<li><strong>Email:</strong> {$user['email']}</li>";
                echo "<li><strong>Phone:</strong> {$user['phone']}</li>";
                echo "</ul>";
            }
            
        } else {
            echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ LOGIN FAILED</p>";
            echo "<p><strong>Error:</strong> {$message}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Invalid JSON response</p>";
    }
}

// Test 2: Login with wrong password
echo "<h2>3. Testing Wrong Password</h2>";

$wrong_result = testLogin($test_email, 'wrongpassword');

echo "<p><strong>HTTP Code:</strong> {$wrong_result['http_code']}</p>";

if ($wrong_result['data'] && $wrong_result['data']['status'] === 'error') {
    echo "<p style='color: green;'>✅ Wrong password correctly rejected</p>";
    echo "<p><strong>Error Message:</strong> {$wrong_result['data']['message']}</p>";
} else {
    echo "<p style='color: red;'>❌ Wrong password should have been rejected</p>";
}

// Test 3: Login with non-existent user
echo "<h2>4. Testing Non-existent User</h2>";

$nonexistent_result = testLogin('<EMAIL>', 'password123');

echo "<p><strong>HTTP Code:</strong> {$nonexistent_result['http_code']}</p>";

if ($nonexistent_result['data'] && $nonexistent_result['data']['status'] === 'error') {
    echo "<p style='color: green;'>✅ Non-existent user correctly rejected</p>";
    echo "<p><strong>Error Message:</strong> {$nonexistent_result['data']['message']}</p>";
} else {
    echo "<p style='color: red;'>❌ Non-existent user should have been rejected</p>";
}

// Flutter code example
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📱 Flutter Implementation</h2>";
echo "<p>Here's how to use this login in your Flutter app:</p>";

echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// Login function
Future<bool> login(String email, String password) async {
  try {
    final response = await http.post(
      Uri.parse('http://localhost/ecom/api/v1/auth/login'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'email': email,
        'password': password,
      }),
    );
    
    final data = json.decode(response.body);
    
    if (data['status'] == 'success') {
      // Save token
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', data['data']['token']);
      await prefs.setString('user_data', json.encode(data['data']['user']));
      
      return true;
    } else {
      // Show error
      print('Login failed: \${data['message']}');
      return false;
    }
  } catch (e) {
    print('Network error: \$e');
    return false;
  }
}

// Usage in login screen
void _handleLogin() async {
  bool success = await login(
    _emailController.text,
    _passwordController.text,
  );
  
  if (success) {
    Navigator.pushReplacementNamed(context, '/home');
  } else {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Login failed')),
    );
  }
}
");
echo "</pre>";
echo "</div>";

if ($result['success'] && $result['data'] && $result['data']['status'] === 'success') {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 SUCCESS!</h2>";
    echo "<p><strong>The login issue has been fixed!</strong></p>";
    echo "<p>✅ Authentication is working perfectly</p>";
    echo "<p>✅ JWT tokens are being generated</p>";
    echo "<p>✅ User data is being returned correctly</p>";
    echo "<p>✅ Protected endpoints are accessible with token</p>";
    echo "<p><strong>Your authentication system is ready for production!</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>⚠️ Still Issues</h2>";
    echo "<p>There are still some issues with the login. Please check:</p>";
    echo "<ul>";
    echo "<li>Database connection</li>";
    echo "<li>Table structure</li>";
    echo "<li>User credentials</li>";
    echo "<li>Server error logs</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Login test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
