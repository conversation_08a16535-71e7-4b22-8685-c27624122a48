<?php
/**
 * Settings Endpoints
 * Handles application settings and configuration
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path
$sub_path = $segments[1] ?? '';

switch ($method) {
    case 'GET':
        switch ($sub_path) {
            case 'app':
                handleGetAppSettings($db);
                break;

            case 'payment':
                handleGetPaymentSettings($db);
                break;

            case 'contact':
                handleGetContactSettings($db);
                break;

            case 'social':
                handleGetSocialSettings($db);
                break;

            default:
                handleGetAllSettings($db);
        }
        break;

    default:
        Response::methodNotAllowed(['GET']);
}

/**
 * Get all application settings
 */
function handleGetAllSettings($db) {
    try {
        $settings = $db->fetchAll("SELECT * FROM tbl_settings WHERE id = 1");
        $setting = !empty($settings) ? $settings[0] : null;
    } catch (Exception $e) {
        // Table doesn't exist, use fallback
        $setting = null;
    }

    $formatted_settings = [
        'app' => [
            'name' => $setting['website_name'] ?? 'Ecommerce Store',
            'logo' => $setting['logo'] ? '/assets/uploads/' . $setting['logo'] : null,
            'favicon' => $setting['favicon'] ? '/assets/uploads/' . $setting['favicon'] : null,
            'footer_text' => $setting['footer_text'] ?? '',
            'footer_copyright' => $setting['footer_copyright'] ?? '',
            'currency' => DEFAULT_CURRENCY,
            'installation_fee' => (int)DEFAULT_INSTALLATION_FEE
        ],
        'contact' => [
            'address' => $setting['contact_address'] ?? '',
            'email' => $setting['contact_email'] ?? '',
            'phone' => $setting['contact_phone'] ?? '',
            'map_iframe' => $setting['contact_map_iframe'] ?? ''
        ],
        'social' => [
            'facebook' => $setting['social_facebook'] ?? '',
            'twitter' => $setting['social_twitter'] ?? '',
            'linkedin' => $setting['social_linkedin'] ?? '',
            'youtube' => $setting['social_youtube'] ?? '',
            'instagram' => $setting['social_instagram'] ?? '',
            'pinterest' => $setting['social_pinterest'] ?? ''
        ],
        'seo' => [
            'meta_title_home' => $setting['meta_title_home'] ?? '',
            'meta_keyword_home' => $setting['meta_keyword_home'] ?? '',
            'meta_description_home' => $setting['meta_description_home'] ?? ''
        ],
        'email' => [
            'send_email_from' => $setting['send_email_from'] ?? '',
            'receive_email_to' => $setting['receive_email_to'] ?? '',
            'smtp_active' => (bool)($setting['smtp_active'] ?? false),
            'smtp_host' => $setting['smtp_host'] ?? '',
            'smtp_port' => $setting['smtp_port'] ?? '',
            'smtp_username' => $setting['smtp_username'] ?? ''
        ],
        'payment' => [
            'paypal_email' => $setting['paypal_email'] ?? '',
            'stripe_public_key' => $setting['stripe_public_key'] ?? '',
            'bank_detail' => $setting['bank_detail'] ?? ''
        ]
    ];

    Response::success($formatted_settings, 'Settings retrieved successfully');
}

/**
 * Get app-specific settings
 */
function handleGetAppSettings($db) {
    try {
        $setting = $db->fetchOne("SELECT * FROM tbl_settings WHERE id = 1");
    } catch (Exception $e) {
        // Table doesn't exist, use fallback settings
        $setting = null;
    }

    // Use fallback settings if table doesn't exist or no data found
    $app_settings = [
        'name' => $setting['website_name'] ?? 'Ecommerce Store',
        'logo' => $setting['logo'] ? '/assets/uploads/' . $setting['logo'] : null,
        'favicon' => $setting['favicon'] ? '/assets/uploads/' . $setting['favicon'] : null,
        'footer_text' => $setting['footer_text'] ?? 'Welcome to our online store',
        'footer_copyright' => $setting['footer_copyright'] ?? '© 2024 Ecommerce Store. All rights reserved.',
        'currency' => DEFAULT_CURRENCY,
        'installation_fee' => (int)DEFAULT_INSTALLATION_FEE,
        'meta_title' => $setting['meta_title_home'] ?? 'Ecommerce Store',
        'meta_description' => $setting['meta_description_home'] ?? 'Your trusted online shopping destination',
        'version' => API_VERSION,
        'contact_email' => $setting['contact_email'] ?? '<EMAIL>',
        'contact_phone' => $setting['contact_phone'] ?? '+255 123 456 789'
    ];

    Response::success($app_settings, 'App settings retrieved successfully');
}

/**
 * Get payment settings
 */
function handleGetPaymentSettings($db) {
    $setting = $db->fetchOne("SELECT * FROM tbl_settings WHERE id = 1");

    if (!$setting) {
        Response::notFound('Settings not found');
    }

    $payment_settings = [
        'paypal_email' => $setting['paypal_email'] ?? '',
        'stripe_public_key' => $setting['stripe_public_key'] ?? '',
        'bank_detail' => $setting['bank_detail'] ?? '',
        'currency' => DEFAULT_CURRENCY,
        'payment_methods' => [
            'paypal' => !empty($setting['paypal_email']),
            'stripe' => !empty($setting['stripe_public_key']),
            'bank_transfer' => !empty($setting['bank_detail'])
        ]
    ];

    Response::success($payment_settings, 'Payment settings retrieved successfully');
}

/**
 * Get contact settings
 */
function handleGetContactSettings($db) {
    $setting = $db->fetchOne("SELECT * FROM tbl_settings WHERE id = 1");

    if (!$setting) {
        Response::notFound('Settings not found');
    }

    $contact_settings = [
        'address' => $setting['contact_address'] ?? '',
        'email' => $setting['contact_email'] ?? '',
        'phone' => $setting['contact_phone'] ?? '',
        'map_iframe' => $setting['contact_map_iframe'] ?? '',
        'business_hours' => $setting['business_hours'] ?? '',
        'support_email' => $setting['receive_email_to'] ?? $setting['contact_email'] ?? ''
    ];

    Response::success($contact_settings, 'Contact settings retrieved successfully');
}

/**
 * Get social media settings
 */
function handleGetSocialSettings($db) {
    $setting = $db->fetchOne("SELECT * FROM tbl_settings WHERE id = 1");

    if (!$setting) {
        Response::notFound('Settings not found');
    }

    $social_settings = [
        'facebook' => $setting['social_facebook'] ?? '',
        'twitter' => $setting['social_twitter'] ?? '',
        'linkedin' => $setting['social_linkedin'] ?? '',
        'youtube' => $setting['social_youtube'] ?? '',
        'instagram' => $setting['social_instagram'] ?? '',
        'pinterest' => $setting['social_pinterest'] ?? '',
        'whatsapp' => $setting['whatsapp_number'] ?? '',
        'telegram' => $setting['telegram_username'] ?? ''
    ];

    Response::success($social_settings, 'Social media settings retrieved successfully');
}
?>
