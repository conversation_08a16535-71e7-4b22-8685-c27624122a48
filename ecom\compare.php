  <?php
  ob_start();
  session_start();
  include("../admin/inc/config.php");
  include("../admin/inc/functions.php");
  include("../admin/inc/CSRF_Protect.php");

  $product_ids = [];
  $products_to_compare = [];
  $error_message = '';
  $max_compare = 4; // Limit comparison to 4 items

  if (isset($_GET['ids'])) {
    $ids_string = trim($_GET['ids']);
    if (!empty($ids_string)) {
      $raw_ids = explode(',', $ids_string);
      // Sanitize and validate IDs
      foreach ($raw_ids as $id) {
        $clean_id = filter_var(trim($id), FILTER_VALIDATE_INT);
        if ($clean_id !== false && $clean_id > 0) {
          $product_ids[] = $clean_id;
        }
      }
      // Remove duplicates and limit to max
      $product_ids = array_slice(array_unique($product_ids), 0, $max_compare);

      if (count($product_ids) >= 2) {
        // Fetch product data
        $placeholders = implode(',', array_fill(0, count($product_ids), '?'));
        $statement = $pdo->prepare("SELECT * FROM tbl_product
                                    WHERE p_id IN ($placeholders) AND p_is_active = 1");
        // Ensure the order matches the input IDs for consistent display
        $statement->execute($product_ids);
        $fetched_products = $statement->fetchAll(PDO::FETCH_ASSOC);

        // Re-order fetched products based on the original $product_ids array
        $indexed_products = [];
        foreach ($fetched_products as $prod) {
          $indexed_products[$prod['p_id']] = $prod;
        }
        foreach ($product_ids as $id) {
          if (isset($indexed_products[$id])) {
            $products_to_compare[] = $indexed_products[$id];
          }
        }

        if (count($products_to_compare) < 2) {
          $error_message = "Could not find at least two valid products to compare.";
          $products_to_compare = []; // Clear if not enough found
        }
      } else {
        $error_message = "Please select at least two products to compare.";
      }
    } else {
      $error_message = "No products selected for comparison.";
    }
  } else {
    $error_message = "No products specified for comparison.";
  }

  // Fetch settings for header/footer
  $statement_settings = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
  $statement_settings->execute();
  $settings = $statement_settings->fetch(PDO::FETCH_ASSOC);
  $footer_copyright = $settings['footer_copyright'] ?? "© " . date("Y") . " SMART LIFE. All rights reserved.";

  // Fetch categories for the mobile menu
  $statement = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC");
  $statement->execute();
  $all_categories = $statement->fetchAll(PDO::FETCH_ASSOC);

  // Define attributes to display in the comparison table
  $attributes_to_compare = [
    'p_featured_photo' => 'Image',
    'p_name' => 'Name',
    'p_current_price' => 'Price',
    'p_short_description' => 'Short Description',
    'p_qty' => 'Stock',
    'p_is_featured' => 'Featured',
  ];

  ?>
  <!DOCTYPE html>
  <html lang="en">

  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Compare Products | SMART</title>
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      :root {
        --primary: #4f46e5;
        --primary-hover: #4338ca;
        --secondary: #00c2ff;
        --secondary-hover: #00a3d9;
      }

      /* Custom Scrollbar */
      .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 10px;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #94a3b8;
        border-radius: 10px;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #64748b;
      }

      /* Mobile Menu Styles */
      #mobileMenu {
        width: 50%;
        height: 100vh;
        transition: transform 0.3s ease-in-out;
        z-index: 50;
        overflow-y: auto;
      }

      #mobileMenuBackdrop {
        transition: opacity 0.3s ease-in-out;
      }

      /* Dropdown Styles for Mobile Menu */
      .mobile-dropdown-toggle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 10px 0;
        border-bottom: 1px solid #f3f4f6;
      }

      .mobile-dropdown-icon {
        transition: transform 0.3s ease;
      }

      .mobile-dropdown-icon.open {
        transform: rotate(180deg);
      }

      .mobile-dropdown-menu {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
        padding-left: 1rem;
      }

      .mobile-dropdown-menu.open {
        max-height: 500px;
      }

      /* Compare Table Styles */
      .compare-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      .compare-table th,
      .compare-table td {
        vertical-align: top;
        padding: 1.25rem 1rem;
        border-bottom: 1px solid #e5e7eb;
        position: relative;
      }

      .compare-table tr:last-child th,
      .compare-table tr:last-child td {
        border-bottom: none;
      }

      .compare-table th {
        background-color: #f3f4f6;
        font-weight: 600;
        text-align: left;
        color: #1f2937;
        width: 20%;
        position: sticky;
        left: 0;
        z-index: 10;
        box-shadow: 4px 0 6px -1px rgba(0, 0, 0, 0.05);
      }

      .compare-table td {
        text-align: center;
        background-color: white;
        width: calc((100% - 20%) / var(--product-count, 2));
        min-width: 200px;
      }

      .compare-table tr:nth-child(odd) th {
        background-color: #f9fafb;
      }

      .compare-table tr:nth-child(odd) td {
        background-color: #f9fafb;
      }

      .compare-table tr:hover th {
        background-color: #f0f9ff;
      }

      .compare-table tr:hover td {
        background-color: #f0f9ff;
      }

      .compare-table img {
        width: 160px;
        height: 160px;
        margin: 0 auto;
        border-radius: 0.5rem;
        object-fit: contain;
        background-color: #f8fafc;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .compare-table img:hover {
        transform: scale(1.05);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }

      .compare-table .product-name {
        font-weight: 700;
        color: #111827;
        margin-bottom: 0.75rem;
        font-size: 1.125rem;
        line-height: 1.4;
        height: 3.2rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .compare-table .product-price {
        font-weight: 700;
        color: var(--primary);
        font-size: 1.25rem;
        margin-bottom: 1rem;
        position: relative;
        display: inline-block;
      }

      .compare-table .product-price::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 2px;
        background-color: var(--secondary);
      }

      .compare-table .product-description {
        font-size: 0.9375rem;
        color: #4b5563;
        text-align: left;
        line-height: 1.6;
        max-height: 120px;
        overflow-y: auto;
        padding-right: 5px;
      }

      .compare-table .product-description::-webkit-scrollbar {
        width: 4px;
      }

      .compare-table .product-description::-webkit-scrollbar-track {
        background: #f1f5f9;
      }

      .compare-table .product-description::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 10px;
      }

      .compare-table .add-to-cart {
        margin-top: 1.25rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .compare-table .add-to-cart::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
        transition: left 0.7s ease;
      }

      .compare-table .add-to-cart:hover::before {
        left: 100%;
      }

      /* Color Display */
      .color-swatch {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-block;
        border: 2px solid white;
        box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
        margin-right: 8px;
        vertical-align: middle;
      }

      /* Stock Status */
      .stock-status {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
      }

      .stock-status.in-stock {
        background-color: #d1fae5;
        color: #065f46;
      }

      .stock-status.low-stock {
        background-color: #fef3c7;
        color: #92400e;
      }

      .stock-status.out-of-stock {
        background-color: #fee2e2;
        color: #b91c1c;
      }

      /* Featured Badge */
      .featured-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
        background-color: #e0e7ff;
        color: #4338ca;
      }

      /* Responsive Table */
      .compare-table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      }

      @media (max-width: 768px) {
        .compare-table-wrapper {
          margin: 0 -1rem;
          padding: 0 1rem;
        }

        .compare-table th {
          min-width: 120px;
        }

        .compare-table td {
          min-width: 200px;
          width: 200px;
        }

        .compare-table img {
          width: 120px;
          height: 120px;
        }
      }

      /* Toast Notification Styles */
      #toastContainer {
        z-index: 1000;
      }

      .toast {
        transform: translateY(1rem);
        opacity: 0;
        transition: transform 0.3s ease, opacity 0.3s ease;
      }

      .toast.show {
        transform: translateY(0);
        opacity: 1;
      }

      /* Animations */
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
      }

      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }

      .animate-fade-in {
        animation: fadeIn 0.5s ease forwards;
      }

      .animate-slide-in {
        animation: slideInRight 0.5s ease forwards;
      }

      .animate-pulse {
        animation: pulse 2s infinite;
      }

      /* Scrollbar Styling */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 10px;
      }

      ::-webkit-scrollbar-thumb {
        background: #94a3b8;
        border-radius: 10px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #64748b;
      }
    </style>
  </head>

  <body class="bg-gray-50 text-gray-800">
    <!-- Header -->
    <header class="bg-white shadow-md sticky top-0 z-50">
      <div class="container mx-auto px-4 flex items-center justify-between h-16">
        <a href="index.php" class="text-2xl font-bold text-gray-800 flex items-center">
          SMART LIFE TZ<span class="text-[#00c2ff]">.</span>
        </a>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <a href="index.php" class="text-gray-700 hover:text-[#00c2ff] transition-colors duration-200 font-medium">Home</a>
          <a href="all_products.php" class="text-gray-700 hover:text-[#00c2ff] transition-colors duration-200 font-medium">All Products</a>
          <a href="index.php#contact" class="text-gray-700 hover:text-[#00c2ff] transition-colors duration-200 font-medium">Contact</a>

          <!-- Search Bar -->
          <div class="relative">
            <input type="text" id="searchInput" placeholder="Search products..."
                  class="w-64 pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent"
                  autocomplete="off">
            <div class="absolute left-3 top-2.5 text-gray-400">
              <i class="fas fa-search"></i>
            </div>
            <div id="searchSuggestions" class="absolute left-0 right-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100"></div>
          </div>

          <!-- Cart Icon -->
          <div class="cart-icon">
            <a href="cart.php" class="text-gray-700 hover:text-[#00c2ff] relative transition-colors duration-200">
              <i class="fas fa-shopping-cart text-xl"></i>
              <span class="cart-count absolute -top-1 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">0</span>
            </a>
          </div>
        </nav>

        <!-- Mobile Menu Button -->
        <button id="mobileMenuButton" class="md:hidden flex items-center">
          <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
          </svg>
        </button>
      </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
      <div class="flex flex-col h-full">
        <div class="flex justify-between items-center p-4 border-b">
          <a href="index.php" class="text-xl font-bold text-gray-900">
            SMART LIFE<span class="text-[#00c2ff]">.</span>
          </a>
          <button id="closeMobileMenu" class="text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
        <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
          <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
          <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
          <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
          <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
          <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>
          <!-- Search in Mobile Menu -->
          <div class="relative mt-4">
            <input id="mobileSearchInput" type="text" placeholder="Search products, categories..."
                   class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                   autocomplete="off">
            <div id="mobileSearchSuggestions"
                 class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100"></div>
          </div>
          <!-- Cart in Mobile Menu -->
          <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
            <span class="text-xl mr-2">🛒</span>
            <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
          </a>
        </nav>
      </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <main class="py-8 md:py-12">
      <div class="container mx-auto px-4">
        <!-- Page Header with Breadcrumbs -->
        <div class="mb-8">
          <div class="flex items-center text-sm text-gray-500 mb-4">
            <a href="index.php" class="hover:text-blue-600 transition-colors">Home</a>
            <svg class="mx-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <a href="all_products.php" class="hover:text-blue-600 transition-colors">All Products</a>
            <svg class="mx-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-gray-700 font-medium">Compare Products</span>
          </div>

          <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">Product Comparison</h1>
          <p class="text-gray-600 max-w-3xl">Compare product features side by side to find the perfect match for your needs.</p>
        </div>

        <?php if (!empty($error_message)): ?>
          <!-- Error Message -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8 border-l-4 border-red-500 animate-fade-in">
            <div class="flex items-center">
              <div class="flex-shrink-0 mr-4">
                <svg class="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-1">Unable to Compare Products</h3>
                <p class="text-gray-600"><?= htmlspecialchars($error_message) ?></p>
                <div class="mt-4">
                  <a href="all_products.php" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Browse Products
                  </a>
                </div>
              </div>
            </div>
          </div>
        <?php elseif (empty($products_to_compare)): ?>
          <!-- No Products Found -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-8 border-l-4 border-yellow-500 animate-fade-in">
            <div class="flex items-center">
              <div class="flex-shrink-0 mr-4">
                <svg class="h-12 w-12 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-1">No Products to Compare</h3>
                <p class="text-gray-600">No products found for comparison based on the selected IDs.</p>
                <div class="mt-4">
                  <a href="all_products.php" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Browse Products
                  </a>
                </div>
              </div>
            </div>
          </div>
        <?php else: ?>
          <!-- Comparison Table -->
          <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8 animate-fade-in">
            <div class="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
              <h2 class="text-xl font-bold text-gray-800">Comparing <?= count($products_to_compare) ?> Products</h2>
            </div>

            <div class="compare-table-wrapper custom-scrollbar">
              <table class="w-full compare-table">
                <tbody>
                  <!-- Product Header Row with Remove Buttons -->
                  <tr class="bg-gray-50">
                    <th scope="row" class="bg-gray-100">Actions</th>
                    <?php foreach ($products_to_compare as $product): ?>
                      <td>
                        <button onclick="removeProductFromComparison(<?= $product['p_id'] ?>)"
                                class="bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-all duration-200 flex items-center justify-center gap-1.5 w-full py-2 px-3 rounded-lg border border-red-200 hover:border-red-300 shadow-sm hover:shadow">
                          <i class="fas fa-trash-alt text-sm"></i>
                          <span class="font-medium">Remove</span>
                        </button>
                      </td>
                    <?php endforeach; ?>
                  </tr>

                  <?php foreach ($attributes_to_compare as $key => $label): ?>
                    <tr>
                      <th scope="row"><?= htmlspecialchars($label) ?></th>
                      <?php foreach ($products_to_compare as $product): ?>
                        <td>
                          <?php
                          $value = $product[$key] ?? 'N/A';
                          if ($key === 'p_featured_photo') {
                            // Image with hover effect
                            echo '<div class="relative group">';
                            echo '<a href="product_detail.php?id=' . $product['p_id'] . '">';
                            echo '<img src="../assets/uploads/' . htmlspecialchars($value) . '" alt="' . htmlspecialchars($product['p_name']) . '" class="mx-auto">';
                            echo '<div class="absolute inset-0 bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center">';
                            echo '<span class="text-white font-medium px-3 py-1 bg-[#00c2ff] rounded-lg text-sm">View Details</span>';
                            echo '</div>';
                            echo '</a>';
                            echo '</div>';
                          } elseif ($key === 'p_current_price') {
                            echo '<span class="product-price">Tsh ' . number_format((float)$value, 0) . '</span>';
                          } elseif ($key === 'p_name') {
                            echo '<a href="product_detail.php?id=' . $product['p_id'] . '" class="product-name hover:text-[#00c2ff] transition-colors">' . htmlspecialchars($value) . '</a>';
                          } elseif ($key === 'p_short_description') {
                            echo '<div class="product-description">' . nl2br(strip_tags($value)) . '</div>';
                          } elseif ($key === 'p_qty') {
                            // Stock status with visual indicator
                            $stock = (int)$value;
                            if ($stock <= 0) {
                              echo '<span class="stock-status out-of-stock"><i class="fas fa-times-circle mr-1"></i> Out of Stock</span>';
                            } elseif ($stock < 5) {
                              echo '<span class="stock-status low-stock"><i class="fas fa-exclamation-circle mr-1"></i> Low Stock (' . $stock . ')</span>';
                            } else {
                              echo '<span class="stock-status in-stock"><i class="fas fa-check-circle mr-1"></i> In Stock (' . $stock . ')</span>';
                            }
                          } elseif ($key === 'p_is_featured') {
                            // Featured badge
                            if ($value == 1) {
                              echo '<span class="featured-badge"><i class="fas fa-star mr-1"></i> Featured</span>';
                            } else {
                              echo '<span class="text-gray-400">Standard</span>';
                            }
                          } else {
                            echo nl2br(htmlspecialchars($value));
                          }
                          ?>
                          <?php if ($key === array_key_last($attributes_to_compare)): ?>
                            <div class="flex flex-col gap-2 mt-4">
                              <button class="add-to-cart w-full flex items-center justify-center gap-2 bg-gradient-to-r from-blue-600 to-[#00c2ff] hover:from-blue-700 hover:to-[#00a3d9] text-white font-medium py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5"
                                data-product-id="<?= $product['p_id'] ?>" onclick="event.stopPropagation();">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Add to Cart
                              </button>
                              <a href="product_detail.php?id=<?= $product['p_id'] ?>" class="text-center text-[#00c2ff] hover:text-blue-800 font-medium transition-colors duration-200 text-sm">
                                View Details
                              </a>
                            </div>
                          <?php endif; ?>
                        </td>
                      <?php endforeach; ?>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Back to Products Button -->
          <div class="flex justify-between items-center">
            <a href="all_products.php" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Products
            </a>

            <!-- Share Buttons -->
            <div class="flex items-center gap-3">
              <span class="text-gray-600 text-sm">Share:</span>
              <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="text-blue-400 hover:text-blue-600 transition-colors">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="text-red-600 hover:text-red-800 transition-colors">
                <i class="fab fa-pinterest-p"></i>
              </a>
              <a href="#" class="text-green-600 hover:text-green-800 transition-colors">
                <i class="fab fa-whatsapp"></i>
              </a>
            </div>
          </div>
        <?php endif; ?>

        <!-- Related Products Section -->
        <?php if (!empty($products_to_compare)): ?>
          <div class="mt-16">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">You Might Also Like</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
              <?php
              // Get category IDs from compared products
              $category_ids = [];
              foreach ($products_to_compare as $product) {
                if (!empty($product['tcat_id'])) {
                  $category_ids[] = $product['tcat_id'];
                }
              }

              // Get related products from same categories
              if (!empty($category_ids)) {
                $placeholders = implode(',', array_fill(0, count($category_ids), '?'));
                $exclude_ids = implode(',', array_fill(0, count($product_ids), '?'));

                $stmt = $pdo->prepare("SELECT * FROM tbl_product
                                      WHERE tcat_id IN ($placeholders)
                                      AND p_id NOT IN ($exclude_ids)
                                      AND p_is_active = 1
                                      ORDER BY RAND() LIMIT 4");

                $params = array_merge($category_ids, $product_ids);
                $stmt->execute($params);
                $related_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($related_products as $related):
              ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden group hover:shadow-lg transition-shadow duration-300">
                  <a href="product_detail.php?id=<?= $related['p_id'] ?>" class="block">
                    <div class="h-48 overflow-hidden">
                      <img src="../assets/uploads/<?= htmlspecialchars($related['p_featured_photo']) ?>"
                           alt="<?= htmlspecialchars($related['p_name']) ?>"
                           class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    <div class="p-4">
                      <h3 class="font-semibold text-gray-800 mb-1 line-clamp-1"><?= htmlspecialchars($related['p_name']) ?></h3>
                      <div class="text-blue-600 font-bold">Tsh <?= number_format($related['p_current_price'], 0) ?></div>
                      <button onclick="event.preventDefault(); addToCart(<?= $related['p_id'] ?>);"
                              class="mt-3 w-full py-1.5 bg-gray-100 hover:bg-blue-600 hover:text-white text-gray-700 rounded text-sm font-medium transition-colors duration-300">
                        Add to Cart
                      </button>
                    </div>
                  </a>
                </div>
              <?php
                endforeach;
              }
              ?>
            </div>
          </div>
        <?php endif; ?>
      </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <!-- Toast Notifications Container -->
    <div id="toastContainer" class="fixed bottom-4 right-4 z-[1000] w-full max-w-xs"></div>

    <script>
      // Initialize cart if not exists
      if (!localStorage.getItem('cart')) {
        localStorage.setItem('cart', JSON.stringify([]));
      }

      // Initialize compare items if not exists
      if (!localStorage.getItem('compareItems')) {
        localStorage.setItem('compareItems', JSON.stringify([]));
      }

      // Add to Cart Functionality
      function addToCart(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', 1);

        const buttons = document.querySelectorAll(`.add-to-cart[data-product-id="${productId}"]`);
        buttons.forEach(button => {
          button.disabled = true;
          button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Adding...';
        });

        fetch('add_to_cart.php', {
            method: 'POST',
            body: formData
          })
          .then(response => {
            const contentType = response.headers.get("content-type");
            if (response.ok && contentType && contentType.includes("application/json")) {
              return response.json();
            } else {
              return response.text().then(text => {
                throw new Error(`Server error: ${text.substring(0,200)}`);
              });
            }
          })
          .then(data => {
            if (data && data.status === 'success' && data.added_item) {
              // Update localStorage cart
              let cart = JSON.parse(localStorage.getItem('cart') || '[]');
              const productIdStr = String(data.added_item.product_id);
              const existingIndex = cart.findIndex(item => String(item.product_id) === productIdStr);

              if (existingIndex > -1) {
                cart[existingIndex].quantity = data.added_item.quantity;
              } else {
                cart.push(data.added_item);
              }

              localStorage.setItem('cart', JSON.stringify(cart));
              updateCartCount();
              checkCartStatus(); // Update button states

              showToast('success', 'Product added to cart!');
            } else {
              showToast('error', data.message || 'Error adding product.');
              buttons.forEach(button => {
                button.disabled = false;
                button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" /></svg> Add to Cart';
              });
            }
          })
          .catch(error => {
            console.error("Add to Cart Error:", error);
            showToast('error', 'Could not add product to cart.');
            buttons.forEach(button => {
              button.disabled = false;
              button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" /></svg> Add to Cart';
            });
          });
      }

      // Function to check if product is in cart and update button state
      function checkCartStatus() {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        document.querySelectorAll('.add-to-cart').forEach(button => {
          const productId = button.getAttribute('data-product-id');
          const isInCart = cart.some(item => String(item.product_id) === productId);

          if (isInCart) {
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');
            button.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Added to Cart
            `;
          }
        });
      }

      // Update Cart Count from localStorage
      function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        const totalItems = cart.reduce((total, item) => total + (parseInt(item.quantity) || 0), 0);

        // Update all cart count elements
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
          element.textContent = totalItems;

          // Add animation if count increased
          element.classList.add('animate-pulse');
          setTimeout(() => {
            element.classList.remove('animate-pulse');
          }, 1000);
        });
      }

      // Toast Notification
      function showToast(type = 'success', message = 'Action completed.') {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        const toastId = 'toast-' + Date.now();
        const toast = document.createElement('div');
        toast.id = toastId;

        // Create toast with icon
        if (type === 'success') {
          toast.className = 'flex items-center p-4 mb-3 bg-white border-l-4 border-green-500 rounded-lg shadow-lg toast';
          toast.innerHTML = `
            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg mr-3">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-800">${message}</div>
          `;
        } else {
          toast.className = 'flex items-center p-4 mb-3 bg-white border-l-4 border-red-500 rounded-lg shadow-lg toast';
          toast.innerHTML = `
            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg mr-3">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-800">${message}</div>
          `;
        }

        container.appendChild(toast);

        // Animate in
        setTimeout(() => {
          toast.classList.add('show');
        }, 10);

        // Remove after delay
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => toast.remove(), 300);
        }, 4000);
      }

      // Mobile Menu Functionality
      // Function to remove a product from comparison
      function removeProductFromComparison(productId) {
        // Get current URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const currentIds = urlParams.get('ids') ? urlParams.get('ids').split(',') : [];

        // Remove the product ID from the array
        const newIds = currentIds.filter(id => id != productId);

        if (newIds.length < 2) {
          // If less than 2 products remain, show a message and redirect to all products
          showToast('error', 'At least 2 products are needed for comparison.');
          setTimeout(() => {
            window.location.href = 'all_products.php';
          }, 2000);
          return;
        }

        // Update localStorage
        let compareItems = JSON.parse(localStorage.getItem('compareItems') || '[]');
        compareItems = compareItems.filter(id => id != productId);
        localStorage.setItem('compareItems', JSON.stringify(compareItems));

        // Redirect to the new URL with updated IDs
        window.location.href = `compare.php?ids=${newIds.join(',')}`;
      }

      document.addEventListener('DOMContentLoaded', function() {
        // Initialize cart count and check cart status
        updateCartCount();
        checkCartStatus();

        // Set CSS variable for product count to ensure uniform column width
        const productCount = document.querySelectorAll('.compare-table tr:first-child td').length;
        document.documentElement.style.setProperty('--product-count', productCount);

        // Add event listeners to all add-to-cart buttons
        document.querySelectorAll('.add-to-cart').forEach(button => {
          button.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent any card-level click events

            // Don't proceed if button is disabled
            if (this.disabled) {
              return;
            }

            const productId = this.getAttribute('data-product-id');
            addToCart(productId);
          });
        });

        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        const closeMobileMenu = document.getElementById('closeMobileMenu');
        const mobileMenu = document.getElementById('mobileMenu');
        const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

        if (mobileMenuButton && mobileMenu && mobileMenuBackdrop) {
          mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.remove('translate-x-full');
            mobileMenuBackdrop.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
          });

          function closeMenu() {
            mobileMenu.classList.add('translate-x-full');
            mobileMenuBackdrop.classList.add('hidden');
            document.body.style.overflow = 'auto'; // Re-enable scrolling
          }

          closeMobileMenu.addEventListener('click', closeMenu);
          mobileMenuBackdrop.addEventListener('click', closeMenu);
        }

        // Mobile category dropdowns
        const dropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
        dropdownToggles.forEach(toggle => {
          toggle.addEventListener('click', function() {
            const dropdownMenu = this.nextElementSibling;
            const icon = this.querySelector('.mobile-dropdown-icon');

            if (dropdownMenu.classList.contains('open')) {
              dropdownMenu.classList.remove('open');
              icon.classList.remove('open');
            } else {
              // Close all other open dropdowns
              document.querySelectorAll('.mobile-dropdown-menu.open').forEach(menu => {
                if (menu !== dropdownMenu) {
                  menu.classList.remove('open');
                  menu.previousElementSibling.querySelector('.mobile-dropdown-icon').classList.remove('open');
                }
              });

              dropdownMenu.classList.add('open');
              icon.classList.add('open');
            }
          });
        });

        // Back to top button
        const backToTopButton = document.getElementById('backToTop');

        window.addEventListener('scroll', function() {
          if (window.pageYOffset > 300) {
            backToTopButton.classList.remove('scale-0');
            backToTopButton.classList.add('scale-100');
          } else {
            backToTopButton.classList.remove('scale-100');
            backToTopButton.classList.add('scale-0');
          }
        });

        backToTopButton.addEventListener('click', function() {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      });
    </script>
  </body>
  </html>
  <?php ob_end_flush(); ?>