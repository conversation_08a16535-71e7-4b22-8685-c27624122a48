<?php require_once('header.php'); ?>

<?php
if(!isset($_REQUEST['id']) || !isset($_REQUEST['status'])) {
    header('location: order.php');
    exit;
} else {
    // Check the id is valid or not
    $statement = $pdo->prepare("SELECT * FROM orders WHERE id=?");
    $statement->execute(array($_REQUEST['id']));
    $total = $statement->rowCount();
    if($total == 0) {
        header('location: order.php');
        exit;
    } else {
        // Valid status values
        $valid_statuses = array('success', 'pending', 'failed');
        
        // Check if the status is valid
        if(!in_array($_REQUEST['status'], $valid_statuses)) {
            header('location: order.php');
            exit;
        }
        
        // Update the payment status
        $statement = $pdo->prepare("UPDATE orders SET payment_status=? WHERE id=?");
        $statement->execute(array($_REQUEST['status'], $_REQUEST['id']));
        
        header('location: order.php');
        exit;
    }
}
?>
