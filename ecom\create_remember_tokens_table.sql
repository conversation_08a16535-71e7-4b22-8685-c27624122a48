-- Create table for storing remember me tokens
-- Run this SQL script in your database to create the required table

CREATE TABLE IF NOT EXISTS `tbl_remember_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `token_hash` varchar(64) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_hash` (`token_hash`),
  KEY `customer_id` (`customer_id`),
  KEY `expires_at` (`expires_at`),
  FOREIGN KEY (`customer_id`) REFERENCES `tbl_customer` (`cust_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create index for better performance
CREATE INDEX `idx_customer_expires` ON `tbl_remember_tokens` (`customer_id`, `expires_at`);

-- Optional: Add a cleanup event to automatically remove expired tokens
-- This will run daily to clean up expired tokens
DELIMITER $$
CREATE EVENT IF NOT EXISTS `cleanup_expired_remember_tokens`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
  DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
END$$
DELIMITER ;

-- Enable the event scheduler if not already enabled
-- SET GLOBAL event_scheduler = ON;
