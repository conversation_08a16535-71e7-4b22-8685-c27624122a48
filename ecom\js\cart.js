// js/cart.js
// Version: 2025-04-23
// Includes fixes for Name, Price, Photo Path, Photo Filename Fallback, verified Installation Logic,
// Dynamic Country-Based Shipping, Shipping Persistence, and Variation Link Check.

document.addEventListener("DOMContentLoaded", function () {
  // Retrieve global variables generated by PHP (ensure these are available in cart.php)
  // IMPORTANT: productData MUST include p_id, p_name, p_current_price, p_qty, p_featured_photo
  var productData = window.productData || [];
  var defaultInstallationFee = window.defaultInstallationFee || 15000; // Default if not set via PHP
  var productInstallationFees = window.productInstallationFees || {}; // Product-specific installation fees

  // Debug: Log the installation fees
  console.log("Default installation fee:", defaultInstallationFee);
  console.log("Product-specific installation fees:", productInstallationFees);

  var productColors = window.productColors || {};
  var productVariationsData = window.productVariationsData || {};

  // ****** MODIFIED: Shipping Variables ******
  var shippingOptions = window.shippingOptions || []; // Array of {id, name, cost} passed from PHP
  var defaultShippingCost = window.defaultShippingCost || 0; // Default cost from PHP (likely corresponds to defaultShippingAreaId)
  var defaultShippingAreaId = window.defaultShippingAreaId || null; // <<< ADDED: Get default area ID from PHP

  // Get initial value: 1. PHP Session, 2. localStorage, 3. PHP Default Area ID, 4. null
  var selectedCountryId =
    window.selectedCountryId ||
    localStorage.getItem("selectedCountryId") ||
    defaultShippingAreaId ||
    null;
  // ****** END MODIFIED ******

  // --- Login Status ---
  var isLoggedIn =
    typeof window.isUserLoggedIn !== "undefined" ? window.isUserLoggedIn : false;

  // --- Build Lookup Map (productsMap) ---
  var productsMap = {};
  productData.forEach(function (prod) {
    productsMap[prod.p_id] = prod;
    if (productVariationsData[prod.p_id]) {
      productsMap[prod.p_id].variations = productVariationsData[prod.p_id];
    }
    if (productColors[prod.p_id]) {
      productsMap[prod.p_id].colors = productColors[prod.p_id];
    }
  });

  // --- Retrieve Cart from localStorage ---
  var cart = [];
  try {
    // Get cart from localStorage
    const storedCart = localStorage.getItem("cart");
    if (storedCart) {
      cart = JSON.parse(storedCart);

      // Debug the cart contents
      console.log("Cart loaded from localStorage:", cart);

      // Filter out any invalid items (missing product_id or id)
      cart = cart.filter((item) => {
        // Check for either product_id (from server) or id (from client)
        if (!item || (!item.product_id && !item.id)) {
          console.warn("Removing invalid cart item:", item);
          return false;
        }

        // Normalize the item structure - ensure it has both id and product_id properties
        if (item.product_id && !item.id) {
          item.id = item.product_id;
          console.log("Added id property from product_id:", item);
        } else if (item.id && !item.product_id) {
          item.product_id = item.id;
          console.log("Added product_id property from id:", item);
        }

        return true;
      });

      // Update localStorage with the filtered and normalized cart
      localStorage.setItem("cart", JSON.stringify(cart));

      // If cart is not empty, sync with server
      if (cart.length > 0) {
        console.log("Syncing cart with server on page load");
        updateCartSession();
      }
    }
  } catch (error) {
    console.error("Error loading cart from localStorage:", error);
    // Reset cart if there was an error
    localStorage.setItem("cart", JSON.stringify([]));
  }

  // --- Restore shipping country selection ---
  function restoreShippingCountry() {
    // Get the country selector element
    const countrySelector = document.getElementById("countrySelector");
    if (!countrySelector) {
      console.warn(
        "Country selector not found, cannot restore shipping country"
      );
      return;
    }

    // Try to get the selected country ID from localStorage
    const selectedCountryId = localStorage.getItem("selectedCountryId");
    if (selectedCountryId) {
      console.log(
        "Found selected country ID in localStorage:",
        selectedCountryId
      );

      // Set the country selector value
      countrySelector.value = selectedCountryId;

      // Also set a cookie for server-side persistence
      document.cookie = `selectedCountryId=${selectedCountryId}; path=/; max-age=86400`; // 24 hours

      // Trigger the change event to update shipping fee and totals
      countrySelector.dispatchEvent(new Event("change"));

      console.log("Restored shipping country selection:", selectedCountryId);
    } else {
      console.log("No shipping country selection found in localStorage");
    }
  }

  // Call the function to restore shipping country selection
  document.addEventListener("DOMContentLoaded", function () {
    setTimeout(restoreShippingCountry, 500); // Slight delay to ensure the country selector is loaded
  });

  // --- DOM Elements ---
  var cartItemsContainer = document.getElementById("cartItems");
  var installationRow = document.querySelector(".installation-fee-row");
  var productsSubtotalDisplay = document.getElementById("productsSubtotal");
  var shippingFeeDisplay = document.getElementById("shippingFee");
  var installationFeeTotalDisplay = document.getElementById(
    "installationFeeTotal"
  );
  var cartSubtotalDisplay = document.getElementById("cartSubtotal"); // Consider renaming label in HTML to "Subtotal (Products + Install)"
  var cartTotalDisplay = document.getElementById("cartTotal");

  // ****** ADDED/MODIFIED: DOM Elements for Shipping and Summary Visibility ******
  var countrySelector = document.getElementById("countrySelector"); // The dropdown
  var countrySelectorRow = document.querySelector(".country-selector-row"); // The div containing the selector
  var cartSummarySection = document.querySelector(".cart-summary"); // The whole summary div container
  // ****** END ADDED/MODIFIED ******

  // ****** Modal DOM Elements ******
  var modal = document.getElementById("summaryModal");
  var summaryContent = document.getElementById("summaryContent");
  var summaryItemsContainer = summaryContent?.querySelector(".summary-items");
  var summaryTotalsContainer = summaryContent?.querySelector(".summary-totals");
  var modalEmptyCartMsg = summaryContent?.querySelector(".modal-empty-cart");
  var confirmBtn = document.getElementById("confirmCheckout");
  var cancelBtn = modal?.querySelector(".cancel-btn");
  var closeBtn = modal?.querySelector(".close-modal");
  var proceedBtn = document.getElementById("proceedToSummaryBtn");
  var whatsappBtn = document.getElementById("whatsappOrderBtn");
  // ****** END ADDED ******

  // =========================================================================
  // == CALCULATE SHIPPING COST FUNCTION == (Enhanced for better persistence)
  // =========================================================================
  function calculateShippingCost() {
    // First try to get from localStorage
    const storedShippingFee = localStorage.getItem("shippingFee");
    if (storedShippingFee && !isNaN(parseFloat(storedShippingFee))) {
      console.log("Using shipping fee from localStorage:", storedShippingFee);
      return parseFloat(storedShippingFee);
    }

    // Then try to get from cookie
    const cookieValue = document.cookie
      .split("; ")
      .find((row) => row.startsWith("shippingFee="))
      ?.split("=")[1];
    if (cookieValue && !isNaN(parseFloat(cookieValue))) {
      console.log("Using shipping fee from cookie:", cookieValue);
      const fee = parseFloat(cookieValue);
      // Store in localStorage for future use
      localStorage.setItem("shippingFee", fee.toString());
      return fee;
    }

    // If no stored value, try to get from country selector
    if (!countrySelector) {
      console.warn(
        "Country selector element ('#countrySelector') not found in the HTML."
      );
      return defaultShippingCost;
    }

    const selectedValue = countrySelector.value;
    if (!selectedValue || selectedValue === "") {
      // If "-- PLEASE SELECT AREA --" is selected, use the default cost from PHP
      return defaultShippingCost;
    }

    const selectedOptionElement =
      countrySelector.options[countrySelector.selectedIndex];
    if (!selectedOptionElement) {
      console.warn(
        "Could not find the selected option element for value:",
        selectedValue
      );
      return defaultShippingCost; // Fallback
    }

    const costAttribute = selectedOptionElement.dataset.cost;
    if (costAttribute && costAttribute !== "") {
      // Specific cost defined for this area
      const fee = parseFloat(costAttribute);

      // Store the fee for persistence
      localStorage.setItem("shippingFee", fee.toString());
      document.cookie = `shippingFee=${fee}; path=/; max-age=86400`; // 24 hours

      console.log("Using shipping fee from country selector:", fee);
      return fee;
    } else {
      // No specific cost (or data-cost missing/empty), use the default
      console.warn(
        `No specific cost (data-cost) found for selected option value ${selectedValue}. Using default: ${defaultShippingCost}`
      );

      // Store the default fee for persistence
      localStorage.setItem("shippingFee", defaultShippingCost.toString());
      document.cookie = `shippingFee=${defaultShippingCost}; path=/; max-age=86400`; // 24 hours

      return defaultShippingCost;
    }
  }

  // =========================================================================
  // == UPDATE SESSION TOTALS FUNCTION ==
  // =========================================================================
  function updateSessionTotals() {
    // Get current totals
    const productsSubtotal = cart.reduce((sum, item) => {
      return sum + parseFloat(item.price || 0) * Number(item.quantity || 1);
    }, 0);

    const currentShippingFee = calculateShippingCost();

    const installationFeeTotal = cart.reduce((sum, item) => {
      // Get product-specific installation fee or use default
      const productId = item.product_id || item.id;

      // Debug: Log the product ID and installation fee
      console.log("Session - Product ID:", productId);
      console.log(
        "Session - Product installation fees:",
        productInstallationFees
      );
      console.log(
        "Session - Product-specific fee:",
        productInstallationFees[productId]
      );
      console.log("Session - Default fee:", defaultInstallationFee);

      const productFee =
        productId &&
        productInstallationFees &&
        productInstallationFees[productId]
          ? productInstallationFees[productId]
          : defaultInstallationFee;

      console.log("Session - Using fee:", productFee);

      return sum + (item.installation ? productFee : 0);
    }, 0);

    const grandTotal =
      productsSubtotal + currentShippingFee + installationFeeTotal;

    // Update PHP session with current totals
    fetch("cart_totals.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        products_subtotal: productsSubtotal.toString(),
        shipping_fee: currentShippingFee.toString(),
        installation_fee: installationFeeTotal.toString(),
        final_total: grandTotal.toString(),
      }),
    }).catch((error) => console.error("Error updating session totals:", error));
  }

  // =========================================================================
  // == UPDATE CART TOTALS FUNCTION ==
  // =========================================================================
  // Define the function in the global scope so it can be called from outside
  window.updateCartTotals = function () {
    const shippingFee = parseFloat(localStorage.getItem("shippingFee")) || 0;
    const productsSubtotal =
      parseFloat(document.getElementById("productsSubtotal")?.textContent) || 0;
    const installationFeeTotal =
      parseFloat(
        document.getElementById("installationFeeTotal")?.textContent
      ) || 0;

    // Update shipping fee display
    const shippingFeeElement = document.getElementById("shippingFee");
    if (shippingFeeElement) {
      shippingFeeElement.textContent = formatPrice(shippingFee);
    }

    // Calculate and update total
    const total = productsSubtotal + shippingFee + installationFeeTotal;
    const cartTotalElement = document.getElementById("cartTotal");
    if (cartTotalElement) {
      cartTotalElement.textContent = formatPrice(total);
    }

    // Update hidden payment amount if it exists
    const paymentAmountInput = document.getElementById("paymentAmount");
    if (paymentAmountInput) {
      paymentAmountInput.value = total;
    }
  };

  // We can use window.updateCartTotals directly since it's globally accessible

  // =========================================================================
  // == RENDER CART TABLE FUNCTION ==
  // =========================================================================
  function renderCart() {
    cartItemsContainer.innerHTML = ""; // Clear previous cart items display
    var productsSubtotal = 0;
    var installationFeeTotal = 0;
    var hasInstallation = false;

    // --- Handle Empty Cart ---
    if (cart.length === 0) {
      var emptyRow = `
                <tr>
                    <td colspan="8">
                        <div class="empty-cart" style="text-align: center; padding: 40px 20px;">
                            <i class="fas fa-shopping-cart" style="font-size: 3rem; color: #ccc; margin-bottom: 20px;"></i>
                            <h3>Your cart is empty</h3>
                            <p style="color: #666; margin-bottom: 20px;">Looks like you haven't added any items yet.</p>
                            <a href="index.php#products" class="shop-btn" style="display: inline-block; background-color: var(--secondary); color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; transition: all 0.3s;">Shop Now</a>
                        </div>
                    </td>
                </tr>
            `;
      cartItemsContainer.innerHTML = emptyRow;

      // Hide summary and country selector if cart is empty
      if (cartSummarySection) cartSummarySection.style.display = "none";
      if (countrySelectorRow) countrySelectorRow.style.display = "none";
      // Reset totals display
      if (productsSubtotalDisplay) productsSubtotalDisplay.textContent = "0";
      if (shippingFeeDisplay) shippingFeeDisplay.textContent = "0";
      if (installationFeeTotalDisplay)
        installationFeeTotalDisplay.textContent = "0";
      if (installationRow) installationRow.style.display = "none";
      if (cartSubtotalDisplay) cartSubtotalDisplay.textContent = "0";
      if (cartTotalDisplay) cartTotalDisplay.textContent = "0";
    } else {
      // Show summary and country selector if cart has items
      if (cartSummarySection) cartSummarySection.style.display = "block"; // Or your default
      if (countrySelectorRow) countrySelectorRow.style.display = "block"; // Or 'flex', etc.

      // --- Loop Through Cart Items and Render Rows ---
      cart.forEach(function (item, index) {
        // Get the product ID from either product_id or id property
        const productId = item.product_id || item.id;

        // Ensure item has an id property for consistency
        if (!item.id && productId) {
          item.id = productId;
        }

        const baseProduct = productsMap[productId]; // Get base product data

        // --- Determine Item Details (Name, Price, Photo) with Fallbacks ---
        var itemName = item.name || baseProduct?.p_name || "Unknown Product";
        var itemPrice = parseFloat(
          item.price || baseProduct?.p_current_price || 0
        );

        let photoFilename = null;
        let photoBasePath = "../assets/uploads/";
        if (item.variation_id && item.variation_id != 0) {
          photoBasePath = "../assets/uploads/product_variations/";
          photoFilename = item.photo;
        } else {
          photoBasePath = "../assets/uploads/";
          photoFilename = item.photo || baseProduct?.p_featured_photo; // Fallback added
        }
        let finalPhotoSrc = "../assets/uploads/placeholder.png";
        if (photoFilename) {
          finalPhotoSrc = `${photoBasePath}${photoFilename}`;
        }
        var itemPhoto = finalPhotoSrc;

        // Stock Calculation logic (remains the same)
        // ...

        // --- Subtotal & Fee Calculations ---
        var quantity = Number(item.quantity || 1);
        var itemSubtotal = itemPrice * quantity;
        productsSubtotal += itemSubtotal;

        if (item.installation) {
          // Get product-specific installation fee or use default
          const productId = item.product_id || item.id;

          // Debug: Log the product ID and installation fee
          console.log("Cart - Product ID:", productId);
          console.log(
            "Cart - Product installation fees:",
            productInstallationFees
          );
          console.log(
            "Cart - Product-specific fee:",
            productInstallationFees[productId]
          );
          console.log("Cart - Default fee:", defaultInstallationFee);

          const productFee =
            productId &&
            productInstallationFees &&
            productInstallationFees[productId]
              ? productInstallationFees[productId]
              : defaultInstallationFee;

          console.log("Cart - Using fee:", productFee);

          installationFeeTotal += productFee;
          hasInstallation = true;
        }

        // --- Create Table Row Element ---
        var tr = document.createElement("tr");
        tr.dataset.index = index;

        // 1. Image Cell
        var tdImg = document.createElement("td");
        tdImg.setAttribute("data-label", "Product");
        var img = document.createElement("img");
        img.src = itemPhoto;
        img.alt = itemName;
        img.className = "cart-img";
        tdImg.appendChild(img);
        tr.appendChild(tdImg);

        // 2. Details Cell (Name, Color, Size/Variation)
        var tdName = document.createElement("td");
        tdName.setAttribute("data-label", "Details");
        var nameDiv = document.createElement("div");

        // ****** MODIFIED: Variation Link Check ******
        var productIdForLink = item.id; // Get base product ID from cart item
        if (productIdForLink) {
          // Check if ID exists and is not null/undefined/0
          var aName = document.createElement("a");
          aName.href = "product_detail.php?id=" + productIdForLink; // Link to base product page
          aName.textContent = itemName;
          aName.className = "product-name";
          nameDiv.appendChild(aName);
        } else {
          // If no valid ID, just display the name without a link
          var nameSpan = document.createElement("span");
          nameSpan.textContent = itemName;
          nameSpan.className = "product-name"; // Keep styling
          nameDiv.appendChild(nameSpan);
          console.warn(
            "Cart item at index",
            index,
            "is missing a valid product ID for linking.",
            item
          );
        }
        // ****** END MODIFIED ******

        // Display Color if available
        if (item.color_name) {
          var colorDiv = document.createElement("div");
          colorDiv.className = "product-color";
          colorDiv.textContent = "Color: " + item.color_name;
          if (item.color_code) {
            var colorPreview = document.createElement("span");
            colorPreview.className = "color-preview";
            colorPreview.style.backgroundColor = item.color_code;
            colorDiv.appendChild(colorPreview);
          }
          nameDiv.appendChild(colorDiv);
        }

        // Display Variation if available
        if (item.variation_name) {
          var variationDiv = document.createElement("div");
          variationDiv.className = "product-variation";
          var variationText = item.variation_name; // Default to name
          variationDiv.textContent = variationText; // Simplified display
          nameDiv.appendChild(variationDiv);
        }
        tdName.appendChild(nameDiv);
        tr.appendChild(tdName);

        // 3. Price Cell (Unchanged)
        var tdPrice = document.createElement("td");
        tdPrice.setAttribute("data-label", "Price");
        tdPrice.innerHTML = `<span class="currency-symbol">Tsh</span> <span class="price-amount">${itemPrice.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</span>`;
        tr.appendChild(tdPrice);

        // 4. Quantity Cell (with +/- buttons) - Assuming your existing complex logic here is mostly correct
        const tdQuantity = document.createElement("td");
        tdQuantity.setAttribute("data-label", "Quantity");
        const quantityDiv = document.createElement("div");
        quantityDiv.className = "quantity-selector";

        // Function to get available quantity (KEEP YOUR ASYNC FETCH LOGIC)
        const getAvailableQuantity = async (product) => {
          try {
            // For variation products
            if (product.variation_id && product.variation_id != 0) {
              // Check variation_id exists and is not 0
              const response = await fetch(
                `get_variation_stock.php?variation_id=${product.variation_id}`
              );
              if (response.ok) {
                const data = await response.json();
                // Ensure data exists and has the property before parsing
                return data && data.variation_qty
                  ? parseInt(data.variation_qty)
                  : 0;
              } else {
                console.warn(
                  `Failed to fetch stock for variation ${product.variation_id}: ${response.status}`
                );
              }
            }
            // For base products (item.id should exist)
            else if (product.id) {
              const response = await fetch(
                `get_product_stock.php?product_id=${product.id}`
              );
              if (response.ok) {
                const data = await response.json();
                // Ensure data exists and has the property before parsing
                return data && data.p_qty ? parseInt(data.p_qty) : 0;
              } else {
                console.warn(
                  `Failed to fetch stock for product ${product.id}: ${response.status}`
                );
              }
            } else {
              console.warn(
                "Cannot fetch stock, missing product ID or variation ID:",
                product
              );
            }
            return 0; // Fallback to 0
          } catch (error) {
            console.error("Error fetching stock data:", error);
            return 0;
          }
        };

        const btnDecrease = document.createElement("button");
        btnDecrease.className = "quantity-btn";
        btnDecrease.innerHTML = "&#8722;";
        btnDecrease.ariaLabel = "Decrease quantity";

        const spanQty = document.createElement("span");
        spanQty.className = "quantity-input";
        spanQty.textContent = quantity; // Use the already determined quantity
        spanQty.ariaLive = "polite";

        const btnIncrease = document.createElement("button");
        btnIncrease.className = "quantity-btn";
        btnIncrease.innerHTML = "&#43;";
        btnIncrease.ariaLabel = "Increase quantity";

        // Function to update button states
        const updateButtonStates = (currentQty, availableQty) => {
          btnDecrease.disabled = currentQty <= 1;
          btnIncrease.disabled = currentQty >= availableQty;
        };

        // Get available quantity and setup buttons
        getAvailableQuantity(item)
          .then((availableQty) => {
            // Initial state based on current quantity and fetched stock
            updateButtonStates(quantity, availableQty);

            // Decrease button handler
            btnDecrease.addEventListener("click", function () {
              // No need for async here if updateCartItemQuantity is sync
              let currentQuantity = parseInt(spanQty.textContent);
              if (currentQuantity > 1) {
                currentQuantity--;
                item.quantity = currentQuantity; // Update the item in the main cart array
                spanQty.textContent = currentQuantity;
                updateCartItemQuantity(item); // Update localStorage/session
                updateButtonStates(currentQuantity, availableQty); // Update buttons
                renderCart(); // Refresh totals
              }
            });

            // Increase button handler
            btnIncrease.addEventListener("click", async function () {
              // Needs async if getAvailableQuantity is async
              let currentQuantity = parseInt(spanQty.textContent);
              // Re-check stock at the moment of clicking increase
              const currentStock = await getAvailableQuantity(item);
              if (currentQuantity < currentStock) {
                currentQuantity++;
                item.quantity = currentQuantity; // Update the item in the main cart array
                spanQty.textContent = currentQuantity;
                updateCartItemQuantity(item); // Update localStorage/session
                updateButtonStates(currentQuantity, currentStock); // Update buttons based on potentially new stock value
                renderCart(); // Refresh totals
              } else {
                alert(`Only ${currentStock} available in stock.`);
                btnIncrease.disabled = true; // Disable button as we are at max stock
              }
            });
          })
          .catch((error) => {
            console.error(
              "Error setting up quantity buttons for item:",
              item,
              error
            );
            // Disable buttons if stock check fails
            btnDecrease.disabled = true;
            btnIncrease.disabled = true;
          });

        // Add elements to the quantity div
        quantityDiv.appendChild(btnDecrease);
        quantityDiv.appendChild(spanQty);
        quantityDiv.appendChild(btnIncrease);
        tdQuantity.appendChild(quantityDiv);
        tr.appendChild(tdQuantity);

        // Helper function to update cart item quantity in localStorage and trigger session update
        // Keep this function as you had it, ensuring saveCart() is called
        function updateCartItemQuantity(itemToUpdate) {
          // Read the latest cart from localStorage inside the function
          let currentCart = JSON.parse(localStorage.getItem("cart")) || [];
          const itemIndex = currentCart.findIndex((cartItem) => {
            // Match based on variation_id if it exists and is not 0, otherwise match on base id
            if (itemToUpdate.variation_id && itemToUpdate.variation_id != 0) {
              return (
                cartItem.id === itemToUpdate.id &&
                cartItem.variation_id === itemToUpdate.variation_id
              );
            }
            // Ensure we are matching only base products if the itemToUpdate is a base product
            // (cartItem.variation_id should be null, undefined or 0)
            return (
              cartItem.id === itemToUpdate.id &&
              (!cartItem.variation_id || cartItem.variation_id == 0)
            );
          });

          if (itemIndex !== -1) {
            currentCart[itemIndex].quantity = itemToUpdate.quantity; // Update the quantity
            localStorage.setItem("cart", JSON.stringify(currentCart)); // Save back to localStorage
            // Update the global 'cart' variable as well to reflect the change immediately for subsequent operations within this render cycle if needed
            cart = currentCart;
            saveCart(); // Trigger session update and header count update
          } else {
            console.error(
              "Could not find item in cart to update quantity:",
              itemToUpdate
            );
          }
        }

        // 5. Color Selector Cell (FIXED: Use item.product_id)
        var tdColor = document.createElement("td");
        tdColor.setAttribute("data-label", "Color");

        // --- Get Base Product Data using product_id ---
        const baseProductForColor = productsMap[item.product_id]; // <<< FIXED: Use product_id

        // --- Check if Base Product Data and Colors Exist ---
        if (!baseProductForColor) {
          // Defensive check: If baseProduct wasn't found
          console.warn(
            `Could not find base product data in productsMap for item ID: ${item.product_id}. Cannot display colors for cart item at index ${index}.`,
            item
          ); // <<< FIXED: Log product_id
          tdColor.textContent = "N/A";
        } else if (
          baseProductForColor.colors &&
          baseProductForColor.colors.length > 0
        ) {
          // ... (rest of the color cell logic remains the same) ...
          var colorWrapper = document.createElement("div");
          colorWrapper.className = "color-select-wrapper";
          var colorSelect = document.createElement("select");
          colorSelect.className = "color-select";
          colorSelect.dataset.itemIndex = index;

          const currentItemColorId = item.color_id
            ? parseInt(item.color_id)
            : null;

          var defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.textContent = "Select";
          defaultOption.disabled = !!currentItemColorId;
          defaultOption.selected = !currentItemColorId;
          colorSelect.appendChild(defaultOption);

          baseProductForColor.colors.forEach(function (color) {
            var option = document.createElement("option");
            option.value = color.color_id;
            option.textContent = color.color_name;
            option.dataset.colorCode = color.color_code;
            option.selected = currentItemColorId === parseInt(color.color_id);
            colorSelect.appendChild(option);
          });

          colorSelect.onchange = function () {
            updateColorSelection(this);
          };
          colorWrapper.appendChild(colorSelect);

          if (item.color_code) {
            var colorPreview = document.createElement("span");
            colorPreview.className = "color-preview";
            colorPreview.style.backgroundColor = item.color_code;
            colorWrapper.appendChild(colorPreview);
          }
          tdColor.appendChild(colorWrapper);
        } else {
          tdColor.textContent = "N/A";
        }
        tr.appendChild(tdColor);
        // --- End of Color Cell ---

        // 6. Installation Checkbox Cell (Unchanged)
        var tdInstallation = document.createElement("td");
        tdInstallation.setAttribute("data-label", "Installation");
        var installationDiv = document.createElement("div");
        installationDiv.className = "installation-option";
        var installationCheckbox = document.createElement("input");
        installationCheckbox.type = "checkbox";
        installationCheckbox.id = `installation-${index}`;
        installationCheckbox.checked = item.installation || false;
        installationCheckbox.dataset.itemIndex = index;
        installationCheckbox.onchange = function () {
          updateInstallation(this);
        };
        // Get product-specific installation fee for this product
        // Use the productId that was already defined above
        const itemProductFee =
          productId && productInstallationFees[productId]
            ? productInstallationFees[productId]
            : defaultInstallationFee;

        var installationLabel = document.createElement("label");
        installationLabel.htmlFor = installationCheckbox.id;
        // Format the price for display
        const formattedPrice = itemProductFee.toLocaleString("en-TZ", {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        });
        installationLabel.textContent = `Add (TSH ${formattedPrice})`;
        installationDiv.appendChild(installationCheckbox);
        installationDiv.appendChild(installationLabel);
        tdInstallation.appendChild(installationDiv);
        tr.appendChild(tdInstallation);

        // 7. Subtotal Cell (Unchanged)
        var tdSubtotal = document.createElement("td");
        tdSubtotal.setAttribute("data-label", "Subtotal");
        tdSubtotal.innerHTML = `<span class="currency-symbol">Tsh</span> <span class="price-amount">${itemSubtotal.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</span>`;
        tr.appendChild(tdSubtotal);

        // 8. Action Cell (Remove Button) (Unchanged)
        var tdAction = document.createElement("td");
        tdAction.setAttribute("data-label", "Action");
        var btnRemove = document.createElement("button");
        btnRemove.innerHTML = '<i class="fas fa-trash-alt"></i> Remove'; // Icon + Text
        btnRemove.className = "action-btn remove-btn"; // CSS classes
        btnRemove.title = "Remove Item"; // Tooltip
        btnRemove.onclick = function () {
          removeItem(index);
        }; // Call remove function on click
        tdAction.appendChild(btnRemove);
        tr.appendChild(tdAction);

        // --- Append the completed row to the table body ---
        cartItemsContainer.appendChild(tr);
      }); // End cart.forEach loop
    } // End else (cart not empty)

    // --- Update Price Summary Section ---

    // Calculate shipping cost AFTER loop, based on current dropdown selection
    const currentShippingFee = cart.length > 0 ? calculateShippingCost() : 0;

    if (productsSubtotalDisplay)
      productsSubtotalDisplay.textContent = productsSubtotal.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      );
    if (shippingFeeDisplay)
      shippingFeeDisplay.textContent = currentShippingFee.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      );

    // Handle Installation Fee Display
    if (installationRow) {
      if (hasInstallation && cart.length > 0) {
        installationRow.style.display = "flex"; // Or 'table-row'
        if (installationFeeTotalDisplay)
          installationFeeTotalDisplay.textContent =
            installationFeeTotal.toLocaleString("en-TZ", {
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            });
      } else {
        installationRow.style.display = "none";
      }
    }

    // Calculate Grand Total
    const applicableInstallationFee = hasInstallation
      ? installationFeeTotal
      : 0;
    var grandTotal =
      productsSubtotal + currentShippingFee + applicableInstallationFee;

    // Update "Subtotal" (Products + Installation)
    if (cartSubtotalDisplay)
      cartSubtotalDisplay.textContent = (
        productsSubtotal + applicableInstallationFee
      ).toLocaleString("en-TZ", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      });
    // Update Grand Total
    if (cartTotalDisplay)
      cartTotalDisplay.textContent = grandTotal.toLocaleString("en-TZ", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      });

    // Update cart count in the header
    updateCartCountHeader();

    // After updating all totals, sync with session
    updateSessionTotals();
  } // End renderCart function

  // =========================================================================
  // == HELPER FUNCTIONS == (Mostly Unchanged - Check updateColorSelection/updateInstallation if needed)
  // =========================================================================

  // --- Update Color Selection ---
  function updateColorSelection(selectElement) {
    var index = parseInt(selectElement.dataset.itemIndex);
    // Use the global 'cart' variable which should be up-to-date
    if (cart[index]) {
      var selectedOption = selectElement.options[selectElement.selectedIndex];
      cart[index].color_id = selectElement.value
        ? parseInt(selectElement.value)
        : null;
      cart[index].color_name =
        selectedOption.textContent === "Select"
          ? null
          : selectedOption.textContent;
      cart[index].color_code = selectedOption.dataset.colorCode || null;
      saveCart(); // Save changes to localStorage and session
      renderCart(); // Redraw the cart
    }
  }

  // --- Update Installation Status ---
  function updateInstallation(checkboxElement) {
    var index = parseInt(checkboxElement.dataset.itemIndex);
    // Use the global 'cart' variable
    if (cart[index]) {
      cart[index].installation = checkboxElement.checked;

      // Debug: Log the product ID and its installation fee
      const productId = cart[index].product_id || cart[index].id;
      console.log("Product ID for installation:", productId);
      console.log("Installation checked:", checkboxElement.checked);
      console.log(
        "Product-specific installation fee:",
        productInstallationFees[productId]
      );
      console.log("Default installation fee:", defaultInstallationFee);
      console.log(
        "Fee to be used:",
        productId && productInstallationFees[productId]
          ? productInstallationFees[productId]
          : defaultInstallationFee
      );

      saveCart();
      renderCart();
    }
  }

  // --- Remove Item from Cart ---
  function removeItem(index) {
    // Create a custom confirmation alert
    const alertId = 'remove-confirm-' + Date.now();
    const alertContainer = document.getElementById('alertContainer');

    // Create alert element
    const alert = document.createElement('div');
    alert.className = 'alert alert-warning';
    alert.id = alertId;

    // Create alert content
    alert.innerHTML = `
      <div class="alert-icon">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      </div>
      <div class="alert-content">
        <div class="alert-title">Remove Item</div>
        <div class="alert-message">Are you sure you want to remove this item from your cart?</div>
        <div class="alert-buttons mt-3 flex justify-end gap-2">
          <button class="alert-btn cancel-btn px-4 py-2 rounded-md bg-gray-200 hover:bg-gray-300 text-gray-800" onclick="document.getElementById('${alertId}').remove()">Cancel</button>
          <button class="alert-btn confirm-btn px-4 py-2 rounded-md bg-red-600 hover:bg-red-700 text-white" onclick="confirmRemoveItem(${index}, '${alertId}')">Remove</button>
        </div>
      </div>
    `;

    // Add to container and show
    alertContainer.appendChild(alert);
    setTimeout(() => alert.classList.add('show'), 10);
  }

  // Helper function to handle the actual removal after confirmation
  window.confirmRemoveItem = function(index, alertId) {
    // Get the item to be removed
    const itemToRemove = cart[index];
    if (!itemToRemove) {
      console.error("Item not found in cart array at index:", index);
      showAlert('Error', 'Failed to remove item. Please try again.', 'error');
      return;
    }

    // Generate the cart key consistently with how it's stored in the session
    const productId = itemToRemove.product_id || itemToRemove.id;
    const variationId = itemToRemove.variation_id && itemToRemove.variation_id != 0 ? itemToRemove.variation_id : '0';
    const colorId = itemToRemove.color_id || '0';
    const cartKey = `${productId}-${variationId}-${colorId}`;

    // Remove from local cart array
    cart.splice(index, 1);

    // Save to localStorage
    localStorage.setItem("cart", JSON.stringify(cart));

    // Update cart count in header
    updateCartCountHeader();

    // Remove from server session
    fetch("update_cart_session.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "X-Requested-With": "XMLHttpRequest"
      },
      body: `action=remove_item&cart_key=${cartKey}`
    })
    .then(response => response.json())
    .then(data => {
      if (data.status === 'success') {
        showAlert('Success', 'Item removed from cart successfully.', 'success');
        // Redraw the cart
        renderCart();
      } else {
        console.error("Failed to remove item from server session:", data.message);
        showAlert('Error', 'Failed to remove item from cart. Please try again.', 'error');
        // Restore the item in the local cart if server removal failed
        cart.splice(index, 0, itemToRemove);
        localStorage.setItem("cart", JSON.stringify(cart));
        renderCart();
      }
    })
    .catch(error => {
      console.error("Error removing item from server session:", error);
      showAlert('Error', 'An error occurred while removing the item. Please try again.', 'error');
      // Restore the item in the local cart if server removal failed
      cart.splice(index, 0, itemToRemove);
      localStorage.setItem("cart", JSON.stringify(cart));
      renderCart();
    });

    // Remove the confirmation alert
    const alert = document.getElementById(alertId);
    if (alert) {
      alert.remove();
    }
  };

  // --- Save Cart to localStorage & Update Session ---
  function saveCart() {
    // Use the global 'cart' variable which should reflect the latest state

    // Ensure all cart items have both id and product_id properties for compatibility
    cart.forEach((item) => {
      if (item.product_id && !item.id) {
        item.id = item.product_id;
      } else if (item.id && !item.product_id) {
        item.product_id = item.id;
      }
    });

    localStorage.setItem("cart", JSON.stringify(cart));
    updateCartSession(); // Sync with PHP session
    updateCartCountHeader(); // Update header count
  }

  // --- Update PHP Session via Fetch --- (Ensure structure matches PHP expectation)
  function updateCartSession() {
    // If cart is empty, clear the session
    if (cart.length === 0) {
      // Use sync_cart.php to completely replace the cart
      fetch("sync_cart.php", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ cart: {} }), // Empty object to clear the cart
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.status !== "success") {
            console.error("Failed to clear cart in session:", data.message);
          }
        })
        .catch((error) => {
          console.error("Error clearing cart in session:", error);
        });
      return; // Exit early
    }

    // For non-empty cart, convert to the format expected by the server
    const cartForPhpSession = {};

    // Use the global 'cart' variable
    cart.forEach((item) => {
      // Get the product ID from either product_id or id property
      const productId = item.product_id || item.id;

      // Skip items without a valid product ID
      if (!productId) {
        console.warn("Skipping cart item without product ID:", item);
        return;
      }

      // Ensure variation_id is treated consistently (e.g., 0 or null for base products)
      const variationId =
        item.variation_id && item.variation_id != 0 ? item.variation_id : 0;
      const key = `${productId}-${variationId}`;
      const baseProduct = productsMap[productId];

      let sessionItemName = item.name || baseProduct?.p_name || "Unknown";
      let sessionItemPrice = item.price;
      if (sessionItemPrice === undefined || sessionItemPrice === null) {
        sessionItemPrice = baseProduct?.p_current_price;
      }
      sessionItemPrice = sessionItemPrice ?? 0; // Default to 0 if still missing

      let photoFilenameForSession = item.photo;
      // Use variation photo if variation_id is valid, otherwise fallback to base photo or featured photo
      if (variationId && variationId != 0) {
        // We already assigned item.photo correctly based on variation earlier if it exists
        photoFilenameForSession = item.photo || null; // Send null if variation has no specific photo? Or handle in PHP?
      } else {
        // Base product: use item.photo if set (e.g., from initial add), fallback to global product data's featured photo
        photoFilenameForSession =
          item.photo || baseProduct?.p_featured_photo || null;
      }

      cartForPhpSession[key] = {
        product_id: productId,
        variation_id: variationId, // Send 0 for base products
        quantity: Number(item.quantity || 1),
        price: parseFloat(sessionItemPrice),
        name: sessionItemName,
        photo: photoFilenameForSession || "", // Send determined filename or empty string
        color_id: item.color_id || null,
        color_name: item.color_name || null,
        color_code: item.color_code || null,
        variation_name: item.variation_name || null, // <<< ADDED: Send variation name if exists
        installation: item.installation ? 1 : 0,
      };
    });

    // Use sync_cart.php to completely replace the cart
    fetch("sync_cart.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ cart: cartForPhpSession }),
    })
      .then((response) => {
        if (!response.ok) {
          return response.text().then((text) => {
            throw new Error(
              `HTTP error! status: ${response.status}, message: ${
                text || "Server error"
              }`
            );
          });
        }
        return response.json();
      })
      .then((data) => {
        if (data.status !== "success") {
          console.error("Failed to update PHP session:", data.message);
        } else {
          // Optional: console.log('PHP session updated successfully.');
        }
      })
      .catch((error) => {
        console.error("Error in updateCartSession fetch:", error);
      });
  }

  // --- Update Cart Count in Header ---
  function updateCartCountHeader() {
    // Use the global 'cart' variable
    var totalCount = cart.reduce(
      (sum, item) => sum + Number(item.quantity || 0),
      0
    );
    document.querySelectorAll(".cart-count").forEach((elem) => {
      elem.textContent = totalCount;
      elem.style.display = totalCount > 0 ? "inline-block" : "none";
    });
  }

  // --- Clear Cart Function (Global) ---
  window.clearCartData = function() {
    // Clear the global cart variable
    cart = [];

    // Clear localStorage
    localStorage.setItem('cart', JSON.stringify([]));

    // Update cart count immediately
    updateCartCountHeader();

    // Re-render cart display
    renderCart();

    // Clear server session
    updateCartSession();

    console.log('Cart cleared successfully');
  };

  // --- Get Cart from localStorage Function (Global) ---
  window.getCartFromLocalStorage = function() {
    try {
      const cartData = localStorage.getItem('cart');
      return cartData ? JSON.parse(cartData) : [];
    } catch (e) {
      console.error("Error reading cart from localStorage:", e);
      return [];
    }
  };

  // =========================================================================
  // == ORDER SUMMARY MODAL LOGIC == (MODIFIED FOR SHIPPING)
  // =========================================================================
  function showSummaryModal() {
    var currentCart = JSON.parse(localStorage.getItem("cart")) || []; // Read fresh cart

    if (
      !modal ||
      !summaryItemsContainer ||
      !summaryTotalsContainer ||
      !modalEmptyCartMsg ||
      !confirmBtn
    ) {
      console.error("Modal summary elements not found!");
      return;
    }

    if (currentCart.length === 0) {
      summaryItemsContainer.innerHTML = "";
      summaryTotalsContainer.style.display = "none";
      modalEmptyCartMsg.style.display = "block";
      confirmBtn.style.display = "none";
    } else {
      modalEmptyCartMsg.style.display = "none";
      var htmlItems = "";
      var modalProductsSubtotal = 0;
      var modalInstallationFeeTotal = 0;
      var modalHasInstallation = false;

      // Get shipping cost for modal based on the *current selection in the main page dropdown*
      const modalShippingFee = calculateShippingCost(); // Uses the function that reads the dropdown

      currentCart.forEach(function (item) {
        const baseProduct = productsMap[item.id];
        const displayName =
          item.name || baseProduct?.p_name || "Unknown Product";
        const itemPrice = parseFloat(
          item.price || baseProduct?.p_current_price || 0
        );

        // Determine Photo logic (same as renderCart)
        let photoFilenameModal = null;
        let photoBasePathModal = "../assets/uploads/";
        if (item.variation_id && item.variation_id != 0) {
          photoBasePathModal = "../assets/uploads/product_variations/";
          photoFilenameModal = item.photo;
        } else {
          photoBasePathModal = "../assets/uploads/";
          photoFilenameModal = item.photo || baseProduct?.p_featured_photo;
        }
        let finalPhotoSrcModal = "../assets/uploads/placeholder.png";
        if (photoFilenameModal) {
          finalPhotoSrcModal = `${photoBasePathModal}${photoFilenameModal}`;
        }
        const photoPath = finalPhotoSrcModal;

        const quantity = Number(item.quantity || 1);
        const itemSubtotal = itemPrice * quantity;
        modalProductsSubtotal += itemSubtotal;

        if (item.installation) {
          // Get product-specific installation fee or use default
          const productId = item.product_id || item.id;

          // Debug: Log the product ID and installation fee
          console.log("Modal - Product ID:", productId);
          console.log(
            "Modal - Product installation fees:",
            productInstallationFees
          );
          console.log(
            "Modal - Product-specific fee:",
            productInstallationFees[productId]
          );
          console.log("Modal - Default fee:", defaultInstallationFee);

          const productFee =
            productId &&
            productInstallationFees &&
            productInstallationFees[productId]
              ? productInstallationFees[productId]
              : defaultInstallationFee;

          console.log("Modal - Using fee:", productFee);

          modalInstallationFeeTotal += productFee;
          modalHasInstallation = true;
        }

        // Build HTML for item (Keep your structure)
        htmlItems += `<div class="summary-item">`;
        htmlItems += `<div class="summary-item-details">`;
        htmlItems += `<img src="${photoPath}" class="summary-item-image" alt="${displayName}">`;
        htmlItems += `<div class="summary-item-text">`;
        htmlItems += `<div class="summary-item-title">${displayName}</div>`;
        htmlItems += `<div class="summary-item-meta">`;
        htmlItems += `<div>Qty: ${item.quantity}</div>`;
        if (item.color_name) {
          htmlItems += `<div>Color: ${item.color_name}${
            item.color_code
              ? `<span class="summary-color" style="background-color:${item.color_code}"></span>`
              : ""
          }</div>`;
        }
        // If variation name exists, display it
        if (item.variation_name) {
          htmlItems += `<div>Variation: ${item.variation_name}</div>`;
        } // Display variation name
        if (item.installation) {
          htmlItems += `<div style="color:var(--secondary); font-weight:500;"><i class="fas fa-tools"></i> Installation Added</div>`;
        }
        htmlItems += "</div>"; // close meta
        htmlItems += "</div>"; // close text
        htmlItems += "</div>"; // close details
        htmlItems += `<div class="summary-item-price">Tsh ${itemSubtotal.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</div>`;
        htmlItems += `</div>`; // close item
      });

      summaryItemsContainer.innerHTML = htmlItems;

      // --- Build Totals HTML ---
      var htmlTotals = "";
      htmlTotals += `<div class="summary-total-row"><span>Products Subtotal:</span><span>Tsh ${modalProductsSubtotal.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      )}</span></div>`;

      if (modalHasInstallation) {
        htmlTotals += `<div class="summary-total-row"><span>Installation Fee:</span><span>Tsh ${modalInstallationFeeTotal.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</span></div>`;
      }
      // Show shipping row using the cost calculated from the main page selector
      htmlTotals += `<div class="summary-total-row"><span>Shipping Fee:</span><span>Tsh ${modalShippingFee.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      )}</span></div>`;

      const applicableModalInstallationFee = modalHasInstallation
        ? modalInstallationFeeTotal
        : 0;
      var modalGrandTotal =
        modalProductsSubtotal +
        modalShippingFee +
        applicableModalInstallationFee;
      htmlTotals += `<div class="summary-total-row summary-grand-total"><span>Total:</span><span>Tsh ${modalGrandTotal.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      )}</span></div>`;

      summaryTotalsContainer.innerHTML = htmlTotals;
      summaryTotalsContainer.style.display = "block";
      confirmBtn.style.display = "inline-block";
    }

    modal.style.display = "block"; // Display the modal
  } // End showSummaryModal

  // =========================================================================
  // == EVENT LISTENERS == (MODIFIED FOR SHIPPING PERSISTENCE & CHECKS)
  // =========================================================================

  // --- Listener for Country Selector Change ---
  if (countrySelector) {
    countrySelector.addEventListener("change", function () {
      const selectedIndex = this.selectedIndex;
      const countryId = this.value;

      // Check if there's a valid selected option
      if (selectedIndex >= 0 && selectedIndex < this.options.length) {
        const selectedCountry = this.options[selectedIndex];

        // Safely access dataset.cost with fallback
        let shippingFee = window.defaultShippingCost;
        if (
          selectedCountry &&
          selectedCountry.dataset &&
          selectedCountry.dataset.cost
        ) {
          shippingFee = selectedCountry.dataset.cost;
        }

        console.log("Country changed:", {
          countryId: countryId,
          selectedIndex: selectedIndex,
          shippingFee: shippingFee,
        });

        // Store in session via AJAX
        fetch("update_shipping.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: `country_id=${countryId}&shipping_fee=${shippingFee}`,
        });

        // Update local storage
        localStorage.setItem("selectedCountryId", countryId);
        localStorage.setItem("shippingFee", shippingFee);

        // Also set a cookie for the server to read
        document.cookie = `selectedCountryId=${countryId}; path=/; max-age=86400`; // 24 hours

        // Update display
        renderCart();
      } else {
        console.warn("Invalid selection index:", selectedIndex);
      }
    });
  } else {
    console.warn(
      "Country selector element not found, cannot attach change listener."
    );
  }

  // ****** ADDED: Set initial dropdown value based on retrieved selectedCountryId ******
  if (countrySelector && selectedCountryId) {
    countrySelector.value = selectedCountryId; // Set the dropdown to the stored/default value
    // Optional: Trigger change event manually if needed, but renderCart will calculate based on this value anyway
    countrySelector.dispatchEvent(new Event("change"));
  }
  // ****** END ADDED ******

  renderCart(); // Draw the initial cart table. This will now use the
  // selectedCountryId set above (from session/localStorage/default)
  // to calculate the initial shipping cost.

  // --- Listener for "Proceed to Checkout/Summary" Button ---
  if (proceedBtn) {
    proceedBtn.addEventListener("click", function (e) {
      e.preventDefault();
      // Use the global 'cart' variable
      if (cart.length === 0) {
        alert("Your cart is empty.");
        return;
      }

      // Check if a country/area is selected (value is not empty string)
      if (
        countrySelector &&
        (!countrySelector.value || countrySelector.value === "")
      ) {
        alert("PLEASE SELECT THE AREA before proceeding."); // Use the new text
        if (countrySelector.focus) countrySelector.focus();
        return;
      }

      // Ensure session is up-to-date BEFORE showing summary
      // updateCartSession(); // saveCart called within renderCart usually handles this
      // Show the modal after ensuring calculations/updates are likely done
      showSummaryModal();
      // setTimeout(showSummaryModal, 50); // Short delay might not be needed now
    });
  }

  // --- Listener for "Confirm & Checkout" Button inside Modal ---
  if (confirmBtn) {
    confirmBtn.addEventListener("click", function () {
      // Use the global 'cart' variable
      // Final check for country selection (redundant if proceedBtn check works, but safe)
      if (
        cart.length > 0 &&
        countrySelector &&
        (!countrySelector.value || countrySelector.value === "")
      ) {
        alert(
          "PLEASE SELECT THE AREA on the main cart page before confirming checkout."
        );
        if (modal) modal.style.display = "none"; // Close modal
        if (countrySelector && countrySelector.focus) countrySelector.focus();
        return;
      }

      // Login Check and Redirect Logic (Unchanged)
      if (!isLoggedIn) {
        console.log("User not logged in. Redirecting to login...");
        sessionStorage.setItem("redirectAfterLogin", "checkout.php");
        window.location.href = "login.php";
        fetch("cart_totals.php", {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: new URLSearchParams({
            shipping_fee: document.getElementById("shippingFee").textContent,
            installation_fee: document.getElementById("installationFeeTotal")
              .textContent,
            final_total: document.getElementById("cartTotal").textContent,
          }),
        }).then(() => {
          document.getElementById("checkoutForm").submit();
        });
      } else {
        console.log("User logged in. Proceeding to checkout...");

        // First, update the cart totals in the session
        // Get the values from the DOM elements
        const productsSubtotalElement =
          document.getElementById("productsSubtotal");
        const shippingFeeElement = document.getElementById("shippingFee");
        const installationFeeElement = document.getElementById(
          "installationFeeTotal"
        );
        const finalTotalElement = document.getElementById("cartTotal");

        // Extract the numeric values
        function extractNumericValue(element) {
          if (!element) return "0";
          const text = element.textContent.trim();
          // Remove currency symbols, commas, and other non-numeric characters
          return text.replace(/[^0-9.]/g, "");
        }

        const productsSubtotal = extractNumericValue(productsSubtotalElement);
        const shippingFee = extractNumericValue(shippingFeeElement);
        const installationFee = extractNumericValue(installationFeeElement);
        const finalTotal = extractNumericValue(finalTotalElement);

        console.log("Sending cart totals to server:", {
          products_subtotal: productsSubtotal,
          shipping_fee: shippingFee,
          installation_fee: installationFee,
          final_total: finalTotal,
        });

        // Show loading indicator
        const modal = document.getElementById("summaryModal");
        if (modal) {
          const modalContent = modal.querySelector(".modal-content");
          if (modalContent) {
            const loadingDiv = document.createElement("div");
            loadingDiv.id = "checkoutLoading";
            loadingDiv.style.textAlign = "center";
            loadingDiv.style.padding = "20px";
            loadingDiv.innerHTML = `
              <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
              <p style="margin-top: 10px;">Processing your order...</p>
              <style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>
            `;

            // Add loading indicator to modal
            modalContent.appendChild(loadingDiv);

            // Disable buttons
            const confirmBtn = document.getElementById("confirmCheckout");
            const cancelBtn = document.querySelector(".modal-btn.cancel-btn");
            if (confirmBtn) confirmBtn.disabled = true;
            if (cancelBtn) cancelBtn.disabled = true;
          }
        }

        // Make sure we have the shipping country ID in a cookie
        const selectedCountryId =
          localStorage.getItem("selectedCountryId") || "";
        document.cookie = `selectedCountryId=${selectedCountryId}; path=/; max-age=86400`; // 24 hours

        // Also ensure we have the shipping fee in the session
        const shippingFeeValue = localStorage.getItem("shippingFee") || "0";

        // First, update the shipping information
        fetch("update_shipping.php", {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: `country_id=${selectedCountryId}&shipping_fee=${shippingFeeValue}`,
        })
          .then((response) => response.json())
          .then(() => {
            console.log(
              "Shipping information updated, now processing checkout"
            );

            // Get cart data from localStorage (use global cart variable)
            const cartData = cart;

            // Convert cart to PHP session format
            const cartForPhpSession = {};
            cartData.forEach((item, index) => {
              const key = `${item.product_id || item.id}-${item.variation_id || 0}`;
              cartForPhpSession[key] = {
                product_id: item.product_id || item.id,
                variation_id: item.variation_id || null,
                quantity: Number(item.quantity || 1),
                price: parseFloat(item.price || 0),
                name: item.name || '',
                photo: item.photo || '',
                color_id: item.color_id || null,
                color_name: item.color_name || null,
                installation: item.installation ? 1 : 0
              };
            });

            // Use our new checkout handler
            return fetch("checkout_handler.php", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                products_subtotal: productsSubtotal,
                shipping_fee: shippingFee,
                installation_fee: installationFee,
                final_total: finalTotal,
                country_id: selectedCountryId,
                cart: cartForPhpSession, // Include cart data
              }),
            });
          })
          .then((response) => response.json())
          .then((data) => {
            console.log("Checkout response:", data);

            if (data.status === "success") {
              // Redirect to payment page
              window.location.href = data.redirect;
            } else {
              // Show error message
              alert(
                data.message ||
                  "There was a problem processing your order. Please try again."
              );

              // Remove loading indicator
              const loadingDiv = document.getElementById("checkoutLoading");
              if (loadingDiv) loadingDiv.remove();

              // Re-enable buttons
              const confirmBtn = document.getElementById("confirmCheckout");
              const cancelBtn = document.querySelector(".modal-btn.cancel-btn");
              if (confirmBtn) confirmBtn.disabled = false;
              if (cancelBtn) cancelBtn.disabled = false;

              // If there's a redirect, follow it
              if (data.redirect) {
                window.location.href = data.redirect;
              }
            }
          })
          .catch((error) => {
            console.error("Error during checkout:", error);
            alert(
              "There was a problem processing your order. Please try again."
            );

            // Remove loading indicator
            const loadingDiv = document.getElementById("checkoutLoading");
            if (loadingDiv) loadingDiv.remove();

            // Re-enable buttons
            const confirmBtn = document.getElementById("confirmCheckout");
            const cancelBtn = document.querySelector(".modal-btn.cancel-btn");
            if (confirmBtn) confirmBtn.disabled = false;
            if (cancelBtn) cancelBtn.disabled = false;
          });
      }
    });
  }

  // --- Listener for WhatsApp Order Button ---
  if (whatsappBtn) {
    whatsappBtn.addEventListener("click", async function (e) {
      e.preventDefault();

      // Check if cart is empty
      if (cart.length === 0) {
        showAlert('Error', 'Your cart is empty.', 'error');
        return;
      }

      // Check if user is logged in
      if (!isLoggedIn) {
        showAlert('Info', 'Please login to place an order.', 'info');
        sessionStorage.setItem("redirectAfterLogin", "cart.php");
        window.location.href = "login.php";
        return;
      }

      // Check if shipping country is selected
      const countrySelector = document.getElementById('countrySelector');
      if (!countrySelector || !countrySelector.value || countrySelector.value === "") {
        showAlert('Warning', 'Please select your shipping location first.', 'warning');
        if (countrySelector && countrySelector.focus) countrySelector.focus();
        return;
      }

      // Show loading state
      const originalText = whatsappBtn.innerHTML;
      whatsappBtn.disabled = true;
      whatsappBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

      try {
        // Collect order data
        const orderData = {
          products_subtotal: extractNumericValue(document.getElementById("productsSubtotal")),
          shipping_fee: extractNumericValue(document.getElementById("shippingFee")),
          installation_fee: extractNumericValue(document.getElementById("installationFeeTotal")),
          final_total: extractNumericValue(document.getElementById("cartTotal")),
          country_id: countrySelector.value
        };

        // Helper function to extract numeric values
        function extractNumericValue(element) {
          if (!element) return "0";
          const text = element.textContent.trim();
          return text.replace(/[^0-9.]/g, "");
        }

        // Send order to server
        const response = await fetch('whatsapp_order.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(orderData)
        });

        const result = await response.json();

        if (result.success) {
          // Generate WhatsApp URL and redirect
          const whatsappURL = generateWhatsAppURL(result.order_data);

          // Show success message
          showAlert('Success', 'Order saved! Redirecting to WhatsApp...', 'success');

          // Clear cart after successful order
          if (typeof window.clearCartData === 'function') {
            window.clearCartData();
          }

          // Redirect to WhatsApp after a short delay
          setTimeout(() => {
            window.open(whatsappURL, '_blank');
            // Optionally redirect to order confirmation page
            // window.location.href = 'order_success.php?order_id=' + result.order_data.order_id;
          }, 1500);

        } else {
          showAlert('Error', result.message || 'Failed to process order', 'error');
        }

      } catch (error) {
        console.error('WhatsApp order error:', error);
        showAlert('Error', 'Failed to process order. Please try again.', 'error');
      } finally {
        // Restore button state
        whatsappBtn.disabled = false;
        whatsappBtn.innerHTML = originalText;
      }
    });
  }

  // --- Listeners for Closing the Modal --- (Unchanged)
  if (closeBtn) {
    closeBtn.onclick = function () {
      if (modal) modal.style.display = "none";
    };
  }
  if (cancelBtn) {
    cancelBtn.onclick = function () {
      if (modal) modal.style.display = "none";
    };
  }
  window.onclick = function (event) {
    if (event.target == modal) {
      if (modal) modal.style.display = "none";
    }
  };

  // =========================================================================
  // == INITIAL ACTIONS ON PAGE LOAD == (MODIFIED FOR SHIPPING PERSISTENCE)
  // =========================================================================

  // Country selector initialization is already handled above, no need to duplicate it here

  renderCart(); // Draw the initial cart table. This will now use the
  // selectedCountryId set above (from session/localStorage/default)
  // to calculate the initial shipping cost.

  // Ensure cart count is updated immediately on page load
  updateCartCountHeader();
}); // End DOMContentLoaded Listener
