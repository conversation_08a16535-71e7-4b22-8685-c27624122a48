# php -- <PERSON><PERSON><PERSON> cPanel-generated handler, do not edit
# Set the "ea-php82" package as the default "PHP" programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php82 .php .php8 .phtml
</IfModule>

# Allow all request methods and origins
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, X-Requested-With"
    
    # Handle pre-flight requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
    
    # Set CORS headers for image files
    <FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, OPTIONS"
    </FilesMatch>
</IfModule>

# URL Rewriting Rules
<IfModule mod_rewrite.c>
  # Enable the rewrite engine
  RewriteEngine On

  # Set the base directory for rewrites
  RewriteBase /ecom/

  # Handle OPTIONS method
  RewriteCond %{REQUEST_METHOD} OPTIONS
  RewriteRule ^(.*)$ $1 [R=200,L]

  # Skip rewriting for actual files and directories
  RewriteCond %{REQUEST_FILENAME} -f [OR]
  RewriteCond %{REQUEST_FILENAME} -d
  RewriteRule ^ - [L]

  # Skip rewriting for AJAX requests
  RewriteCond %{HTTP:X-Requested-With} XMLHttpRequest
  RewriteRule ^ - [L]

  # Skip rewriting for specific PHP files
  RewriteRule ^(add_to_cart|remove_from_cart|update_cart|search_suggestions|contact_process|subscribe)\.php$ - [L]

  # Rewrite the URL to append .php extension internally
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME}.php -f
  RewriteRule ^(.*)$ $1.php [L]

  # Redirect requests with .php extension to clean URLs
  RewriteCond %{THE_REQUEST} \s/+(.+?)\.php[\s?] [NC]
  RewriteRule ^ /%1 [R=301,NE,L]
</IfModule>

# php -- END cPanel-generated handler, do not edit 