<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Define currency constants
define('CURRENCY_CODE', 'TZS'); // ISO 4217 code for Tanzanian Shillings
define('CURRENCY_SYMBOL', 'TSH'); // Display symbol
define('CURRENCY_FORMAT', 'en-TZ'); // Locale for Tanzania

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 SMART LIFE. All rights reserved.";

// Fetch products with variations
$statement = $pdo->prepare("
    SELECT
        p.p_id,
        p.p_name,
        p.p_featured_photo,
        p.p_current_price AS base_price,
        p.p_qty AS base_qty,
        pv.variation_id,
        pv.variation_name,
        pv.variation_price,
        pv.variation_qty,
        pv.variation_image,
        c.color_id AS variation_color_id,
        c.color_name AS variation_color_name,
        c.color_code AS variation_color_code
    FROM tbl_product p
    LEFT JOIN tbl_product_variation pv ON p.p_id = pv.p_id
    LEFT JOIN tbl_color c ON pv.variation_color = c.color_id
    WHERE p.p_is_active = 1
");
$statement->execute();
$all_products_with_variations = $statement->fetchAll(PDO::FETCH_ASSOC);

$products_data = [];
foreach ($all_products_with_variations as $row) {
    $p_id = $row['p_id'];
    if (!isset($products_data[$p_id])) {
        $products_data[$p_id] = [
            'p_id' => $row['p_id'],
            'p_name' => $row['p_name'],
            'p_featured_photo' => $row['p_featured_photo'],
            'base_price' => $row['base_price'],
            'base_qty' => $row['base_qty'],
            'variations' => [],
        ];
    }
    if ($row['variation_id']) {
        $products_data[$p_id]['variations'][$row['variation_id']] = [
            'variation_id' => $row['variation_id'],
            'variation_name' => $row['variation_name'],
            'variation_price' => $row['variation_price'],
            'variation_qty' => $row['variation_qty'],
            'variation_image' => $row['variation_image'],
            'color' => [
                'color_id' => $row['variation_color_id'],
                'color_name' => $row['variation_color_name'],
                'color_code' => $row['variation_color_code'],
            ],
        ];
    }
}

// --- Default Installation Fee ---
$default_installation_fee = 15000; // Default value in TSH

// --- Fetch product-specific installation fees ---
$product_installation_fees = [];
try {
    $stmt = $pdo->prepare("SELECT p_id, installation_fee FROM tbl_product WHERE p_is_active = 1");
    $stmt->execute();
    $installation_fees_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($installation_fees_data as $fee_data) {
        // Convert to integer and store in the array
        $product_installation_fees[$fee_data['p_id']] = intval($fee_data['installation_fee']);
    }
} catch (PDOException $e) {
    // If there's an error, we'll use the default fee for all products
    error_log("Error fetching product installation fees: " . $e->getMessage());
}

// Fetch shipping costs by country
$statement = $pdo->prepare("SELECT * FROM tbl_country ORDER BY country_name ASC");
$statement->execute();
$countries = $statement->fetchAll(PDO::FETCH_ASSOC);

$shipping_costs = [];
$statement = $pdo->prepare("SELECT * FROM tbl_shipping_cost");
$statement->execute();
$shipping_cost_data = $statement->fetchAll(PDO::FETCH_ASSOC);
foreach ($shipping_cost_data as $row) {
    $shipping_costs[$row['country_id']] = $row['amount'];
}

// Fetch default shipping cost for all other locations
$statement = $pdo->prepare("SELECT * FROM tbl_shipping_cost_all WHERE sca_id = 1"); // Assuming only one default cost
$statement->execute();
$default_shipping_cost_data = $statement->fetch(PDO::FETCH_ASSOC);
$default_shipping_cost = $default_shipping_cost_data ? $default_shipping_cost_data['amount'] : 0;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Your Shopping Cart | SMART LIFE</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/cart.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Mobile Menu Styles */
        .mobile-menu {
            display: none;
            background: white;
            padding: 10px;
            border-radius: 4px;
            margin-left: auto;
        }

        .menu-btn {
            width: 30px;
            height: 20px;
            position: relative;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            background: white;
            border: none;
        }

        .menu-btn span {
            display: block;
            width: 100%;
            height: 3px;
            background-color: #333;
            transition: all 0.3s ease;
        }

        /* Currency Display Styles */
        .currency-display {
            display: inline-flex;
            align-items: center;
        }

        .currency-symbol {
            font-size: 0.9em;
            margin-right: 3px;
            font-weight: 600;
            color: #333;
        }

        .price-amount {
            font-weight: 600;
        }

        /* Payment Gateway Specific */
        .payment-info {
            display: none; /* Hidden but contains payment gateway required data */
        }

        /* Mobile menu active state */
        .menu-btn.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        .menu-btn.active span:nth-child(2) {
            opacity: 0;
        }
        .menu-btn.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -7px);
        }

        @media (max-width: 768px) {
            .mobile-menu {
                display: block;
            }

            .nav-links {
                position: fixed;
                top: 70px;
                right: -50%;
                width: 50%;
                height: calc(100vh - 70px);
                background-color: white;
                flex-direction: column;
                align-items: flex-start;
                padding: 20px;
                transition: all 0.3s ease;
                z-index: 999;
                box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            }

            .nav-links.active {
                right: 0;
            }

            .nav-links a {
                padding: 12px 0;
                width: 100%;
                text-align: left;
                border-bottom: 1px solid #eee;
            }

            /* Overlay for the rest of the screen */
            .nav-overlay {
                position: fixed;
                top: 70px;
                left: 0;
                width: 100%;
                height: calc(100vh - 70px);
                background-color: rgba(0,0,0,0.5);
                z-index: 998;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.3s ease;
            }

            .nav-links.active ~ .nav-overlay {
                opacity: 1;
                pointer-events: auto;
            }
        }

        /* Shipping Area Styles */
        .shipping-selection {
            margin-bottom: 20px;
        }

        .shipping-selection label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .shipping-selection select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
    <script src="https://checkout.flutterwave.com/v3.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <a href="index.php" class="logo">SMART LIFE<span>.</span></a>
            <div class="nav-links">
                <a href="index.php">Home</a>
                <a href="index.php#about">About</a>
                <a href="index.php#products">Products</a>
                <a href="index.php#gallery">Gallery</a>
                <a href="index.php#contact">Contact</a>
                <div class="cart-icon">
                    <a href="cart.php"><i class="fas fa-shopping-cart"></i> <span class="cart-count">0</span></a>
                </div>
                <div class="user-actions" style="margin-left: 20px;">
                    <?php if (isUserLoggedIn()): ?>
                        <span>Hi, <?php echo htmlspecialchars($_SESSION['customer']['cust_fname']); ?>!</span> |
                        <a href="logout.php" style="color:var(--secondary);">Logout</a>
                    <?php else: ?>
                        <a href="login.php" style="color:var(--secondary);">Login/Register</a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="mobile-menu">
                <div class="menu-btn"><span></span><span></span><span></span></div>
            </div>
            <div class="nav-overlay"></div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="cart-container">
                <div class="cart-header">
                    <h1><i class="fas fa-shopping-cart"></i> Your Shopping Cart</h1>
                    <?php
                    if (isset($_SESSION['error_message'])) {
                        echo '<div class="error-message" style="margin-top: 15px;">' . $_SESSION['error_message'] . '</div>';
                        unset($_SESSION['error_message']);
                    }
                    if (isset($_SESSION['success_message'])) {
                        echo '<div class="success-message" style="margin-top: 15px;">' . $_SESSION['success_message'] . '</div>';
                        unset($_SESSION['success_message']);
                    }
                    ?>
                </div>

                <div class="cart-table-container">
                    <table class="cart-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Details</th>
                                <th>Price (<span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>)</th>
                                <th>Quantity</th>
                                <th>Color</th>
                                <th>Installation</th>
                                <th>Subtotal (<span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>)</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="cartItems">
                            <tr>
                                <td colspan="8">
                                    <div class="empty-cart" style="display: none; text-align: center; padding: 40px 20px;">
                                        <i class="fas fa-shopping-cart" style="font-size: 3rem; color: #ccc; margin-bottom: 20px;"></i>
                                        <h3>Your cart is empty</h3>
                                        <p style="color: #666; margin-bottom: 20px;">Looks like you haven't added any items yet.</p>
                                        <a href="index.php#products" class="shop-btn" style="display: inline-block; background-color: var(--secondary); color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; transition: all 0.3s;">Shop Now</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="price-summary">
                    <div class="shipping-selection">
                        <label for="shipping_country">Shipping Country:</label>
                        <select name="shipping_country" id="shipping_country">
                            <option value="">Select Country</option>
                            <?php foreach ($countries as $country): ?>
                                <option value="<?php echo htmlspecialchars($country['country_id']); ?>">
                                    <?php echo htmlspecialchars($country['country_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="price-row">
                        <span class="price-label">Products Subtotal:</span>
                        <span class="price-value currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span id="productsSubtotal" class="price-amount">0.00</span>
                        </span>
                    </div>
                    <div class="price-row">
                        <span class="price-label">Shipping Fee:</span>
                        <span class="price-value currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span id="shippingFee" class="price-amount">0.00</span>
                        </span>
                    </div>
                    <div class="price-row installation-fee-row" style="display: none;">
                        <span class="price-label">Professional Installation:</span>
                        <span class="price-value currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span id="installationFeeTotal" class="price-amount">0.00</span>
                        </span>
                    </div>
                    <div class="subtotal-row price-row">
                        <span class="price-label">Subtotal:</span>
                        <span class="price-value currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span id="cartSubtotal" class="price-amount">0.00</span>
                        </span>
                    </div>
                    <div class="total-row price-row">
                        <span class="price-label">Total:</span>
                        <span class="price-value total-price currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span id="cartTotal" class="price-amount">0.00</span>
                        </span>
                    </div>

                    <div class="payment-info">
                        <input type="hidden" id="paymentCurrency" value="<?= CURRENCY_CODE ?>">
                        <input type="hidden" id="paymentAmount" value="0">
                    </div>

                    <div class="cart-actions">
                        <button id="proceedToSummaryBtn" class="checkout-btn">
                            Proceed to Checkout
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div id="summaryModal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2><i class="fas fa-clipboard-check"></i> Order Summary</h2>
            <div id="summaryContent">
                <div style="text-align:center; padding:20px; display:none;" class="modal-empty-cart">
                    <i class="fas fa-shopping-cart" style="font-size:3rem;color:#ccc;"></i>
                    <h3>Your cart is empty</h3>
                </div>
                <div class="summary-items">
                </div>
                <div class="summary-totals" style="display:none;">
                    <div class="summary-row">
                        <span>Products Subtotal:</span>
                        <span id="summaryProductsSubtotal" class="currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span class="price-amount">0.00</span>
                        </span>
                    </div>
                    <div class="summary-row">
                        <span>Shipping Fee:</span>
                        <span id="summaryShippingFee" class="currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span class="price-amount">0.00</span>
                        </span>
                    </div>
                    <div class="summary-row installation-fee-row" style="display: none;">
                        <span>Professional Installation:</span>
                        <span id="summaryInstallationFeeTotal" class="currency-display">
                            <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span class="price-amount">0.00</span>
                        </span>
                    </div>
                    <div class="summary-row total">
                        <span>Total:</span>
                        <span id="summaryTotal" class="currency-display total-price">
                          <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                            <span class="price-amount">0.00</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="modal-btn cancel-btn">Continue Shopping</button>
                <button type="button" id="confirmCheckout" class="modal-btn confirm-btn">Confirm & Checkout</button>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="footer-bottom">
                <div class="copyright"><?php echo htmlspecialchars($footer_copyright); ?></div>
            </div>
        </div>
    </footer>
    <script src="https://checkout.flutterwave.com/v3.js"></script>
    <script>
        // Constants for JavaScript use
        const CURRENCY = {
            code: '<?= CURRENCY_CODE ?>', // 'TZS' for payment processing
            symbol: '<?= CURRENCY_SYMBOL ?>', // 'TSH' for display
            locale: '<?= CURRENCY_FORMAT ?>' // 'en-TZ' for formatting
        };

        window.productsWithVariations = <?php echo json_encode($products_data); ?>;
        window.defaultInstallationFee = <?php echo json_encode($default_installation_fee); ?> || 15000;
        window.productInstallationFees = <?php echo json_encode($product_installation_fees); ?> || {};
        window.countries = <?php echo json_encode($countries); ?>;
        window.shippingCosts = <?php echo json_encode($shipping_costs); ?>;
        window.defaultShippingCost = <?php echo json_encode($default_shipping_cost); ?>;

        // Define the absolute base path from your web root to the uploads folder
        // Adjust '/ecom4/' if your project is not directly under htdocs/ecom4/
        var baseUploadPath = "<?php echo '/ecom4/assets/uploads/'; ?>";

        // Pass login status to JS
        var isUserLoggedIn = <?php echo json_encode(isUserLoggedIn()); ?>;
    </script>
    <script src="js/cart.js"></script>
    <script src="js/script.js"></script>
    <script src="js/checkout.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const shippingCountrySelect = document.getElementById('shipping_country');
            const shippingFeeDisplay = document.getElementById('shippingFee');
            const summaryShippingFeeDisplay = document.getElementById('summaryShippingFee');
            const cartSubtotalDisplay = document.getElementById('cartSubtotal');
            const cartTotalDisplay = document.getElementById('cartTotal');
            const summaryProductsSubtotalDisplay = document.getElementById('summaryProductsSubtotal');
            const summaryTotalDisplay = document.getElementById('summaryTotal');

            function updateShippingCost() {
                const selectedCountryId = shippingCountrySelect.value;
                let shippingAmount = parseFloat(window.defaultShippingCost); // Default

                if (window.shippingCosts && window.shippingCosts[selectedCountryId]) {
                    shippingAmount = parseFloat(window.shippingCosts[selectedCountryId]);
                }

                shippingFeeDisplay.textContent = formatPrice(shippingAmount);
                summaryShippingFeeDisplay.textContent = formatPrice(shippingAmount);
                updateCartTotal();
            }

            function updateCartTotal() {
                const productsSubtotal = parseFloat(document.getElementById('productsSubtotal').textContent.replace(/[^0-9.-]+/g,""));
                const shippingFee = parseFloat(document.getElementById('shippingFee').textContent.replace(/[^0-9.-]+/g,""));
                const installationFeeTotalElement = document.getElementById('installationFeeTotal');
                const installationFee = installationFeeTotalElement && installationFeeTotalElement.offsetParent !== null ? parseFloat(installationFeeTotalElement.textContent.replace(/[^0-9.-]+/g,"")) : 0;

                const subtotal = productsSubtotal + shippingFee + installationFee;
                cartSubtotalDisplay.textContent = formatPrice(subtotal);
                cartTotalDisplay.textContent = formatPrice(subtotal);
                summaryTotalDisplay.textContent = formatPrice(subtotal);
            }

            if (shippingCountrySelect) {
                shippingCountrySelect.addEventListener('change', updateShippingCost);
            }

            // Initial update of shipping cost if a country is pre-selected (e.g., based on user profile)
            if (shippingCountrySelect && shippingCountrySelect.value) {
                updateShippingCost();
            } else {
                // Initialize with default shipping cost
                shippingFeeDisplay.textContent = formatPrice(parseFloat(window.defaultShippingCost));
                summaryShippingFeeDisplay.textContent = formatPrice(parseFloat(window.defaultShippingCost));
                updateCartTotal(); // Recalculate total with default shipping
            }

            const proceedToSummaryBtn = document.getElementById('proceedToSummaryBtn');
            const summaryModal = document.getElementById('summaryModal');
            const closeSummaryModal = summaryModal.querySelector('.close-modal');
            const cancelSummaryBtn = summaryModal.querySelector('.cancel-btn');
            const confirmCheckoutBtn = document.getElementById('confirmCheckout');
            const summaryItemsContainer = summaryModal.querySelector('.summary-items');
            const summaryTotalsContainer = summaryModal.querySelector('.summary-totals');
            const modalEmptyCart = summaryModal.querySelector('.modal-empty-cart');

            if (proceedToSummaryBtn) {
                proceedToSummaryBtn.addEventListener('click', function() {
                    const cartItems = getCartItems();
                    if (cartItems.length > 0) {
                        modalEmptyCart.style.display = 'none';
                        summaryItemsContainer.innerHTML = '';
                        let summaryProductsSubtotal = 0;

                        cartItems.forEach(item => {
                            const product = window.productsWithVariations[item.productId];
                            const variation = item.variationId && product.variations ? product.variations[item.variationId] : null;
                            const itemName = variation ? `${product.p_name} - ${variation.variation_name}` : product.p_name;
                            const itemPrice = variation ? parseFloat(variation.variation_price) : parseFloat(product.base_price);
                            const itemSubtotal = itemPrice * item.quantity;
                            summaryProductsSubtotal += itemSubtotal;

                            const listItem = document.createElement('div');
                            listItem.classList.add('summary-item');
                            listItem.innerHTML = `
                                <span>${itemName} x ${item.quantity}</span>
                                <span class="currency-display">
                                    <span class="currency-symbol"><?= CURRENCY_SYMBOL ?></span>
                                    <span class="price-amount">${formatPrice(itemSubtotal)}</span>
                                </span>
                            `;
                            summaryItemsContainer.appendChild(listItem);
                        });

                        summaryProductsSubtotalDisplay.textContent = formatPrice(summaryProductsSubtotal);
                        summaryTotalsContainer.style.display = 'block';
                        updateCartTotal(); // Ensure total in modal is up-to-date
                        summaryModal.style.display = 'block';
                    } else {
                        modalEmptyCart.style.display = 'block';
                        summaryItemsContainer.innerHTML = '';
                        summaryTotalsContainer.style.display = 'none';
                        summaryModal.style.display = 'block';
                    }
                });
            }

            if (closeSummaryModal) {
                closeSummaryModal.addEventListener('click', function() {
                    summaryModal.style.display = 'none';
                });
            }

            if (cancelSummaryBtn) {
                cancelSummaryBtn.addEventListener('click', function() {
                    summaryModal.style.display = 'none';
                });
            }

            if (confirmCheckoutBtn) {
                confirmCheckoutBtn.addEventListener('click', function() {
                    const cartItems = getCartItems();
                    const totalAmount = parseFloat(document.getElementById('cartTotal').textContent.replace(/[^0-9.-]+/g,""));
                    const selectedCountryId = document.getElementById('shipping_country').value;
                    const shippingFee = parseFloat(document.getElementById('shippingFee').textContent.replace(/[^0-9.-]+/g,""));
                    const includeInstallation = document.querySelector('.installation-checkbox') ? document.querySelector('.installation-checkbox').checked : false;
                    // Get product-specific installation fee or use default
                    const productId = cartItems[0]?.productId;
                    const installationFeeValue = includeInstallation ?
                        (productId && window.productInstallationFees && window.productInstallationFees[productId]
                            ? window.productInstallationFees[productId]
                            : window.defaultInstallationFee)
                        : 0;

                    if (cartItems.length > 0) {
                        // Prepare data for checkout (replace with your actual checkout process)
                        const orderData = {
                            items: cartItems,
                            total: totalAmount,
                            shippingCountryId: selectedCountryId,
                            shippingFee: shippingFee,
                            installationFee: installationFeeValue,
                            currency: CURRENCY.code
                            // Add other relevant data like user info, etc.
                        };

                        console.log('Checkout Data:', orderData);
                        // In a real scenario, you would send this orderData to your server-side
                        // to process the order and initiate payment.
                        // For now, we'll just log it.
                        alert('Checkout initiated! Check console for order data.');
                        summaryModal.style.display = 'none';
                        // Optionally, redirect the user to a payment gateway page here.
                    } else {
                        alert('Your cart is empty. Nothing to checkout.');
                    }
                });
            }
        });
    </script>
</body>
</html>