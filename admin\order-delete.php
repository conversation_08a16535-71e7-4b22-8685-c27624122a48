<?php require_once('header.php'); ?>

<?php
if(!isset($_REQUEST['id'])) {
	header('location: logout.php');
	exit;
} else {
	// Check the id is valid or not
	$statement = $pdo->prepare("SELECT * FROM orders WHERE id=?");
	$statement->execute(array($_REQUEST['id']));
	$total = $statement->rowCount();
	if( $total == 0 ) {
		header('location: logout.php');
		exit;
	} else {
		$result = $statement->fetchAll(PDO::FETCH_ASSOC);							
		foreach ($result as $row) {
			$order_id = $row['id'];
			$payment_status = $row['payment_status'];
		}
	}
}
?>

<?php
	// Delete from order_items
	$statement = $pdo->prepare("DELETE FROM order_items WHERE order_id=?");
	$statement->execute(array($order_id));

	// Delete from orders
	$statement = $pdo->prepare("DELETE FROM orders WHERE id=?");
	$statement->execute(array($_REQUEST['id']));

	header('location: order.php');
?>