<?php
/**
 * API Installation Script
 * Sets up the API system and creates necessary tables
 */

// Security check - remove this file after installation
if (file_exists(__DIR__ . '/.installed')) {
    http_response_code(403);
    echo json_encode(['error' => 'API already installed. Remove .installed file to reinstall.']);
    exit;
}

// Include configuration
require_once __DIR__ . '/config/config.php';

$step = $_GET['step'] ?? 'check';
$format = $_GET['format'] ?? 'html';

// Installation steps
switch ($step) {
    case 'check':
        $checks = performSystemChecks();
        break;
    case 'install':
        $checks = performInstallation();
        break;
    default:
        $checks = ['error' => 'Invalid step'];
}

// Return JSON if requested
if ($format === 'json') {
    header('Content-Type: application/json');
    echo json_encode($checks, JSON_PRETTY_PRINT);
    exit;
}

/**
 * Perform system checks
 */
function performSystemChecks() {
    global $pdo;

    $checks = [
        'php_version' => [
            'name' => 'PHP Version',
            'required' => '7.4+',
            'current' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '7.4.0', '>=') ? 'pass' : 'fail'
        ],
        'database' => [
            'name' => 'Database Connection',
            'required' => 'MySQL connection',
            'current' => 'Testing...',
            'status' => 'unknown'
        ],
        'extensions' => [
            'name' => 'Required Extensions',
            'required' => 'PDO, JSON, OpenSSL',
            'current' => [],
            'status' => 'unknown'
        ],
        'permissions' => [
            'name' => 'File Permissions',
            'required' => 'Write access to storage',
            'current' => [],
            'status' => 'unknown'
        ]
    ];

    // Test database connection
    try {
        $stmt = $pdo->query("SELECT 1");
        $checks['database']['current'] = 'Connected to ' . DB_NAME;
        $checks['database']['status'] = 'pass';
    } catch (Exception $e) {
        $checks['database']['current'] = 'Failed: ' . $e->getMessage();
        $checks['database']['status'] = 'fail';
    }

    // Check extensions
    $required_extensions = ['pdo', 'json', 'openssl', 'hash'];
    $missing_extensions = [];

    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $missing_extensions[] = $ext;
        }
    }

    if (empty($missing_extensions)) {
        $checks['extensions']['current'] = 'All required extensions loaded';
        $checks['extensions']['status'] = 'pass';
    } else {
        $checks['extensions']['current'] = 'Missing: ' . implode(', ', $missing_extensions);
        $checks['extensions']['status'] = 'fail';
    }

    // Check permissions
    $directories = [
        __DIR__ . '/storage',
        __DIR__ . '/storage/rate_limits'
    ];

    $permission_issues = [];

    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                $permission_issues[] = "Cannot create directory: $dir";
            }
        } elseif (!is_writable($dir)) {
            $permission_issues[] = "Directory not writable: $dir";
        }
    }

    if (empty($permission_issues)) {
        $checks['permissions']['current'] = 'All directories writable';
        $checks['permissions']['status'] = 'pass';
    } else {
        $checks['permissions']['current'] = implode(', ', $permission_issues);
        $checks['permissions']['status'] = 'fail';
    }

    // Overall status
    $all_passed = true;
    foreach ($checks as $check) {
        if ($check['status'] === 'fail') {
            $all_passed = false;
            break;
        }
    }

    $checks['overall'] = [
        'status' => $all_passed ? 'ready' : 'not_ready',
        'message' => $all_passed ? 'System ready for installation' : 'Please fix the issues above'
    ];

    return $checks;
}

/**
 * Perform installation
 */
function performInstallation() {
    global $pdo;

    $results = [
        'steps' => [],
        'success' => false,
        'message' => ''
    ];

    try {
        // Step 1: Run database migrations
        $results['steps'][] = [
            'name' => 'Database Migration',
            'status' => 'running'
        ];

        // Use the dedicated migration script
        $_GET['web_install'] = true; // Allow web execution
        ob_start();
        $migration_result = include __DIR__ . '/migrations/migrate.php';
        $migration_output = ob_get_clean();

        if (!$migration_result || !$migration_result['success']) {
            throw new Exception('Migration failed: ' . ($migration_result['error'] ?? 'Unknown error'));
        }

        $results['steps'][0]['status'] = 'completed';
        $results['steps'][0]['details'] = "Ran {$migration_result['migrations_run']} migrations";

        // Step 2: Create storage directories
        $results['steps'][] = [
            'name' => 'Create Storage Directories',
            'status' => 'running'
        ];

        $directories = [
            __DIR__ . '/storage',
            __DIR__ . '/storage/rate_limits',
            __DIR__ . '/maintenance/reports'
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }

        $results['steps'][1]['status'] = 'completed';

        // Step 3: Generate secure JWT secret
        $results['steps'][] = [
            'name' => 'Generate Security Keys',
            'status' => 'running'
        ];

        $jwt_secret = bin2hex(random_bytes(32));

        // Update config file with new JWT secret
        $config_content = file_get_contents(__DIR__ . '/config/config.php');
        $config_content = str_replace(
            'your-super-secret-jwt-key-change-this-in-production',
            $jwt_secret,
            $config_content
        );
        file_put_contents(__DIR__ . '/config/config.php', $config_content);

        $results['steps'][2]['status'] = 'completed';

        // Step 4: Create installation marker
        $results['steps'][] = [
            'name' => 'Finalize Installation',
            'status' => 'running'
        ];

        $install_info = [
            'installed_at' => date('Y-m-d H:i:s'),
            'api_version' => API_VERSION,
            'php_version' => PHP_VERSION,
            'jwt_secret_generated' => true
        ];

        file_put_contents(__DIR__ . '/.installed', json_encode($install_info, JSON_PRETTY_PRINT));

        $results['steps'][3]['status'] = 'completed';

        $results['success'] = true;
        $results['message'] = 'API installation completed successfully!';

        // Log installation
        require_once __DIR__ . '/utils/Logger.php';
        Logger::logInfo('API installation completed', $install_info);

    } catch (Exception $e) {
        $results['success'] = false;
        $results['message'] = 'Installation failed: ' . $e->getMessage();

        // Mark current step as failed
        if (!empty($results['steps'])) {
            $last_step = count($results['steps']) - 1;
            $results['steps'][$last_step]['status'] = 'failed';
            $results['steps'][$last_step]['error'] = $e->getMessage();
        }
    }

    return $results;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Installation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px; }
        .card { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .check { display: flex; justify-content: space-between; align-items: center; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .check.pass { background: #d4edda; border-left: 4px solid #28a745; }
        .check.fail { background: #f8d7da; border-left: 4px solid #dc3545; }
        .check.unknown { background: #fff3cd; border-left: 4px solid #ffc107; }
        .status { font-weight: bold; padding: 5px 10px; border-radius: 3px; color: white; }
        .status.pass { background: #28a745; }
        .status.fail { background: #dc3545; }
        .status.unknown { background: #ffc107; color: black; }
        .btn { background: #667eea; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #5a6fd8; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        .step { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .step.completed { background: #d4edda; }
        .step.running { background: #fff3cd; }
        .step.failed { background: #f8d7da; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Ecommerce API Installation</h1>
            <p>Version <?= API_VERSION ?></p>
        </div>

        <?php if ($step === 'check'): ?>
            <div class="card">
                <h2>System Requirements Check</h2>

                <?php foreach ($checks as $key => $check): ?>
                    <?php if ($key === 'overall') continue; ?>
                    <div class="check <?= $check['status'] ?>">
                        <div>
                            <strong><?= $check['name'] ?></strong><br>
                            <small>Required: <?= $check['required'] ?></small><br>
                            <small>Current: <?= is_array($check['current']) ? implode(', ', $check['current']) : $check['current'] ?></small>
                        </div>
                        <span class="status <?= $check['status'] ?>"><?= strtoupper($check['status']) ?></span>
                    </div>
                <?php endforeach; ?>

                <div style="margin-top: 30px; text-align: center;">
                    <?php if ($checks['overall']['status'] === 'ready'): ?>
                        <p style="color: #28a745; margin-bottom: 20px;">✓ All checks passed! Ready to install.</p>
                        <a href="?step=install" class="btn">Install API</a>
                    <?php else: ?>
                        <p style="color: #dc3545; margin-bottom: 20px;">✗ Please fix the issues above before installing.</p>
                        <button class="btn" disabled>Install API</button>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($step === 'install'): ?>
            <div class="card">
                <h2>Installation Progress</h2>

                <?php foreach ($checks['steps'] as $step_info): ?>
                    <div class="step <?= $step_info['status'] ?>">
                        <strong><?= $step_info['name'] ?></strong>
                        <span style="float: right;"><?= strtoupper($step_info['status']) ?></span>
                        <?php if (isset($step_info['error'])): ?>
                            <br><small style="color: #dc3545;">Error: <?= htmlspecialchars($step_info['error']) ?></small>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>

                <div style="margin-top: 30px; text-align: center;">
                    <?php if ($checks['success']): ?>
                        <p style="color: #28a745; margin-bottom: 20px;">✓ <?= $checks['message'] ?></p>

                        <?php
                        $script_dir = dirname($_SERVER['SCRIPT_NAME']);
                        $monitor_token = md5(JWT_SECRET . date('Y-m-d'));
                        ?>
                        <div class="code">
                            <strong>Next Steps:</strong><br>
                            1. Remove this install.php file for security<br>
                            2. Visit <a href="<?= $script_dir ?>/v1/docs"><?= $script_dir ?>/v1/docs</a> for API documentation<br>
                            3. Test the API with <a href="<?= $script_dir ?>/test.php"><?= $script_dir ?>/test.php</a><br>
                            4. Monitor API health at <a href="<?= $script_dir ?>/monitor/dashboard.php?token=<?= $monitor_token ?>"><?= $script_dir ?>/monitor/dashboard.php</a>
                        </div>

                        <a href="<?= $script_dir ?>/v1/" class="btn">View API</a>
                        <a href="<?= $script_dir ?>/v1/docs" class="btn">Documentation</a>
                    <?php else: ?>
                        <p style="color: #dc3545; margin-bottom: 20px;">✗ <?= $checks['message'] ?></p>
                        <a href="?step=check" class="btn">Back to Checks</a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <div style="text-align: center; color: #666; margin-top: 20px;">
            <p>Ecommerce API Installation Script</p>
            <p><small>Add ?format=json for JSON output</small></p>
        </div>
    </div>
</body>
</html>
