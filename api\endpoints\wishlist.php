<?php
/**
 * Wishlist Endpoints
 * Handles user wishlist operations
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path and ID
$sub_path = $segments[1] ?? '';
$product_id = $segments[2] ?? null;

switch ($method) {
    case 'GET':
        if ($sub_path === 'count') {
            handleGetWishlistCount($db);
        } else {
            handleGetWishlist($db);
        }
        break;
        
    case 'POST':
        if ($sub_path === 'add') {
            handleAddToWishlist($db, $input);
        } elseif ($sub_path === 'toggle') {
            handleToggleWishlist($db, $input);
        } else {
            Response::notFound('Wishlist endpoint not found');
        }
        break;
        
    case 'DELETE':
        if ($product_id) {
            handleRemoveFromWishlist($db, $product_id);
        } elseif ($sub_path === 'clear') {
            handleClearWishlist($db);
        } else {
            Response::error('Product ID is required', 400);
        }
        break;
        
    default:
        Response::methodNotAllowed(['GET', 'POST', 'DELETE']);
}

/**
 * Get user's wishlist
 */
function handleGetWishlist($db) {
    $user = AuthMiddleware::requireAuth();
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
    
    $sql = "
        SELECT 
            w.id as wishlist_id,
            w.created_at as added_at,
            p.p_id,
            p.p_name,
            p.p_old_price,
            p.p_current_price,
            p.p_qty,
            p.p_featured_photo,
            p.p_short_description,
            p.p_is_featured,
            p.installation_fee,
            tc.tcat_name as category_name,
            mc.mcat_name as subcategory_name
        FROM tbl_wishlist w
        JOIN tbl_product p ON w.p_id = p.p_id
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        LEFT JOIN tbl_mid_category mc ON p.mcat_id = mc.mcat_id
        WHERE w.cust_id = ? AND p.p_is_active = 1
        ORDER BY w.created_at DESC
    ";
    
    $result = $db->paginate($sql, [$user['user_id']], $page, $limit);
    
    // Format wishlist items
    $wishlist_items = array_map('formatWishlistItem', $result['data']);
    
    Response::paginated($wishlist_items, $result['total'], $page, $limit, 'Wishlist retrieved successfully');
}

/**
 * Add product to wishlist
 */
function handleAddToWishlist($db, $input) {
    $user = AuthMiddleware::requireAuth();
    
    $validator = new Validator($input);
    $validator->required('product_id')->integer('product_id');
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    $product_id = $input['product_id'];
    
    // Check if product exists and is active
    $product = $db->fetchOne(
        "SELECT p_id, p_name FROM tbl_product WHERE p_id = ? AND p_is_active = 1",
        [$product_id]
    );
    
    if (!$product) {
        Response::notFound('Product not found');
    }
    
    // Check if already in wishlist
    $existing = $db->fetchOne(
        "SELECT id FROM tbl_wishlist WHERE cust_id = ? AND p_id = ?",
        [$user['user_id'], $product_id]
    );
    
    if ($existing) {
        Response::error('Product already in wishlist', 409, 'ALREADY_IN_WISHLIST');
    }
    
    // Add to wishlist
    $wishlist_data = [
        'cust_id' => $user['user_id'],
        'p_id' => $product_id,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $wishlist_id = $db->insert('tbl_wishlist', $wishlist_data);
    
    Response::success([
        'wishlist_id' => (int)$wishlist_id,
        'product_id' => (int)$product_id,
        'product_name' => $product['p_name']
    ], 'Product added to wishlist successfully', 201);
}

/**
 * Toggle product in wishlist (add if not exists, remove if exists)
 */
function handleToggleWishlist($db, $input) {
    $user = AuthMiddleware::requireAuth();
    
    $validator = new Validator($input);
    $validator->required('product_id')->integer('product_id');
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    $product_id = $input['product_id'];
    
    // Check if product exists and is active
    $product = $db->fetchOne(
        "SELECT p_id, p_name FROM tbl_product WHERE p_id = ? AND p_is_active = 1",
        [$product_id]
    );
    
    if (!$product) {
        Response::notFound('Product not found');
    }
    
    // Check if already in wishlist
    $existing = $db->fetchOne(
        "SELECT id FROM tbl_wishlist WHERE cust_id = ? AND p_id = ?",
        [$user['user_id'], $product_id]
    );
    
    if ($existing) {
        // Remove from wishlist
        $db->delete('tbl_wishlist', 'id = ?', [$existing['id']]);
        
        Response::success([
            'action' => 'removed',
            'product_id' => (int)$product_id,
            'in_wishlist' => false
        ], 'Product removed from wishlist');
    } else {
        // Add to wishlist
        $wishlist_data = [
            'cust_id' => $user['user_id'],
            'p_id' => $product_id,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $wishlist_id = $db->insert('tbl_wishlist', $wishlist_data);
        
        Response::success([
            'action' => 'added',
            'wishlist_id' => (int)$wishlist_id,
            'product_id' => (int)$product_id,
            'in_wishlist' => true
        ], 'Product added to wishlist');
    }
}

/**
 * Remove product from wishlist
 */
function handleRemoveFromWishlist($db, $product_id) {
    $user = AuthMiddleware::requireAuth();
    
    // Check if product is in user's wishlist
    $wishlist_item = $db->fetchOne(
        "SELECT id FROM tbl_wishlist WHERE cust_id = ? AND p_id = ?",
        [$user['user_id'], $product_id]
    );
    
    if (!$wishlist_item) {
        Response::notFound('Product not found in wishlist');
    }
    
    // Remove from wishlist
    $db->delete('tbl_wishlist', 'id = ?', [$wishlist_item['id']]);
    
    Response::success(null, 'Product removed from wishlist successfully');
}

/**
 * Clear entire wishlist
 */
function handleClearWishlist($db) {
    $user = AuthMiddleware::requireAuth();
    
    $deleted_count = $db->delete('tbl_wishlist', 'cust_id = ?', [$user['user_id']]);
    
    Response::success([
        'deleted_count' => $deleted_count
    ], 'Wishlist cleared successfully');
}

/**
 * Get wishlist item count
 */
function handleGetWishlistCount($db) {
    $user = AuthMiddleware::requireAuth();
    
    $count = $db->count('tbl_wishlist', 'cust_id = ?', [$user['user_id']]);
    
    Response::success(['count' => (int)$count], 'Wishlist count retrieved successfully');
}

/**
 * Format wishlist item data for API response
 */
function formatWishlistItem($item) {
    return [
        'wishlist_id' => (int)$item['wishlist_id'],
        'added_at' => $item['added_at'],
        'product' => [
            'id' => (int)$item['p_id'],
            'name' => $item['p_name'],
            'old_price' => (float)$item['p_old_price'],
            'current_price' => (float)$item['p_current_price'],
            'quantity' => (int)$item['p_qty'],
            'installation_fee' => (int)($item['installation_fee'] ?? DEFAULT_INSTALLATION_FEE),
            'featured_photo' => $item['p_featured_photo'] ? 
                '/assets/uploads/' . $item['p_featured_photo'] : null,
            'short_description' => $item['p_short_description'] ?? '',
            'is_featured' => (bool)($item['p_is_featured'] ?? false),
            'category' => $item['category_name'] ?? null,
            'subcategory' => $item['subcategory_name'] ?? null,
            'in_stock' => (int)$item['p_qty'] > 0
        ]
    ];
}
?>
