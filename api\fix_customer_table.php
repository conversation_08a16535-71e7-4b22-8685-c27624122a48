<?php
/**
 * Fix Customer Table Structure
 * Adds missing columns that auth endpoint expects
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>🔧 Fixing Customer Table Structure</h1>";

try {
    // Get current table structure
    $columns = $pdo->query("DESCRIBE tbl_customer")->fetchAll();
    $existing_columns = array_column($columns, 'Field');
    
    echo "<h2>📋 Current Columns</h2>";
    echo "<ul>";
    foreach ($existing_columns as $column) {
        echo "<li>{$column}</li>";
    }
    echo "</ul>";
    
    // Define all required columns for auth endpoint
    $required_columns = [
        'cust_id' => 'int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY',
        'cust_fname' => 'varchar(100) NOT NULL',
        'cust_lname' => 'varchar(100) NOT NULL',
        'cust_email' => 'varchar(255) NOT NULL UNIQUE',
        'cust_password' => 'varchar(255) NOT NULL',
        'cust_phone' => 'varchar(20) DEFAULT NULL',
        'cust_photo' => 'varchar(255) DEFAULT NULL',
        'cust_address' => 'text DEFAULT NULL',
        'cust_address_street' => 'varchar(255) DEFAULT NULL',
        'cust_address_city' => 'varchar(100) DEFAULT NULL',
        'cust_address_region' => 'varchar(100) DEFAULT NULL',
        'cust_address_zip' => 'varchar(20) DEFAULT NULL',
        'cust_city' => 'varchar(100) DEFAULT NULL',
        'cust_region' => 'varchar(100) DEFAULT NULL',
        'cust_country' => 'varchar(100) DEFAULT NULL',
        'cust_status' => 'tinyint(1) DEFAULT 1',
        'cust_created_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP',
        'cust_last_login' => 'timestamp NULL DEFAULT NULL'
    ];
    
    echo "<h2>🔧 Adding Missing Columns</h2>";
    
    $added_columns = [];
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            try {
                // Skip primary key if it's cust_id and already exists
                if ($column === 'cust_id' && in_array('cust_id', $existing_columns)) {
                    continue;
                }
                
                $alter_sql = "ALTER TABLE `tbl_customer` ADD COLUMN `{$column}` {$definition}";
                $pdo->exec($alter_sql);
                echo "<p style='color: green;'>✅ Added column: {$column}</p>";
                $added_columns[] = $column;
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to add {$column}: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ Column {$column} already exists</p>";
        }
    }
    
    // Update existing user data to have proper structure
    echo "<h2>📊 Updating Existing User Data</h2>";
    
    // Check if we have users with missing data
    $users = $pdo->query("SELECT cust_id, cust_email, cust_address FROM tbl_customer")->fetchAll();
    
    foreach ($users as $user) {
        $updates = [];
        
        // If cust_address exists but address components don't, split it
        if (!empty($user['cust_address']) && in_array('cust_address_street', $added_columns)) {
            $updates['cust_address_street'] = $user['cust_address'];
        }
        
        // Set default values for new columns
        if (in_array('cust_photo', $added_columns)) {
            $updates['cust_photo'] = null;
        }
        
        if (in_array('cust_address_zip', $added_columns)) {
            $updates['cust_address_zip'] = '';
        }
        
        if (!empty($updates)) {
            $set_clause = implode(', ', array_map(function($col) { return "`{$col}` = ?"; }, array_keys($updates)));
            $stmt = $pdo->prepare("UPDATE tbl_customer SET {$set_clause} WHERE cust_id = ?");
            $values = array_values($updates);
            $values[] = $user['cust_id'];
            $stmt->execute($values);
            
            echo "<p style='color: green;'>✅ Updated user {$user['cust_email']}</p>";
        }
    }
    
    // Create/update the test user with proper data
    echo "<h2>👤 Creating Test User</h2>";
    
    $test_email = '<EMAIL>';
    $test_password = 'changawa';
    
    // Delete existing test user
    $pdo->prepare("DELETE FROM tbl_customer WHERE cust_email = ?")->execute([$test_email]);
    
    // Create test user with all required fields
    $test_user_data = [
        'cust_fname' => 'Chanom',
        'cust_lname' => 'Media',
        'cust_email' => $test_email,
        'cust_password' => Auth::hashPassword($test_password),
        'cust_phone' => '+255123456789',
        'cust_photo' => null,
        'cust_address' => '123 Test Street, Dar es Salaam',
        'cust_address_street' => '123 Test Street',
        'cust_address_city' => 'Dar es Salaam',
        'cust_address_region' => 'Dar es Salaam',
        'cust_address_zip' => '12345',
        'cust_city' => 'Dar es Salaam',
        'cust_region' => 'Dar es Salaam',
        'cust_country' => 'Tanzania',
        'cust_status' => 1,
        'cust_created_at' => date('Y-m-d H:i:s')
    ];
    
    $columns_str = implode(', ', array_map(function($col) { return "`{$col}`"; }, array_keys($test_user_data)));
    $placeholders = ':' . implode(', :', array_keys($test_user_data));
    
    $stmt = $pdo->prepare("INSERT INTO tbl_customer ({$columns_str}) VALUES ({$placeholders})");
    $result = $stmt->execute($test_user_data);
    
    if ($result) {
        $user_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ Test user created successfully</p>";
        echo "<p><strong>User ID:</strong> {$user_id}</p>";
        echo "<p><strong>Email:</strong> {$test_email}</p>";
        echo "<p><strong>Password:</strong> {$test_password}</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create test user</p>";
    }
    
    // Show final table structure
    echo "<h2>📋 Final Table Structure</h2>";
    $final_columns = $pdo->query("DESCRIBE tbl_customer")->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "</tr>";
    
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ Customer Table Fixed!</h2>";
    echo "<p>The customer table now has all required columns for the authentication system.</p>";
    echo "<p><strong>Test the login now:</strong></p>";
    echo "<ul>";
    echo "<li><a href='/ecom/api/flutter_auth_test.php'>Run Flutter Auth Test</a></li>";
    echo "<li><a href='/ecom/api/debug_login.php'>Run Login Debug</a></li>";
    echo "</ul>";
    echo "<p><strong>Test Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> {$test_email}</li>";
    echo "<li><strong>Password:</strong> {$test_password}</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo $e->getTraceAsString();
    echo "</pre>";
}

echo "<hr>";
echo "<p><strong>Table fix completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
