<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: index.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);

    if (empty($email)) {
        $_SESSION['error_message'] = 'Email address is required.';
        header('Location: forgot_password.php');
        exit;
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['error_message'] = 'Please enter a valid email address.';
        header('Location: forgot_password.php');
        exit;
    }

    try {
        // Check if email exists in database
        $stmt = $pdo->prepare("SELECT cust_id, cust_fname, cust_lname FROM tbl_customer WHERE cust_email = ? AND cust_status = 1");
        $stmt->execute([$email]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$customer) {
            $_SESSION['error_message'] = 'No account found with this email address.';
            header('Location: forgot_password.php');
            exit;
        }

        // Generate OTP for password reset
        $otp = generateOTP();
        
        // Store password reset data in session
        $_SESSION['password_reset_data'] = [
            'email' => $email,
            'customer_id' => $customer['cust_id'],
            'customer_name' => $customer['cust_fname'] . ' ' . $customer['cust_lname']
        ];
        
        // Store OTP with email context
        storeOTP($email, $otp);

        // Prepare email content
        $subject = "Password Reset Code - SMART LIFE";
        $message = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                .header { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }
                .content { padding: 20px; }
                .otp-box { font-size: 24px; font-weight: bold; text-align: center; padding: 15px; background-color: #f1f1f1; border-radius: 5px; margin: 20px 0; letter-spacing: 5px; }
                .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
                .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px; margin: 15px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>SMART LIFE Password Reset</h2>
                </div>
                <div class='content'>
                    <p>Hello " . htmlspecialchars($customer['cust_fname']) . ",</p>
                    <p>You requested to reset your password for your SMART LIFE account. Please use the following One-Time Password (OTP) to proceed:</p>
                    <div class='otp-box'>$otp</div>
                    <div class='warning'>
                        <strong>Security Notice:</strong> This OTP is valid for 10 minutes only. If you did not request this password reset, please ignore this email and your password will remain unchanged.
                    </div>
                    <p>For your security, never share this code with anyone.</p>
                </div>
                <div class='footer'>
                    <p>&copy; " . date('Y') . " SMART LIFE. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        // Send OTP via email
        $emailSent = sendEmailWithPHPMailer($email, $subject, $message);

        if (!$emailSent) {
            $_SESSION['error_message'] = "Failed to send reset code. Please try again.";
            header('Location: forgot_password.php');
            exit;
        }

        $_SESSION['success_message'] = "A password reset code has been sent to your email address.";
        header('Location: reset_password.php');
        exit;

    } catch (PDOException $e) {
        error_log("Forgot Password Error: " . $e->getMessage());
        $_SESSION['error_message'] = 'An error occurred. Please try again.';
        header('Location: forgot_password.php');
        exit;
    }
} else {
    header('Location: forgot_password.php');
    exit;
}

ob_end_flush();
?>
