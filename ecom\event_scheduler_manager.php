<?php
/**
 * Event Scheduler Manager for Persistent Login System
 * This script helps you set up and manage MySQL Event Scheduler
 * for automatic cleanup of expired remember tokens
 */

// Include database connection
include("../admin/inc/config.php");
include("session_config.php");
include("auto_cleanup.php");

// Set content type
header('Content-Type: text/html; charset=UTF-8');

// Handle actions
$message = '';
$message_type = '';

if (isset($_GET['action'])) {
    try {
        switch ($_GET['action']) {
            case 'enable_scheduler':
                $pdo->exec("SET GLOBAL event_scheduler = ON");
                $message = "Event Scheduler has been enabled successfully.";
                $message_type = 'success';
                break;

            case 'disable_scheduler':
                $pdo->exec("SET GLOBAL event_scheduler = OFF");
                $message = "Event Scheduler has been disabled.";
                $message_type = 'warning';
                break;

            case 'create_events':
                createCleanupEvents($pdo);
                $message = "Cleanup events have been created successfully.";
                $message_type = 'success';
                break;

            case 'drop_events':
                dropCleanupEvents($pdo);
                $message = "Cleanup events have been removed.";
                $message_type = 'warning';
                break;

            case 'test_cleanup':
                $result = testCleanup($pdo);
                $message = "Manual cleanup completed. Removed {$result} expired tokens.";
                $message_type = 'info';
                break;
        }
    } catch (PDOException $e) {
        $message = "Error: " . $e->getMessage();
        $message_type = 'error';
    }
}

// Get current status
$scheduler_status = getSchedulerStatus($pdo);
$events_status = getEventsStatus($pdo);
$cleanup_stats = getCleanupStats($pdo);

/**
 * Create cleanup events
 */
function createCleanupEvents($pdo) {
    // Daily cleanup event
    $daily_event = "
    CREATE EVENT IF NOT EXISTS `cleanup_expired_remember_tokens`
    ON SCHEDULE EVERY 1 DAY
    STARTS CURRENT_TIMESTAMP
    COMMENT 'Daily cleanup of expired remember me tokens'
    DO
    BEGIN
        DECLARE deleted_count INT DEFAULT 0;
        DECLARE cleanup_time DATETIME DEFAULT NOW();

        DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
        SET deleted_count = ROW_COUNT();

        INSERT IGNORE INTO `tbl_cleanup_log`
        (`cleanup_type`, `tokens_deleted`, `last_cleanup`)
        VALUES ('remember_tokens', deleted_count, cleanup_time);
    END";

    // Weekly emergency cleanup event
    $weekly_event = "
    CREATE EVENT IF NOT EXISTS `emergency_cleanup_old_tokens`
    ON SCHEDULE EVERY 1 WEEK
    STARTS CURRENT_TIMESTAMP
    COMMENT 'Weekly emergency cleanup of very old tokens (60+ days)'
    DO
    BEGIN
        DECLARE deleted_count INT DEFAULT 0;

        DELETE FROM `tbl_remember_tokens`
        WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 60 DAY);

        SET deleted_count = ROW_COUNT();

        INSERT IGNORE INTO `tbl_cleanup_log`
        (`cleanup_type`, `tokens_deleted`, `last_cleanup`)
        VALUES ('emergency_cleanup', deleted_count, NOW());
    END";

    $pdo->exec($daily_event);
    $pdo->exec($weekly_event);
}

/**
 * Drop cleanup events
 */
function dropCleanupEvents($pdo) {
    $pdo->exec("DROP EVENT IF EXISTS `cleanup_expired_remember_tokens`");
    $pdo->exec("DROP EVENT IF EXISTS `emergency_cleanup_old_tokens`");
}

/**
 * Get scheduler status
 */
function getSchedulerStatus($pdo) {
    try {
        $stmt = $pdo->query("SELECT @@event_scheduler as status");
        return $stmt->fetch(PDO::FETCH_ASSOC)['status'];
    } catch (PDOException $e) {
        return 'ERROR';
    }
}

/**
 * Get events status
 */
function getEventsStatus($pdo) {
    try {
        $stmt = $pdo->query("
            SELECT
                EVENT_NAME,
                STATUS,
                INTERVAL_VALUE,
                INTERVAL_FIELD,
                LAST_EXECUTED,
                EVENT_COMMENT
            FROM information_schema.EVENTS
            WHERE EVENT_SCHEMA = DATABASE()
            AND EVENT_NAME LIKE '%cleanup%'
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Test manual cleanup
 */
function testCleanup($pdo) {
    try {
        $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE expires_at < NOW()");
        $stmt->execute();
        return $stmt->rowCount();
    } catch (PDOException $e) {
        return 0;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Scheduler Manager - SMART LIFE</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4f46e5, #06b6d4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content { padding: 30px; }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4f46e5;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #4f46e5, #06b6d4);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover { transform: translateY(-2px); }
        .btn-danger { background: linear-gradient(135deg, #dc3545, #c82333); }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .btn-success { background: linear-gradient(135deg, #28a745, #20c997); }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #4f46e5;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-on { background: #10b981; }
        .status-off { background: #ef4444; }
        .status-enabled { background: #10b981; }
        .status-disabled { background: #ef4444; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Event Scheduler Manager</h1>
            <p>MySQL Event Scheduler Setup & Management</p>
        </div>

        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?= $message_type ?>">
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>

            <div class="status-card">
                <h3>📊 Current Status</h3>
                <p>
                    <strong>Event Scheduler:</strong>
                    <span class="status-indicator status-<?= strtolower($scheduler_status) ?>"></span>
                    <?= strtoupper($scheduler_status) ?>
                </p>
                <p><strong>Active Events:</strong> <?= count($events_status) ?></p>
                <p><strong>Database:</strong> <?= htmlspecialchars($pdo->query("SELECT DATABASE()")->fetchColumn()) ?></p>
            </div>

            <h2>🛠 Management Actions</h2>
            <div style="margin: 20px 0;">
                <?php if ($scheduler_status === 'OFF' || $scheduler_status === 'DISABLED'): ?>
                    <a href="?action=enable_scheduler" class="btn btn-success">🟢 Enable Event Scheduler</a>
                <?php else: ?>
                    <a href="?action=disable_scheduler" class="btn btn-warning">🟡 Disable Event Scheduler</a>
                <?php endif; ?>

                <a href="?action=create_events" class="btn">📅 Create Cleanup Events</a>
                <a href="?action=drop_events" class="btn btn-danger">🗑 Remove All Events</a>
                <a href="?action=test_cleanup" class="btn btn-warning">🧪 Test Manual Cleanup</a>
            </div>

            <h2>📅 Event Status</h2>
            <?php if (empty($events_status)): ?>
                <div class="alert info">
                    No cleanup events found. Click "Create Cleanup Events" to set them up.
                </div>
            <?php else: ?>
                <table>
                    <thead>
                        <tr>
                            <th>Event Name</th>
                            <th>Status</th>
                            <th>Schedule</th>
                            <th>Last Executed</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($events_status as $event): ?>
                        <tr>
                            <td><?= htmlspecialchars($event['EVENT_NAME']) ?></td>
                            <td>
                                <span class="status-indicator status-<?= strtolower($event['STATUS']) ?>"></span>
                                <?= htmlspecialchars($event['STATUS']) ?>
                            </td>
                            <td>Every <?= htmlspecialchars($event['INTERVAL_VALUE']) ?> <?= htmlspecialchars($event['INTERVAL_FIELD']) ?></td>
                            <td><?= $event['LAST_EXECUTED'] ? htmlspecialchars($event['LAST_EXECUTED']) : 'Never' ?></td>
                            <td><?= htmlspecialchars($event['EVENT_COMMENT']) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>

            <h2>📋 Setup Instructions</h2>
            <div class="alert info">
                <strong>Quick Setup:</strong>
                <ol>
                    <li>Enable Event Scheduler (if not already enabled)</li>
                    <li>Create Cleanup Events</li>
                    <li>Verify events are running</li>
                    <li>Monitor cleanup logs</li>
                </ol>
            </div>

            <h3>🔧 Manual SQL Commands</h3>
            <p>You can also run these SQL commands directly in your MySQL client:</p>

            <div class="code-block">
-- Enable Event Scheduler
SET GLOBAL event_scheduler = ON;

-- Check status
SELECT @@event_scheduler;

-- View all events
SHOW EVENTS;
            </div>

            <h3>⚠️ Important Notes</h3>
            <div class="alert warning">
                <ul>
                    <li><strong>Privileges:</strong> Your MySQL user needs EVENT privileges</li>
                    <li><strong>Persistence:</strong> Add <code>event_scheduler = ON</code> to your MySQL config file (my.cnf/my.ini)</li>
                    <li><strong>Monitoring:</strong> Check MySQL error logs if events don't execute</li>
                    <li><strong>Backup:</strong> Events are database-specific and should be included in backups</li>
                </ul>
            </div>

            <h2>📈 Cleanup Statistics</h2>
            <div class="status-card">
                <p><strong>Active Tokens:</strong> <?= $cleanup_stats['active_tokens'] ?? 0 ?></p>
                <p><strong>Expired Tokens:</strong> <?= $cleanup_stats['expired_tokens'] ?? 0 ?></p>
                <p><strong>Last Cleanup:</strong> <?= $cleanup_stats['last_cleanup'] ?? 'Never' ?></p>
            </div>

            <p>
                <a href="cleanup_dashboard.php" class="btn">📊 Cleanup Dashboard</a>
                <a href="test_persistent_login.php" class="btn">🧪 Test System</a>
                <a href="?refresh=1" class="btn">🔄 Refresh</a>
            </p>
        </div>
    </div>
</body>
</html>
