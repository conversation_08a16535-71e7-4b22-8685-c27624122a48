async function initiateCheckout(cartData) {
    try {
        const response = await fetch('start_checkout.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                cart: cartData.items,
                total_amount: cartData.total,
                currency: 'TZS'
            }),
            credentials: 'same-origin'
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Checkout failed');
        }

        if (data.status === 'success') {
            // Initialize Flutterwave payment
            FlutterwaveCheckout({
                public_key: data.payment_details.public_key,
                tx_ref: data.payment_details.tx_ref,
                amount: data.payment_details.amount,
                currency: data.payment_details.currency,
                payment_options: "card,mobilemoney,applepay",
                redirect_url: data.payment_details.redirect_url,
                customer: data.payment_details.customer,
                customizations: data.payment_details.customizations,
                callback: function(response) {
                    // Handle callback here
                    window.location.href = data.payment_details.redirect_url;
                },
                onclose: function() {
                    // Handle modal close
                    console.log("Payment modal closed");
                }
            });
        } else {
            throw new Error(data.message || 'Checkout initialization failed');
        }
    } catch (error) {
        console.error('Checkout Error:', error);
        alert('Checkout failed: ' + error.message);
    }
}