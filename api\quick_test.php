<?php
/**
 * Quick API Test
 * Simple test to verify the API is working after fixing syntax errors
 */

echo "Testing API Components...\n\n";

// Test 1: Include configuration
echo "1. Testing configuration...\n";
try {
    require_once __DIR__ . '/config/config.php';
    echo "✓ Configuration loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Configuration error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Test Database connection
echo "\n2. Testing Database connection...\n";
try {
    $stmt = $pdo->query("SELECT 1");
    echo "✓ Database connection successful\n";

    // Test Database class
    $db = new Database();
    echo "✓ Database class instantiated successfully\n";
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}

// Test 3: Test JWT functionality
echo "\n3. Testing JWT functionality...\n";
try {
    $payload = ['user_id' => 1, 'exp' => time() + 3600];
    $token = SimpleJWT::encode($payload, JWT_SECRET);
    $decoded = SimpleJWT::decode($token, JWT_SECRET);

    if ($decoded['user_id'] == 1) {
        echo "✓ JWT encoding/decoding working correctly\n";
    } else {
        echo "✗ JWT test failed - data mismatch\n";
    }
} catch (Exception $e) {
    echo "✗ JWT error: " . $e->getMessage() . "\n";
}

// Test 4: Test Response class
echo "\n4. Testing Response class...\n";
try {
    // This would normally exit, so we'll just check if the class exists
    if (class_exists('Response')) {
        echo "✓ Response class available\n";
    } else {
        echo "✗ Response class not found\n";
    }
} catch (Exception $e) {
    echo "✗ Response class error: " . $e->getMessage() . "\n";
}

// Test 5: Test Validator class
echo "\n5. Testing Validator class...\n";
try {
    $validator = new Validator(['email' => '<EMAIL>']);
    $validator->required('email')->email('email');

    if ($validator->passes()) {
        echo "✓ Validator working correctly\n";
    } else {
        echo "✗ Validator test failed\n";
    }
} catch (Exception $e) {
    echo "✗ Validator error: " . $e->getMessage() . "\n";
}

// Test 6: Test Auth class
echo "\n6. Testing Auth class...\n";
try {
    $user_data = [
        'cust_id' => 1,
        'cust_email' => '<EMAIL>',
        'cust_fname' => 'Test',
        'cust_lname' => 'User'
    ];

    $token = Auth::generateToken($user_data);

    if (!empty($token)) {
        echo "✓ Auth token generation working\n";
    } else {
        echo "✗ Auth token generation failed\n";
    }
} catch (Exception $e) {
    echo "✗ Auth error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Summary ===\n";
echo "All core API components have been tested.\n";
echo "If you see ✓ marks above, the API is ready to use!\n";
echo "\nNext steps:\n";
echo "1. Visit /api/install.php to complete installation\n";
echo "2. Test the full API with /api/test.php\n";
echo "3. View documentation at /api/v1/docs\n";
echo "\nAPI is ready for Flutter integration! 🚀\n";
