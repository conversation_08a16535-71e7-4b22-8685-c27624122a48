<?php
/**
 * API Logger Utility
 * Handles logging of API requests and responses
 */

class Logger {
    
    private static $start_time;
    private static $db;
    
    /**
     * Initialize logger
     */
    public static function init($database = null) {
        global $pdo;
        self::$db = $database ?: new Database($pdo);
        self::$start_time = microtime(true);
    }
    
    /**
     * Log API request
     */
    public static function logRequest($endpoint, $method, $user_id = null, $request_data = null, $response_code = 200) {
        if (!self::$db) {
            self::init();
        }
        
        $response_time = self::$start_time ? round((microtime(true) - self::$start_time) * 1000, 3) : null;
        $ip_address = self::getClientIP();
        
        try {
            self::$db->insert('api_logs', [
                'endpoint' => $endpoint,
                'method' => $method,
                'ip_address' => $ip_address,
                'user_id' => $user_id,
                'request_data' => $request_data ? json_encode($request_data) : null,
                'response_code' => $response_code,
                'response_time' => $response_time,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Fail silently to avoid breaking the API
            error_log("Logger error: " . $e->getMessage());
        }
    }
    
    /**
     * Log error
     */
    public static function logError($message, $context = []) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'ERROR',
            'message' => $message,
            'context' => $context,
            'ip' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ];
        
        error_log(json_encode($log_entry));
    }
    
    /**
     * Log warning
     */
    public static function logWarning($message, $context = []) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'WARNING',
            'message' => $message,
            'context' => $context,
            'ip' => self::getClientIP()
        ];
        
        error_log(json_encode($log_entry));
    }
    
    /**
     * Log info
     */
    public static function logInfo($message, $context = []) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'INFO',
            'message' => $message,
            'context' => $context,
            'ip' => self::getClientIP()
        ];
        
        error_log(json_encode($log_entry));
    }
    
    /**
     * Get client IP address
     */
    private static function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Clean old logs (call this periodically)
     */
    public static function cleanOldLogs($days = 30) {
        if (!self::$db) {
            self::init();
        }
        
        try {
            $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $deleted = self::$db->delete('api_logs', 'created_at < ?', [$cutoff_date]);
            
            self::logInfo("Cleaned {$deleted} old log entries older than {$days} days");
            
            return $deleted;
        } catch (Exception $e) {
            self::logError("Failed to clean old logs: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get API statistics
     */
    public static function getStats($days = 7) {
        if (!self::$db) {
            self::init();
        }
        
        try {
            $since = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            // Total requests
            $total_requests = self::$db->fetchOne(
                "SELECT COUNT(*) as count FROM api_logs WHERE created_at >= ?",
                [$since]
            )['count'];
            
            // Requests by status code
            $status_codes = self::$db->fetchAll(
                "SELECT response_code, COUNT(*) as count 
                 FROM api_logs 
                 WHERE created_at >= ? 
                 GROUP BY response_code 
                 ORDER BY count DESC",
                [$since]
            );
            
            // Top endpoints
            $top_endpoints = self::$db->fetchAll(
                "SELECT endpoint, COUNT(*) as count 
                 FROM api_logs 
                 WHERE created_at >= ? 
                 GROUP BY endpoint 
                 ORDER BY count DESC 
                 LIMIT 10",
                [$since]
            );
            
            // Average response time
            $avg_response_time = self::$db->fetchOne(
                "SELECT AVG(response_time) as avg_time 
                 FROM api_logs 
                 WHERE created_at >= ? AND response_time IS NOT NULL",
                [$since]
            )['avg_time'];
            
            // Top IPs
            $top_ips = self::$db->fetchAll(
                "SELECT ip_address, COUNT(*) as count 
                 FROM api_logs 
                 WHERE created_at >= ? 
                 GROUP BY ip_address 
                 ORDER BY count DESC 
                 LIMIT 10",
                [$since]
            );
            
            return [
                'period_days' => $days,
                'total_requests' => (int)$total_requests,
                'avg_response_time' => round((float)$avg_response_time, 2),
                'status_codes' => $status_codes,
                'top_endpoints' => $top_endpoints,
                'top_ips' => $top_ips
            ];
            
        } catch (Exception $e) {
            self::logError("Failed to get API stats: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Start request timing
     */
    public static function startTimer() {
        self::$start_time = microtime(true);
    }
    
    /**
     * Get elapsed time since start
     */
    public static function getElapsedTime() {
        return self::$start_time ? round((microtime(true) - self::$start_time) * 1000, 3) : 0;
    }
}
