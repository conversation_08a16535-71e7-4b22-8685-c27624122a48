<?php
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

header('Content-Type: application/json');

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get email
    $email = trim($_POST['subscriber_email']);

    // Validate email
    if (empty($email)) {
        echo json_encode(['status' => 'error', 'message' => 'Email is required.']);
        exit;
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['status' => 'error', 'message' => 'Please enter a valid email address.']);
        exit;
    }

    try {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM tbl_subscriber WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->rowCount() > 0) {
            echo json_encode(['status' => 'error', 'message' => 'This email is already subscribed.']);
            exit;
        }

        // Insert new subscriber
        $stmt = $pdo->prepare("INSERT INTO tbl_subscriber (email) VALUES (?)");
        $stmt->execute([$email]);

        // Return success message
        echo json_encode(['status' => 'success', 'message' => 'Thank you for subscribing to our newsletter!']);
        exit;

    } catch (PDOException $e) {
        // Log error and return user-friendly message
        error_log("Newsletter subscription error: " . $e->getMessage());
        echo json_encode(['status' => 'error', 'message' => 'Sorry, there was an error processing your subscription. Please try again later.']);
        exit;
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method.']);
    exit;
} 