<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Handle contact form submission
$success_message = '';
$error_message = '';

if(isset($_POST['contact_form'])) {
    $valid = 1;
    
    if(empty($_POST['name'])) {
        $valid = 0;
        $error_message .= 'Name is required.<br>';
    }
    
    if(empty($_POST['email'])) {
        $valid = 0;
        $error_message .= 'Email is required.<br>';
    } elseif(!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        $valid = 0;
        $error_message .= 'Please enter a valid email address.<br>';
    }
    
    if(empty($_POST['subject'])) {
        $valid = 0;
        $error_message .= 'Subject is required.<br>';
    }
    
    if(empty($_POST['message'])) {
        $valid = 0;
        $error_message .= 'Message is required.<br>';
    }
    
    if($valid == 1) {
        // Here you would typically save to database or send email
        // For now, we'll just show a success message
        $success_message = 'Thank you for your message! We will get back to you within 24 hours.';
        
        // Clear form data
        unset($_POST['name']);
        unset($_POST['email']);
        unset($_POST['subject']);
        unset($_POST['message']);
    }
}

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | SMART LIFE</title>
    <meta name="description" content="Get in touch with Smart Life for smart home solutions, technical support, and expert consultation. We're here to help transform your home.">
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00c2ff',
                        'primary-dark': '#00a8e0',
                        'primary-light': '#e0f7ff',
                    },
                    fontFamily: {
                        sans: ['Sora', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700;800&display=swap');

        .bg-gradient-radial {
            background-image: radial-gradient(circle at center, rgba(0, 194, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(5deg); }
            66% { transform: translateY(5px) rotate(-5deg); }
            100% { transform: translateY(0px) rotate(0deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card-hover-effect {
            transition: all 0.3s ease;
        }

        .card-hover-effect:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
        }

        .text-gradient {
            background: linear-gradient(135deg, #00c2ff 0%, #0066ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-input {
            transition: all 0.3s ease;
        }

        .form-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 194, 255, 0.2);
        }

        .contact-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-sans">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="about.php" class="hover:text-blue-600 transition">About</a>
                <a href="all_products.php" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="contact.php" class="text-primary font-medium">Contact</a>
                <a href="faq.php" class="hover:text-blue-600 transition">FAQs</a>
                <a href="plans.php" class="hover:text-blue-600 transition">Plans</a>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-[#00c2ff]">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
                <a href="about.php" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
                <a href="all_products.php" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
                <a href="contact.php" class="block text-[#00c2ff] font-medium">Contact</a>
                <a href="faq.php" class="block text-gray-700 hover:text-[#00c2ff] transition">FAQs</a>
                <a href="plans.php" class="block text-gray-700 hover:text-[#00c2ff] transition">Plans</a>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <!-- Hero Section -->
    <section class="pt-32 pb-20 relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-32 h-32 bg-primary-light rounded-full filter blur-3xl opacity-30 animate-float"></div>
            <div class="absolute top-40 right-20 w-24 h-24 bg-purple-200 rounded-full filter blur-2xl opacity-40 animate-float" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-blue-100 rounded-full filter blur-3xl opacity-25 animate-float" style="animation-delay: 2s;"></div>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-4xl mx-auto animate-fade-in">
                <h1 class="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                    Get In <span class="text-gradient">Touch</span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
                    Ready to Transform Your Home? Let's Talk!
                </p>
                <div class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Our smart home experts are here to help you create the perfect connected living experience. 
                    Reach out for consultations, support, or any questions about our products and services.
                </div>
            </div>
        </div>

        <!-- Floating Icons -->
        <div class="absolute right-10 top-1/4 text-6xl text-primary opacity-20 animate-float">
            <i class="fas fa-phone"></i>
        </div>
        <div class="absolute left-10 top-1/2 text-5xl text-blue-500 opacity-20 animate-float" style="animation-delay: 1s;">
            <i class="fas fa-envelope"></i>
        </div>
        <div class="absolute right-1/4 bottom-20 text-4xl text-purple-500 opacity-20 animate-float" style="animation-delay: 2s;">
            <i class="fas fa-map-marker-alt"></i>
        </div>
    </section>

    <!-- Contact Methods Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Phone Contact -->
                <div class="contact-card rounded-2xl p-8 text-center card-hover-effect">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-phone text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Call Us</h3>
                    <p class="text-gray-600 mb-4">Speak directly with our smart home experts</p>
                    <a href="tel:+255123456789" class="text-primary font-semibold hover:text-primary-dark transition-colors">
                        +255 123 456 789
                    </a>
                    <p class="text-sm text-gray-500 mt-2">Mon-Sat: 9AM - 6PM</p>
                </div>

                <!-- Email Contact -->
                <div class="contact-card rounded-2xl p-8 text-center card-hover-effect">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-envelope text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Email Us</h3>
                    <p class="text-gray-600 mb-4">Send us your questions anytime</p>
                    <a href="mailto:<EMAIL>" class="text-primary font-semibold hover:text-primary-dark transition-colors">
                        <EMAIL>
                    </a>
                    <p class="text-sm text-gray-500 mt-2">Response within 24 hours</p>
                </div>

                <!-- Live Chat -->
                <div class="contact-card rounded-2xl p-8 text-center card-hover-effect">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-comments text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Live Chat</h3>
                    <p class="text-gray-600 mb-4">Get instant help from our support team</p>
                    <button class="text-primary font-semibold hover:text-primary-dark transition-colors" onclick="openLiveChat()">
                        Start Chat
                    </button>
                    <p class="text-sm text-gray-500 mt-2">Available during business hours</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form & Info Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-radial pointer-events-none"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
                <!-- Contact Form -->
                <div class="animate-slide-up">
                    <div class="bg-white rounded-3xl p-8 shadow-xl">
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Send Us a Message</h2>

                        <?php if($success_message): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                            <?php echo $success_message; ?>
                        </div>
                        <?php endif; ?>

                        <?php if($error_message): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                            <?php echo $error_message; ?>
                        </div>
                        <?php endif; ?>

                        <form method="post" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                    <input type="text" id="name" name="name"
                                           value="<?php if(isset($_POST['name'])){echo $_POST['name'];} ?>"
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                           placeholder="Your full name" required>
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                    <input type="email" id="email" name="email"
                                           value="<?php if(isset($_POST['email'])){echo $_POST['email'];} ?>"
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                           placeholder="<EMAIL>" required>
                                </div>
                            </div>

                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                                <select id="subject" name="subject"
                                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" required>
                                    <option value="">Select a subject</option>
                                    <option value="Product Inquiry" <?php if(isset($_POST['subject']) && $_POST['subject']=='Product Inquiry') echo 'selected'; ?>>Product Inquiry</option>
                                    <option value="Technical Support" <?php if(isset($_POST['subject']) && $_POST['subject']=='Technical Support') echo 'selected'; ?>>Technical Support</option>
                                    <option value="Installation Service" <?php if(isset($_POST['subject']) && $_POST['subject']=='Installation Service') echo 'selected'; ?>>Installation Service</option>
                                    <option value="General Question" <?php if(isset($_POST['subject']) && $_POST['subject']=='General Question') echo 'selected'; ?>>General Question</option>
                                    <option value="Partnership" <?php if(isset($_POST['subject']) && $_POST['subject']=='Partnership') echo 'selected'; ?>>Partnership</option>
                                    <option value="Other" <?php if(isset($_POST['subject']) && $_POST['subject']=='Other') echo 'selected'; ?>>Other</option>
                                </select>
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                                <textarea id="message" name="message" rows="6"
                                          class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                                          placeholder="Tell us how we can help you..." required><?php if(isset($_POST['message'])){echo $_POST['message'];} ?></textarea>
                            </div>

                            <button type="submit" name="contact_form"
                                    class="w-full bg-primary hover:bg-primary-dark text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="animate-slide-up" style="animation-delay: 0.2s;">
                    <div class="space-y-8">
                        <!-- Office Location -->
                        <div class="bg-white rounded-2xl p-8 shadow-lg">
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Visit Our Office</h3>
                            <div class="space-y-4">
                                <div class="flex items-start space-x-4">
                                    <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-map-marker-alt text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">Address</h4>
                                        <p class="text-gray-600">123 Smart Street<br>Dar es Salaam, Tanzania</p>
                                    </div>
                                </div>

                                <div class="flex items-start space-x-4">
                                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-clock text-white"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">Business Hours</h4>
                                        <p class="text-gray-600">
                                            Monday - Friday: 9:00 AM - 6:00 PM<br>
                                            Saturday: 10:00 AM - 4:00 PM<br>
                                            Sunday: Closed
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Map Placeholder -->
                        <div class="bg-gray-200 rounded-2xl h-64 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-map text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-500 font-medium">Interactive Map</p>
                                <p class="text-sm text-gray-400">Location: Dar es Salaam, Tanzania</p>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl p-6 text-white">
                            <h4 class="text-xl font-bold mb-4">Emergency Support</h4>
                            <p class="mb-4 opacity-90">Need urgent technical assistance?</p>
                            <a href="tel:+255987654321" class="inline-flex items-center bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-phone mr-2"></i>
                                +255 987 654 321
                            </a>
                            <p class="text-sm opacity-75 mt-2">Available 24/7 for existing customers</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Quick Links -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Quick Answers</h2>
                <p class="text-lg text-gray-600">Find instant answers to common questions</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <a href="faq.php" class="card-hover-effect bg-gray-50 rounded-xl p-6 text-center border border-gray-200 hover:border-primary transition-colors">
                    <i class="fas fa-question-circle text-3xl text-primary mb-4"></i>
                    <h4 class="font-semibold text-gray-900 mb-2">General FAQs</h4>
                    <p class="text-sm text-gray-600">Common questions about our products and services</p>
                </a>

                <a href="plans.php" class="card-hover-effect bg-gray-50 rounded-xl p-6 text-center border border-gray-200 hover:border-primary transition-colors">
                    <i class="fas fa-shield-alt text-3xl text-primary mb-4"></i>
                    <h4 class="font-semibold text-gray-900 mb-2">Care Plans</h4>
                    <p class="text-sm text-gray-600">Learn about our upcoming maintenance and support plans</p>
                </a>

                <a href="all_products.php" class="card-hover-effect bg-gray-50 rounded-xl p-6 text-center border border-gray-200 hover:border-primary transition-colors">
                    <i class="fas fa-shopping-bag text-3xl text-primary mb-4"></i>
                    <h4 class="font-semibold text-gray-900 mb-2">Product Catalog</h4>
                    <p class="text-sm text-gray-600">Browse our complete range of smart home devices</p>
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary-light relative overflow-hidden">
        <div class="absolute right-0 bottom-0 w-64 h-64 bg-primary rounded-full filter blur-3xl opacity-10"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-2xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Ready to Get Started?</h2>
                <p class="text-lg text-gray-600 mb-8">
                    Don't wait to transform your home into a smart, connected space. Our team is ready to help you every step of the way.
                </p>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="all_products.php" class="px-8 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-full shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Shop Now
                    </a>
                    <a href="tel:+255123456789" class="px-8 py-3 bg-white text-primary border border-primary rounded-full font-medium shadow-sm hover:bg-gray-50 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-phone mr-2"></i>
                        Call Now
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function openMobileMenu() {
                mobileMenu.classList.remove('translate-x-full');
                mobileMenuBackdrop.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            function closeMobileMenuFunc() {
                mobileMenu.classList.add('translate-x-full');
                mobileMenuBackdrop.classList.add('hidden');
                document.body.style.overflow = '';
            }

            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', openMobileMenu);
            }

            if (closeMobileMenu) {
                closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
            }

            if (mobileMenuBackdrop) {
                mobileMenuBackdrop.addEventListener('click', closeMobileMenuFunc);
            }

            // Form validation enhancement
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });

                input.addEventListener('input', function() {
                    if (this.classList.contains('error')) {
                        validateField(this);
                    }
                });
            });

            function validateField(field) {
                const value = field.value.trim();
                let isValid = true;

                // Remove existing error styling
                field.classList.remove('error', 'border-red-500');

                // Basic validation
                if (field.hasAttribute('required') && !value) {
                    isValid = false;
                } else if (field.type === 'email' && value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    isValid = emailRegex.test(value);
                }

                // Apply error styling if invalid
                if (!isValid) {
                    field.classList.add('error', 'border-red-500');
                } else {
                    field.classList.add('border-green-500');
                    setTimeout(() => {
                        field.classList.remove('border-green-500');
                    }, 2000);
                }

                return isValid;
            }

            // Form submission enhancement
            form.addEventListener('submit', function(e) {
                let isFormValid = true;

                inputs.forEach(input => {
                    if (!validateField(input)) {
                        isFormValid = false;
                    }
                });

                if (!isFormValid) {
                    e.preventDefault();
                    // Scroll to first error
                    const firstError = form.querySelector('.error');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstError.focus();
                    }
                }
            });
        });

        // Live chat function
        function openLiveChat() {
            // This would typically integrate with a live chat service
            alert('Live chat feature coming soon! For immediate assistance, please call us at +255 123 456 789 or send us an email.');
        }
    </script>

</body>
</html>
