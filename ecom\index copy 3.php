<?php
// index.php


ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// ----------------------
// FETCH SETTINGS
// ----------------------
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright'])
    ? $settings['footer_copyright']
    : "© 2025 SMART LIFE. All rights reserved.";

// ----------------------
// FETCH SLIDER DATA
// ----------------------
$statement = $pdo->prepare("SELECT * FROM tbl_slider");
$statement->execute();
$sliders = $statement->fetchAll(PDO::FETCH_ASSOC);

// ----------------------
// FETCH FEATURED PRODUCTS (for "Signature Blends")
$statement = $pdo->prepare("SELECT * FROM tbl_product WHERE p_is_active=? AND p_is_featured=? LIMIT 3");
$statement->execute(array(1, 1));
$featured_products = $statement->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SMART | Security</title>
  <!-- External CSS -->
  <link rel="stylesheet" href="css/style.css" />
  
  <!-- Inline CSS for Hero Section, Slider Carousel, and Products Section -->
  <style>
    
    /* --- Hero Section & Slider Carousel Styles --- */
    .hero {
      height: 70vh;
      display: flex;
      align-items: center;
      background-color: var(--light);
      position: relative;
      overflow: hidden;
    }
    .hero::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at center, rgba(255,255,255,0.3) 0%, rgba(248,249,250,0.9) 100%);
      z-index: 1;
    }
    .hero-video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.6;
    }
    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 1700px;
    }
    .item {
      opacity: 0;
      transition: opacity 0.8s ease-in-out;
      position: absolute;
      width: 100%;
      height: 100%;
    }
    .item.active {
      opacity: 1;
      z-index: 1;
    }
    #bootstrap-touch-slider {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    #bootstrap-touch-slider .carousel-inner,
    #bootstrap-touch-slider .item {
      width: 100%;
      height: 100%;
    }
    #bootstrap-touch-slider .item {
      background-size: cover;
      background-position: center center;
      opacity: 0;
      transition: opacity 1s ease-in-out;
    }
    #bootstrap-touch-slider .item.active {
      position: relative;
      opacity: 1;
    }
    #bootstrap-touch-slider .carousel-indicators {
      position: absolute;
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%);
      list-style: none;
      margin: 0;
      padding: 0;
      z-index: 100;
    }
    #bootstrap-touch-slider .carousel-indicators li {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin: 0 5px;
      background-color: #fff;
      border-radius: 50%;
      cursor: pointer;
    }
    #bootstrap-touch-slider .carousel-indicators .active {
      background-color: #000;
    }
    .slide-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
      color: #fff;
      text-align: center;
      width: 80%;
    }
    .slide_style_left { text-align: left; left: 10%; transform: translateY(-50%); }
    .slide_style_center { text-align: center; }
    .slide_style_right { text-align: right; right: 10%; left: auto; transform: translateY(-50%); }
    .hero .carousel, .hero .carousel-inner { height: 100%; }

    
    
    /* --- Products Section Styles --- */
    .category-section {
      margin-bottom: 60px;
    }
    .products-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
      justify-content: space-between;
    }
    .product-card {
      flex: 1 0 calc(33.333% - 20px);  /* Three items per row */
      background: #fff;
      border: 1px solid #eee;
      padding: 15px;
      text-align: center;
      box-shadow: var(--shadow);
      transition: transform 0.3s;
      box-sizing: border-box;
    }
    .product-card:hover {
      transform: translateY(-5px);
    }
    .product-img img {
      max-width: 100%;
      height: auto;
      margin-bottom: 10px;
    }
    .product-title {
      font-size: 1.2rem;
      margin-bottom: 5px;
    }
    .product-price {
      font-size: 1rem;
      margin-bottom: 10px;
      color: var(--secondary);
    }
    .product-card p {
      font-size: 0.9rem;
      margin-bottom: 10px;
    }
    .btn.add-to-cart-btn {
      background-color: var(--secondary);
      color: var(--primary);
      padding: 8px 15px;
      text-decoration: none;
      display: inline-block;
      border-radius: 4px;
      transition: background-color 0.3s;
    }
    .btn.add-to-cart-btn:hover {
      background-color: var(--accent);
    }
    .view-more {
      text-align: center;
    }
    .btn.view-more-btn {
      background-color: var(--secondary);
      color: var(--primary);
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 4px;
      display: inline-block;
      transition: background-color 0.3s;
    }
    .btn.view-more-btn:hover {
      background-color: var(--accent);
    }
    
    /* --- Responsive Styling (Vertical arrangement on small screens) --- */
    @media (max-width: 768px) {
      .products-grid {
        flex-direction: column;
      }
      .product-card {
        flex: 1 0 100%;
      }
    }
    @media (max-width: 768px) { /* Target tablets and phones */
    .hero {
        height: 60vh; /* Reduce overall height slightly */
        /* Maybe force content alignment if needed */
        /* display: flex; */
        /* flex-direction: column; */
        /* justify-content: center; */
    }

    /* Adjust the static content that appears above the slider */
    .hero-content {
        padding: 0 48px; /* Add some horizontal padding */
        /* Reduce text sizes */
    }
    .hero-title {
        font-size: 2rem; /* Decrease font size */
        line-height: 1.2;
        margin-bottom: 10px;
    }
    .hero-subtitle {
        font-size: 0.9rem; /* Decrease font size */
        margin-bottom: 20px;
        max-width: 50%; /* Prevent it being too wide */
        margin-left: 0.8%;
        margin-right: auto;
    }
    .hero-btn {
        padding: 8px 16px; /* Slightly smaller button */
        font-size: 0.9rem;
    }

    /* Adjust the text *within* the slider items */
    .slide-text {
        width: 90%; /* Allow text block to use more width */
        padding: 0 10px; /* Add internal padding */
        /* Adjust positioning if needed, e.g., move slightly down */
        /* top: 55%; */
    }

    /* Reduce font sizes for text inside the slider (if you uncomment it) */
    .slide-text h1 {
        font-size: 1.8rem; /* Decrease font size */
        margin-bottom: 8px;
    }
    .slide-text p {
        font-size: 0.85rem; /* Decrease font size */
        /* Optionally hide on very small screens if still too cluttered */
        /* display: none; */
    }
     .slide-text .btn { /* If slider has buttons */
        padding: 6px 12px;
        font-size: 0.8rem;
     }


    /* Center alignment might work better on mobile for all positions */
    .slide_style_left,
    .slide_style_right {
        text-align: center; /* Force center */
        left: 50%; /* Re-center */
        transform: translate(-50%, -50%); /* Standard centering transform */
        right: auto; /* Reset right positioning */
    }

    /* Optional: Make controls less intrusive */
     .carousel-control .fa {
        font-size: 24px; /* Smaller arrows */
     }
     .carousel-indicators {
         bottom: 10px; /* Adjust position */
     }
     .carousel-indicators li {
         width: 10px;
         height: 10px;
     }
}

@media (max-width: 480px) { /* Even smaller screens */
    .hero {
        height: 55vh; /* Further reduce height */
    }
     .hero-title {
        font-size: 1.7rem;
     }
     .hero-subtitle {
        font-size: 0.8rem;
        /* display: none; /* Option to hide subtitle entirely */
     }
     .slide-text h1 {
         font-size: 1.5rem;
     }
     .slide-text p {
         font-size: 0.8rem;
         /* display: none; /* Hide slider text paragraph */
     }
}

   
    /* Other existing CSS (About, Gallery, Contact, Footer) remains unchanged */
  </style>
</head>
<body>
  <!-- Header Section -->
  <header>
    <div class="container">
      <nav>
        <a href="#" class="logo">SMART LIFE<span>.</span></a>
        <div class="nav-links">
          <a href="#home">Home</a>
          <a href="#about">About</a>
          <a href="#products">Products</a>
          <a href="#gallery">Best Deals</a>
          <a href="#contact">Contact</a>
          <!-- Cart Button -->
          <div class="cart-icon">
            <a href="cart.php">
              🛒 <span class="cart-count">0</span>
            </a>
          </div>
        </div>
        <div class="mobile-menu">
          <div class="menu-btn">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </nav>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="hero" id="home">
    <div class="container hero-content">
      <h1 class="hero-title"> Your Gateway to a Smarter <span>Luxurious Home</span></h1>
      <p class="hero-subtitle">Experience cutting-edge home automation with our premium smart devices, engineered for seamless connectivity and intelligent control</p>
      <a href="#products" class="btn hero-btn">SHOP NOW <span class="btn-icon">→</span></a>
    </div>
    <div class="left-gradient"></div>
    <div id="bootstrap-touch-slider" class="carousel bs-slider fade control-round indicators-line" data-ride="carousel" data-pause="hover" data-interval="false">
      <!-- Indicators -->
      <ol class="carousel-indicators">
        <?php
        $i = 0;
        foreach ($sliders as $row):
        ?>
          <li data-target="#bootstrap-touch-slider" data-slide-to="<?php echo $i; ?>" <?php if ($i == 0) echo 'class="active"'; ?>></li>
        <?php
          $i++;
        endforeach;
        ?>
      </ol>
      <!-- Slides Wrapper -->
      <div class="carousel-inner" role="listbox">
        <?php
        $i = 0;
        foreach ($sliders as $row):
          $photo = !empty($row['photo']) ? "../assets/uploads/" . $row['photo'] : "./images/hero.jpg";
        ?>
          <div class="item <?php if ($i == 0) echo 'active'; ?>" style="background-image: url('<?php echo $photo; ?>');">
            <div class="bs-slider-overlay"></div>
            <div class="container">
              <div class="row">
                <div class="slide-text <?php
                  if ($row['position'] == 'Left') {
                      echo 'slide_style_left';
                  } elseif ($row['position'] == 'Center') {
                      echo 'slide_style_center';
                  } elseif ($row['position'] == 'Right') {
                      echo 'slide_style_right';
                  }
                ?>">
                  <h1 data-animation="animated <?php
                    if ($row['position'] == 'Left') {
                        echo 'zoomInLeft';
                    } elseif ($row['position'] == 'Center') {
                        echo 'flipInX';
                    } elseif ($row['position'] == 'Right') {
                        echo 'zoomInRight';
                    }
                  ?>"></h1>
                  <p data-animation="animated <?php
                    if ($row['position'] == 'Left') {
                        echo 'fadeInLeft';
                    } elseif ($row['position'] == 'Center') {
                        echo 'fadeInDown';
                    } elseif ($row['position'] == 'Right') {
                        echo 'fadeInRight';
                    }
                  ?>"></p>
                </div>
              </div>
            </div>
          </div>
        <?php
          $i++;
        endforeach;
        ?>
      </div>
      <!-- Slider Controls -->
      <a class="left carousel-control" href="#bootstrap-touch-slider" role="button" data-slide="prev">
        <span class="fa fa-angle-left" aria-hidden="true"></span>
        <span class="sr-only">Previous</span>
      </a>
      <a class="right carousel-control" href="#bootstrap-touch-slider" role="button" data-slide="next">
        <span class="fa fa-angle-right" aria-hidden="true"></span>
        <span class="sr-only">Next</span>
      </a>
    </div>
  </section>

  <!-- About Section -->
<section class="about" id="about">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Your Gateway to Intelligent Luxury</h2>
    </div>
    <div class="about-container">
      <div class="about-img">
        <img src="../admin/img/img1.jpg" alt="Luxury Smart Home System" />
      </div>
      <div class="about-content">
        <h3>Where Technology Meets Elegance</h3>
        <p>
          We curate the world's most sophisticated smart home systems for those who demand both cutting-edge innovation and 
          impeccable design. Each device is meticulously engineered to deliver uncompromising performance while elevating 
          your living space with sleek, minimalist aesthetics.
        </p>
        <p>
          Our exclusive partnerships with award-winning designers and tech innovators allow us to offer you bespoke home 
          automation solutions that blend invisibly into your luxury environment while delivering extraordinary convenience.
        </p>
        <div class="features">
          <div class="feature">
            <div class="feature-icon">🏆</div>
            <div class="feature-content">
              <h4>Designer-Grade Hardware</h4>
              <p>Brushed metal and tempered glass finishes that complement high-end interiors</p>
            </div>
          </div>
          <div class="feature">
            <div class="feature-icon">🎚️</div>
            <div class="feature-content">
              <h4>Concierge Automation</h4>
              <p>AI that learns your preferences to create perfect lighting, climate and ambiance</p>
            </div>
          </div>
          <div class="feature">
            <div class="feature-icon">🤵</div>
            <div class="feature-content">
              <h4>White-Glove Service</h4>
              <p>Dedicated smart home specialists for personalized system design and installation</p>
            </div>
          </div>
          <div class="feature">
            <div class="feature-icon">🔐</div>
            <div class="feature-content">
              <h4>Discreet Security</h4>
              <p>Enterprise-grade protection hidden behind elegant interfaces</p>
            </div>
          </div>
        </div>
        <a href="#contact" class="cta-button">Schedule Your Private Consultation</a>
      </div>
    </div>
  </div>
</section>

  <!-- Dynamically Generated Products Section -->
  <section class="products" id="products">
    <div class="container">
      <?php
      // Fetch all top-level categories that are to be shown on the menu.
      $stmt_cat = $pdo->prepare("SELECT * FROM tbl_top_category WHERE show_on_menu = 1 ORDER BY tcat_name ASC");
      $stmt_cat->execute();
      $categories = $stmt_cat->fetchAll(PDO::FETCH_ASSOC);
      
      // Loop through each category
      foreach($categories as $category):
        // For each category, fetch latest 3 active products.
        $stmt_prod = $pdo->prepare("SELECT * FROM tbl_product WHERE tcat_id = ? AND p_is_active = 1 ORDER BY p_id DESC LIMIT 3");
        $stmt_prod->execute(array($category['tcat_id']));
        $products = $stmt_prod->fetchAll(PDO::FETCH_ASSOC);
      ?>
      <div class="category-section">
        <div class="section-header">
          <h2 class="section-title"><?php echo htmlspecialchars($category['tcat_name']); ?></h2>
        </div>
        <div class="products-grid">
          <?php if(!empty($products)): ?>
            <?php foreach($products as $product): ?>
              <div class="product-card">
                <div class="product-img">
                  <img src="../assets/uploads/<?php echo htmlspecialchars($product['p_featured_photo']); ?>" alt="<?php echo htmlspecialchars($product['p_name']); ?>">
                </div>
                <div class="product-content">
                  <h3 class="product-title"><?php echo htmlspecialchars($product['p_name']); ?></h3>
                  <div class="product-price">Tsh <?php echo htmlspecialchars($product['p_current_price']); ?></div>
                  <p><?php echo (strlen($product['p_short_description']) > 100)
                          ? substr(htmlspecialchars($product['p_short_description']), 0, 100) . '...'
                          : htmlspecialchars($product['p_short_description']); ?></p>
                  <button class="add-to-cart" data-product-id="<?php echo $product['p_id']; ?>">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon">
        <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
    </svg>
    Add to Cart
</button>
                </div>
              </div>
            <?php endforeach; ?>
          <?php else: ?>
            <p>No products found in this category.</p>
          <?php endif; ?>
        </div>
        <div class="view-more">
          <a href="category.php?id=<?php echo $category['tcat_id']; ?>" class="btn view-more-btn">View More &rarr;</a>
        </div>
      </div>
      <?php endforeach; ?>
    </div>
  </section>

  <!-- Best Deals Section -->
<section class="best-deals" id="gallery">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">🔥 HOT DEALS 🔥</h2>
      <p>Limited-time offers on our most popular smart home devices</p>
    </div>
    <div class="deals-container">
      <?php
      // Fetch 5 cheapest active products
      $stmt_deals = $pdo->prepare("SELECT * FROM tbl_product 
                                  WHERE p_is_active = 1 
                                  ORDER BY p_current_price ASC 
                                  LIMIT 5");
      $stmt_deals->execute();
      $deals = $stmt_deals->fetchAll(PDO::FETCH_ASSOC);
      
      foreach($deals as $deal):
        // Calculate discount percentage if old price exists
        $discount = '';
        if($deal['p_old_price'] > 0) {
          $discount_percent = round(($deal['p_old_price'] - $deal['p_current_price']) / $deal['p_old_price'] * 100);
          $discount = '<span class="discount-badge">🔥 '.$discount_percent.'% OFF</span>';
        }
      ?>
      <div class="deal-card">
        <?php echo $discount; ?>
        <div class="deal-img">
          <img src="../assets/uploads/<?php echo htmlspecialchars($deal['p_featured_photo']); ?>" alt="<?php echo htmlspecialchars($deal['p_name']); ?>">
        </div>
        <div class="deal-content">
          <h3 class="deal-title"><?php echo htmlspecialchars($deal['p_name']); ?></h3>
          <div class="price-container">
            <?php if($deal['p_old_price'] > 0): ?>
              <span class="old-price">Tsh <?php echo number_format($deal['p_old_price']); ?></span>
            <?php endif; ?>
            <span class="current-price">Tsh <?php echo number_format($deal['p_current_price']); ?></span>
          </div>
          <p class="deal-description"><?php echo htmlspecialchars(substr($deal['p_short_description'], 0, 80)); ?>...</p>
          <button class="add-to-cart" data-product-id="<?php echo $deal['p_id']; ?>">
            🛒 Add to Cart
          </button>
        </div>
      </div>
      <?php endforeach; ?>
    </div>
    <div class="view-more">
      <a href="deals.php" class="btn view-more-btn">View All Deals 🔥</a>
    </div>
  </div>
</section>
  <!-- Contact Section -->
  <section class="contact" id="contact">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Get In Touch</h2>
        <p>Have questions about our coffee or interested in wholesale partnerships? We'd love to hear from you.</p>
      </div>
      <div class="contact-container">
        <div class="contact-info">
          <h3>Let's Connect</h3>
          <ul class="info-list">
            <li class="info-item">
              <div class="info-icon">📍</div>
              <div class="info-content">
                <h4>Our Flagship Store</h4>
                <p>1235 Coffee Avenue, Design District<br />New York, NY 10001</p>
              </div>
            </li>
            <li class="info-item">
              <div class="info-icon">⏰</div>
              <div class="info-content">
                <h4>Opening Hours</h4>
                <p>Monday - Friday: 7AM - 8PM<br />Saturday - Sunday: 8AM - 9PM</p>
              </div>
            </li>
            <li class="info-item">
              <div class="info-icon">📞</div>
              <div class="info-content">
                <h4>Contact</h4>
                <p>Phone: +****************<br />Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
              </div>
            </li>
          </ul>
          <div class="social-links">
            <a href="#" class="social-link">𝕏</a>
            <a href="#" class="social-link">𝕀</a>
            <a href="#" class="social-link">𝔽</a>
            <a href="#" class="social-link">𝕐</a>
          </div>
        </div>
        <div class="contact-form">
          <h3>Send a Message</h3>
          <form action="contact_process.php" method="post">
            <div class="form-group">
              <input type="text" class="form-control" name="name" placeholder="Your Name" required />
            </div>
            <div class="form-group">
              <input type="email" class="form-control" name="email" placeholder="Your Email" required />
            </div>
            <div class="form-group">
              <input type="text" class="form-control" name="subject" placeholder="Subject" />
            </div>
            <div class="form-group">
              <textarea class="form-control" name="message" placeholder="Your Message" required></textarea>
            </div>
            <button type="submit" class="btn">Send Message <span class="btn-icon">→</span></button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer>
    <div class="container">
      <div class="footer-top">
        <div class="footer-col">
          <div class="footer-logo">SMART LIFE<span>.</span></div>
          <p>Your Gateway to a Smarter Luxurious Home</p>
        </div>
        <div class="footer-col">
          <h4>Quick Links</h4>
          <ul class="footer-links">
            <li><a href="#home">Home</a></li>
            <li><a href="#about">About Us</a></li>
            <li><a href="#products">Products</a></li>
            <li><a href="#gallery">Best Deals</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h4>Information</h4>
          <ul class="footer-links">
            <li><a href="#">Subscription Details</a></li>
            <li><a href="#">Brewing Guides</a></li>
            <li><a href="#">Shipping Policy</a></li>
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms &amp; Conditions</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h4>Newsletter</h4>
          <p>Subscribe to our newsletter for exclusive offers and coffee insights.</p>
          <form class="newsletter-form" action="subscribe.php" method="post">
            <input type="email" class="newsletter-input" name="subscriber_email" placeholder="Your Email" required />
            <button type="submit" class="newsletter-btn">→</button>
          </form>
        </div>
      </div>
      <div class="footer-bottom">
        <div class="copyright"><?php echo $footer_copyright; ?></div>
        <div class="footer-nav">
          <a href="#">Privacy</a>
          <a href="#">Terms</a>
          <a href="#">Sitemap</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- External JavaScript -->
  <script src="js/script.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      // Initialize cart if not exists
      if (!localStorage.getItem('cart')) {
          localStorage.setItem('cart', JSON.stringify([]));
      }
      
      // Update cart count display
      updateCartCount();
      
      // Add to cart buttons
      document.querySelectorAll('.add-to-cart').forEach(button => {
          button.addEventListener('click', function() {
              const productId = this.getAttribute('data-product-id');
              addToCart(productId);
          });
      });
      
      function addToCart(productId) {
          let cart = JSON.parse(localStorage.getItem('cart'));
          
          // Check if product already in cart
          const existingItem = cart.find(item => item.id === productId);
          
          if (existingItem) {
              existingItem.quantity += 1;
          } else {
              cart.push({
                  id: productId,
                  quantity: 1
              });
          }
          
          localStorage.setItem('cart', JSON.stringify(cart));
          updateCartCount();
          
          // Show added notification
          alert('Product added to cart!');
      }
      
      function updateCartCount() {
          const cart = JSON.parse(localStorage.getItem('cart'));
          const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
          document.querySelector('.cart-count').textContent = totalItems;
      }
    });
    document.addEventListener("DOMContentLoaded", function() {
      var slider = document.getElementById("bootstrap-touch-slider");
      if (!slider) return;
      var items = slider.querySelectorAll(".item");
      var indicators = slider.querySelectorAll(".carousel-indicators li");
      var current = 0;
      if (items.length <= 1) return;
      function nextSlide() {
        items[current].classList.remove("active");
        if (indicators.length > 0) indicators[current].classList.remove("active");
        current = (current + 1) % items.length;
        items[current].classList.add("active");
        if (indicators.length > 0) indicators[current].classList.add("active");
      }
      var slideInterval = setInterval(nextSlide, 5000);
      indicators.forEach(function(indicator, index) {
        indicator.addEventListener("click", function() {
          clearInterval(slideInterval);
          items[current].classList.remove("active");
          if (indicators.length > 0) indicators[current].classList.remove("active");
          current = index;
          items[current].classList.add("active");
          if (indicators.length > 0) indicators[current].classList.add("active");
          slideInterval = setInterval(nextSlide, 5000);
        });
      });
    });
  </script>
</body>
</html>
