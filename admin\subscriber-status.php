<?php
require_once('inc/config.php');

header('Content-Type: application/json');

// Check if it's an AJAX request
if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // Validate input
    if(isset($_POST['id']) && isset($_POST['status'])) {
        $id = (int)$_POST['id'];
        $status = (int)$_POST['status'];
        
        try {
            // Update the status
            $statement = $pdo->prepare("UPDATE tbl_subscriber SET status = ? WHERE id = ?");
            $statement->execute(array($status, $id));
            
            // Check if update was successful
            if($statement->rowCount() > 0) {
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'message' => 'No changes made']);
            }
        } catch(PDOException $e) {
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid input']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
}
?> 