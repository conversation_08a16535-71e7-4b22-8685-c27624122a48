<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include "functions.php";

// Clear logout flag when user visits register page (they want to register)
if (isset($_SESSION['just_logged_out'])) {
    unset($_SESSION['just_logged_out']);
}

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Fetch settings for footer
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© 2025 SMART LIFE. All rights reserved.";

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Register | SMART LIFE</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    /* Theme Variables */
    :root {
      --bg-primary: #f8f9fa;
      --bg-secondary: #ffffff;
      --bg-tertiary: #f3f4f6;
      --text-primary: #1f2937;
      --text-secondary: #6b7280;
      --text-tertiary: #9ca3af;
      --border-color: #e5e7eb;
      --shadow-color: rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] {
      --bg-primary: #111827;
      --bg-secondary: #1f2937;
      --bg-tertiary: #374151;
      --text-primary: #f9fafb;
      --text-secondary: #d1d5db;
      --text-tertiary: #9ca3af;
      --border-color: #374151;
      --shadow-color: rgba(0, 0, 0, 0.3);
    }

    body {
      background-color: var(--bg-primary);
      color: var(--text-primary);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Theme toggle button */
    .theme-toggle {
      position: relative;
      width: 60px;
      height: 30px;
      background: var(--bg-tertiary);
      border-radius: 15px;
      border: 1px solid var(--border-color);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .theme-toggle::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 24px;
      height: 24px;
      background: var(--bg-secondary);
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px var(--shadow-color);
    }

    [data-theme="dark"] .theme-toggle::before {
      transform: translateX(28px);
    }

    .theme-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .theme-icon.sun {
      left: 6px;
      color: #fbbf24;
    }

    .theme-icon.moon {
      right: 6px;
      color: #60a5fa;
    }

    [data-theme="dark"] .theme-icon.sun {
      opacity: 0.3;
    }

    [data-theme="light"] .theme-icon.moon {
      opacity: 0.3;
    }

    /* Auth Container Styling */
    .auth-container {
      background: var(--bg-secondary);
      border: 1px solid var(--border-color);
      box-shadow: 0 10px 25px var(--shadow-color);
      border-radius: 16px;
      padding: 2.5rem;
      max-width: 600px;
      margin: 0 auto;
      position: relative;
      backdrop-filter: blur(10px);
    }

    .auth-container h2 {
      color: var(--text-primary);
      font-size: 2rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 2rem;
      background: linear-gradient(135deg, #4f46e5, #06b6d4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    .form-control {
      width: 100%;
      padding: 0.875rem 1rem;
      border: 2px solid var(--border-color);
      border-radius: 12px;
      font-size: 1rem;
      background: var(--bg-primary);
      color: var(--text-primary);
      transition: all 0.3s ease;
      box-sizing: border-box;
    }

    .form-control:focus {
      outline: none;
      border-color: #4f46e5;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
      background: var(--bg-secondary);
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
        gap: 0;
      }

      .form-row .form-group {
        margin-bottom: 1.5rem;
      }
    }

    .btn {
      width: 100%;
      padding: 0.875rem 1.5rem;
      background: linear-gradient(135deg, #4f46e5, #06b6d4);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 1rem;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
    }

    .auth-links {
      text-align: center;
      margin-top: 1.5rem;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    .auth-links a {
      color: #4f46e5;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .auth-links a:hover {
      color: #06b6d4;
      text-decoration: underline;
    }

    /* Error/Success Messages */
    .error-message, .success-message {
      padding: 1rem;
      margin-bottom: 1.5rem;
      border-radius: 12px;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .error-message {
      background: linear-gradient(135deg, #fef2f2, #fee2e2);
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    .success-message {
      background: linear-gradient(135deg, #f0fdf4, #dcfce7);
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
      .auth-container {
        margin: 1rem;
        padding: 2rem;
        max-width: none;
      }
    }
  </style>
</head>
<body>
  <!-- Theme initialization script (must be before any content) -->
  <script>
    // Initialize theme before page renders
    (function() {
      const savedTheme = localStorage.getItem('theme');
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      const theme = savedTheme || systemTheme;

      document.documentElement.setAttribute('data-theme', theme);

      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        }
      });
    })();

    // Theme Functions
    function toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    }
  </script>

  <!-- Header (from all_products.php) -->
  <header class="fixed inset-x-0 top-0 z-50" style="background: var(--bg-secondary); box-shadow: 0 1px 3px var(--shadow-color);">
    <div class="container mx-auto px-4 flex items-center justify-between py-4">
      <a href="index.php" class="text-2xl font-bold" style="color: var(--text-primary);">
        SMART LIFE<span class="text-blue-600">.</span>
      </a>
      <nav class="hidden md:flex items-center space-x-6">
        <a href="index.php#home" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Home</a>
        <a href="index.php#about" class="hover:text-blue-600 transition" style="color: var(--text-primary);">About</a>
        <a href="index.php#products" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Products</a>
        <a href="index.php#gallery" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Best Deals</a>
        <a href="index.php#contact" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Contact</a>
        <!-- Search -->
        <div class="relative">
          <form action="search_results.php" method="GET" class="flex">
            <input id="searchInput" name="q" type="text" placeholder="Search products, categories..."
                   class="w-64 px-4 py-2 rounded-l-md border focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                   style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                   autocomplete="off">
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors">
              <i class="fas fa-search"></i>
            </button>
          </form>
        </div>
        <!-- Theme Toggle -->
        <div class="theme-toggle" onclick="toggleTheme()" title="Toggle dark/light mode">
          <i class="fas fa-sun theme-icon sun"></i>
          <i class="fas fa-moon theme-icon moon"></i>
        </div>
        <!-- Cart -->
        <a href="cart.php" class="relative text-xl hover:text-blue-600 transition" style="color: var(--text-primary);">
          🛒
          <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
        </a>
      </nav>
      <!-- Mobile Menu Button -->
      <button id="mobileMenuButton" class="md:hidden flex items-center">
        <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
        </svg>
      </button>
    </div>
  </header>

  <!-- Mobile Menu -->
  <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
    <div class="flex flex-col h-full">
      <div class="flex justify-between items-center p-4 border-b">
        <a href="index.php" class="text-xl font-bold text-gray-900">
          SMART LIFE<span class="text-[#00c2ff]">.</span>
        </a>
        <button id="closeMobileMenu" class="text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
        <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
        <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
        <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
        <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
        <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>
        <!-- Cart in Mobile Menu -->
        <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
          <span class="text-xl mr-2">🛒</span>
          <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
        </a>
      </nav>
    </div>
  </div>
  <!-- Backdrop for mobile menu -->
  <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

  <main class="pt-24 pb-12 min-h-screen flex items-center justify-center">
    <div class="container mx-auto px-4">
      <div class="auth-container">
        <h2>Create Account</h2>
        <?php
        // Display messages
        if (isset($_SESSION['error_message'])) {
            echo '<div class="error-message">' . $_SESSION['error_message'] . '</div>';
            unset($_SESSION['error_message']);
        }
        if (isset($_SESSION['success_message'])) {
            echo '<div class="success-message">' . $_SESSION['success_message'] . '</div>';
            unset($_SESSION['success_message']);
        }
        ?>
        <form action="register_process.php" method="POST" enctype="multipart/form-data">
          <div class="form-row">
            <div class="form-group">
              <label for="fname">First Name</label>
              <input type="text" id="fname" name="fname" required class="form-control" placeholder="Enter your first name">
            </div>
            <div class="form-group">
              <label for="lname">Last Name</label>
              <input type="text" id="lname" name="lname" required class="form-control" placeholder="Enter your last name">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="email">Email Address</label>
              <input type="email" id="email" name="email" required class="form-control" placeholder="Enter your email">
            </div>
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone" required class="form-control" placeholder="Enter your phone number">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="password">Password</label>
              <input type="password" id="password" name="password" required class="form-control" placeholder="Create a password">
            </div>
            <div class="form-group">
              <label for="confirm_password">Confirm Password</label>
              <input type="password" id="confirm_password" name="confirm_password" required class="form-control" placeholder="Confirm your password">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="address_city">City</label>
              <input type="text" id="address_city" name="address_city" required class="form-control" placeholder="Enter your city">
            </div>
            <div class="form-group">
              <label for="country">Country</label>
              <input type="text" id="country" name="country" required class="form-control" placeholder="Enter your country">
            </div>
          </div>

          <div class="form-group">
            <label for="photo">Profile Photo (Optional)</label>
            <input type="file" id="photo" name="photo" accept="image/*" class="form-control">
          </div>

          <button type="submit" class="btn">Create Account</button>
        </form>

        <div class="auth-links">
          Already have an account? <a href="login.php">Sign in here</a>
        </div>
      </div>
    </div>
  </main>

  <?php include 'includes/footer.php'; ?>

  <script>
    // Mobile Menu functionality
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuButton = document.getElementById('mobileMenuButton');
      const mobileMenu = document.getElementById('mobileMenu');
      const closeMobileMenu = document.getElementById('closeMobileMenu');
      const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

      function openMobileMenu() {
        mobileMenu.classList.remove('translate-x-full');
        mobileMenuBackdrop.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
      }

      function closeMobileMenuFunc() {
        mobileMenu.classList.add('translate-x-full');
        mobileMenuBackdrop.classList.add('hidden');
        document.body.style.overflow = '';
      }

      if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', openMobileMenu);
      }

      if (closeMobileMenu) {
        closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
      }

      if (mobileMenuBackdrop) {
        mobileMenuBackdrop.addEventListener('click', closeMobileMenuFunc);
      }

      // Update cart count
      let cartCount = <?php
        $count = 0;
        if(isset($_SESSION['cart'])) {
          foreach($_SESSION['cart'] as $item) {
            $count += $item['quantity'];
          }
        }
        echo $count;
      ?>;

      const cartCountElements = document.querySelectorAll('.cart-count');
      cartCountElements.forEach(elem => {
        if(elem) {
          elem.textContent = cartCount;
          elem.style.display = cartCount > 0 ? 'inline-block' : 'none';
        }
      });

      // Password validation
      const password = document.getElementById('password');
      const confirmPassword = document.getElementById('confirm_password');

      function validatePasswords() {
        if (password.value !== confirmPassword.value) {
          confirmPassword.setCustomValidity('Passwords do not match');
        } else {
          confirmPassword.setCustomValidity('');
        }
      }

      password.addEventListener('input', validatePasswords);
      confirmPassword.addEventListener('input', validatePasswords);
    });
  </script>
</body>
</html>