<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us | SMART LIFE</title>
    <meta name="description" content="Learn about Smart Life - Your trusted partner for smart home automation solutions in Tanzania. Discover our mission, values, and commitment to transforming homes.">
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00c2ff',
                        'primary-dark': '#00a8e0',
                        'primary-light': '#e0f7ff',
                    },
                    fontFamily: {
                        sans: ['Sora', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700;800&display=swap');

        .bg-gradient-radial {
            background-image: radial-gradient(circle at center, rgba(0, 194, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(5deg); }
            66% { transform: translateY(5px) rotate(-5deg); }
            100% { transform: translateY(0px) rotate(0deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card-hover-effect {
            transition: all 0.3s ease;
        }

        .card-hover-effect:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
        }

        .text-gradient {
            background: linear-gradient(135deg, #00c2ff 0%, #0066ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stats-counter {
            font-variant-numeric: tabular-nums;
        }

        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-sans">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="about.php" class="text-primary font-medium">About</a>
                <a href="all_products.php" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="contact.php" class="hover:text-blue-600 transition">Contact</a>
                <a href="faq.php" class="hover:text-blue-600 transition">FAQs</a>
                <a href="plans.php" class="hover:text-blue-600 transition">Plans</a>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-[#00c2ff]">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
                <a href="about.php" class="block text-[#00c2ff] font-medium">About</a>
                <a href="all_products.php" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
                <a href="contact.php" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>
                <a href="faq.php" class="block text-gray-700 hover:text-[#00c2ff] transition">FAQs</a>
                <a href="plans.php" class="block text-gray-700 hover:text-[#00c2ff] transition">Plans</a>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <!-- Hero Section -->
    <section class="pt-32 pb-20 relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-32 h-32 bg-primary-light rounded-full filter blur-3xl opacity-30 animate-float"></div>
            <div class="absolute top-40 right-20 w-24 h-24 bg-purple-200 rounded-full filter blur-2xl opacity-40 animate-float" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-20 left-1/4 w-40 h-40 bg-blue-100 rounded-full filter blur-3xl opacity-25 animate-float" style="animation-delay: 2s;"></div>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-4xl mx-auto animate-fade-in">
                <h1 class="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                    About <span class="text-gradient">SMART LIFE</span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
                    Your Gateway to a Smarter, More Luxurious Home
                </p>
                <div class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Transforming homes across Tanzania with cutting-edge smart home automation solutions, 
                    professional installation services, and unmatched customer support.
                </div>
            </div>
        </div>

        <!-- Floating Icons -->
        <div class="absolute right-10 top-1/4 text-6xl text-primary opacity-20 animate-float">
            <i class="fas fa-home"></i>
        </div>
        <div class="absolute left-10 top-1/2 text-5xl text-blue-500 opacity-20 animate-float" style="animation-delay: 1s;">
            <i class="fas fa-lightbulb"></i>
        </div>
        <div class="absolute right-1/4 bottom-20 text-4xl text-purple-500 opacity-20 animate-float" style="animation-delay: 2s;">
            <i class="fas fa-wifi"></i>
        </div>
    </section>

    <!-- Company Story Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="animate-slide-up">
                    <h2 class="text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                        Founded with a vision to revolutionize how Tanzanians experience their homes, Smart Life has been at the forefront of smart home technology since our inception. We believe that every home deserves to be intelligent, efficient, and secure.
                    </p>
                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                        What started as a passion project to bring cutting-edge automation technology to East Africa has grown into Tanzania's most trusted smart home solutions provider. We've helped thousands of families transform their living spaces into modern, connected environments.
                    </p>
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                            <i class="fas fa-award text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Award-Winning Service</h4>
                            <p class="text-gray-600">Recognized for excellence in smart home solutions</p>
                        </div>
                    </div>
                </div>
                <div class="relative animate-slide-up" style="animation-delay: 0.2s;">
                    <div class="bg-gradient-to-br from-primary to-blue-600 rounded-3xl p-8 text-white relative overflow-hidden">
                        <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
                        <div class="relative z-10">
                            <h3 class="text-2xl font-bold mb-4">Our Mission</h3>
                            <p class="text-lg opacity-90 mb-6">
                                To make smart home technology accessible, affordable, and reliable for every Tanzanian family, while providing exceptional service that exceeds expectations.
                            </p>
                            <div class="flex items-center">
                                <i class="fas fa-rocket text-3xl mr-4"></i>
                                <span class="text-lg font-semibold">Innovation • Quality • Trust</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-gray-100">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="animate-slide-up">
                    <div class="text-4xl md:text-5xl font-bold text-primary mb-2 stats-counter" data-target="1000">0</div>
                    <p class="text-gray-600 font-medium">Happy Customers</p>
                </div>
                <div class="animate-slide-up" style="animation-delay: 0.1s;">
                    <div class="text-4xl md:text-5xl font-bold text-primary mb-2 stats-counter" data-target="50">0</div>
                    <p class="text-gray-600 font-medium">Smart Products</p>
                </div>
                <div class="animate-slide-up" style="animation-delay: 0.2s;">
                    <div class="text-4xl md:text-5xl font-bold text-primary mb-2 stats-counter" data-target="5">0</div>
                    <p class="text-gray-600 font-medium">Years Experience</p>
                </div>
                <div class="animate-slide-up" style="animation-delay: 0.3s;">
                    <div class="text-4xl md:text-5xl font-bold text-primary mb-2 stats-counter" data-target="24">0</div>
                    <p class="text-gray-600 font-medium">Support Hours</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    These principles guide everything we do and shape how we serve our customers
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card-hover-effect bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-shield-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Trust & Reliability</h3>
                    <p class="text-gray-600 leading-relaxed">
                        We build lasting relationships through honest communication, reliable products, and dependable service that you can count on.
                    </p>
                </div>

                <div class="card-hover-effect bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-leaf text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Innovation & Quality</h3>
                    <p class="text-gray-600 leading-relaxed">
                        We continuously seek the latest technologies and maintain the highest quality standards in everything we offer.
                    </p>
                </div>

                <div class="card-hover-effect bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
                        <i class="fas fa-heart text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Customer First</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Your satisfaction is our priority. We go above and beyond to ensure every customer has an exceptional experience.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-radial pointer-events-none"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Why Choose Smart Life?</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    We're more than just a smart home company - we're your partners in creating the perfect connected home
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="space-y-8">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-tools text-white"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900 mb-2">Expert Installation</h4>
                            <p class="text-gray-600">Our certified technicians ensure perfect setup and integration of all your smart devices.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-headset text-white"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900 mb-2">24/7 Support</h4>
                            <p class="text-gray-600">Round-the-clock customer support to help you with any questions or technical issues.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-mobile-alt text-white"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900 mb-2">Single App Control</h4>
                            <p class="text-gray-600">Manage all your smart devices through one intuitive app for seamless home automation.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-shield-check text-white"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900 mb-2">Quality Guarantee</h4>
                            <p class="text-gray-600">All products come with comprehensive warranties and our satisfaction guarantee.</p>
                        </div>
                    </div>
                </div>

                <div class="relative">
                    <div class="glassmorphism rounded-3xl p-8 text-center">
                        <div class="w-24 h-24 bg-gradient-to-br from-primary to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-star text-3xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Trusted by Thousands</h3>
                        <p class="text-gray-600 mb-6">
                            Join over 1,000 satisfied customers who have transformed their homes with Smart Life solutions.
                        </p>
                        <div class="flex justify-center space-x-1 mb-4">
                            <i class="fas fa-star text-yellow-400 text-xl"></i>
                            <i class="fas fa-star text-yellow-400 text-xl"></i>
                            <i class="fas fa-star text-yellow-400 text-xl"></i>
                            <i class="fas fa-star text-yellow-400 text-xl"></i>
                            <i class="fas fa-star text-yellow-400 text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-500">4.9/5 Customer Rating</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary-light relative overflow-hidden">
        <div class="absolute right-0 bottom-0 w-64 h-64 bg-primary rounded-full filter blur-3xl opacity-10"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-2xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Ready to Transform Your Home?</h2>
                <p class="text-lg text-gray-600 mb-8">
                    Let's work together to create the smart home of your dreams. Our experts are ready to help you get started.
                </p>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="all_products.php" class="px-8 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-full shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-shopping-bag mr-2"></i>
                        Shop Products
                    </a>
                    <a href="contact.php" class="px-8 py-3 bg-white text-primary border border-primary rounded-full font-medium shadow-sm hover:bg-gray-50 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-phone mr-2"></i>
                        Contact Us
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function openMobileMenu() {
                mobileMenu.classList.remove('translate-x-full');
                mobileMenuBackdrop.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }

            function closeMobileMenuFunc() {
                mobileMenu.classList.add('translate-x-full');
                mobileMenuBackdrop.classList.add('hidden');
                document.body.style.overflow = '';
            }

            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', openMobileMenu);
            }

            if (closeMobileMenu) {
                closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
            }

            if (mobileMenuBackdrop) {
                mobileMenuBackdrop.addEventListener('click', closeMobileMenuFunc);
            }

            // Stats counter animation
            function animateCounter(element, target) {
                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current);
                }, 20);
            }

            // Intersection Observer for stats animation
            const statsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = parseInt(entry.target.dataset.target);
                        animateCounter(entry.target, target);
                        statsObserver.unobserve(entry.target);
                    }
                });
            });

            document.querySelectorAll('.stats-counter').forEach(counter => {
                statsObserver.observe(counter);
            });
        });
    </script>

</body>
</html>
