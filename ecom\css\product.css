/* ============================================= */
/* === Enhanced Product Details Modal Styles === */
/* ============================================= */

/* --- The Modal Background / Overlay --- */
.modal {
    display: none;                     /* Hidden by default */
    position: fixed;                   /* Stay in place */
    z-index: 1050;                     /* Higher z-index if needed (e.g., above navbars) */
    left: 0;
    top: 0;
    width: 100%;                       /* Full width */
    height: 100%;                      /* Full height */
    overflow-y: auto;                  /* Allow scrolling ONLY on the modal overlay if content overflows viewport height */
    background-color: rgba(0, 0, 0, 0.7); /* Slightly darker overlay */
    opacity: 0;                        /* Start invisible for transition */
    transition: opacity 0.3s ease-out; /* Smoother fade-in */
  }
  
  .modal.show {
    display: block;                    /* Show when active */
    opacity: 1;                        /* Fade in */
  }
  
  /* --- The Modal Content Box --- */
  .modal-content {
    background-color: #ffffff;         /* Clean white background */
    margin: 5% auto 10% auto;        /* Centered, more bottom margin */
    padding: 30px 40px;              /* Increased padding */
    border: none;                      /* Remove basic border */
    width: 90%;                      /* Responsive width */
    max-width: 750px;                /* Slightly wider max-width */
    border-radius: 10px;               /* Slightly more rounded corners */
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); /* Softer, more diffused shadow */
    transform: translateY(-30px) scale(0.98); /* Start slightly up and smaller */
    transition: transform 0.3s ease-out, opacity 0.3s ease-out; /* Smooth transitions for transform and opacity */
    opacity: 0;                      /* Start content invisible too */
  }
  
  .modal.show .modal-content {
    transform: translateY(0) scale(1);   /* Animate to final position and size */
    opacity: 1;                      /* Fade in content */
  }
  
  /* --- The Close Button (Using Font Awesome) --- */
  /* Ensure Font Awesome is linked in your HTML <head> */
  .modal-close-btn {
    position: absolute;
    top: 15px;                         /* Adjust position */
    right: 20px;
    width: 30px;                       /* Clickable area */
    height: 30px;
    line-height: 30px;                 /* Center icon vertically */
    text-align: center;                /* Center icon horizontally */
    font-size: 1.3em;                  /* Icon size */
    color: #888;                      /* Muted color */
    background-color: transparent;     /* No background initially */
    border: none;
    border-radius: 50%;                /* Circular hover effect */
    cursor: pointer;
    transition: color 0.2s ease, background-color 0.2s ease; /* Smooth transitions */
  }
  
  .modal-close-btn:hover,
  .modal-close-btn:focus {
    color: #333;                      /* Darker on hover */
    background-color: #f0f0f0;       /* Subtle background on hover */
    outline: none;                   /* Remove focus outline if desired */
  }
  
  /* --- Modal Header/Title --- */
  .modal h2 {
    margin-top: 0;
    margin-bottom: 25px;               /* More space below title */
    color: #222;                      /* Darker title color */
    border-bottom: 1px solid #e5e5e5;  /* Lighter border */
    padding-bottom: 15px;              /* More padding below title */
    font-size: 1.6em;                  /* Larger title */
    font-weight: 600;                /* Slightly bolder */
    line-height: 1.3;
  }
  
  /* --- Modal Sections --- */
  .modal-section {
    margin-bottom: 25px;               /* Increased space between sections */
    padding-top: 15px;                 /* Add space above content within section */
    border-top: 1px dashed #eee;      /* Subtle dashed separator */
  }
  .modal-section:first-of-type {
      border-top: none;                /* No border for the very first section */
      padding-top: 0;
  }
  .modal-section:last-child {
    margin-bottom: 0;
  }
  
  /* --- Section Headings --- */
  .modal-section h4 {
    margin-top: 0;
    margin-bottom: 12px;               /* More space below heading */
    color:  #007bff;    /* Use theme's primary color */
    font-size: 1.2em;                  /* Slightly larger section headings */
    font-weight: 600;                /* Bolder section headings */
  }
  
  /* --- Text Content Styling --- */
  .modal-text-content {
    line-height: 1.7;                /* Improved readability */
    color: #444;                      /* Slightly darker text */
    font-size: 0.95em;               /* Adjust base text size if needed */
  }
  .modal-text-content p:last-child {
    margin-bottom: 0;
  }
  .modal-text-content ul,
  .modal-text-content ol {
    margin-left: 0;                  /* Reset margin */
    margin-bottom: 1em;
    padding-left: 25px;              /* More distinct indent */
  }
  .modal-text-content li {
    margin-bottom: 0.6em;              /* Slightly more space between list items */
  }
  .modal-text-content strong {
    color: #111;                      /* Stronger emphasis color */
    font-weight: 600;
  }
  .modal-text-content em {
    color: #555;
    font-style: italic;
  }
  
  /* --- Style for the Trigger Button --- */
  .details-modal-trigger {
    display: inline-block;
    margin: 15px 0 20px 0;             /* Adjust margins as needed */
    padding: 10px 18px;                /* Slightly larger padding */
    font-size: 0.95em;
    font-weight: 500;                /* Medium weight */
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    border-radius: 6px;                /* Consistent border-radius */
    transition: all 0.2s ease;
    /*
     * IMPORTANT: Adapt colors and border to match your site's
     * secondary or tertiary button styles for consistency.
     * The example below uses a common secondary style.
    */
    background-color: #6c757d;         /* Grey background */
    border: 1px solid #6c757d;
    color: #fff;                      /* White text */
  }
  
  .details-modal-trigger:hover {
    background-color: #5a6268;         /* Darker grey on hover */
    border-color: #545b62;
    color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* Subtle shadow on hover */
  }
  
  /* --- Prevent Body Scroll When Modal is Open --- */
  body.modal-open {
    overflow: hidden;
  }
 
 
 /* --- Base & Layout Styles --- */
 :root { --primary: #ffffff; --secondary: #00c2ff; --secondary-light: #e0f7ff; --accent: #00a8e0; --dark: #333333; --text: #555555; --gray: #777777; --light: #f8f9fa; --border: #dee2e6; }
 *, *::before, *::after { box-sizing: border-box; }
 body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; color: var(--text); line-height: 1.6; margin: 0; background-color: var(--light); }
 html, body { min-height: 100%; display: flex; flex-direction: column; }
 main { flex: 1; padding: 40px 0; }
 .container { width: 90%; max-width: 1200px; margin: 0 auto; padding: 0 15px; }
 header, footer { background-color: var(--primary); padding: 15px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.05); z-index: 100; position: relative; }
 header .container, footer .container { padding: 0 15px; }
 header nav { display: flex; justify-content: space-between; align-items: center; }
 header .logo { font-size: 1.5rem; font-weight: bold; text-decoration: none; color: var(--dark); }
 header .logo span { color: var(--secondary); }
 header .nav-links { display: flex; align-items: center; list-style: none; padding: 0; margin: 0; }
 header .nav-links li { margin-left: 20px; }
 header .nav-links a { text-decoration: none; color: var(--text); transition: color 0.2s; }
 header .nav-links a:hover { color: var(--secondary); }
 header .cart-icon { margin-left: 25px; }
 header .cart-icon a { text-decoration: none; color: var(--text); position: relative; font-size: 1.2rem; }
 header .cart-count { background: var(--secondary); color: white; border-radius: 50%; padding: 1px 5px; font-size: 0.7rem; position: absolute; top: -8px; right: -10px; line-height: 1; }
 footer .copyright { text-align: center; font-size: 0.9rem; color: var(--gray); padding: 15px 0; margin-top: 15px; border-top: 1px solid var(--border); }
 .mobile-menu { display: none; cursor: pointer; padding: 5px;}
 .menu-btn { width: 30px; height: 22px; position: relative; }
 .menu-btn span { display: block; width: 100%; height: 3px; background-color: var(--dark); border-radius: 3px; position: absolute; transition: all 0.3s ease; }
 .menu-btn span:nth-child(1) { top: 0; }
 .menu-btn span:nth-child(2) { top: 9px; }
 .menu-btn span:nth-child(3) { top: 18px; }
 .menu-btn.active span:nth-child(1) { transform: rotate(45deg); top: 9px; }
 .menu-btn.active span:nth-child(2) { opacity: 0; }
 .menu-btn.active span:nth-child(3) { transform: rotate(-45deg); top: 9px; }
 @media (max-width: 768px) {
     header .nav-links { display: none; position: absolute; top: 100%; left: 0; right: 0; background-color: var(--primary); flex-direction: column; padding: 15px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-top: 1px solid var(--border); align-items: flex-start; }
     header .nav-links.active { display: flex; }
     header .nav-links li { margin: 10px 0; width: 100%; }
     header .nav-links a { display: block; width: 100%; }
     header .cart-icon { margin-left: 0; margin-top: 10px; }
     .mobile-menu { display: block; }
 }

 /* --- Product Detail Specific Styles --- */
 .product-detail-container { background: var(--primary); border-radius: 8px; box-shadow: 0 5px 25px rgba(0,0,0,0.07); padding: 30px; display: grid; grid-template-columns: 1fr; gap: 40px; }
 .product-gallery { display: flex; flex-direction: column; }
 .main-image-container { position: relative; height: auto; max-height: 500px; overflow: hidden; border-radius: 8px; margin-bottom: 15px; border: 1px solid var(--border); text-align: center; background-color: #fdfdfd; display: flex; justify-content: center; align-items: center;}
 .main-image { width: auto; max-width: 100%; height: auto; max-height: 480px; display: block; object-fit: contain; transition: opacity 0.3s ease-in-out; }
 .thumbnail-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(65px, 1fr)); gap: 10px; }
 .thumbnail { width: 100%; height: 65px; object-fit: cover; border: 2px solid transparent; border-radius: 4px; cursor: pointer; transition: all 0.3s; opacity: 0.7; background-color: #eee; }
 .thumbnail:hover { opacity: 1; }
 .thumbnail.active { border-color: var(--secondary); opacity: 1; }

 .product-info { display: flex; flex-direction: column; }
 .product-title { font-size: clamp(1.5rem, 4vw, 2rem); margin-bottom: 8px; color: var(--dark); line-height: 1.2; }
 .product-price { font-size: clamp(1.4rem, 5vw, 1.8rem); color: var(--secondary); margin-bottom: 15px; font-weight: 700; min-height: 1.8rem; /* Prevent layout jump */ }
 .product-meta { margin-bottom: 15px; font-size: 0.9rem; color: var(--gray); border-bottom: 1px solid var(--border); padding-bottom: 15px; display: flex; flex-wrap: wrap; gap: 10px 20px; }
 .product-meta span { display: inline-block; }
 .product-meta strong { font-weight: 600; }
 .variation-attributes { font-size: 0.9rem; color: var(--gray); margin-bottom: 15px; display: flex; flex-wrap: wrap; gap: 15px; min-height: 1.2em; /* Prevent jump when hidden/shown */ }
 .variation-attributes span { display: none; /* Hide by default, show via JS */ align-items: center; gap: 5px; }
 .variation-attributes .color-swatch { width: 16px; height: 16px; border-radius: 50%; border: 1px solid var(--border); display: inline-block; }

 .product-description-container { margin-bottom: 25px; font-size: 0.95rem;}
 .short-description { font-weight: 400; margin-bottom: 15px; color: var(--gray); }
 .short-description:empty { display: none; }
 .base-description { margin-bottom: 10px; line-height: 1.7; color: var(--text); }
 .base-description:empty { display: none; }
 #variationDescription { margin-top: 10px; padding-top: 10px; border-top: 1px dashed var(--border); color: var(--gray); line-height: 1.7; }
 #variationDescription:empty { display: none; } /* Hide if empty */

 .options-section { border-top: 1px solid var(--border); padding-top: 25px; margin-top: 25px; }
 .options-section h4, .variation-selector-group label.group-label { margin-bottom: 15px; font-size: 1.1rem; font-weight: 600; color: var(--dark); }
 .variation-selector-group { margin-bottom: 25px; }
 .variation-selector-group label.group-label { display: block; margin-bottom: 10px; font-size: 1rem; }
 .variation-selector-group.variation-name-container:empty { display: none; } /* Hide if no variations */


 .variation-name-selector { display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 15px; }
 .variation-name-option { position: relative; }
 .variation-name-radio { position: absolute; opacity: 0; width: 0; height: 0; }
 .variation-name-label { display: block; padding: 8px 15px; border: 1px solid var(--border); border-radius: 4px; cursor: pointer; transition: all 0.2s; font-size: 0.95rem; background-color: #fff; }
 .variation-name-radio:checked + .variation-name-label { border-color: var(--secondary); background-color: var(--secondary-light); color: var(--secondary); font-weight: 600; box-shadow: 0 0 0 2px var(--secondary-light); }
 .variation-name-radio:disabled + .variation-name-label { cursor: not-allowed; opacity: 0.5; background-color: #f8f9fa; color: #aaa; border-color: #eee; }
 .variation-name-label:hover:not(.disabled) { border-color: #aaa; }

 .quantity-selector { display: flex; align-items: center; gap: 10px; margin-bottom: 25px; }
 .quantity-selector label { font-weight: 600; font-size: 1.1rem; margin-right: 5px; flex-shrink: 0; }
 .quantity-input { padding: 8px 12px; border: 1px solid var(--border); border-radius: 4px; width: 70px; text-align: center; font-size: 1rem; -webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield; }
 .quantity-input::-webkit-outer-spin-button, .quantity-input::-webkit-inner-spin-button { -webkit-appearance: none; margin: 0; }
 .quantity-input:focus { outline: none; border-color: var(--secondary); box-shadow: 0 0 0 2px rgba(0, 194, 255, 0.2); }
 .quantity-input:disabled { background-color: #eee; cursor: not-allowed; }

 .additional-options { margin-bottom: 25px; }
 .additional-options h4 { margin-bottom: 15px; font-size: 1.1rem; font-weight: 600; color: var(--dark); }
 .shipping-option, .fee-option { display: flex; align-items: center; margin-bottom: 12px; font-size: 0.95rem; flex-wrap: wrap; gap: 10px; }
 .shipping-option label, .fee-option label { flex-basis: 150px; flex-grow: 1; color: var(--text); cursor: pointer; }
 .shipping-select { padding: 8px 12px; border: 1px solid var(--border); border-radius: 4px; flex-grow: 2; min-width: 150px; font-size: 0.95rem; height: 38px; background-color: #fff; }
 #shippingCostDisplay { color: var(--secondary); font-weight: 600; white-space: nowrap; padding-left: 5px; font-size: 0.9rem; align-self: center; flex-shrink: 0; min-height: 1em; } /* Added min-height */
 .fee-option input[type="checkbox"] { margin-right: 5px; width: 16px; height: 16px; accent-color: var(--secondary); flex-shrink: 0; }
 .fee-amount { color: var(--secondary); font-weight: 600; white-space: nowrap; padding-left: 5px; flex-shrink: 0; }
 .fee-option label { flex-basis: auto; }


 /* Price & Action */
 .price-action-section { border-top: 1px solid var(--border); padding-top: 25px; margin-top: 25px; }
 .total-price { font-size: 1.5rem; margin-bottom: 25px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px; }
 .total-price .label { font-weight: 600; color: var(--dark); }
 .total-price .amount { color: var(--secondary); font-weight: 700; text-align: right; flex-grow: 1;}

 .add-to-cart-btn { background: var(--secondary); color: white; border: none; padding: 15px 25px; font-size: 1.05rem; border-radius: 5px; cursor: pointer; width: 100%; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; gap: 10px; text-transform: uppercase; letter-spacing: 0.5px; line-height: 1; }
 .add-to-cart-btn:hover:not(:disabled) { background: var(--accent); transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); }
 .add-to-cart-btn:disabled { background: #ccc; cursor: not-allowed; opacity: 0.7; transform: none; box-shadow: none; }
 .add-to-cart-btn .btn-icon { display: inline-block; line-height: 1; }
 .add-to-cart-btn .btn-icon svg, .add-to-cart-btn .btn-icon i { vertical-align: middle; }
 .add-to-cart-btn .btn-icon .fa-check, .add-to-cart-btn .btn-icon .fa-times-circle { font-size: 1.1em; }

 .return-to-cart { display: block; margin-top: 15px; text-align: center; width: 100%; color: var(--secondary); font-weight: 500; text-decoration: none; transition: all 0.3s; font-size: 0.9rem; }
 .return-to-cart:hover { color: var(--accent); text-decoration: underline; }

 /* Loading Spinner */
 .loading-spinner { display: inline-block; animation: spin 1s linear infinite; line-height: 1; margin-right: 8px; vertical-align: middle; }
 .loading-spinner .fa-spinner { font-size: 1em; }
 @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

 /* Responsive Adjustments */
 @media (min-width: 768px) { .product-detail-container { grid-template-columns: 1fr 1fr; gap: 50px; padding: 40px; } }
 @media (min-width: 992px) { .product-detail-container { grid-template-columns: minmax(0, 5fr) minmax(0, 4fr); gap: 60px; } }

 /* ============================================= */
 /* === Gallery Modal Styles === */
 /* ============================================= */

.gallery-modal {
    display: none;
    position: fixed;
    z-index: 1060;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.85);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-modal.show {
    display: flex;
    opacity: 1;
    align-items: center;
    justify-content: center;
}

.gallery-modal-content {
    background-color: #fff;
    width: 30%;
    max-width: 300px;
    max-height: 75vh;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    position: relative;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(to bottom, #ffffff, #fafafa);
}

.gallery-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--dark);
    font-weight: 600;
}

.gallery-close {
    background: rgba(0, 0, 0, 0.05);
    border: none;
    font-size: 1rem;
    color: #666;
    cursor: pointer;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.gallery-close:hover {
    background-color: #f0f0f0;
    color: #333;
}

.gallery-body {
    padding: 10px;
    overflow-y: auto;
    max-height: calc(75vh - 45px);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.gallery-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    height: 170px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.gallery-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.gallery-main-image {
    max-width: 100%;
    max-height: 170px;
    object-fit: contain;
    display: block;
    margin: 0 auto;
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.05);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 5;
    transition: all 0.2s;
    color: #444;
}

.gallery-prev {
    left: 10px;
}

.gallery-next {
    right: 10px;
}

.gallery-nav:hover {
    background: var(--secondary-light);
    color: var(--secondary);
}

.gallery-thumbnails {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    gap: 6px;
    padding: 6px 0;
}

.gallery-thumbnail {
    width: 100%;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s;
    opacity: 0.7;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-thumbnail.active {
    border-color: var(--secondary);
    opacity: 1;
}

.gallery-thumbnail:hover {
    opacity: 1;
}

/* Mobile adjustments for gallery modal */
@media (max-width: 768px) {
    .gallery-modal-content {
        width: 75%;
        max-width: 320px;
        max-height: 75vh;
    }
    
    .gallery-main {
        height: 180px;
    }
    
    .gallery-main-image {
        max-height: 180px;
    }
    
    .gallery-thumbnails {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    }
    
    .gallery-thumbnail {
        height: 50px;
    }
    
    .gallery-nav {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }
}