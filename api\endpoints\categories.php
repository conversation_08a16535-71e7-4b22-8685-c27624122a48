<?php
/**
 * Categories Endpoints
 * Handles product category operations
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path and ID
$sub_path = $segments[1] ?? '';
$category_id = $segments[2] ?? null;

switch ($method) {
    case 'GET':
        if ($category_id) {
            handleGetCategory($db, $category_id);
        } elseif ($sub_path === 'tree') {
            handleGetCategoryTree($db);
        } else {
            handleGetCategories($db);
        }
        break;
        
    case 'POST':
        AuthMiddleware::requireAdmin();
        handleCreateCategory($db, $input);
        break;
        
    case 'PUT':
        AuthMiddleware::requireAdmin();
        if (!$category_id) {
            Response::error('Category ID is required', 400);
        }
        handleUpdateCategory($db, $category_id, $input);
        break;
        
    case 'DELETE':
        AuthMiddleware::requireAdmin();
        if (!$category_id) {
            Response::error('Category ID is required', 400);
        }
        handleDeleteCategory($db, $category_id);
        break;
        
    default:
        Response::methodNotAllowed(['GET', 'POST', 'PUT', 'DELETE']);
}

/**
 * Get all top-level categories
 */
function handleGetCategories($db) {
    $sql = "
        SELECT 
            tc.tcat_id,
            tc.tcat_name,
            tc.show_on_menu,
            COUNT(p.p_id) as product_count
        FROM tbl_top_category tc
        LEFT JOIN tbl_product p ON tc.tcat_id = p.tcat_id AND p.p_is_active = 1
        GROUP BY tc.tcat_id, tc.tcat_name, tc.show_on_menu
        ORDER BY tc.tcat_name
    ";
    
    $categories = $db->fetchAll($sql);
    
    $formatted_categories = array_map(function($category) {
        return [
            'id' => (int)$category['tcat_id'],
            'name' => $category['tcat_name'],
            'show_on_menu' => (bool)$category['show_on_menu'],
            'product_count' => (int)$category['product_count']
        ];
    }, $categories);
    
    Response::success($formatted_categories, 'Categories retrieved successfully');
}

/**
 * Get single category with subcategories
 */
function handleGetCategory($db, $category_id) {
    // Get category details
    $category = $db->fetchOne(
        "SELECT * FROM tbl_top_category WHERE tcat_id = ?",
        [$category_id]
    );
    
    if (!$category) {
        Response::notFound('Category not found');
    }
    
    // Get subcategories
    $subcategories = $db->fetchAll(
        "SELECT 
            mc.mcat_id,
            mc.mcat_name,
            COUNT(p.p_id) as product_count
         FROM tbl_mid_category mc
         LEFT JOIN tbl_product p ON mc.mcat_id = p.mcat_id AND p.p_is_active = 1
         WHERE mc.tcat_id = ?
         GROUP BY mc.mcat_id, mc.mcat_name
         ORDER BY mc.mcat_name",
        [$category_id]
    );
    
    // Get total product count for this category
    $total_products = $db->fetchOne(
        "SELECT COUNT(*) as count FROM tbl_product WHERE tcat_id = ? AND p_is_active = 1",
        [$category_id]
    )['count'];
    
    $formatted_category = [
        'id' => (int)$category['tcat_id'],
        'name' => $category['tcat_name'],
        'show_on_menu' => (bool)$category['show_on_menu'],
        'product_count' => (int)$total_products,
        'subcategories' => array_map(function($sub) {
            return [
                'id' => (int)$sub['mcat_id'],
                'name' => $sub['mcat_name'],
                'product_count' => (int)$sub['product_count']
            ];
        }, $subcategories)
    ];
    
    Response::success($formatted_category, 'Category details retrieved successfully');
}

/**
 * Get complete category tree
 */
function handleGetCategoryTree($db) {
    // Get all categories with their subcategories
    $sql = "
        SELECT 
            tc.tcat_id,
            tc.tcat_name,
            tc.show_on_menu,
            mc.mcat_id,
            mc.mcat_name as sub_name,
            COUNT(p.p_id) as product_count
        FROM tbl_top_category tc
        LEFT JOIN tbl_mid_category mc ON tc.tcat_id = mc.tcat_id
        LEFT JOIN tbl_product p ON mc.mcat_id = p.mcat_id AND p.p_is_active = 1
        GROUP BY tc.tcat_id, tc.tcat_name, tc.show_on_menu, mc.mcat_id, mc.mcat_name
        ORDER BY tc.tcat_name, mc.mcat_name
    ";
    
    $results = $db->fetchAll($sql);
    
    // Group by top category
    $tree = [];
    foreach ($results as $row) {
        $cat_id = (int)$row['tcat_id'];
        
        if (!isset($tree[$cat_id])) {
            $tree[$cat_id] = [
                'id' => $cat_id,
                'name' => $row['tcat_name'],
                'show_on_menu' => (bool)$row['show_on_menu'],
                'subcategories' => []
            ];
        }
        
        if ($row['mcat_id']) {
            $tree[$cat_id]['subcategories'][] = [
                'id' => (int)$row['mcat_id'],
                'name' => $row['sub_name'],
                'product_count' => (int)$row['product_count']
            ];
        }
    }
    
    // Calculate total product counts for each category
    foreach ($tree as &$category) {
        $total_count = $db->fetchOne(
            "SELECT COUNT(*) as count FROM tbl_product WHERE tcat_id = ? AND p_is_active = 1",
            [$category['id']]
        )['count'];
        $category['product_count'] = (int)$total_count;
    }
    
    Response::success(array_values($tree), 'Category tree retrieved successfully');
}

/**
 * Create new category (admin only)
 */
function handleCreateCategory($db, $input) {
    $validator = new Validator($input);
    $validator->required('name')->maxLength('name', 255)
             ->required('type')->in('type', ['top', 'mid']);
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    $type = $input['type'];
    $name = $input['name'];
    
    if ($type === 'top') {
        // Create top category
        $data = [
            'tcat_name' => $name,
            'show_on_menu' => $input['show_on_menu'] ?? 1
        ];
        
        $category_id = $db->insert('tbl_top_category', $data);
        
        Response::success([
            'id' => (int)$category_id,
            'name' => $name,
            'type' => 'top',
            'show_on_menu' => (bool)($input['show_on_menu'] ?? 1)
        ], 'Top category created successfully', 201);
        
    } else {
        // Create mid category
        $validator->required('parent_id')->integer('parent_id');
        
        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }
        
        // Check if parent category exists
        if (!$db->exists('tbl_top_category', 'tcat_id = ?', [$input['parent_id']])) {
            Response::error('Parent category not found', 404);
        }
        
        $data = [
            'tcat_id' => $input['parent_id'],
            'mcat_name' => $name
        ];
        
        $category_id = $db->insert('tbl_mid_category', $data);
        
        Response::success([
            'id' => (int)$category_id,
            'name' => $name,
            'type' => 'mid',
            'parent_id' => (int)$input['parent_id']
        ], 'Subcategory created successfully', 201);
    }
}

/**
 * Update category (admin only)
 */
function handleUpdateCategory($db, $category_id, $input) {
    $validator = new Validator($input);
    $validator->required('type')->in('type', ['top', 'mid']);
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    $type = $input['type'];
    
    if ($type === 'top') {
        // Update top category
        if (!$db->exists('tbl_top_category', 'tcat_id = ?', [$category_id])) {
            Response::notFound('Category not found');
        }
        
        $data = [];
        if (isset($input['name'])) {
            $data['tcat_name'] = $input['name'];
        }
        if (isset($input['show_on_menu'])) {
            $data['show_on_menu'] = $input['show_on_menu'] ? 1 : 0;
        }
        
        if (empty($data)) {
            Response::error('No data to update', 400);
        }
        
        $db->update('tbl_top_category', $data, 'tcat_id = ?', [$category_id]);
        
    } else {
        // Update mid category
        if (!$db->exists('tbl_mid_category', 'mcat_id = ?', [$category_id])) {
            Response::notFound('Subcategory not found');
        }
        
        $data = [];
        if (isset($input['name'])) {
            $data['mcat_name'] = $input['name'];
        }
        if (isset($input['parent_id'])) {
            if (!$db->exists('tbl_top_category', 'tcat_id = ?', [$input['parent_id']])) {
                Response::error('Parent category not found', 404);
            }
            $data['tcat_id'] = $input['parent_id'];
        }
        
        if (empty($data)) {
            Response::error('No data to update', 400);
        }
        
        $db->update('tbl_mid_category', $data, 'mcat_id = ?', [$category_id]);
    }
    
    Response::success(null, 'Category updated successfully');
}

/**
 * Delete category (admin only)
 */
function handleDeleteCategory($db, $category_id) {
    $type = $_GET['type'] ?? 'top';
    
    if ($type === 'top') {
        // Check if category exists
        if (!$db->exists('tbl_top_category', 'tcat_id = ?', [$category_id])) {
            Response::notFound('Category not found');
        }
        
        // Check if category has products
        if ($db->exists('tbl_product', 'tcat_id = ?', [$category_id])) {
            Response::error('Cannot delete category with products', 409);
        }
        
        // Check if category has subcategories
        if ($db->exists('tbl_mid_category', 'tcat_id = ?', [$category_id])) {
            Response::error('Cannot delete category with subcategories', 409);
        }
        
        $db->delete('tbl_top_category', 'tcat_id = ?', [$category_id]);
        
    } else {
        // Delete subcategory
        if (!$db->exists('tbl_mid_category', 'mcat_id = ?', [$category_id])) {
            Response::notFound('Subcategory not found');
        }
        
        // Check if subcategory has products
        if ($db->exists('tbl_product', 'mcat_id = ?', [$category_id])) {
            Response::error('Cannot delete subcategory with products', 409);
        }
        
        $db->delete('tbl_mid_category', 'mcat_id = ?', [$category_id]);
    }
    
    Response::success(null, 'Category deleted successfully');
}
?>
