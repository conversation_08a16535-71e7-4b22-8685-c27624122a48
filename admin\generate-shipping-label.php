<?php
// Start output buffering
ob_start();

require_once('header.php');
require_once('../vendor/autoload.php'); // Make sure you have TCPDF installed via composer

use TCPDF;

if(isset($_GET['id'])) {
    $order_id = $_GET['id'];
    
    // Get order details
    $statement = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $statement->execute(array($order_id));
    $order = $statement->fetch(PDO::FETCH_ASSOC);
    
    // Clear any previous output
    ob_clean();
    
    // Create new PDF document
    $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('Ecommerce System');
    $pdf->SetAuthor('Admin');
    $pdf->SetTitle('Shipping Label - Order #' . $order['tx_ref']);
    
    // Remove default header/footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    // Set minimal margins
    $pdf->SetMargins(10, 5, 10);
    
    // Set the Y position to start content closer to the top
    $pdf->SetY(1);
    
    // Add a page
    $pdf->AddPage();
    
    // Set font
    $pdf->SetFont('helvetica', '', 9);
    
    // Calculate center position
    $pageWidth = 210; // A4 width in mm
    $contentWidth = 190; // Width of our content
    $startX = ($pageWidth - $contentWidth) / 2;
    
    // Shipping Label Content
    $html = '
    <div style="border: 1px solid #000; padding: 8px; width: 190mm;">
        <table cellpadding="0" cellspacing="0" style="width: 100%;">
            <tr>
                <td style="width: 25%;">
                    <img src="../assets/uploads/logo.png" style="width: 25mm; height: auto;">
                </td>
                <td style="width: 75%; text-align: center;">
                    <h2 style="margin: 0; font-size: 14px;">SHIPPING LABEL</h2>
                </td>
            </tr>
        </table>
        <table cellpadding="3" style="width: 100%; margin-top: 5px;">
            <tr>
                <td width="50%" style="padding-right: 5px;">
                    <strong>Order #:</strong> ' . $order['tx_ref'] . '<br>
                    <strong>Date:</strong> ' . date('Y-m-d') . '<br>
                    <strong>Tracking #:</strong> ' . $order['tracking_number'] . '<br>
                    <strong>Carrier:</strong> ' . $order['carrier'] . '
                </td>
                <td width="50%" style="padding-left: 5px;">
                    <strong>Estimated Delivery:</strong> ' . $order['estimated_delivery'] . '<br>
                    <strong>Shipping Method:</strong> Standard
                </td>
            </tr>
        </table>
        
        <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd;">
            <h3 style="margin: 0 0 5px 0; font-size: 11px;">SHIP TO:</h3>
            <p style="margin: 0; line-height: 1.2;">
                ' . $order['firstname'] . ' ' . $order['lastname'] . '<br>
                ' . $order['address'] . '<br>
                ' . $order['shipping_country'] . '<br>
                Phone: ' . $order['phone'] . '<br>
                Email: ' . $order['email'] . '
            </p>
        </div>
        
        <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #ddd;">
            <h3 style="margin: 0 0 5px 0; font-size: 11px;">SHIP FROM:</h3>
            <p style="margin: 0; line-height: 1.2;">
                Smart Home Co.<br>
                255 Mwongozi<br>
                Dar es salaam<br>
                Phone: 070000000000
            </p>
        </div>
    </div>';
    
    // Add barcode if tracking number exists
    if(!empty($order['tracking_number'])) {
        $pdf->write1DBarcode($order['tracking_number'], 'C128', $startX + 5, 110, 180, 15, 0.4, array(
            'position' => 'C',
            'border' => true,
            'padding' => 4,
            'fgcolor' => array(0,0,0),
            'bgcolor' => array(255,255,255),
            'text' => true
        ));
    }
    
    // Output the HTML content
    $pdf->writeHTML($html, true, false, true, false, '');
    
    // Close and output PDF document
    $pdf->Output('shipping_label_' . $order['tx_ref'] . '.pdf', 'D');
    exit;
} else {
    header('location: shipping-tracking.php');
    exit;
}
?> 