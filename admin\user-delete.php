<?php
require_once('header.php');

if(!isset($_REQUEST['id'])) {
    header('location: user-view.php');
    exit;
} else {
    // Check the id is valid or not
    $statement = $pdo->prepare("SELECT * FROM tbl_user WHERE id=?");
    $statement->execute(array($_REQUEST['id']));
    $total = $statement->rowCount();
    if( $total == 0 ) {
        header('location: user-view.php');
        exit;
    }
}

// Delete the user
$statement = $pdo->prepare("DELETE FROM tbl_user WHERE id=?");
$statement->execute(array($_REQUEST['id']));

header('location: user-view.php');
exit;
?> 