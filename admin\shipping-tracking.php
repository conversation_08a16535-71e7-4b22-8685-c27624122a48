<?php require_once('header.php'); ?>

<?php
// Handle shipping status update
if (isset($_POST['update_shipping_status'])) {
    $order_id = $_POST['order_id'];
    $tracking_number = $_POST['tracking_number'];
    $carrier = $_POST['carrier'];
    $estimated_delivery = $_POST['estimated_delivery'];
    $notes = $_POST['notes'];

    $statement = $pdo->prepare("UPDATE orders SET
        tracking_number = ?,
        carrier = ?,
        estimated_delivery = ?,
        shipping_notes = ?,
        shipping_updated_at = NOW()
        WHERE id = ?");
    $statement->execute([
        $tracking_number,
        $carrier,
        $estimated_delivery,
        $notes,
        $order_id
    ]);

    $_SESSION['success_message'] = 'Shipping details updated successfully!';
    header('location: shipping-tracking.php');
    exit;
}

// Handle shipping history add/update/delete
if (isset($_POST['add_shipping_history'])) {
    $order_id    = $_POST['order_id'];
    $location    = $_POST['location'];
    $status      = $_POST['status'];
    $description = $_POST['description'];

    $stmt = $pdo->prepare("INSERT INTO shipping_history (order_id, location, status, description, created_at) VALUES (?, ?, ?, ?, NOW())");
    $stmt->execute([$order_id, $location, $status, $description]);

    $_SESSION['success_message'] = 'Shipping history added successfully!';
    header('location: shipping-tracking.php');
    exit;
} elseif (isset($_POST['update_shipping_history'])) {
    $history_id  = $_POST['history_id'];
    $location    = $_POST['location'];
    $status      = $_POST['status'];
    $description = $_POST['description'];

    $stmt = $pdo->prepare("UPDATE shipping_history SET
        location = ?,
        status = ?,
        description = ?
        WHERE id = ?");
    $stmt->execute([$location, $status, $description, $history_id]);

    $_SESSION['success_message'] = 'Shipping history updated successfully!';
    header('location: shipping-tracking.php');
    exit;
} elseif (isset($_POST['delete_shipping_history'])) {
    $history_id = $_POST['history_id'];

    $stmt = $pdo->prepare("DELETE FROM shipping_history WHERE id = ?");
    $stmt->execute([$history_id]);

    $_SESSION['success_message'] = 'Shipping history deleted successfully!';
    header('location: shipping-tracking.php');
    exit;
}
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Shipping Tracking</h1>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($_SESSION['success_message'], ENT_QUOTES, 'UTF-8'); ?></div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <div class="box-body table-responsive">
                    <table id="example1" class="table table-bordered table-hover table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Shipping Address</th>
                                <th>Tracking Number</th>
                                <th>Carrier</th>
                                <th>Status</th>
                                <th>Estimated Delivery</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $modals = '';
                            $i = 0;
                            $stmt = $pdo->prepare(
                                "SELECT * FROM orders
                                 WHERE payment_status = 'success'
                                   AND verification_status = 'verified'
                                 ORDER BY id DESC"
                            );
                            $stmt->execute();
                            $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            if ($orders) {
                                foreach ($orders as $row) {
                                    $i++;
                                    // sanitize values
                                    $txRef          = htmlspecialchars($row['tx_ref'] ?? '', ENT_QUOTES, 'UTF-8');
                                    $firstLastName  = htmlspecialchars(($row['firstname'] ?? '') . ' ' . ($row['lastname'] ?? ''), ENT_QUOTES, 'UTF-8');
                                    $email          = htmlspecialchars($row['email'] ?? '', ENT_QUOTES, 'UTF-8');
                                    $phone          = htmlspecialchars($row['phone'] ?? '', ENT_QUOTES, 'UTF-8');
                                    $address        = nl2br(htmlspecialchars($row['address'] ?? '', ENT_QUOTES, 'UTF-8'));
                                    $country        = htmlspecialchars($row['shipping_country'] ?? '', ENT_QUOTES, 'UTF-8');
                                    $trackingNumber = htmlspecialchars($row['tracking_number'] ?? '', ENT_QUOTES, 'UTF-8');
                                    $carrierName    = htmlspecialchars($row['carrier'] ?? '', ENT_QUOTES, 'UTF-8');
                                    $estimatedDel   = htmlspecialchars($row['estimated_delivery'] ?? '', ENT_QUOTES, 'UTF-8');
                                    $statusClass    = $row['shipping_status'] === 'delivered'
                                                        ? 'btn-success active' : ($row['shipping_status'] === 'processing'
                                                        ? 'btn-warning active' : 'btn-danger active');

                                    echo "<tr>";
                                    echo "<td>{$i}</td>";
                                    echo "<td>{$txRef}</td>";
                                    echo "<td>{$firstLastName}<br>{$email}<br>{$phone}</td>";
                                    echo "<td>{$address}<br>{$country}</td>";
                                    echo "<td>{$trackingNumber}</td>";
                                    echo "<td>{$carrierName}</td>";
                                    echo "<td>";
                                    echo "<div style='margin-bottom:5px;'>";
                                    if ($row['shipping_status'] === 'delivered') {
                                        echo '<span class="label label-success" style="font-size:11px;padding:3px 7px;border-radius:3px;"><i class="fa fa-check"></i> Delivered</span>';
                                    } elseif ($row['shipping_status'] === 'processing') {
                                        echo '<span class="label label-warning" style="font-size:11px;padding:3px 7px;border-radius:3px;"><i class="fa fa-clock-o"></i> Processing</span>';
                                    } else {
                                        echo '<span class="label label-danger" style="font-size:11px;padding:3px 7px;border-radius:3px;"><i class="fa fa-times"></i> Not Delivered</span>';
                                    }
                                    echo "</div>";
                                    echo "<div class='btn-group btn-group-xs' style='width:100%;'>";
                                    echo "<button class='btn " . ($row['shipping_status']==='delivered' ? 'btn-success active' : 'btn-default') . "' style='width:33%;' onclick=\"window.location='shipping-status-update.php?id={$row['id']}&status=delivered'\"><i class='fa fa-check'></i></button>";
                                    echo "<button class='btn " . ($row['shipping_status']==='processing' ? 'btn-warning active' : 'btn-default') . "' style='width:33%;' onclick=\"window.location='shipping-status-update.php?id={$row['id']}&status=processing'\"><i class='fa fa-clock-o'></i></button>";
                                    echo "<button class='btn " . ($row['shipping_status']==='not_delivered' ? 'btn-danger active' : 'btn-default') . "' style='width:34%;' onclick=\"window.location='shipping-status-update.php?id={$row['id']}&status=not_delivered'\"><i class='fa fa-times'></i></button>";
                                    echo "</div>";
                                    echo "</td>";
                                    echo "<td>{$estimatedDel}</td>";
                                    echo "<td>";
                                    echo "<a href='#' class='btn btn-primary btn-xs' data-toggle='modal' data-target='#shippingModal{$row['id']}'>Edit</a> ";
                                    echo "<a href='#' class='btn btn-info btn-xs' data-toggle='modal' data-target='#historyModal{$row['id']}'>History</a> ";
                                    echo "<a href='#' class='btn btn-success btn-xs' data-toggle='modal' data-target='#labelModal{$row['id']}'>Label</a>";
                                    echo "</td>";
                                    echo "</tr>";

                                    // Build modals
                                    $modals .= "
<!-- Edit Shipping Modal for Order {$row['tx_ref']} -->
<div class='modal fade' id='shippingModal{$row['id']}' tabindex='-1' role='dialog' aria-labelledby='shippingModalLabel'>
  <div class='modal-dialog' role='document'>
    <div class='modal-content'>
      <div class='modal-header'>
        <button type='button' class='close' data-dismiss='modal' aria-label='Close'><span aria-hidden='true'>&times;</span></button>
        <h4 class='modal-title' id='shippingModalLabel'>Edit Shipping Details</h4>
      </div>
      <div class='modal-body'>
        <form method='post'>
          <input type='hidden' name='order_id' value='{$row['id']}'>
          <div class='form-group'><label>Tracking Number</label><input class='form-control' name='tracking_number' value='{$trackingNumber}'></div>
          <div class='form-group'><label>Carrier</label><input class='form-control' name='carrier' value='{$carrierName}'></div>
          <div class='form-group'><label>Estimated Delivery</label><input type='date' class='form-control' name='estimated_delivery' value='{$estimatedDel}'></div>
          <div class='form-group'><label>Notes</label><textarea class='form-control' name='notes'>" . htmlspecialchars($row['shipping_notes'] ?? '', ENT_QUOTES, 'UTF-8') . "</textarea></div>
          <button name='update_shipping_status' class='btn btn-primary'>Update</button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Shipping History Modal for Order {$row['tx_ref']} -->
<div class='modal fade' id='historyModal{$row['id']}' tabindex='-1' role='dialog' aria-labelledby='historyModalLabel'>
  <div class='modal-dialog' role='document'>
    <div class='modal-content'>
      <div class='modal-header'>
        <button type='button' class='close' data-dismiss='modal'><span>&times;</span></button>
        <h4 class='modal-title'>Shipping History - #{$row['tx_ref']}</h4>
      </div>
      <div class='modal-body'>
        <form method='post'>
          <input type='hidden' name='order_id' value='{$row['id']}'>
          <div class='form-group'><label>Location</label><input class='form-control' name='location'></div>
          <div class='form-group'><label>Status</label><input class='form-control' name='status'></div>
          <div class='form-group'><label>Description</label><textarea class='form-control' name='description'></textarea></div>
          <button name='add_shipping_history' class='btn btn-primary'>Add History</button>
        </form>
        <hr>
        <h4>History Log</h4>
        <table class='table table-bordered'>
          <thead><tr><th>Date</th><th>Location</th><th>Status</th><th>Description</th><th>Action</th></tr></thead>
          <tbody>";
                                    // history entries
                                    $histStmt = $pdo->prepare("SELECT * FROM shipping_history WHERE order_id = ? ORDER BY created_at DESC");
                                    $histStmt->execute([$row['id']]);
                                    $historyEntries = $histStmt->fetchAll(PDO::FETCH_ASSOC);
                                    foreach ($historyEntries as $h) {
                                        $modals .= "<tr>";
                                        $modals .= "<td>{$h['created_at']}</td>";
                                        $modals .= "<td>" . htmlspecialchars($h['location'], ENT_QUOTES, 'UTF-8') . "</td>";
                                        $modals .= "<td>" . htmlspecialchars($h['status'], ENT_QUOTES, 'UTF-8') . "</td>";
                                        $modals .= "<td>" . htmlspecialchars($h['description'], ENT_QUOTES, 'UTF-8') . "</td>";
                                        $modals .= "<td><a class='btn btn-primary btn-xs' data-toggle='modal' data-target='#editHistoryModal{$h['id']}'>Edit</a> <a class='btn btn-danger btn-xs' data-href='shipping-history-delete.php?id={$h['id']} &order_id={$row['id']}' data-toggle='modal' data-target='#confirm-delete'>Delete</a></td>";
                                        $modals .= "</tr>";
                                        // edit history modal
                                        $modals .= "
<div class='modal fade' id='editHistoryModal{$h['id']}' tabindex='-1'><div class='modal-dialog'><div class='modal-content'><div class='modal-header'><button class='close' data-dismiss='modal'>&times;</button><h4>Edit History</h4></div><div class='modal-body'><form method='post'><input type='hidden' name='history_id' value='{$h['id']}'><input type='hidden' name='order_id' value='{$row['id']}'><div class='form-group'><label>Location</label><input class='form-control' name='location' value='" . htmlspecialchars($h['location'], ENT_QUOTES, 'UTF-8') . "'></div><div class='form-group'><label>Status</label><input class='form-control' name='status' value='" . htmlspecialchars($h['status'], ENT_QUOTES, 'UTF-8') . "'></div><div class='form-group'><label>Description</label><textarea class='form-control' name='description'>" . htmlspecialchars($h['description'], ENT_QUOTES, 'UTF-8') . "</textarea></div><button name='update_shipping_history' class='btn btn-primary'>Update</button></form></div></div></div></div>";
                                    }
                                    $modals .= "</tbody></table></div></div></div></div>";
                                    // shipping label modal
                                    $modals .= "
<div class='modal fade' id='labelModal{$row['id']}' tabindex='-1'><div class='modal-dialog'><div class='modal-content'><div class='modal-header'><button class='close' data-dismiss='modal'>&times;</button><h4>Shipping Label</h4></div><div class='modal-body'><p><strong>Order #:</strong> {$row['tx_ref']}</p><p><strong>Date:</strong> " . date('Y-m-d') . "</p><p><strong>Tracking #:</strong> {$trackingNumber}</p><p><strong>Carrier:</strong> {$carrierName}</p><hr><h4>SHIP TO:</h4><p>{$firstLastName}<br>{$address}<br>{$country}<br>Phone: {$phone}<br>Email: {$email}</p></div><div class='modal-footer'><a href='generate-shipping-label.php?id={$row['id']}' class='btn btn-primary'>Generate PDF Label</a><button class='btn btn-default' data-dismiss='modal'>Close</button></div></div></div></div>";
                                }
                            } else {
                                echo "<tr><td colspan='9' class='text-center'>No orders found</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                    <?php echo $modals; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Delete Confirmation Modal -->
<div class='modal fade' id='confirm-delete' tabindex='-1'><div class='modal-dialog'><div class='modal-content'><div class='modal-header'><button class='close' data-dismiss='modal'>&times;</button><h4>Delete Confirmation</h4></div><div class='modal-body'><p>Are you sure you want to delete this shipping history entry?</p></div><div class='modal-footer'><button class='btn btn-default' data-dismiss='modal'>Cancel</button><a class='btn btn-danger btn-ok'>Delete</a></div></div></div></div>

<script>
    $('#confirm-delete').on('show.bs.modal', function(e) {
        $(this).find('.btn-ok').attr('href', $(e.relatedTarget).data('href'));
    });
</script>

<?php require_once('footer.php'); ?>
