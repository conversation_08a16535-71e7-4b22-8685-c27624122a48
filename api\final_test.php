<?php
/**
 * Final Comprehensive API Test
 * Complete test of all API functionality
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>🚀 Final API Test</h1>";

// Detect the correct base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$script_dir = dirname($_SERVER['SCRIPT_NAME']);
$base_url = $protocol . '://' . $host . $script_dir . '/v1';

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🔧 Configuration</h2>";
echo "<p><strong>Base URL:</strong> {$base_url}</p>";
echo "<p><strong>API Version:</strong> " . API_VERSION . "</p>";
echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
echo "<p><strong>Currency:</strong> " . DEFAULT_CURRENCY . "</p>";
echo "</div>";

// Test endpoints
$endpoints = [
    'API Root' => [
        'url' => $base_url . '/',
        'method' => 'GET',
        'description' => 'API information and available endpoints'
    ],
    'App Settings' => [
        'url' => $base_url . '/settings/app',
        'method' => 'GET',
        'description' => 'Application configuration settings'
    ],
    'Products List' => [
        'url' => $base_url . '/products?limit=5',
        'method' => 'GET',
        'description' => 'Product catalog with pagination'
    ],
    'Categories List' => [
        'url' => $base_url . '/categories',
        'method' => 'GET',
        'description' => 'Product categories'
    ],
    'Shipping Countries' => [
        'url' => $base_url . '/shipping/countries',
        'method' => 'GET',
        'description' => 'Available shipping countries'
    ]
];

echo "<h2>🧪 API Endpoint Tests</h2>";

$passed = 0;
$failed = 0;

foreach ($endpoints as $name => $config) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>🔍 Testing: {$name}</h3>";
    echo "<p><strong>URL:</strong> {$config['url']}</p>";
    echo "<p><strong>Method:</strong> {$config['method']}</p>";
    echo "<p><strong>Description:</strong> {$config['description']}</p>";
    
    // Test with cURL for most reliable results
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $config['url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json',
        'User-Agent: API-Test/1.0'
    ]);
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    if ($error) {
        echo "<p style='color: red; font-weight: bold;'>❌ FAILED - cURL Error: {$error}</p>";
        $failed++;
    } elseif ($http_code !== 200) {
        echo "<p style='color: red; font-weight: bold;'>❌ FAILED - HTTP {$http_code}</p>";
        echo "<p>Response: " . htmlspecialchars(substr($response, 0, 200)) . "</p>";
        $failed++;
    } else {
        // Validate JSON
        $data = json_decode($response, true);
        $json_error = json_last_error();
        
        if ($json_error !== JSON_ERROR_NONE) {
            echo "<p style='color: red; font-weight: bold;'>❌ FAILED - Invalid JSON: " . json_last_error_msg() . "</p>";
            echo "<p>Response preview: " . htmlspecialchars(substr($response, 0, 200)) . "</p>";
            $failed++;
        } elseif (!isset($data['status'])) {
            echo "<p style='color: red; font-weight: bold;'>❌ FAILED - Missing 'status' field</p>";
            echo "<p>Available fields: " . implode(', ', array_keys($data)) . "</p>";
            $failed++;
        } elseif ($data['status'] !== 'success') {
            echo "<p style='color: orange; font-weight: bold;'>⚠️ WARNING - Status: {$data['status']}</p>";
            echo "<p>Message: " . ($data['message'] ?? 'No message') . "</p>";
            $failed++;
        } else {
            echo "<p style='color: green; font-weight: bold;'>✅ PASSED</p>";
            echo "<p><strong>Status:</strong> {$data['status']}</p>";
            echo "<p><strong>Message:</strong> " . ($data['message'] ?? 'No message') . "</p>";
            
            if (isset($data['data'])) {
                $data_count = is_array($data['data']) ? count($data['data']) : 1;
                echo "<p><strong>Data:</strong> {$data_count} items</p>";
            }
            
            $passed++;
        }
        
        echo "<p><strong>Response Time:</strong> {$response_time}ms</p>";
        echo "<p><strong>Content Type:</strong> {$content_type}</p>";
    }
    
    echo "</div>";
}

// Summary
echo "<div style='background: " . ($failed > 0 ? '#f8d7da' : '#d4edda') . "; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📊 Test Summary</h2>";
echo "<p><strong>Total Tests:</strong> " . ($passed + $failed) . "</p>";
echo "<p style='color: green; font-weight: bold;'>✅ Passed: {$passed}</p>";
echo "<p style='color: red; font-weight: bold;'>❌ Failed: {$failed}</p>";

if ($failed === 0) {
    echo "<h3 style='color: green;'>🎉 ALL TESTS PASSED!</h3>";
    echo "<p>Your API is working perfectly and ready for production use!</p>";
} else {
    echo "<h3 style='color: red;'>⚠️ Some tests failed</h3>";
    echo "<p>Please check the failed endpoints above for details.</p>";
}
echo "</div>";

// Next steps
if ($failed === 0) {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🚀 Next Steps</h2>";
    echo "<ol>";
    echo "<li><strong>Flutter Integration:</strong> Use base URL <code>{$base_url}</code></li>";
    echo "<li><strong>Authentication:</strong> Test login/register endpoints</li>";
    echo "<li><strong>Documentation:</strong> Visit <a href='{$script_dir}/v1/docs'>{$script_dir}/v1/docs</a></li>";
    echo "<li><strong>Monitoring:</strong> Set up API monitoring dashboard</li>";
    echo "<li><strong>Production:</strong> Configure security settings</li>";
    echo "</ol>";
    echo "</div>";
}

// Database status
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>💾 Database Status</h2>";
try {
    $product_count = $pdo->query("SELECT COUNT(*) FROM tbl_product")->fetchColumn();
    $customer_count = $pdo->query("SELECT COUNT(*) FROM tbl_customer")->fetchColumn();
    $category_count = $pdo->query("SELECT COUNT(*) FROM tbl_top_category")->fetchColumn();
    
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    echo "<p><strong>Products:</strong> {$product_count}</p>";
    echo "<p><strong>Customers:</strong> {$customer_count}</p>";
    echo "<p><strong>Categories:</strong> {$category_count}</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>API Test completed at:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "<small>Ecommerce API v" . API_VERSION . " - Ready for Flutter Integration</small>";
echo "</p>";
?>
