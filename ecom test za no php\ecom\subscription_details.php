<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Details | SMART</title>
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00c2ff',
                        'primary-dark': '#00a8e0',
                        'primary-light': '#e0f7ff',
                    },
                    fontFamily: {
                        sans: ['Sora', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 3s ease-in-out infinite',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700&display=swap');

        .bg-gradient-radial {
            background-image: radial-gradient(circle at center, rgba(0, 194, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .card-hover-effect {
            transition: all 0.3s ease;
        }

        .card-hover-effect:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .pricing-popular {
            overflow: hidden;
            position: relative;
        }

        .pricing-popular::before {
            content: "POPULAR";
            position: absolute;
            top: 30px;
            right: -35px;
            transform: rotate(45deg);
            background-color: #00c2ff;
            color: white;
            padding: 5px 40px;
            font-size: 0.75rem;
            font-weight: bold;
            z-index: 1;
        }

        .faq-item {
            transition: all 0.3s ease;
        }

        .faq-item.active {
            border-color: #00c2ff;
            box-shadow: 0 4px 6px -1px rgba(0, 194, 255, 0.1), 0 2px 4px -1px rgba(0, 194, 255, 0.06);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-sans">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
                <a href="index.php#products" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>

                <!-- Search -->
                <div class="relative">
                    <input id="searchInput" type="text" placeholder="Search products, categories..."
                           class="w-64 px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="searchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-[#00c2ff]">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
                <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
                <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
                <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>

                <!-- Search in Mobile Menu -->
                <div class="relative mt-4">
                    <input id="mobileSearchInput" type="text" placeholder="Search products, categories..."
                           class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="mobileSearchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <!-- Hero Section -->
    <section class="pt-32 pb-16 relative overflow-hidden bg-gradient-to-b from-white to-blue-50">
        <div class="absolute inset-0 bg-gradient-radial pointer-events-none"></div>
        <div class="absolute right-0 top-0 w-64 h-64 bg-primary-light rounded-full filter blur-3xl opacity-20 -mr-32 -mt-32"></div>
        <div class="absolute left-0 bottom-0 w-64 h-64 bg-primary-light rounded-full filter blur-3xl opacity-20 -ml-32 -mb-32"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-3xl mx-auto">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Smart Home <span class="text-primary">Devices</span></h1>
                <p class="text-lg md:text-xl text-gray-600 mb-8">Transform your home with our cutting-edge smart devices</p>

                <div class="flex flex-wrap justify-center gap-4 mt-8">
                    <a href="#products" class="px-8 py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-full shadow-md hover:shadow-lg transition-all duration-300">
                        View Devices <i class="fas fa-arrow-down ml-2"></i>
                    </a>
                    <a href="#faq" class="px-8 py-3 bg-white text-primary border border-primary rounded-full font-medium shadow-sm hover:bg-primary-light transition-all duration-300">
                        Read FAQs <i class="fas fa-question-circle ml-2"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute right-10 top-40 text-5xl text-primary opacity-10 animate-pulse-slow">
            <i class="fas fa-home-alt"></i>
        </div>
        <div class="absolute left-10 bottom-20 text-5xl text-primary opacity-10 animate-pulse-slow" style="animation-delay: 1s;">
            <i class="fas fa-wifi"></i>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Smart Devices?</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">Experience the future of home automation with our premium smart devices</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-gray-50 rounded-xl p-8 text-center card-hover-effect">
                    <div class="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-shield-alt text-2xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Premium Quality</h3>
                    <p class="text-gray-600">Built with high-quality materials and advanced technology for long-lasting performance</p>
                </div>

                <div class="bg-gray-50 rounded-xl p-8 text-center card-hover-effect">
                    <div class="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-bolt text-2xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Easy Integration</h3>
                    <p class="text-gray-600">Seamlessly connects with your existing smart home ecosystem and other devices</p>
                </div>

                <div class="bg-gray-50 rounded-xl p-8 text-center card-hover-effect">
                    <div class="w-16 h-16 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-headset text-2xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Expert Support</h3>
                    <p class="text-gray-600">Dedicated customer support team to help with setup and troubleshooting</p>
                </div>
            </div>
        </div>
    </section>

   
    <!-- Testimonials Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">What Our Customers Say</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">Hear from homeowners who have transformed their living spaces with our smart devices</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-gray-50 rounded-xl p-8 card-hover-effect">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6">"The smart devices from Smart Life have completely transformed my home. The quality is exceptional, and the automation features make daily tasks so much easier. Installation was a breeze!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-300 rounded-full overflow-hidden mr-4">
                            <img src="https://randomuser.me/api/portraits/women/45.jpg" alt="Sarah Johnson" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-900">Sarah Johnson</h4>
                            <p class="text-sm text-gray-500">Verified Buyer</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-xl p-8 card-hover-effect">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6">"I've tried various smart home products, but Smart Life devices are in a league of their own. The build quality is outstanding, and they integrate perfectly with my existing setup. Highly recommended!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-300 rounded-full overflow-hidden mr-4">
                            <img src="https://randomuser.me/api/portraits/men/83.jpg" alt="Charles Venture" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-900">Charles Venture</h4>
                            <p class="text-sm text-gray-500">Verified Buyer</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-xl p-8 card-hover-effect">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6">"The customer support team was incredibly helpful during setup. The devices work flawlessly, and the app interface is intuitive. My home feels so much more modern now!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-300 rounded-full overflow-hidden mr-4">
                            <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Emily Paulen" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-900">Emily Paulen</h4>
                            <p class="text-sm text-gray-500">Verified Buyer</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">Find answers to common questions about our smart devices</p>
            </div>

            <div class="max-w-3xl mx-auto space-y-6">
                <div class="faq-item bg-white rounded-xl p-6 shadow-sm">
                    <button class="faq-question w-full flex justify-between items-center text-left focus:outline-none">
                        <span class="text-lg font-semibold text-gray-900">How do I purchase a smart device?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="faq-answer mt-4 text-gray-600 hidden">
                        <p>You can purchase our smart devices directly through our website. Browse our product catalog, select your desired device, and proceed to checkout. We accept various payment methods including credit cards, PayPal, and bank transfers.</p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-xl p-6 shadow-sm">
                    <button class="faq-question w-full flex justify-between items-center text-left focus:outline-none">
                        <span class="text-lg font-semibold text-gray-900">What payment methods do you accept?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="faq-answer mt-4 text-gray-600 hidden">
                        <p>We accept all major credit cards (Visa, Mastercard, Mobile Money), PayPal, and bank transfers for payments. All transactions are processed securely through our payment gateway.</p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-xl p-6 shadow-sm">
                    <button class="faq-question w-full flex justify-between items-center text-left focus:outline-none">
                        <span class="text-lg font-semibold text-gray-900">What warranty do you provide?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="faq-answer mt-4 text-gray-600 hidden">
                        <p>All our smart devices come with a 1-year manufacturer's warranty. This covers any manufacturing defects or hardware issues. Extended warranty options are also available for purchase.</p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-xl p-6 shadow-sm">
                    <button class="faq-question w-full flex justify-between items-center text-left focus:outline-none">
                        <span class="text-lg font-semibold text-gray-900">Do you offer installation services?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="faq-answer mt-4 text-gray-600 hidden">
                        <p>Yes, we offer professional installation services for all our smart devices. Our certified technicians ensure proper setup and integration with your existing smart home system. Installation services can be added during checkout.</p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-xl p-6 shadow-sm">
                    <button class="faq-question w-full flex justify-between items-center text-left focus:outline-none">
                        <span class="text-lg font-semibold text-gray-900">Are your devices compatible with other smart home systems?</span>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                    </button>
                    <div class="faq-answer mt-4 text-gray-600 hidden">
                        <p>Yes, our devices are designed to be compatible with major smart home platforms including Google Home, Amazon Alexa, and Apple HomeKit. Each product page lists specific compatibility details.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary-light relative overflow-hidden">
        <div class="absolute right-0 bottom-0 w-64 h-64 bg-primary rounded-full filter blur-3xl opacity-10"></div>
        <div class="absolute left-0 top-0 w-64 h-64 bg-primary rounded-full filter blur-3xl opacity-10"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-3xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="flex flex-col md:flex-row">
                    <div class="md:w-1/2 bg-primary p-8 text-white flex items-center">
                        <div>
                            <h3 class="text-2xl font-bold mb-4">Need Help Choosing?</h3>
                            <p class="mb-6">Our support team is here to help you select the perfect subscription plan for your smart home needs.</p>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Personalized recommendations</span>
                            </div>
                            <div class="flex items-center text-sm mt-2">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Expert advice on features</span>
                            </div>
                            <div class="flex items-center text-sm mt-2">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Help with setup and migration</span>
                            </div>
                        </div>
                    </div>
                    <div class="md:w-1/2 p-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Our Support Team</h3>
                        <form class="space-y-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                                <input type="text" id="name" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                <input type="email" id="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Your Question</label>
                                <textarea id="message" rows="3" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"></textarea>
                            </div>
                            <button type="submit" class="w-full bg-primary hover:bg-primary-dark text-white font-medium py-3 rounded-lg transition-colors duration-300">
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-200 py-8">
        <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
                <h4 class="text-xl font-semibold mb-4">SMART LIFE<span class="text-[#00c2ff]">.</span></h4>
                <p>Your Gateway to a Smarter Luxurious Home</p>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="index.php#home" class="hover:text-[#00c2ff] transition">Home</a></li>
                    <li><a href="index.php#about" class="hover:text-[#00c2ff] transition">About Us</a></li>
                    <li><a href="index.php#products" class="hover:text-[#00c2ff] transition">Products</a></li>
                    <li><a href="index.php#gallery" class="hover:text-[#00c2ff] transition">Best Deals</a></li>
                    <li><a href="index.php#contact" class="hover:text-[#00c2ff] transition">Contact</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Information</h4>
                <ul class="space-y-2">
                    <li><a href="subscription_details.php" class="hover:text-[#00c2ff] transition">FAQs & Details</a></li>
                    <li><a href="guides.php" class="hover:text-[#00c2ff] transition">Guides</a></li>
                    <li><a href="shipping_policy.php" class="hover:text-[#00c2ff] transition">Shipping Policy</a></li>
                    <li><a href="privacy_policy.php" class="hover:text-[#00c2ff] transition">Privacy Policy</a></li>
                    <li><a href="#" class="hover:text-[#00c2ff] transition">Terms &amp; Conditions</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Newsletter</h4>
                <p class="mb-4">Subscribe for exclusive offers and smart home insights.</p>
                <form action="subscribe.php" method="post" class="flex flex-col">
                    <div class="flex">
                        <input type="email" name="subscriber_email" required
                               class="w-full px-4 py-2 rounded-l-md bg-gray-700 text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent">
                        <button type="submit"
                                class="px-4 bg-[#00c2ff] hover:bg-[#00a8e0] text-white font-medium rounded-r-md transition">
                            →
                        </button>
                    </div>
                    <div id="newsletter-message" class="mt-2"></div>
                </form>
            </div>
        </div>
        <div class="mt-8 border-t border-gray-700 pt-4 text-center text-sm">
            <?= $footer_copyright ?>
            <div class="mt-2 space-x-4">
                <a href="#" class="hover:text-[#00c2ff] transition">Privacy</a>
                <a href="#" class="hover:text-[#00c2ff] transition">Terms</a>
                <a href="#" class="hover:text-[#00c2ff] transition">Sitemap</a>
            </div>
        </div>
    </footer>

    <!-- External JavaScript -->
    <script src="js/script.js"></script>

    <!-- Inline Page-Specific JavaScript -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Initialize cart if not exists
            if (!localStorage.getItem('cart')) {
                localStorage.setItem('cart', JSON.stringify([]));
            }

            // Update cart count display
            updateCartCount();

            function updateCartCount() {
                const cart = JSON.parse(localStorage.getItem('cart'));
                const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
                document.querySelectorAll('.cart-count').forEach(el => {
                    el.textContent = totalItems;
                });
            }

            // Mobile Menu Functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function toggleMobileMenu() {
                mobileMenu.classList.toggle('translate-x-full');
                mobileMenuBackdrop.classList.toggle('hidden');
                document.body.style.overflow = mobileMenu.classList.contains('translate-x-full') ? 'auto' : 'hidden';
            }

            mobileMenuButton.addEventListener('click', toggleMobileMenu);
            closeMobileMenu.addEventListener('click', toggleMobileMenu);
            mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

            // Billing Toggle Functionality
            const billingToggle = document.getElementById('billingToggle');
            const monthlyPrices = document.querySelectorAll('.monthly-price');
            const yearlyPrices = document.querySelectorAll('.yearly-price');

            if (billingToggle) {
                billingToggle.addEventListener('change', function() {
                    if (this.checked) {
                        // Show yearly prices
                        monthlyPrices.forEach(el => el.classList.add('hidden'));
                        yearlyPrices.forEach(el => el.classList.remove('hidden'));
                    } else {
                        // Show monthly prices
                        monthlyPrices.forEach(el => el.classList.remove('hidden'));
                        yearlyPrices.forEach(el => el.classList.add('hidden'));
                    }
                });
            }

            // FAQ Accordion Functionality
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    const faqItem = this.closest('.faq-item');
                    const answer = faqItem.querySelector('.faq-answer');
                    const icon = this.querySelector('i');

                    // Toggle active state
                    faqItem.classList.toggle('active');

                    // Toggle answer visibility
                    if (answer.classList.contains('hidden')) {
                        answer.classList.remove('hidden');
                        icon.style.transform = 'rotate(180deg)';
                    } else {
                        answer.classList.add('hidden');
                        icon.style.transform = 'rotate(0)';
                    }
                });
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 100, // Offset for fixed header
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Contact Form Handling
            const contactForm = document.querySelector('.py-16.bg-primary-light form');

            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const nameInput = this.querySelector('#name');
                    const emailInput = this.querySelector('#email');
                    const messageInput = this.querySelector('#message');
                    const submitButton = this.querySelector('button[type="submit"]');

                    // Basic validation
                    if (!nameInput.value.trim()) {
                        showFormMessage(this, 'Please enter your name', 'error');
                        nameInput.focus();
                        return;
                    }

                    if (!emailInput.value.trim() || !isValidEmail(emailInput.value)) {
                        showFormMessage(this, 'Please enter a valid email address', 'error');
                        emailInput.focus();
                        return;
                    }

                    if (!messageInput.value.trim()) {
                        showFormMessage(this, 'Please enter your question', 'error');
                        messageInput.focus();
                        return;
                    }

                    // Show loading state
                    const originalButtonText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Sending...';
                    submitButton.disabled = true;

                    // Simulate form submission
                    setTimeout(() => {
                        showFormMessage(this, 'Thank you for your message! Our team will get back to you shortly.', 'success');
                        this.reset();
                        submitButton.innerHTML = originalButtonText;
                        submitButton.disabled = false;
                    }, 1500);
                });
            }

            function showFormMessage(form, message, type) {
                // Remove any existing message
                const existingMessage = form.querySelector('.form-message');
                if (existingMessage) existingMessage.remove();

                // Create new message
                const messageDiv = document.createElement('div');
                messageDiv.className = `form-message px-4 py-3 rounded-lg mt-4 ${
                    type === 'success'
                        ? 'bg-green-100 text-green-800 border border-green-200'
                        : 'bg-red-100 text-red-800 border border-red-200'
                }`;
                messageDiv.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
                        <span>${message}</span>
                    </div>
                `;

                // Add to form
                form.appendChild(messageDiv);

                // Auto-remove after delay if success
                if (type === 'success') {
                    setTimeout(() => {
                        messageDiv.remove();
                    }, 5000);
                }
            }

            function isValidEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(String(email).toLowerCase());
            }

            // Newsletter Form Handling
            const newsletterForm = document.querySelector('form[action="subscribe.php"]');
            const newsletterMessage = document.getElementById('newsletter-message');

            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(newsletterForm);

                    // Show loading state
                    const submitButton = newsletterForm.querySelector('button[type="submit"]');
                    const originalButtonText = submitButton.innerHTML;
                    submitButton.innerHTML = '...';
                    submitButton.disabled = true;

                    // Simulate form submission
                    setTimeout(() => {
                        // Create message element
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'px-4 py-2 rounded-md bg-green-100 text-green-800 border border-green-200';
                        messageDiv.textContent = 'Thank you for subscribing to our newsletter!';

                        // Clear previous messages and show new one
                        newsletterMessage.innerHTML = '';
                        newsletterMessage.appendChild(messageDiv);

                        // Reset form
                        newsletterForm.reset();

                        // Reset button state
                        submitButton.innerHTML = originalButtonText;
                        submitButton.disabled = false;

                        // Auto-remove message after delay
                        setTimeout(() => {
                            messageDiv.remove();
                        }, 5000);
                    }, 1500);
                });
            }
        });
    </script>
</body>
</html>