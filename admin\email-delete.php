<?php
require_once('header.php');

if(isset($_GET['id'])) {
    $id = $_GET['id'];
    
    // Delete the email log entry
    $statement = $pdo->prepare("DELETE FROM tbl_email_log WHERE id = ?");
    $statement->execute(array($id));
    
    // Redirect back with success message
    $success_message = 'Email log entry deleted successfully.';
    header('Location: email-log.php?success=' . urlencode($success_message));
    exit;
}

// If ID is not set, redirect back with error
$error_message = 'Invalid request.';
header('Location: email-log.php?error=' . urlencode($error_message));
exit;
?>
