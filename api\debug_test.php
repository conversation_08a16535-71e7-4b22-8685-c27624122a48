<?php
/**
 * Debug API Test
 * Shows raw responses to debug the "Invalid response format" issue
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>Debug API Test</h1>";

// Detect the correct base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$script_dir = dirname($_SERVER['SCRIPT_NAME']);
$base_url = $protocol . '://' . $host . $script_dir . '/v1';

echo "<p><strong>Base URL:</strong> {$base_url}</p>";

$test_endpoints = [
    'API Root' => $base_url . '/',
    'Settings' => $base_url . '/settings/app',
    'Products' => $base_url . '/products?limit=3'
];

foreach ($test_endpoints as $name => $url) {
    echo "<h2>Testing: {$name}</h2>";
    echo "<p><strong>URL:</strong> {$url}</p>";
    
    // Create context for the request
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Accept: application/json',
                'Content-Type: application/json'
            ],
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>✗ Failed to connect</p>";
        $error = error_get_last();
        if ($error) {
            echo "<p style='color: red;'>Error: " . $error['message'] . "</p>";
        }
        continue;
    }
    
    echo "<h3>Raw Response:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    echo "<h3>Response Analysis:</h3>";
    echo "<p><strong>Response Length:</strong> " . strlen($response) . " bytes</p>";
    
    // Check if it's valid JSON
    $data = json_decode($response, true);
    $json_error = json_last_error();
    
    if ($json_error === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✓ Valid JSON</p>";
        
        // Check structure
        if (isset($data['status'])) {
            echo "<p style='color: green;'>✓ Has 'status' field: " . $data['status'] . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Missing 'status' field</p>";
        }
        
        if (isset($data['message'])) {
            echo "<p style='color: green;'>✓ Has 'message' field: " . $data['message'] . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Missing 'message' field</p>";
        }
        
        if (isset($data['data'])) {
            echo "<p style='color: green;'>✓ Has 'data' field</p>";
        } else {
            echo "<p style='color: orange;'>⚠ No 'data' field</p>";
        }
        
        echo "<h3>Parsed JSON Structure:</h3>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo json_encode($data, JSON_PRETTY_PRINT);
        echo "</pre>";
        
    } else {
        echo "<p style='color: red;'>✗ Invalid JSON</p>";
        echo "<p style='color: red;'>JSON Error: " . json_last_error_msg() . "</p>";
        
        // Show first 200 characters for debugging
        echo "<h3>Response Preview:</h3>";
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars(substr($response, 0, 200));
        if (strlen($response) > 200) {
            echo "\n... (truncated)";
        }
        echo "</pre>";
    }
    
    echo "<hr>";
}

// Test with cURL for comparison
echo "<h2>cURL Test (for comparison)</h2>";

$curl_url = $base_url . '/';
echo "<p><strong>Testing:</strong> {$curl_url}</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $curl_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json'
]);

$curl_response = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($curl_error) {
    echo "<p style='color: red;'>cURL Error: {$curl_error}</p>";
} else {
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    echo "<h3>cURL Response:</h3>";
    echo "<pre style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($curl_response);
    echo "</pre>";
    
    $curl_data = json_decode($curl_response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✓ cURL response is valid JSON</p>";
    } else {
        echo "<p style='color: red;'>✗ cURL response is invalid JSON: " . json_last_error_msg() . "</p>";
    }
}

echo "<hr>";
echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
