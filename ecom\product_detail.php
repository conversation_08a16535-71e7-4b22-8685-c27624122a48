<?php
// product_detail.php
ob_start(); // Start output buffering
session_start(); // Start session

// --- Configuration & Includes ---
require_once("../admin/inc/config.php");
require_once("../admin/inc/functions.php");
require_once("../admin/inc/CSRF_Protect.php");

$csrf = new CSRF_Protect();

// --- File Paths & Settings ---
$upload_path_featured = "../assets/uploads/";
$upload_path_variations = "../assets/uploads/product_variations/";
$upload_path_gallery = "../assets/uploads/";
$installation_fee = 15000; // Default installation fee, will be overridden by product-specific fee if available

// --- Input Validation ---
if (!isset($_GET['id']) || !filter_var($_GET['id'], FILTER_VALIDATE_INT) || (int)$_GET['id'] <= 0) {
    error_log("Invalid or missing product ID accessed: " . ($_GET['id'] ?? 'Not Set'));
    header("Location: index.php");
    exit();
}
$product_id = (int)$_GET['id'];

// --- Initialize variables ---
$product = null;
$variations_data = [];
$product_photos = [];
$settings = [];
$footer_copyright = ('&copy; ' . date('Y') . ' SMART LIFE. All rights reserved.');
// REMOVED: Shipping variables
$variations_json = [];
$available_variation_names = [];
$variation_images_map = [];
$initial_variation = null;
$initial_price = 0;
$initial_quantity = 0; // Base quantity, variation quantity overrides if selected
$initial_sku = 'N/A';
$initial_image = 'images/default_product.png';
$initial_description = '';
$initial_variation_id = null;
$initial_variation_name = '';
$initial_size_name = null; // Keep size for variations
$in_cart = false;
$cart_button_text = 'Out of Stock';
$cart_button_disabled = true;
$initial_total = 0;
$base_product_description = '';
$base_product_short_description = '';
$product_category_name = '';
$has_variations = false;

// --- NEW: Color Selection Variables ---
$product_colors = []; // Array to hold all available colors for this product
$product_colors_json = []; // JSON representation for JS
$initial_selected_color_id = null;
$initial_selected_color_name = null;
$initial_selected_color_code = null;
$has_colors = false;

// --- Database Operations ---
try {
    // Fetch Base Product Details
    $stmt_product = $pdo->prepare("
        SELECT p.*, tcat.tcat_name
        FROM tbl_product p
        LEFT JOIN tbl_top_category tcat ON p.tcat_id = tcat.tcat_id
        WHERE p.p_id = ? AND p.p_is_active = 1
    ");
    $stmt_product->execute([$product_id]);
    $product = $stmt_product->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        error_log("Product ID {$product_id} not found or inactive.");
        header("Location: index.php");
        exit();
    }

    // Debug: Log the product data to see if installation_fee is included
    error_log("Product data: " . json_encode($product));

    // Set base product details
    $initial_price = is_numeric($product['p_current_price']) ? (float)$product['p_current_price'] : 0;
    $initial_quantity = (int)$product['p_qty']; // Base quantity
    $initial_sku = htmlspecialchars($product['p_id']); // Default fallback SKU

    // --- Fetch SKU code from tbl_sku for this product (SKU CODE ONLY) ---
    $stmt_sku = $pdo->prepare("
        SELECT sku_code
        FROM tbl_sku
        WHERE product_id = ? AND status = 1
        ORDER BY sku_code ASC
        LIMIT 1
    ");
    $stmt_sku->execute([$product_id]);
    $sku_result = $stmt_sku->fetch(PDO::FETCH_ASSOC);

    if ($sku_result && !empty($sku_result['sku_code'])) {
        $initial_sku = htmlspecialchars($sku_result['sku_code']);
    }
    // --- END SKU FETCH ---
    if (!empty($product['p_featured_photo'])) {
        $initial_image = $upload_path_featured . htmlspecialchars($product['p_featured_photo']);
    }
    $base_product_description = $product['p_description'] ?? '';
    $base_product_short_description = $product['p_short_description'] ?? '';
    $initial_description = $base_product_description;
    $product_category_name = htmlspecialchars($product['tcat_name'] ?? '');

    // Get product-specific installation fee if available
    if (isset($product['installation_fee']) && is_numeric($product['installation_fee'])) {
        $installation_fee = (int)$product['installation_fee'];
        error_log("Product-specific installation fee found: " . $installation_fee);
    } else {
        error_log("Using default installation fee: " . $installation_fee);
    }

    // --- NEW: Fetch ALL Associated Product Colors ---
    $stmt_product_colors = $pdo->prepare("
        SELECT tc.color_id, tc.color_name, tc.color_code
        FROM tbl_product_color tpc
        JOIN tbl_color tc ON tpc.color_id = tc.color_id
        WHERE tpc.p_id = ?
        ORDER BY tc.color_name ASC
    ");
    $stmt_product_colors->execute([$product_id]);
    $product_colors_raw = $stmt_product_colors->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($product_colors_raw)) {
        $has_colors = true;
        foreach ($product_colors_raw as $color) {
            $color_data = [
                'id' => (int)$color['color_id'],
                'name' => htmlspecialchars($color['color_name']),
                'code' => htmlspecialchars($color['color_code'])
            ];
            $product_colors[$color['color_id']] = $color_data; // Store by ID for easy lookup
        }
        // Set initial selected color (first one in the list)
        $first_color = reset($product_colors); // Get the first element
        if ($first_color) {
            $initial_selected_color_id = $first_color['id'];
            $initial_selected_color_name = $first_color['name'];
            $initial_selected_color_code = $first_color['code'];
        }
        $product_colors_json = $product_colors; // Assign for JS
    }
    // --- END NEW COLOR FETCH ---


    // Fetch Product Variations (No color info here)
    $stmt_variations = $pdo->prepare("
        SELECT
            pv.variation_id, pv.p_id,
            pv.variation_name, pv.variation_description,
            pv.variation_size as size_id, s.size_name,
            pv.variation_price, pv.variation_qty, pv.variation_sku, pv.variation_image
        FROM tbl_product_variation pv
        LEFT JOIN tbl_size s ON pv.variation_size = s.size_id
        WHERE pv.p_id = ?
        ORDER BY pv.variation_name ASC, s.size_name ASC
    ");
    $stmt_variations->execute([$product_id]);
    $variations_data = $stmt_variations->fetchAll(PDO::FETCH_ASSOC);

    // Fetch Additional Gallery Photos/Videos
    $stmt_photos = $pdo->prepare("SELECT photo_name, media_type FROM tbl_product_photo WHERE p_id = ?");
    $stmt_photos->execute([$product_id]);
    $product_photos = $stmt_photos->fetchAll(PDO::FETCH_ASSOC);

    // Fetch Settings
    $stmt_settings = $pdo->prepare("SELECT footer_copyright FROM tbl_settings WHERE id=1");
    $stmt_settings->execute();
    $settings = $stmt_settings->fetch(PDO::FETCH_ASSOC);
    if ($settings && !empty($settings['footer_copyright'])) {
         $footer_copyright = htmlspecialchars($settings['footer_copyright']);
    }

    // REMOVED: Fetch Shipping Data

} catch (PDOException $e) {
    error_log("Database Error in product_detail.php for product ID {$product_id}: " . $e->getMessage());
    exit('Database Error: ' . $e->getMessage() . ' (Check PHP error log for more details)');
}

// --- Prepare Variation Data for JavaScript & Determine Initial Variation ---
if (!empty($variations_data)) {
    $has_variations = true;
    foreach ($variations_data as $var) {
        // Process variations as before (excluding color)
        $variation_actual_price = is_numeric($var['variation_price']) ? (float)$var['variation_price'] : 0;
        $variation_image_path = $var['variation_image'] ? $upload_path_variations . htmlspecialchars($var['variation_image']) : null;

        // Check if there's a SKU for this variation from tbl_sku (using base product_id)
        $variation_sku_code = $var['variation_sku'] ? htmlspecialchars($var['variation_sku']) : 'N/A';

        // Try to get SKU from tbl_sku table using base product_id
        $stmt_var_sku = $pdo->prepare("
            SELECT sku_code
            FROM tbl_sku
            WHERE product_id = ? AND status = 1
            ORDER BY sku_code ASC
            LIMIT 1
        ");
        $stmt_var_sku->execute([$product_id]);
        $var_sku_result = $stmt_var_sku->fetch(PDO::FETCH_ASSOC);

        if ($var_sku_result && !empty($var_sku_result['sku_code'])) {
            $variation_sku_code = htmlspecialchars($var_sku_result['sku_code']);
        }

        $js_var_data = [
            'id' => (int)$var['variation_id'],
            'name' => htmlspecialchars($var['variation_name'] ?? 'Unnamed Variation'),
            'description' => $var['variation_description'] ?? '',
            'size_id' => $var['size_id'] ? (int)$var['size_id'] : null,
            'size_name' => $var['size_name'] ? htmlspecialchars($var['size_name']) : null,
            'price' => $variation_actual_price,
            'quantity' => (int)$var['variation_qty'],
            'sku' => $variation_sku_code,
            'image' => $variation_image_path
        ];
        $variations_json[$var['variation_id']] = $js_var_data;

        if (!isset($available_variation_names[$var['variation_id']])) {
             $available_variation_names[$var['variation_id']] = $js_var_data['name'];
        }
         if ($variation_image_path) {
              $variation_images_map[$variation_image_path] = $var['variation_id'];
         }
    }

    // Select the first variation as the default
    $first_variation_key = array_key_first($variations_json);
    if ($first_variation_key !== null) {
        $initial_variation = $variations_json[$first_variation_key];
        $initial_variation_id = $initial_variation['id'];
        $initial_variation_name = $initial_variation['name'];
        $initial_price = $initial_variation['price'];
        $initial_quantity = $initial_variation['quantity']; // Variation quantity overrides base
        $initial_sku = $initial_variation['sku'] !== 'N/A' && !empty($initial_variation['sku']) ? $initial_variation['sku'] : $initial_sku;
        $initial_description = !empty($initial_variation['description']) ? $initial_variation['description'] : $base_product_description;
        $initial_size_name = $initial_variation['size_name'];
        if ($initial_variation['image']) {
             $initial_image = $initial_variation['image'];
        }
    }
} else {
    // No variations, use base product details already set
    $has_variations = false;
    // Base price, quantity, description already assigned
    // $initial_quantity remains the base product quantity
}

// --- Check Cart Status for Initial Item (Product + Variation + Color) ---
$item_identifier_for_cart_check = $product_id . '-' . ($initial_variation_id ?? '0') . '-' . ($initial_selected_color_id ?? '0');
if (isset($_SESSION['cart_identifiers']) && is_array($_SESSION['cart_identifiers'])) {
    if (in_array($item_identifier_for_cart_check, $_SESSION['cart_identifiers'])) {
        $in_cart = true;
    }
}
// Note: We'll manage the actual cart check primarily in JS using local storage now.
// The session check here is a basic initial check.


// Determine initial button state
if ($in_cart) { // Basic session check might be insufficient, JS will refine this
    $cart_button_text = 'Already in Cart';
    $cart_button_disabled = true;
} elseif ($has_colors && !$initial_selected_color_id) {
     $cart_button_text = 'Select Color'; // Need color selection first
     $cart_button_disabled = true;
} elseif ($has_variations && !$initial_variation_id && !empty($variations_data)) {
    $cart_button_text = 'Select Option'; // Need variation selection first
    $cart_button_disabled = true;
} elseif ($initial_quantity > 0) {
    $cart_button_text = 'Add to Cart';
    $cart_button_disabled = false; // Will be re-evaluated by JS based on cart & stock
} else {
    $cart_button_text = 'Out of Stock';
    $cart_button_disabled = true;
}

// Calculate initial total price (No shipping, installation added by JS)
$initial_total = $initial_price;

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <title><?php echo htmlspecialchars($product['p_name'] ?? 'Product Details'); ?> <?php echo $initial_variation_name ? '| ' . $initial_variation_name : ''; ?> | SMART Security</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

  <link rel="stylesheet" href="css/product.css" />

  <style>
    /* Search Styles */
    .search-container {
      position: relative;
      margin: 0 15px;
    }

    .search-box {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 8px;
      padding: 5px 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;
    }

    .search-box:focus-within {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      border-color: #00c2ff;
    }

    #searchInput, #mobileSearchInput {
      border: none;
      outline: none;
      padding: 8px;
      width: 200px;
      font-size: 0.95rem;
      background: transparent;
    }

    .search-btn {
      background: none;
      border: none;
      padding: 5px;
      cursor: pointer;
      color: #4a5568;
      transition: color 0.3s ease;
    }

    .search-btn:hover {
      color: #00c2ff;
    }

    #searchSuggestions, #mobileSearchSuggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      margin-top: 5px;
      max-height: 300px;
      overflow-y: auto;
      z-index: 1000;
    }

    .suggestion-item {
      padding: 10px 15px;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: background-color 0.2s ease;
    }

    .suggestion-item:hover {
      background-color: #f8f9fa;
    }

    .suggestion-item .icon {
      margin-right: 10px;
      color: #00c2ff;
    }

    .suggestion-item .type {
      font-size: 0.8rem;
      color: #718096;
      margin-left: auto;
    }

    .suggestion-item .highlight {
      background-color: #fff3cd;
      padding: 0 2px;
      border-radius: 2px;
    }

    @media (max-width: 768px) {
      .search-container {
        margin: 10px 0;
        width: 100%;
      }

      .search-box {
        width: 100%;
      }

      #searchInput, #mobileSearchInput {
        width: 100%;
      }
    }
  </style>
  <style>
      /* Modern Mobile Cart Icon Styles */
      .mobile-cart-icon {
          display: none;
          position: relative;
      }

      .mobile-cart-icon a {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 12px;
          background: linear-gradient(145deg, #f8f9fa, #e6e7e8);
          box-shadow: 5px 5px 10px rgba(0,0,0,0.05),
                     -5px -5px 10px rgba(255,255,255,0.8);
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .mobile-cart-icon a:hover {
          transform: translateY(-2px);
          box-shadow: 6px 6px 12px rgba(0,0,0,0.06),
                     -6px -6px 12px rgba(255,255,255,0.9);
      }

      .mobile-cart-icon a:active {
          transform: translateY(0);
          box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1),
                     inset -2px -2px 5px rgba(255,255,255,0.5);
      }

      .mobile-cart-icon i {
          color: #00c2ff;
          font-size: 1.25rem;
          transition: transform 0.3s ease;
      }

      .mobile-cart-icon a:hover i {
          transform: scale(1.1);
      }

      .mobile-cart-icon .cart-count {
          position: absolute;
          top: -5px;
          right: -5px;
          min-width: 20px;
          height: 20px;
          border-radius: 10px;
          background: #ff3366;
          color: white;
          font-size: 0.7rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 6px;
          box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          transition: all 0.3s ease;
      }

      /* Empty cart style */
      .mobile-cart-icon .cart-count:empty,
      .mobile-cart-icon .cart-count[innerHTML="0"] {
          transform: scale(0.8);
          opacity: 0.7;
      }

      /* Pulse animation */
      @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
      }

      .pulse-animation {
          animation: pulse 0.5s ease-in-out;
      }

      /* Header right controls container */
      .header-right-controls {
          display: flex;
          align-items: center;
      }

      @media (max-width: 768px) {
          .mobile-cart-icon {
              display: block;
              margin-right: 15px;
          }

          nav {
              justify-content: space-between;
          }

          .header-right-controls {
              display: flex;
              align-items: center;
          }
      }

      /* Basic styling for color swatches */
      .color-selector-group { margin-bottom: 20px; }
      .color-selector { display: flex; flex-wrap: wrap; gap: 10px; margin-top: 5px; }
      .color-option { display: inline-block; position: relative; }
      .color-radio {
          opacity: 0; /* Hide radio */
          position: absolute;
          width: 100%; height: 100%;
          top: 0; left: 0;
          cursor: pointer;
          margin: 0;
      }
      .color-swatch-label {
          display: block;
          width: 30px; /* Adjust size */
          height: 30px;
          border-radius: 50%;
          border: 2px solid #eee;
          cursor: pointer;
          transition: border-color 0.2s;
          position: relative; /* For pseudo-element */
      }
       .color-swatch-label::after { /* Optional: Checkmark for selected */
            content: '\f00c'; /* Font Awesome check icon */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            color: white; /* Or black depending on swatch color */
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.2s;
        }

      .color-radio:checked + .color-swatch-label {
          border-color: #333; /* Highlight selected */
          box-shadow: 0 0 5px rgba(0,0,0,0.3);
      }
       .color-radio:checked + .color-swatch-label::after {
           opacity: 1;
       }
       .color-radio:focus + .color-swatch-label { /* Accessibility */
           outline: 2px solid blue;
           outline-offset: 1px;
       }

      .color-name-display { /* To show selected color name */
          margin-top: 10px;
          font-size: 0.9em;
          color: #555;
      }
      .color-name-display strong { color: #000; }

      /* Quantity Control Styling */
      .quantity-control {
          display: flex;
          align-items: center;
          border: 1px solid #ddd;
          border-radius: 4px;
          overflow: hidden;
          width: fit-content;
      }

      .quantity-btn {
          background-color: #f5f5f5;
          border: none;
          color: #333;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: background-color 0.2s;
      }

      .quantity-btn:hover {
          background-color: #e0e0e0;
      }

      .quantity-btn:disabled {
          color: #ccc;
          cursor: not-allowed;
      }

      .quantity-display {
          padding: 0 15px;
          min-width: 40px;
          text-align: center;
          font-weight: bold;
      }

      .group-label {
             display: block;
             margin-bottom: 8px;
             font-weight: bold;
             color: #333;
             font-size: 0.95em;
         }

      /* Add these new styles */
      .main-image-container {
          position: relative;
          cursor: pointer;
      }

      /* Video thumbnail styles */
      .video-thumbnail {
          position: relative;
          display: inline-block;
          width: 60px;
          height: 60px;
          border: 2px solid transparent;
          border-radius: 4px;
          overflow: hidden;
          cursor: pointer;
      }

      .video-thumbnail.active {
          border-color: #00c2ff;
      }

      .video-thumbnail img {
          width: 100%;
          height: 100%;
          object-fit: cover;
      }

      .video-icon {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.3);
          transition: background-color 0.3s ease;
      }

      .video-icon i {
          color: white;
          font-size: 24px;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      }

      .video-thumbnail:hover .video-icon {
          background-color: rgba(0, 0, 0, 0.5);
      }

      .main-image-container::after {
          content: 'Click to view gallery';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 8px;
          text-align: center;
          font-size: 14px;
          opacity: 0;
          transition: opacity 0.3s ease;
      }

      /* Only show the gallery text when hovering over the image, not when video is active */
      .main-image-container:hover::after {
          opacity: 1;
      }

      /* Hide the gallery text when video container is visible */
      /* Using a class for better browser compatibility */
      .main-image-container.video-active:hover::after {
          opacity: 0;
      }

      /* Professional Alert System */
      .alert-container {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 10000;
          max-width: 400px;
          width: 100%;
          pointer-events: none;
      }

      .alert {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 16px;
          padding: 20px;
          margin-bottom: 12px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
                      0 8px 16px rgba(0, 0, 0, 0.05),
                      inset 0 1px 0 rgba(255, 255, 255, 0.8);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transform: translateX(420px);
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          pointer-events: auto;
          position: relative;
          overflow: hidden;
      }

      .alert.show {
          transform: translateX(0);
          opacity: 1;
      }

      .alert::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #00c2ff, #0099cc);
          border-radius: 16px 16px 0 0;
      }

      .alert.success::before {
          background: linear-gradient(90deg, #10b981, #059669);
      }

      .alert.warning::before {
          background: linear-gradient(90deg, #f59e0b, #d97706);
      }

      .alert.error::before {
          background: linear-gradient(90deg, #ef4444, #dc2626);
      }

      .alert-content {
          display: flex;
          align-items: flex-start;
          gap: 12px;
      }

      .alert-icon {
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: white;
          background: #00c2ff;
          margin-top: 2px;
      }

      .alert.success .alert-icon {
          background: #10b981;
      }

      .alert.warning .alert-icon {
          background: #f59e0b;
      }

      .alert.error .alert-icon {
          background: #ef4444;
      }

      .alert-text {
          flex: 1;
          min-width: 0;
      }

      .alert-title {
          font-weight: 600;
          font-size: 16px;
          color: #1f2937;
          margin-bottom: 4px;
          line-height: 1.3;
      }

      .alert-message {
          font-size: 14px;
          color: #6b7280;
          line-height: 1.4;
      }

      .alert-close {
          position: absolute;
          top: 12px;
          right: 12px;
          background: none;
          border: none;
          font-size: 18px;
          color: #9ca3af;
          cursor: pointer;
          padding: 4px;
          border-radius: 50%;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
      }

      .alert-close:hover {
          background: rgba(0, 0, 0, 0.05);
          color: #374151;
      }

      .alert-progress {
          position: absolute;
          bottom: 0;
          left: 0;
          height: 3px;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 0 0 16px 16px;
          transform-origin: left;
          animation: progress-shrink 3s linear forwards;
      }

      @keyframes progress-shrink {
          from { width: 100%; }
          to { width: 0%; }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
          .alert {
              background: rgba(31, 41, 55, 0.95);
              border: 1px solid rgba(75, 85, 99, 0.3);
          }

          .alert-title {
              color: #f9fafb;
          }

          .alert-message {
              color: #d1d5db;
          }

          .alert-close {
              color: #9ca3af;
          }

          .alert-close:hover {
              background: rgba(255, 255, 255, 0.1);
              color: #f3f4f6;
          }
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
          .alert-container {
              top: 10px;
              right: 15px;
              left: auto;
              max-width: 280px;
              width: auto;
          }

          .alert {
              transform: translateY(-100px);
              margin-bottom: 6px;
              padding: 12px 16px;
              border-radius: 12px;
              font-size: 14px;
          }

          .alert.show {
              transform: translateY(0);
          }

          .alert-content {
              gap: 8px;
          }

          .alert-icon {
              width: 20px;
              height: 20px;
              font-size: 10px;
              margin-top: 1px;
          }

          .alert-title {
              font-size: 14px;
              margin-bottom: 2px;
              line-height: 1.2;
          }

          .alert-message {
              font-size: 12px;
              line-height: 1.3;
          }

          .alert-close {
              top: 8px;
              right: 8px;
              width: 24px;
              height: 24px;
              font-size: 16px;
              padding: 2px;
          }

          .alert::before {
              height: 3px;
          }

          .alert-progress {
              height: 2px;
          }
      }

      .main-image-container::before {
          content: '\f002';  /* Changed to correct Font Awesome search icon */
          font-family: 'Font Awesome 6 Free';
          font-weight: 900;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 24px;
          background: rgba(0, 0, 0, 0.7);
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 2;
      }

      .main-image-container:hover::before {
          opacity: 1;
      }

      /* Hide the search icon when video is active */
      .main-image-container.video-active:hover::before {
          opacity: 0;
      }

      .main-image-container:hover img {
          filter: brightness(0.9);
          transition: filter 0.3s ease;
      }

      /* Read More Button Styles */
      .description-preview {
          position: relative;
          overflow: hidden;
          line-height: 1.6;
      }

      .preview-content {
          display: -webkit-box;
          display: box;
          -webkit-line-clamp: 6;
          line-clamp: 6;
          -webkit-box-orient: vertical;
          box-orient: vertical;
          overflow: hidden;
          max-height: 9.6em; /* 6 lines × 1.6 line-height */
      }

      /* Ensure HTML formatting works correctly */
      .preview-content ul, .description-full ul {
          list-style-type: disc;
          padding-left: 20px;
          margin: 10px 0;
      }

      .preview-content ol, .description-full ol {
          list-style-type: decimal;
          padding-left: 20px;
          margin: 10px 0;
      }

      .preview-content li, .description-full li {
          margin-bottom: 5px;
      }

      /* Modern Read More Button */
      .read-more-btn {
          display: inline-flex;
          align-items: center;
          margin-top: 12px;
          padding: 8px 18px;
          background: linear-gradient(135deg, #00c2ff, #0080ff);
          color: white;
          border: none;
          border-radius: 30px;
          font-size: 0.9rem;
          font-weight: 500;
          cursor: pointer;
          box-shadow: 0 4px 10px rgba(0, 194, 255, 0.3);
          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          position: relative;
          overflow: hidden;
      }

      .read-more-btn::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.2);
          transition: width 0.4s ease;
          z-index: 1;
      }

      .read-more-btn:hover {
          transform: translateY(-3px);
          box-shadow: 0 6px 15px rgba(0, 194, 255, 0.4);
      }

      .read-more-btn:hover::after {
          width: 100%;
      }

      .read-more-btn:active {
          transform: translateY(0);
          box-shadow: 0 2px 5px rgba(0, 194, 255, 0.3);
      }

      /* Animation for Read More/Less toggle */
      @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
      }

      @keyframes fadeOut {
          from { opacity: 1; transform: translateY(0); }
          to { opacity: 0; transform: translateY(10px); }
      }

      .description-full {
          margin-top: 10px;
          line-height: 1.6;
          animation: fadeIn 0.5s ease forwards;
      }

      .description-preview.hiding {
          animation: fadeOut 0.5s ease forwards;
      }

      /* Custom CSS for Header and Footer */
      /* Header Styles */
      header.bg-white {
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          transition: transform 0.3s ease;
      }

      /* Auto-hide header when scrolling down */
      header.header-hidden {
          transform: translateY(-100%);
      }

      /* Mobile Menu Styles */
      #mobileMenu {
          box-shadow: -5px 0 15px rgba(0,0,0,0.1);
      }

      /* Footer Styles */
      footer.bg-gray-900 {
          background-color: #1a202c;
      }

      footer .text-blue-500 {
          color: #3b82f6;
      }

      footer .text-gray-400 {
          color: #9ca3af;
      }

      footer .text-white {
          color: #ffffff;
      }

      footer .border-gray-800 {
          border-color: #2d3748;
      }

      /* Back to Top Button */
      #backToTop {
          background-color: #3b82f6;
          color: white;
          z-index: 40;
      }

      #backToTop:hover {
          background-color: #2563eb;
      }

      /* Fix for Tailwind conflicts */
      .container {
          width: 90%;
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 15px;
      }

      /* Fix for bullet points and numbered lists in Technical Specifications */
      .modal-text-content ul {
          list-style-type: disc !important;
          padding-left: 20px !important;
          margin: 10px 0 !important;
      }

      .modal-text-content ol {
          list-style-type: decimal !important;
          padding-left: 20px !important;
          margin: 10px 0 !important;
      }

      .modal-text-content li {
          display: list-item !important;
          margin-bottom: 5px !important;
      }

      @media (max-width: 768px) {
          #mobileMenu {
              width: 80%;
              right: 0;
          }
      }
  </style>
</head>
<body>

<!-- Header -->
<header id="mainHeader" class="bg-white shadow-md sticky top-0 z-40">
    <div class="container mx-auto px-4 flex items-center justify-between h-16">
        <a href="index.php" class="text-2xl font-bold text-gray-800 flex items-center">
            SMART LIFE<span class="text-[#00c2ff]">.</span>
        </a>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
            <a href="index.php" class="text-gray-700 hover:text-[#00c2ff] transition-colors duration-200 font-medium">Home</a>
            <a href="all_products.php" class="text-gray-700 hover:text-[#00c2ff] transition-colors duration-200 font-medium">All Products</a>
            <a href="index.php#about" class="text-gray-700 hover:text-[#00c2ff] transition-colors duration-200 font-medium">About</a>
            <a href="index.php#contact" class="text-gray-700 hover:text-[#00c2ff] transition-colors duration-200 font-medium">Contact</a>

            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="Search products..." autocomplete="off">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div id="searchSuggestions" class="hidden"></div>
            </div>

            <!-- Cart Icon -->
            <div class="cart-icon">
                <a href="cart.php" class="text-gray-700 hover:text-[#00c2ff] relative transition-colors duration-200">
                    <i class="fas fa-shopping-cart text-xl"></i>
                    <span class="cart-count absolute -top-1 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center" id="cartCount">0</span>
                </a>
            </div>
        </nav>

        <!-- Mobile Menu Button -->
        <div class="md:hidden flex items-center">
            <!-- Mobile Cart Icon -->
            <div class="mobile-cart-icon mr-4">
                <a href="cart.php" aria-label="View Shopping Cart">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="mobileCartCount">0</span>
                </a>
            </div>

            <button id="mobileMenuButton" class="flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </div>
</header>

<!-- Mobile Menu -->
<div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-50 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
    <div class="flex flex-col h-full">
        <div class="flex justify-between items-center p-4 border-b sticky top-0 bg-white z-10">
            <a href="index.php" class="text-xl font-bold text-gray-900">
                SMART LIFE<span class="text-[#00c2ff]">.</span>
            </a>
            <button id="closeMobileMenu" class="text-gray-700 p-2 hover:bg-gray-100 rounded-full">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        <div class="overflow-y-auto flex-1" style="max-height: calc(100vh - 70px);">
            <nav class="p-4 space-y-4">
                <!-- Search in Mobile Menu -->
                <div class="search-container mb-4">
                    <div class="search-box">
                        <input type="text" id="mobileSearchInput" placeholder="Search products..." autocomplete="off">
                        <button class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div id="mobileSearchSuggestions" class="hidden"></div>
                </div>

                <a href="index.php" class="block text-gray-700 hover:text-[#00c2ff] transition py-2">Home</a>
                <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition py-2">About</a>
                <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition py-2">Products</a>
                <a href="all_products.php" class="block text-gray-700 hover:text-[#00c2ff] transition py-2">All Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition py-2">Best Deals</a>
                <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition py-2">Contact</a>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition mt-4 py-2">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                    <span class="ml-2">View Cart</span>
                </a>
            </nav>
        </div>
    </div>
</div>

<!-- Backdrop for mobile menu -->
<div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

<main>
    <div class="container">
        <?php if ($product): ?>
        <div class="product-detail-container">
            <div class="product-gallery">
                <div class="main-image-container" id="mainMediaContainer">
                    <img id="mainImage" src="<?php echo $initial_image; ?>" alt="<?php echo htmlspecialchars($product['p_name']); ?>" class="main-image" onerror="this.onerror=null; this.src='images/default_product.png';">
                    <div id="mainVideoContainer" style="display: none; width: 100%; height: 100%; position: relative;">
                        <video id="mainVideo" controls preload="metadata" poster="images/video-placeholder.jpg" style="max-width: 100%; max-height: 100%; display: block; margin: 0 auto;">
                            <source src="" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div id="mainVideoPlayOverlay" class="video-play-overlay" style="display: flex;">
                            <button class="play-button"><i class="fas fa-play"></i></button>
                        </div>
                    </div>
                </div>
                 <div class="thumbnail-container"> <?php $displayed_thumb_images = []; $base_featured_img_path = !empty($product['p_featured_photo']) ? $upload_path_featured . htmlspecialchars($product['p_featured_photo']) : null; if ($base_featured_img_path && !in_array($base_featured_img_path, $displayed_thumb_images) && !array_key_exists($base_featured_img_path, $variation_images_map) ) { $is_active = ($initial_image == $base_featured_img_path); $data_variation_id_attr = ''; if ($has_variations && !$initial_variation && $is_active) { $data_variation_id_attr = ''; } elseif ($initial_variation && !$initial_variation['image'] && $is_active) { $data_variation_id_attr = ' data-variation-id="' . $initial_variation['id'] . '"'; } echo '<img src="' . $base_featured_img_path . '" alt="Featured Thumbnail" class="thumbnail' . ($is_active ? ' active' : '') . '" data-image="' . $base_featured_img_path . '"' . $data_variation_id_attr . ' onerror="this.style.display=\'none\'">'; $displayed_thumb_images[] = $base_featured_img_path; } foreach ($variations_json as $var_id => $var_data): if ($var_data['image'] && !in_array($var_data['image'], $displayed_thumb_images)): $is_active = ($initial_image == $var_data['image']); echo '<img src="' . $var_data['image'] . '" alt="Variation ' . htmlspecialchars($var_data['name']) . '" class="thumbnail' . ($is_active ? ' active' : '') . '" data-image="' . $var_data['image'] . '" data-variation-id="' . $var_id . '" onerror="this.style.display=\'none\'">'; $displayed_thumb_images[] = $var_data['image']; endif; endforeach; foreach ($product_photos as $index => $photo):
    if (!empty($photo['photo_name'])) {
        $gallery_media_path = $upload_path_gallery . htmlspecialchars($photo['photo_name']);
        $is_video = isset($photo['media_type']) && $photo['media_type'] === 'video';

        if (!in_array($gallery_media_path, $displayed_thumb_images)):
            if ($is_video) {
                // Video thumbnail with play icon overlay
                echo '<div class="thumbnail video-thumbnail" data-video="' . $gallery_media_path . '" data-type="video">';
                echo '<img src="images/video-placeholder.jpg" alt="Video Thumbnail ' . ($index + 1) . '" onerror="this.src=\'images/video-placeholder-default.jpg\'">';
                echo '<div class="video-icon"><i class="fas fa-play-circle"></i></div>';
                echo '</div>';
            } else {
                // Regular photo thumbnail
                echo '<img src="' . $gallery_media_path . '" alt="Gallery Thumbnail ' . ($index + 1) . '" class="thumbnail" data-image="' . $gallery_media_path . '" data-type="image" onerror="this.style.display=\'none\'">';
            }
            $displayed_thumb_images[] = $gallery_media_path;
        endif;
    }
endforeach; ?> </div>
            </div>

            <div class="product-info">
                <h1 class="product-title"><?php echo htmlspecialchars($product['p_name']); ?></h1>
                <div class="product-price" id="productPrice">Tsh <?php echo number_format($initial_price, 0); ?></div>

                <div class="product-meta">
                    <span>Availability: <strong id="productAvailability" style="color: <?php echo ($initial_quantity > 0) ? '#1cc88a' : '#e74a3b'; ?>"><?php echo ($initial_quantity > 0) ? 'In Stock' : 'Out of Stock'; ?></strong></span>
                    <span id="productSku">SKU: <?php echo $initial_sku; ?></span>
                    <?php if (!empty($product_category_name)): ?>
                        <span>Category: <?php echo $product_category_name; ?></span>
                    <?php endif; ?>
                </div>

                 <div class="variation-attributes">
                     <span id="selectedColorDisplay" class="color-name-display" style="<?php echo $has_colors ? 'display: block;' : 'display: none;'; ?>">
                        Color: <strong id="selectedColorName"><?php echo $initial_selected_color_name ?? 'N/A'; ?></strong>
                    </span>
                    <span id="variationSizeDisplay" style="<?php echo $initial_size_name ? 'display: inline-flex;' : 'display: none;'; ?>">
                        Size: <strong id="variationSizeName"><?php echo $initial_size_name ?? ''; ?></strong>
                    </span>
                </div>


                <div class="product-description-container">
                    <?php if (!empty($base_product_description)): ?>
                    <h2 class="font-bold text-xl mb-3"><strong>Product Description</strong></h2>
                    <div class="description-content base-description" id="baseDescription">
                        <?php echo $base_product_description; ?>
                    </div>
                    <?php endif; ?>

                    <div id="variationDescriptionContainer" style="display: <?php echo ($initial_variation && !empty($initial_variation['description'])) ? 'block' : 'none'; ?>;">
                        <div class="description-content variation-description" id="variationDescription">
                            <?php if ($initial_variation && !empty($initial_variation['description'])): ?>
                            <div class="description-preview">
                                <?php
                                // Create a temporary div to hold the content
                                echo '<div class="preview-content">' . $initial_variation['description'] . '</div>';
                                ?>
                            </div>
                            <div class="description-full" style="display: none;"><?php echo $initial_variation['description']; ?></div>
                            <button class="read-more-btn" data-target="variationDescription"><i class="fas fa-chevron-down" style="margin-right: 5px;"></i> Read More</button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (!empty($base_product_short_description)): ?>
                    <h2 class="font-bold text-xl mb-3" style="margin-top: 20px;"><strong>Key Features</strong></h2>
                    <div class="description-content short-description" id="shortDescription">
                        <div class="description-preview">
                            <?php
                            // Create a temporary div to hold the content
                            echo '<div class="preview-content">' . $base_product_short_description . '</div>';
                            ?>
                        </div>
                        <div class="description-full" style="display: none;"><?php echo $base_product_short_description; ?></div>
                        <button class="read-more-btn" data-target="shortDescription"><i class="fas fa-chevron-down" style="margin-right: 5px;"></i> Read More</button>
                    </div>
                    <?php endif; ?>
                </div>

                <button id="openDetailsModalBtn" class="details-modal-trigger">View Specifications & Details</button>
                <input type="hidden" id="selectedVariationId" name="variation_id" value="<?php echo $initial_variation_id ?? ''; ?>">
                <input type="hidden" id="selectedColorId" name="color_id" value="<?php echo $initial_selected_color_id ?? ''; ?>">


                <div class="options-section">
                     <?php if ($has_colors): ?>
                    <div class="color-selector-group">
                        <label class="group-label">Select Color:</label>
                        <div class="color-selector" id="colorSelector">
                            <?php foreach ($product_colors as $color_id => $color_data): ?>
                                <div class="color-option" title="<?php echo $color_data['name']; ?>">
                                    <input type="radio" id="color-<?php echo $color_id; ?>"
                                           name="selected_color_id_selector"
                                           value="<?php echo $color_id; ?>"
                                           class="color-radio"
                                           <?php echo ($initial_selected_color_id == $color_id) ? 'checked' : ''; ?>>
                                    <label for="color-<?php echo $color_id; ?>"
                                           class="color-swatch-label"
                                           style="background-color: <?php echo $color_data['code'] ?: '#ffffff'; ?>; border-color: <?php echo ($color_data['code'] && strtolower($color_data['code']) == '#ffffff') ? '#ccc' : 'transparent'; ?>;"
                                           aria-label="<?php echo $color_data['name']; ?>">
                                           </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="variation-selector-group variation-name-container" style="<?php echo $has_variations ? 'display:block;' : 'display:none;'; ?>">
                        <?php if ($has_variations): ?>
                            <label class="group-label">Select Option:</label>
                            <div class="variation-name-selector" id="variationNameSelector">
                                <?php foreach ($available_variation_names as $var_id => $var_name): ?>
                                    <div class="variation-name-option">
                                        <input type="radio" id="variation-<?php echo $var_id; ?>"
                                               name="variation_id_selector" value="<?php echo $var_id; ?>"
                                               class="variation-name-radio"
                                               <?php echo ($initial_variation_id == $var_id) ? 'checked' : ''; ?>>
                                        <label for="variation-<?php echo $var_id; ?>" class="variation-name-label">
                                            <?php echo $var_name; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>


                    <div class="quantity-selector">
                        <label for="quantity">Quantity:</label>
                        <div class="quantity-control">
                            <button type="button" class="quantity-btn minus-btn" aria-label="Decrease quantity">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span id="quantity-display" class="quantity-display">1</span>
                            <input type="hidden" id="quantity" name="quantity" value="1" min="1"
                                   max="<?php echo max(1, $initial_quantity); ?>"
                                   <?php echo ($initial_quantity <= 0) ? 'disabled' : ''; ?>>
                            <button type="button" class="quantity-btn plus-btn" aria-label="Increase quantity">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                     <div class="additional-options">
                        <?php if($installation_fee > 0): ?>
                            <div class="fee-option">
                                <input type="checkbox" id="installation" name="installation">
                                <label for="installation">Professional Installation</label>
                                <span class="fee-amount" data-fee="<?php echo $installation_fee; ?>">+ Tsh <?php echo number_format($installation_fee, 0); ?></span>
                                <!-- Debug: Show the installation fee source -->
                                <span style="display: none;">Fee source: <?php echo isset($product['installation_fee']) ? 'Product-specific' : 'Default'; ?></span>
                            </div>
                         <?php endif; ?>
                         </div>
                 </div>


                <div class="price-action-section">
                     <div class="total-price">
                         <span class="label">Total:</span>
                         <span class="amount" id="totalPrice">
                             Tsh <?php echo number_format($initial_total, 0); ?>
                         </span>
                     </div>

                    <button class="add-to-cart-btn" id="addToCartBtn" <?php echo $cart_button_disabled ? 'disabled' : ''; ?>>
                         <span class="btn-icon"> <?php /* Icon logic */ if ($in_cart) { echo '<i class="fas fa-check"></i>'; } elseif ($cart_button_disabled && !in_array($cart_button_text, ['Select Option', 'Select Color'])) { echo '<i class="fas fa-times-circle"></i>'; } elseif (in_array($cart_button_text, ['Select Option', 'Select Color'])) { echo '<i class="fas fa-info-circle"></i>'; } else { echo '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>'; } ?> </span>
                         <span class="btn-text"><?php echo $cart_button_text; ?></span>
                     </button>

                     <a href="cart.php" class="return-to-cart">View Cart & Checkout →</a>
                 </div>
            </div>
        </div> <?php else: ?>
             <div style="text-align: center; padding: 50px; background: #fff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);"> <h2>Product Not Found</h2> <p>Sorry, the product you are looking for is not available or could not be loaded.</p> <a href="index.php" style="color: var(--secondary, #6c757d); text-decoration: none; font-weight: 600;">← Return to Home</a> </div>
        <?php endif; ?>
    </div> </main>

<?php include 'includes/footer.php'; ?>

<div id="detailsModal" class="modal">
 <div class="modal-content">
 <button class="modal-close-btn" id="closeDetailsModalBtn" aria-label="Close Details"> <i class="fas fa-times"></i> </button>
   <h2>Product Specifications & Details</h2>
   <?php if ($product): ?>
       <div class="modal-section"> <h4>Product Name</h4> <div class="modal-text-content"><?php echo htmlspecialchars($product['p_name']); ?></div> </div>
       <?php if (!empty($product['p_feature'])): ?> <div class="modal-section"> <h4>Features</h4> <div class="modal-text-content"><?php echo $product['p_feature']; ?></div> </div> <?php endif; ?>
       <?php if (!empty($product['p_condition'])): ?> <div class="modal-section"> <h4>Technical Specifications</h4> <div class="modal-text-content"><?php echo $product['p_condition']; ?></div> </div> <?php endif; ?>
       <?php if (!empty($product['p_return_policy'])): ?> <div class="modal-section"> <h4>Return Policy</h4> <div class="modal-text-content"><?php echo $product['p_return_policy']; ?></div> </div> <?php endif; ?>
       <div class="modal-section"> <h4>Availability & Base Info</h4> <p><strong>Base Stock Quantity:</strong> <?php echo (int)$product['p_qty']; ?></p> <?php if($has_variations): ?> <p><em>Note: Price and stock may vary based on selected options. See main page for specific variation details.</em></p> <?php endif; ?> <?php if($has_colors): ?> <p><em>Available colors can be selected on the main page.</em></p> <?php endif; ?> </div>
   <?php else: ?>
       <p>Details could not be loaded.</p>
   <?php endif; ?>
 </div>
</div>

<!-- Photo Gallery Modal -->
<div id="galleryModal" class="gallery-modal">
    <div class="gallery-modal-content">
        <div class="gallery-header">
            <h3>Product Gallery</h3>
            <button class="gallery-close" aria-label="Close Gallery">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="gallery-body">
            <div class="gallery-main">
                <button class="gallery-nav gallery-prev" aria-label="Previous Photo">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="gallery-image-container">
                    <img id="galleryMainImage" src="" alt="" class="gallery-main-image">
                    <video id="galleryMainVideo" class="gallery-main-video" controls preload="metadata" poster="images/video-placeholder.jpg" style="display: none; max-width: 100%; max-height: 100%;">
                        <source src="" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <div id="videoPlayOverlay" class="video-play-overlay" style="display: none;">
                        <button class="play-button"><i class="fas fa-play"></i></button>
                    </div>
                </div>
                <button class="gallery-nav gallery-next" aria-label="Next Photo">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="gallery-thumbnails">
                <?php
                $all_images = [];

                // Add featured image if exists
                if (!empty($product['p_featured_photo'])) {
                    $all_images[] = [
                        'src' => $upload_path_featured . htmlspecialchars($product['p_featured_photo']),
                        'alt' => 'Featured Image',
                        'type' => 'image'
                    ];
                }

                // Add variation images
                foreach ($variations_json as $var_data) {
                    if (!empty($var_data['image'])) {
                        $all_images[] = [
                            'src' => $var_data['image'],
                            'alt' => 'Variation: ' . htmlspecialchars($var_data['name']),
                            'type' => 'image'
                        ];
                    }
                }

                // Add gallery photos and videos
                foreach ($product_photos as $photo) {
                    if (!empty($photo['photo_name'])) {
                        $is_video = isset($photo['media_type']) && $photo['media_type'] === 'video';
                        $media_path = $upload_path_gallery . htmlspecialchars($photo['photo_name']);

                        if ($is_video) {
                            $all_images[] = [
                                'src' => $media_path,
                                'alt' => 'Gallery Video',
                                'type' => 'video',
                                'poster' => 'images/video-placeholder.jpg'
                            ];
                        } else {
                            $all_images[] = [
                                'src' => $media_path,
                                'alt' => 'Gallery Image',
                                'type' => 'image'
                            ];
                        }
                    }
                }

                // Output thumbnails
                foreach ($all_images as $index => $image) {
                    $is_video = isset($image['type']) && $image['type'] === 'video';
                    $thumbnail_class = 'gallery-thumbnail' . ($index === 0 ? ' active' : '');
                    if ($is_video) $thumbnail_class .= ' video-thumb';

                    $data_attrs = 'data-index="' . $index . '" data-type="' . $image['type'] . '"';

                    // Add the appropriate data attribute based on media type
                    if ($is_video) {
                        $data_attrs .= ' data-video="' . $image['src'] . '"';
                    } else {
                        $data_attrs .= ' data-image="' . $image['src'] . '"';
                    }

                    echo '<div class="' . $thumbnail_class . '" ' . $data_attrs . '>';

                    if ($is_video) {
                        $poster = isset($image['poster']) ? $image['poster'] : 'images/video-placeholder.jpg';
                        echo '<img src="' . $poster . '" alt="' . $image['alt'] . '" loading="lazy">';
                        echo '<div class="video-icon"><i class="fas fa-play-circle"></i></div>';
                    } else {
                        echo '<img src="' . $image['src'] . '" alt="' . $image['alt'] . '" loading="lazy">';
                    }

                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<style>
/* Gallery Modal Styles */
.gallery-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
}

.gallery-modal.show {
    display: flex;
}

.gallery-modal-content {
    width: 90%;
    max-width: 900px;
    height: 95vh;
    margin: auto;
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-bottom: 1px solid #eee;
}

.gallery-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: #333;
}

.gallery-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.gallery-close:hover {
    background: #f5f5f5;
}

.gallery-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.gallery-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: #fff;
    padding: 1rem;
}

.gallery-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-main-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: #333;
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.gallery-nav:hover {
    background: #fff;
}

.gallery-prev {
    left: 1rem;
}

.gallery-next {
    right: 1rem;
}

.gallery-thumbnails {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    background: #fff;
    border-top: 1px solid #eee;
}

.gallery-thumbnail {
    flex: 0 0 80px;
    height: 80px;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
}

.gallery-thumbnail.active {
    border-color: #007bff;
}

.gallery-thumbnail.video-thumb {
    position: relative;
}

.gallery-thumbnail.video-thumb .video-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
}

.gallery-thumbnail.video-thumb .video-icon i {
    color: white;
    font-size: 18px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.gallery-main-video {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.video-play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 10;
}

.play-button {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.7);
    border: 3px solid white;
    color: white;
    font-size: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    background-color: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

@media (max-width: 768px) {
    .gallery-modal-content {
        width: 100%;
        height: 100vh;
        border-radius: 0;
        margin: 0;
    }

    .gallery-nav {
        width: 35px;
        height: 35px;
        font-size: 1.25rem;
    }

    .gallery-thumbnail {
        flex: 0 0 60px;
        height: 60px;
    }

    .gallery-header {
        padding: 0.75rem;
    }

    .gallery-header h3 {
        font-size: 1.1rem;
    }

    .gallery-body {
        padding: 0.5rem;
    }

    .gallery-thumbnails {
        padding: 0.5rem;
        gap: 0.25rem;
    }
}
</style>

<!-- Professional Alert Container -->
<div id="alertContainer" class="alert-container"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update cart count immediately when DOM is loaded
    setTimeout(() => {
        updateCartCountDisplay();
    }, 100);

    // --- DOM Element References ---
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');
    const thumbnailContainer = document.querySelector('.thumbnail-container');
    const productPriceElement = document.getElementById('productPrice');
    const availabilityElement = document.getElementById('productAvailability');
    const skuElement = document.getElementById('productSku');
    const quantityInput = document.getElementById('quantity');
    const quantityDisplay = document.getElementById('quantity-display');
    const minusBtn = document.querySelector('.minus-btn');
    const plusBtn = document.querySelector('.plus-btn');
    const addToCartBtn = document.getElementById('addToCartBtn');
    const addToCartBtnIcon = addToCartBtn?.querySelector('.btn-icon');
    const addToCartBtnText = addToCartBtn?.querySelector('.btn-text');
    const totalPriceElement = document.getElementById('totalPrice');
    const variationNameSelectorContainer = document.getElementById('variationNameSelector'); // For Sizes/Options
    const colorSelectorContainer = document.getElementById('colorSelector'); // NEW: For Colors
    const baseDescriptionElement = document.getElementById('baseDescription');
    const variationDescriptionElement = document.getElementById('variationDescription');
    const variationSizeDisplay = document.getElementById('variationSizeDisplay');
    const variationSizeName = document.getElementById('variationSizeName');
    const selectedColorDisplay = document.getElementById('selectedColorDisplay'); // NEW: Display selected color name
    const selectedColorNameElement = document.getElementById('selectedColorName'); // NEW
    const selectedVariationIdInput = document.getElementById('selectedVariationId');
    const selectedColorIdInput = document.getElementById('selectedColorId'); // NEW
    const installationCheckbox = document.getElementById('installation');
    const cartCountElement = document.getElementById('cartCount');

    // Mobile Menu Elements
    const mobileMenuButton = document.getElementById('mobileMenuButton');
    const mobileMenu = document.getElementById('mobileMenu');
    const closeMobileMenu = document.getElementById('closeMobileMenu');
    const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

    // Back to Top Button
    const backToTopButton = document.getElementById('backToTop');

    // Read More Buttons
    const readMoreButtons = document.querySelectorAll('.read-more-btn');

    // --- Basic Check ---
    if (!document.querySelector('.product-detail-container')) { console.error("Product detail container not found."); return; }

    // --- Data from PHP ---
    let variationsData = {}; // Variations (e.g., Size) - NO color info here
    try {
        const variationsJsonString = '<?php echo json_encode($variations_json ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE); ?>';
        if (variationsJsonString) variationsData = JSON.parse(variationsJsonString) || {};
    } catch (e) { console.error("Error parsing variations JSON:", e); displayErrorMessage("Error loading product options."); }

    let productColorsData = {}; // NEW: All available colors for the product
    try {
        // Use JSON_FORCE_OBJECT to handle potential empty arrays correctly
        const colorsJsonString = '<?php echo json_encode($product_colors_json ?? [], JSON_FORCE_OBJECT | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE); ?>';
        if (colorsJsonString) productColorsData = JSON.parse(colorsJsonString) || {};
    } catch (e) { console.error("Error parsing colors JSON:", e); displayErrorMessage("Error loading color options."); }


    const baseProductId = <?php echo $product_id; ?>;
    const baseSku = <?php echo json_encode($initial_sku ?? 'N/A'); ?>;
    const baseProductFeaturedImage = <?php echo json_encode(!empty($product['p_featured_photo']) ? $upload_path_featured . htmlspecialchars($product['p_featured_photo']) : 'images/default_product.png'); ?>;
    const baseProductPrice = <?php echo isset($product['p_current_price']) && is_numeric($product['p_current_price']) ? (float)$product['p_current_price'] : 0; ?>;
    const baseProductQuantity = <?php echo isset($product['p_qty']) ? (int)$product['p_qty'] : 0; ?>;
    const baseProductDescHTML = <?php echo json_encode($base_product_description ?? ''); ?>;
    const installationFee = <?php echo is_numeric($installation_fee) ? (float)$installation_fee : 0; ?>;
    const hasVariations = Object.keys(variationsData).length > 0;
    const hasColors = Object.keys(productColorsData).length > 0;

    // Debug: Log the installation fee
    console.log("Installation fee:", installationFee);

    // --- Professional Alert System ---
    function showAlert(title, message, type = 'info', duration = 3000) {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;

        const alertId = 'alert-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert ${type}`;
        alert.id = alertId;

        // Get appropriate icon
        const getIcon = (alertType) => {
            switch(alertType) {
                case 'success': return '<i class="fas fa-check"></i>';
                case 'warning': return '<i class="fas fa-exclamation-triangle"></i>';
                case 'error': return '<i class="fas fa-times"></i>';
                default: return '<i class="fas fa-info"></i>';
            }
        };

        // Create alert content
        alert.innerHTML = `
            <div class="alert-content">
                <div class="alert-icon">${getIcon(type)}</div>
                <div class="alert-text">
                    <div class="alert-title">${title}</div>
                    <div class="alert-message">${message}</div>
                </div>
            </div>
            <button class="alert-close" onclick="closeAlert('${alertId}')">&times;</button>
            <div class="alert-progress"></div>
        `;

        // Add to container
        alertContainer.appendChild(alert);

        // Trigger show animation
        setTimeout(() => {
            alert.classList.add('show');
        }, 10);

        // Auto close after duration
        setTimeout(() => {
            closeAlert(alertId);
        }, duration);

        return alertId;
    }

    function closeAlert(alertId) {
        const alert = document.getElementById(alertId);
        if (!alert) return;

        alert.classList.remove('show');
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 400);
    }

    // Make closeAlert globally available
    window.closeAlert = closeAlert;

    // --- State Variables ---
    let currentSelectedVariation = variationsData[selectedVariationIdInput?.value] || null;
    let currentSelectedColorId = selectedColorIdInput?.value || null; // NEW

    // --- Helper Functions ---
    function displayErrorMessage(message) { /* ... same ... */ const infoDiv=document.querySelector('.product-info'); if(infoDiv){ const errorP=document.createElement('p'); errorP.style.color='red'; errorP.style.fontWeight='bold'; errorP.textContent=message; infoDiv.insertBefore(errorP, infoDiv.firstChild); }}
    function formatPrice(amount) { /* ... same ... */ const numericAmount=Number(amount); if(isNaN(numericAmount)){ console.warn("formatPrice non-numeric:", amount); return 'Tsh --'; } return 'Tsh ' + numericAmount.toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0}); }


    function updateDisplay() {
        let priceToShow = baseProductPrice;
        let quantityToShow = baseProductQuantity;
        let skuToShow = baseSku;
        let imageToShow = baseProductFeaturedImage;
        let descriptionToShow = baseProductDescHTML;
        let showVariationDescription = false;
        let variationDescriptionContent = '';
        let sizeNameToShow = null;

        // 1. Apply Variation Changes (if a variation is selected)
        if (currentSelectedVariation) {
            priceToShow = currentSelectedVariation.price;
            quantityToShow = currentSelectedVariation.quantity; // Variation quantity overrides base
            skuToShow = (currentSelectedVariation.sku !== 'N/A' && currentSelectedVariation.sku) ? currentSelectedVariation.sku : baseSku;
            if(currentSelectedVariation.image) imageToShow = currentSelectedVariation.image;
            if(currentSelectedVariation.description) {
                descriptionToShow = currentSelectedVariation.description;
                showVariationDescription = true;
                variationDescriptionContent = currentSelectedVariation.description;
            }
            sizeNameToShow = currentSelectedVariation.size_name; // Get size from variation
        }

        // 2. Update DOM Elements
        if (mainImage && mainImage.src !== imageToShow) {
            mainImage.src = imageToShow;
            thumbnails?.forEach(thumb => thumb.classList.toggle('active', thumb.dataset.image === imageToShow));
        }
        if (productPriceElement) productPriceElement.textContent = formatPrice(priceToShow);
        if (skuElement) skuElement.textContent = 'SKU: ' + skuToShow;

        const isInStock = quantityToShow > 0;
        if (availabilityElement) {
            availabilityElement.textContent = isInStock ? 'In Stock' : 'Out of Stock';
            availabilityElement.style.color = isInStock ? '#1cc88a' : '#e74a3b';
        }
        if (quantityInput) {
            quantityInput.max = quantityToShow > 0 ? quantityToShow : 1; // Max is current stock
            quantityInput.disabled = !isInStock;
            quantityInput.min = isInStock ? 1 : 0;
            let currentQty = parseInt(quantityInput.value);
            if (!isInStock || isNaN(currentQty) || currentQty < 1 || currentQty > quantityToShow) {
                 quantityInput.value = isInStock ? 1 : 0; // Reset to 1 if possible, else 0
            }
             if (!isInStock) quantityInput.value = 0;
             else if (parseInt(quantityInput.value) < 1) quantityInput.value = 1;
        }

        // Update descriptions
        if (variationDescriptionElement) {
             variationDescriptionElement.innerHTML = variationDescriptionContent;
             // Show container based on content presence
             document.getElementById('variationDescriptionContainer').style.display = showVariationDescription && variationDescriptionContent.trim() !== '' ? 'block' : 'none';
        }
        if (baseDescriptionElement) {
             // Optionally hide base description if variation description is shown and has content
             // baseDescriptionElement.style.display = (showVariationDescription && variationDescriptionContent.trim() !== '') ? 'none' : 'block';
             if(!(showVariationDescription && variationDescriptionContent.trim() !== '')) {
                 baseDescriptionElement.innerHTML = baseProductDescHTML; // Ensure base is shown if no variation desc
             }
        }


        // Update Size Display
        if (variationSizeDisplay && variationSizeName) {
            if (sizeNameToShow) {
                variationSizeName.textContent = sizeNameToShow;
                variationSizeDisplay.style.display = 'inline-flex';
            } else {
                variationSizeDisplay.style.display = 'none';
            }
        }

        // Update Selected Color Display
        if (selectedColorDisplay && selectedColorNameElement && currentSelectedColorId && productColorsData[currentSelectedColorId]) {
            selectedColorNameElement.textContent = productColorsData[currentSelectedColorId].name;
            selectedColorDisplay.style.display = 'block';
        } else if (selectedColorDisplay) {
            selectedColorDisplay.style.display = 'none';
        }

        // Always update button state and total price
        const alreadyInCart = checkItemInCart(baseProductId, currentSelectedVariation?.id, currentSelectedColorId);
        updateAddToCartButton(isInStock, alreadyInCart);
        calculateAndUpdateTotal();
    }


    function updateAddToCartButton(isInStock, isAlreadyInCart) {
        if (!addToCartBtn || !addToCartBtnText || !addToCartBtnIcon) return;
        let text = '', iconHtml = '', disabled = true;

        if (isAlreadyInCart) {
            text = 'Already in Cart'; iconHtml = '<i class="fas fa-check"></i>'; disabled = true;
        } else if (!isInStock) {
            text = 'Out of Stock'; iconHtml = '<i class="fas fa-times-circle"></i>'; disabled = true;
        } else if (hasColors && !currentSelectedColorId) {
             text = 'Select Color'; iconHtml = '<i class="fas fa-info-circle"></i>'; disabled = true;
        } else if (hasVariations && !currentSelectedVariation) {
             text = 'Select Option'; iconHtml = '<i class="fas fa-info-circle"></i>'; disabled = true;
        } else {
            text = 'Add to Cart'; iconHtml = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>'; disabled = false;
        }

        addToCartBtnText.textContent = text;
        addToCartBtnIcon.innerHTML = iconHtml;
        addToCartBtn.disabled = disabled;
    }

    function calculateAndUpdateTotal() {
        if (!totalPriceElement || !quantityInput) return;
        let quantity = parseInt(quantityInput.value);
        if (quantityInput.disabled || isNaN(quantity) || quantity < 0) quantity = 0;
        else if (quantity < 1 && !quantityInput.disabled) quantity = 1;

        let unitPrice = currentSelectedVariation ? currentSelectedVariation.price : baseProductPrice;
        let currentTotal = unitPrice * quantity;

        if (installationCheckbox && installationCheckbox.checked) {
            currentTotal += installationFee;
        }
        totalPriceElement.textContent = formatPrice(currentTotal);
    }

    // --- Local Storage Cart Functions (Updated Check) ---
    function getCartFromLocalStorage() { /* ... same ... */ try { const d = localStorage.getItem('cart'); return d ? JSON.parse(d) : []; } catch (e) { console.error("LS Cart Read Error:", e); return []; } }
    function saveCartToLocalStorage(cart) { /* ... same ... */ try { localStorage.setItem('cart', JSON.stringify(cart)); } catch (e) { console.error("LS Cart Save Error:", e); } }
    function updateCartCountDisplay() {
        const cart = getCartFromLocalStorage();
        const totalItems = cart.reduce((total, item) => total + (parseInt(item.quantity) || 0), 0);

        // Update desktop cart counter
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            cartCountElement.textContent = totalItems;
        }

        // Update mobile cart counter
        const mobileCartCount = document.getElementById('mobileCartCount');
        if (mobileCartCount) {
            mobileCartCount.textContent = totalItems;

            // Add animation effect when count changes
            mobileCartCount.classList.add('pulse-animation');
            setTimeout(() => {
                mobileCartCount.classList.remove('pulse-animation');
            }, 1000);
        }

        console.log("Cart count updated to:", totalItems);
    }

    // --- MODIFIED: Check includes color_id ---
    function checkItemInCart(productId, variationId = null, colorId = null) {
        let cart = getCartFromLocalStorage();
        const productIdStr = String(productId);
        const varIdToCheck = variationId ? parseInt(variationId) : null;
        const colorIdToCheck = colorId ? parseInt(colorId) : null;

        return cart.some(item => {
             const itemProductIdStr = String(item.product_id);
             const itemVariationId = item.variation_id ? parseInt(item.variation_id) : null;
             const itemColorId = item.color_id ? parseInt(item.color_id) : null; // Check color_id from stored item

             return itemProductIdStr === productIdStr &&
                    itemVariationId === varIdToCheck &&
                    itemColorId === colorIdToCheck; // Match all three
        });
    }


    // --- Quantity Control Functions ---
    function updateQuantityControls() {
        const max = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
        const currentQty = parseInt(quantityInput.value);

        // Update min/max buttons state
        minusBtn.disabled = currentQty <= 1;
        plusBtn.disabled = currentQty >= max;

        // Update display
        quantityDisplay.textContent = currentQty;
    }

    // Quantity button event listeners
    if (minusBtn) {
        minusBtn.addEventListener('click', function() {
            const currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
                // Trigger change event to update calculations
                quantityInput.dispatchEvent(new Event('change'));
                updateQuantityControls();
            }
        });
    }

    if (plusBtn) {
        plusBtn.addEventListener('click', function() {
            const currentValue = parseInt(quantityInput.value);
            const max = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
            if (currentValue < max) {
                quantityInput.value = currentValue + 1;
                // Trigger change event to update calculations
                quantityInput.dispatchEvent(new Event('change'));
                updateQuantityControls();
            }
        });
    }

    // --- Event Listener Setup ---

    // Variation Selection (e.g., Size)
    if (variationNameSelectorContainer) {
        variationNameSelectorContainer.addEventListener('change', function(event) {
            if (event.target.type === 'radio' && event.target.classList.contains('variation-name-radio')) {
                const selectedVarId = event.target.value;

                // Force reset quantity to 1 immediately when variation changes
                if (quantityInput) {
                    quantityInput.value = 1;
                    // Update the display to show 1
                    if (quantityDisplay) {
                        quantityDisplay.textContent = '1';
                    }

                    // Reset button states explicitly
                    if (minusBtn) {
                        minusBtn.disabled = true; // Disable minus button when quantity is 1
                    }
                    if (plusBtn) {
                        plusBtn.disabled = false; // Always enable plus button for new variation
                    }
                }

                currentSelectedVariation = variationsData[selectedVarId] || null;
                if(selectedVariationIdInput) selectedVariationIdInput.value = currentSelectedVariation ? currentSelectedVariation.id : '';

                // Update display first
                updateDisplay();

                // Force update quantity controls after display update to ensure correct state
                updateQuantityControls();

                // Force recalculate total price
                calculateAndUpdateTotal();
            }
        });
    }

     // NEW: Color Selection
     if (colorSelectorContainer) {
         colorSelectorContainer.addEventListener('change', function(event) {
             if (event.target.type === 'radio' && event.target.classList.contains('color-radio')) {

                 // Force reset quantity to 1 immediately when color changes
                 if (quantityInput) {
                     quantityInput.value = 1;
                     // Update the display to show 1
                     if (quantityDisplay) {
                         quantityDisplay.textContent = '1';
                     }

                     // Reset button states explicitly
                     if (minusBtn) {
                         minusBtn.disabled = true; // Disable minus button when quantity is 1
                     }
                     if (plusBtn) {
                         plusBtn.disabled = false; // Always enable plus button for new color
                     }
                 }

                 currentSelectedColorId = event.target.value;
                 if(selectedColorIdInput) selectedColorIdInput.value = currentSelectedColorId;

                 // Update display first
                 updateDisplay();

                 // Force update quantity controls after display update to ensure correct state
                 updateQuantityControls();

                 // Force recalculate total price
                 calculateAndUpdateTotal();
             }
         });
     }
    // Main video play button click handler
    const mainVideoPlayOverlay = document.getElementById('mainVideoPlayOverlay');
    if (mainVideoPlayOverlay) {
        mainVideoPlayOverlay.addEventListener('click', function(event) {
            // Stop event propagation to prevent the gallery from opening
            event.stopPropagation();

            const mainVideo = document.getElementById('mainVideo');
            if (mainVideo) {
                // Load and play the video
                mainVideo.load();

                // Hide the play overlay
                mainVideoPlayOverlay.style.display = 'none';

                // Play the video after it's loaded
                mainVideo.addEventListener('loadeddata', function onceLoaded() {
                    mainVideo.play().catch(e => console.log('Video play error:', e));
                    // Remove the event listener to prevent multiple calls
                    mainVideo.removeEventListener('loadeddata', onceLoaded);
                });

                // Start loading the video
                mainVideo.load();
            }
        });
    }

    // Add click handler to the video element itself to prevent gallery opening
    const mainVideo = document.getElementById('mainVideo');
    if (mainVideo) {
        mainVideo.addEventListener('click', function(event) {
            // Stop event propagation to prevent the gallery from opening
            event.stopPropagation();
        });
    }

    // Thumbnail Clicks
    if (thumbnailContainer) {
        thumbnailContainer.addEventListener('click', function(event) {
            const targetThumbnail = event.target.closest('.thumbnail') || event.target.closest('.video-thumbnail');
            if (!targetThumbnail) return;

            const newImageSrc = targetThumbnail.dataset.image;
            const videoSrc = targetThumbnail.dataset.video;
            const variationId = targetThumbnail.dataset.variationId; // Thumbnails linked to variations
            const mediaType = targetThumbnail.dataset.type || 'image';

            // Remove active class from all thumbnails
            const allThumbnails = document.querySelectorAll('.thumbnail, .video-thumbnail');
            allThumbnails.forEach(t => t.classList.remove('active'));

            // Add active class to clicked thumbnail
            targetThumbnail.classList.add('active');

            // Log which thumbnail was activated for debugging
            console.log(`Activated thumbnail: ${mediaType} - ${newImageSrc || videoSrc}`);

            // Handle media display based on type
            const mainMediaContainer = document.getElementById('mainMediaContainer');

            if (mediaType === 'video' && videoSrc) {
                // Show video, hide image
                if (mainImage) mainImage.style.display = 'none';
                const mainVideoContainer = document.getElementById('mainVideoContainer');
                const mainVideo = document.getElementById('mainVideo');
                const mainVideoPlayOverlay = document.getElementById('mainVideoPlayOverlay');

                if (mainVideoContainer && mainVideo) {
                    mainVideoContainer.style.display = 'block';

                    // Add video-active class to main container to disable gallery text
                    if (mainMediaContainer) {
                        mainMediaContainer.classList.add('video-active');
                    }

                    // Set video source and load a preview
                    const videoSource = mainVideo.querySelector('source');
                    if (videoSource) {
                        videoSource.src = videoSrc;
                    }

                    // Load the video metadata and show a 2-second preview
                    mainVideo.load();

                    // After metadata is loaded, play a short preview
                    mainVideo.addEventListener('loadedmetadata', function previewVideo() {
                        // Remove the event listener to prevent multiple calls
                        mainVideo.removeEventListener('loadedmetadata', previewVideo);

                        // Hide play overlay temporarily during preview
                        if (mainVideoPlayOverlay) {
                            mainVideoPlayOverlay.style.display = 'none';
                        }

                        // Play the first 2 seconds of the video
                        mainVideo.muted = true; // Mute the preview
                        mainVideo.currentTime = 0;
                        mainVideo.play().then(() => {
                            // After 2 seconds, pause the video and show the play button again
                            setTimeout(() => {
                                mainVideo.pause();
                                mainVideo.muted = false; // Unmute for when user plays

                                // Show the play overlay again
                                if (mainVideoPlayOverlay) {
                                    mainVideoPlayOverlay.style.display = 'flex';
                                }
                            }, 2000); // 2 seconds preview
                        }).catch(e => {
                            console.log('Video preview error:', e);
                            // Show the play overlay if preview fails
                            if (mainVideoPlayOverlay) {
                                mainVideoPlayOverlay.style.display = 'flex';
                            }
                        });
                    });
                }
            } else if (newImageSrc && mainImage) {
                // Show image, hide video
                mainImage.style.display = 'block';
                const mainVideoContainer = document.getElementById('mainVideoContainer');
                const mainVideo = document.getElementById('mainVideo');

                if (mainVideoContainer) {
                    mainVideoContainer.style.display = 'none';

                    // Remove video-active class when switching to image
                    if (mainMediaContainer) {
                        mainMediaContainer.classList.remove('video-active');
                    }
                }

                // Pause any playing video
                if (mainVideo && mainVideo.played.length > 0) {
                    mainVideo.pause();
                }

                // Update main image
                mainImage.src = newImageSrc;
            }

            // If thumbnail is linked to a variation, select that variation's radio button
            if (variationId && variationsData[variationId]) {
                const clickedVariation = variationsData[variationId];
                const correspondingRadio = variationNameSelectorContainer?.querySelector(`input[value="${variationId}"]`);
                if (correspondingRadio && !correspondingRadio.checked) {
                    correspondingRadio.checked = true;
                    // Update state and trigger full display update
                    currentSelectedVariation = clickedVariation;
                     if(selectedVariationIdInput) selectedVariationIdInput.value = currentSelectedVariation ? currentSelectedVariation.id : '';
                     updateDisplay();
                } else if (correspondingRadio?.checked && currentSelectedVariation?.id != variationId) {
                    // It's checked but state is somehow out of sync
                     currentSelectedVariation = clickedVariation;
                     if(selectedVariationIdInput) selectedVariationIdInput.value = currentSelectedVariation ? currentSelectedVariation.id : '';
                     updateDisplay();
                }
            }
            // Note: If the clicked thumbnail is NOT linked to a specific variation
            // (e.g., it's a gallery image or the base featured image),
            // we DON'T change the selected variation radio button.
            // The `updateDisplay()` function handles showing the correct image regardless.
        });
    }

    // Quantity & Installation Changes
    quantityInput?.addEventListener('input', calculateAndUpdateTotal); // Price calculation only
    quantityInput?.addEventListener('change', calculateAndUpdateTotal);
    installationCheckbox?.addEventListener('change', calculateAndUpdateTotal);


    // Add to Cart Button Click (Updated to send color_id and use selected color in local storage)
    addToCartBtn?.addEventListener('click', function() {
        const variationId = currentSelectedVariation?.id || null;
        // <<<< MODIFIED: Get color_id from the hidden input >>>>
        const colorId = document.getElementById('selectedColorId')?.value || null;
        const quantity = parseInt(quantityInput?.value);

        // --- Validation ---
        if (hasColors && !colorId) { // Check if color selection is required and missing
            showAlert('Color Required', 'Please select a color before adding to cart.', 'warning');
            return;
        }
        if (hasVariations && !variationId) { // Check if variation selection is required and missing
            showAlert('Option Required', 'Please select a product option (e.g., size) before adding to cart.', 'warning');
            return;
        }

        // Use correct quantity based on whether a variation is selected
        const stockToCheck = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;

        if (isNaN(quantity) || quantity < 1) { Swal.fire('Invalid Quantity', 'Please enter a quantity of at least 1.', 'warning'); return; }
        if (quantity > stockToCheck) { Swal.fire('Insufficient Stock', `Only ${stockToCheck} available. Please enter a lower quantity.`, 'warning'); return; }
        if (stockToCheck <= 0) { Swal.fire('Out of Stock', 'This item/option is currently out of stock.', 'error'); return; }
        // --- End Validation ---


        const formData = new FormData();
        formData.append('product_id', baseProductId);
        if (variationId) formData.append('variation_id', variationId);
        // <<<< MODIFIED: Send color_id >>>>
        if (colorId) formData.append('color_id', colorId);
        formData.append('quantity', quantity);
        formData.append('installation', (installationCheckbox && installationCheckbox.checked) ? '1' : '0');
        // formData.append('csrf_token', '...');

        // --- Button loading state ---
        addToCartBtn.disabled = true;
        if(addToCartBtnIcon && addToCartBtnText){ addToCartBtnIcon.innerHTML = '<span class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></span>'; addToCartBtnText.textContent = 'Adding...'; }

        const addToCartURL = 'add_to_cart.php'; // !!! CHECK PATH !!!
        console.log("Attempting add to cart:", addToCartURL, Object.fromEntries(formData));

        fetch(addToCartURL, { method: 'POST', body: formData })
        .then(response => { /* Error handling */ if(!response.ok){ return response.text().then(text => { throw new Error(`Server error (${response.status}): ${text.substring(0,200)}`) }); } return response.json(); })
        .then(data => {
            console.log("Parsed JSON response:", data);
            if (data && data.status === 'success' && data.added_item) {
                 const addedItem = data.added_item;
                 try {
                     let cart = getCartFromLocalStorage();
                     const addedProductIdStr = String(addedItem.product_id);
                     const addedVariationId = addedItem.variation_id ? parseInt(addedItem.variation_id) : null;
                     // <<<< MODIFIED: Get color_id from response >>>>
                     const addedColorId = addedItem.color_id ? parseInt(addedItem.color_id) : null;

                      // Find item by product, variation, AND color
                     let foundItemIndex = cart.findIndex(item =>
                         String(item.product_id) === addedProductIdStr &&
                         (item.variation_id ? parseInt(item.variation_id) : null) === addedVariationId &&
                         // <<<< MODIFIED: Match color too >>>>
                         (item.color_id ? parseInt(item.color_id) : null) === addedColorId
                     );

                     if (foundItemIndex > -1) {
                         // Update existing item
                         cart[foundItemIndex].quantity = addedItem.quantity; // Server returns NEW total quantity
                         cart[foundItemIndex].installation = addedItem.installation;
                         // Ensure color_id is also present if updating
                         if (addedColorId) cart[foundItemIndex].color_id = addedColorId;
                         console.log("LocalStorage item updated:", cart[foundItemIndex]);
                     } else {
                          // Add new item - use RECEIVED color info for storage
                         const newItemForLocalStorage = {
                             product_id: addedItem.product_id,
                             variation_id: addedVariationId,
                             color_id: addedColorId, // <<< STORE the color ID from response
                             quantity: addedItem.quantity,
                             price: addedItem.price ?? (currentSelectedVariation ? currentSelectedVariation.price : baseProductPrice),
                             name: addedItem.name ?? document.querySelector('.product-title')?.textContent ?? 'Product',
                             variation_name: addedItem.variation_name || (currentSelectedVariation ? currentSelectedVariation.name : null),
                             photo: addedItem.photo ?? mainImage?.src ?? 'images/default_product.png',
                             // <<<< MODIFIED: Use color_name from response/item data >>>>
                             color_name: addedItem.color_name,
                             size_name: addedItem.size_name || (currentSelectedVariation ? currentSelectedVariation.size_name : null),
                             installation: addedItem.installation ?? false
                         };
                         cart.push(newItemForLocalStorage);
                         console.log("LocalStorage item added:", newItemForLocalStorage);
                     }
                     saveCartToLocalStorage(cart);
                     updateCartCountDisplay();
                 } catch (e) {
                     console.error("Error updating localStorage:", e);
                     showAlert('Cart Sync Issue', 'Item added, but local cart display might be out of sync.', 'warning');
                 }

                 showAlert('Successfully Added!', data.message || 'Product has been added to your cart.', 'success');
                 updateAddToCartButton(true, true); // Update button to "Already in Cart"
                 // Reset quantity after add to cart success
                 quantityInput.value = 1;
                 if (quantityDisplay) quantityDisplay.textContent = '1';
                 updateQuantityControls();
                 calculateAndUpdateTotal();

            } else { /* Handle JSON error response */
                  Swal.fire('Error', data.message || 'Could not add item (unexpected response).', 'error');
                  const stockNow = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
                  updateAddToCartButton(stockNow > 0, false); // Reset button state
            }
        })
        .catch(error => { /* Handle fetch error */
            console.error('Add to Cart Fetch Error:', error);
            showAlert('Request Failed', `Could not add item to cart. ${error.message}`, 'error');
            // Reset button based on current selections and stock
             const stockNow = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
             const currentlyInCart = checkItemInCart(baseProductId, currentSelectedVariation?.id, currentSelectedColorId);
             updateAddToCartButton(stockNow > 0, currentlyInCart);
        });
    });


    // Read More Button Functionality with Animation
    if (readMoreButtons.length > 0) {
        readMoreButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const container = document.getElementById(targetId);

                if (container) {
                    const preview = container.querySelector('.description-preview');
                    const fullContent = container.querySelector('.description-full');

                    if (preview && fullContent) {
                        if (preview.style.display !== 'none') {
                            // Expanding content
                            preview.classList.add('hiding');

                            // Use setTimeout to wait for animation to complete
                            setTimeout(() => {
                                preview.style.display = 'none';
                                preview.classList.remove('hiding');
                                fullContent.style.display = 'block';
                            }, 300);

                            // Add icon and change text
                            this.innerHTML = '<i class="fas fa-chevron-up" style="margin-right: 5px;"></i> Read Less';
                        } else {
                            // Collapsing content
                            fullContent.style.animation = 'fadeOut 0.3s ease forwards';

                            // Use setTimeout to wait for animation to complete
                            setTimeout(() => {
                                fullContent.style.display = 'none';
                                fullContent.style.animation = 'fadeIn 0.5s ease forwards';
                                preview.style.display = 'block';
                            }, 300);

                            // Add icon and change text
                            this.innerHTML = '<i class="fas fa-chevron-down" style="margin-right: 5px;"></i> Read More';
                        }
                    }
                }
            });
        });
    }

    // Mobile Menu Toggle
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.remove('translate-x-full');
            mobileMenuBackdrop.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        });
    }

    if (closeMobileMenu) {
        closeMobileMenu.addEventListener('click', function() {
            mobileMenu.classList.add('translate-x-full');
            mobileMenuBackdrop.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        });
    }

    if (mobileMenuBackdrop) {
        mobileMenuBackdrop.addEventListener('click', function() {
            mobileMenu.classList.add('translate-x-full');
            mobileMenuBackdrop.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        });
    }

    // Back to Top Button
    if (backToTopButton) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('scale-0');
                backToTopButton.classList.add('scale-100');
            } else {
                backToTopButton.classList.remove('scale-100');
                backToTopButton.classList.add('scale-0');
            }
        });

        backToTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Auto-hide header when scrolling down
    const mainHeader = document.getElementById('mainHeader');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down & past the threshold
            mainHeader.classList.add('header-hidden');
        } else {
            // Scrolling up or at the top
            mainHeader.classList.remove('header-hidden');
        }

        lastScrollTop = scrollTop;
    });

    // Search Functionality
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    const mobileSearchInput = document.getElementById('mobileSearchInput');
    const mobileSearchSuggestions = document.getElementById('mobileSearchSuggestions');
    let searchTimeout;

    // Function to highlight matching text
    function highlightText(text, searchTerm) {
        if (!searchTerm) return text;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }

    // Function to fetch and display suggestions
    async function fetchSuggestions(searchTerm, inputElement, suggestionsElement) {
        if (searchTerm.length < 2) {
            suggestionsElement.classList.add('hidden');
            return;
        }

        try {
            const response = await fetch(`search_suggestions.php?q=${encodeURIComponent(searchTerm)}`);
            const data = await response.json();

            if (data.length > 0) {
                suggestionsElement.innerHTML = data.map(item => `
                    <div class="suggestion-item" data-type="${item.type}" data-id="${item.id}">
                        <div class="icon">${item.type === 'product' ? '🛍️' : '📁'}</div>
                        <div class="name">${highlightText(item.name, searchTerm)}</div>
                        <div class="type">${item.type}</div>
                    </div>
                `).join('');

                suggestionsElement.classList.remove('hidden');
            } else {
                suggestionsElement.innerHTML = '<div class="suggestion-item">No results found</div>';
                suggestionsElement.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
        }
    }

    // Setup search for desktop
    if (searchInput && searchSuggestions) {
        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                fetchSuggestions(e.target.value, searchInput, searchSuggestions);
            }, 300);
        });

        searchInput.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                fetchSuggestions(this.value, searchInput, searchSuggestions);
            }
        });

        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
                searchSuggestions.classList.add('hidden');
            }
        });

        searchSuggestions.addEventListener('click', function(e) {
            const suggestionItem = e.target.closest('.suggestion-item');
            if (suggestionItem) {
                const type = suggestionItem.dataset.type;
                const id = suggestionItem.dataset.id;

                if (type === 'product') {
                    window.location.href = `product_detail.php?id=${id}`;
                } else if (type === 'category') {
                    window.location.href = `category.php?id=${id}`;
                }
            }
        });
    }

    // Setup search for mobile
    if (mobileSearchInput && mobileSearchSuggestions) {
        mobileSearchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                fetchSuggestions(e.target.value, mobileSearchInput, mobileSearchSuggestions);
            }, 300);
        });

        mobileSearchInput.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                fetchSuggestions(this.value, mobileSearchInput, mobileSearchSuggestions);
            }
        });

        document.addEventListener('click', function(e) {
            if (!mobileSearchInput.contains(e.target) && !mobileSearchSuggestions.contains(e.target)) {
                mobileSearchSuggestions.classList.add('hidden');
            }
        });

        mobileSearchSuggestions.addEventListener('click', function(e) {
            const suggestionItem = e.target.closest('.suggestion-item');
            if (suggestionItem) {
                const type = suggestionItem.dataset.type;
                const id = suggestionItem.dataset.id;

                if (type === 'product') {
                    window.location.href = `product_detail.php?id=${id}`;
                } else if (type === 'category') {
                    window.location.href = `category.php?id=${id}`;
                }
            }
        });
    }

    // --- Modal Logic ---
    const detailsModal = document.getElementById('detailsModal');
    const openModalBtn = document.getElementById('openDetailsModalBtn');
    const closeModalBtn = document.getElementById('closeDetailsModalBtn');
    function openModal() { if(detailsModal){ detailsModal.classList.add('show'); document.body.classList.add('modal-open'); } }
    function closeModal() { if(detailsModal){ detailsModal.classList.remove('show'); document.body.classList.remove('modal-open'); } }
    if(openModalBtn && detailsModal) openModalBtn.addEventListener('click', openModal);
    if(closeModalBtn && detailsModal) closeModalBtn.addEventListener('click', closeModal);
    window.addEventListener('click', (e) => { if(detailsModal && e.target == detailsModal) closeModal(); });
    window.addEventListener('keydown', (e) => { if(detailsModal && detailsModal.classList.contains('show') && e.key === 'Escape') closeModal(); });


    // --- Initial Page Setup ---
    function initializePage() {
        console.log("Initializing page (with color selection)...");

        // Initialize cart if not exists in localStorage
        if (!localStorage.getItem('cart')) {
            localStorage.setItem('cart', JSON.stringify([]));
        }

        updateDisplay(); // Update display based on initial variation & color
        updateCartCountDisplay(); // Update header count
        updateQuantityControls(); // Initialize quantity controls

        // Force update cart count after a short delay to ensure DOM is fully loaded
        setTimeout(() => {
            updateCartCountDisplay();
        }, 500);

        console.log("Page initialization complete.");
    }

    // Call initialization when DOM is loaded
    initializePage();

    // Gallery Modal Functionality
    const galleryModal = document.getElementById('galleryModal');
    const galleryMainImage = document.getElementById('galleryMainImage');
    const galleryMainVideo = document.getElementById('galleryMainVideo');
    const galleryThumbnails = document.querySelectorAll('.gallery-thumbnail');
    const galleryClose = document.querySelector('.gallery-close');
    const galleryPrev = document.querySelector('.gallery-prev');
    const galleryNext = document.querySelector('.gallery-next');

    let currentImageIndex = 0;
    let autoslideInterval = null;
    let currentMediaType = 'image';
    const AUTOSLIDE_DELAY = 3000; // 3 seconds

    // Function to start autoslide
    function startAutoslide() {
        if (autoslideInterval) clearInterval(autoslideInterval);
        autoslideInterval = setInterval(showNextImage, AUTOSLIDE_DELAY);
    }

    // Function to stop autoslide
    function stopAutoslide() {
        if (autoslideInterval) {
            clearInterval(autoslideInterval);
            autoslideInterval = null;
        }
    }

    // Open gallery modal
    document.getElementById('mainMediaContainer').addEventListener('click', function() {
        // Check if a video is currently active and visible
        const mainVideo = document.getElementById('mainVideo');
        const mainVideoContainer = document.getElementById('mainVideoContainer');

        // If video is active and visible, don't open the gallery modal
        if (mainVideoContainer && mainVideoContainer.style.display === 'block') {
            // Don't open gallery when video is active
            return;
        }

        // Pause any playing video in the main view
        if (mainVideo && !mainVideo.paused) {
            mainVideo.pause();
        }

        galleryModal.classList.add('show');
        document.body.classList.add('modal-open');

        // Find the currently active thumbnail in the product view
        const activeThumbnail = document.querySelector('.thumbnail.active, .video-thumbnail.active');
        let activeGalleryIndex = 0;

        if (activeThumbnail) {
            // Get the source of the active thumbnail
            const activeSource = activeThumbnail.dataset.image || activeThumbnail.dataset.video;
            const activeType = activeThumbnail.dataset.type || 'image';

            // Find the matching thumbnail in the gallery
            galleryThumbnails.forEach((thumb, idx) => {
                const thumbSource = thumb.dataset.image || thumb.dataset.video;
                if (thumbSource === activeSource) {
                    activeGalleryIndex = idx;
                }
            });
        }

        // Show the active image/video in the gallery
        showImage(activeGalleryIndex);

        // Only start autoslide for images, not for videos
        if (currentMediaType === 'image') {
            startAutoslide();
        }
    });

    // Close gallery modal
    galleryClose.addEventListener('click', function() {
        closeGallery();
        stopAutoslide(); // Stop autoslide when modal closes
    });

    galleryModal.addEventListener('click', function(e) {
        if (e.target === galleryModal) {
            closeGallery();
            stopAutoslide(); // Stop autoslide when modal closes
        }
    });

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && galleryModal.classList.contains('show')) {
            closeGallery();
            stopAutoslide(); // Stop autoslide when modal closes
        }
    });

    // Navigation
    galleryPrev.addEventListener('click', function() {
        showPreviousImage();
        resetAutoslide(); // Reset autoslide timer when manually navigating
    });

    galleryNext.addEventListener('click', function() {
        showNextImage();
        resetAutoslide(); // Reset autoslide timer when manually navigating
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (!galleryModal.classList.contains('show')) return;

        if (e.key === 'ArrowLeft') {
            showPreviousImage();
            resetAutoslide(); // Reset autoslide timer when manually navigating
        } else if (e.key === 'ArrowRight') {
            showNextImage();
            resetAutoslide(); // Reset autoslide timer when manually navigating
        }
    });

    // Thumbnail click
    galleryThumbnails.forEach((thumb, index) => {
        thumb.addEventListener('click', () => {
            showImage(index);
            resetAutoslide(); // Reset autoslide timer when manually selecting image
        });
    });

    function showImage(index) {
        if (index < 0) index = galleryThumbnails.length - 1;
        if (index >= galleryThumbnails.length) index = 0;

        currentImageIndex = index;
        const thumb = galleryThumbnails[index];
        const mediaType = thumb.getAttribute('data-type') || 'image';
        currentMediaType = mediaType;

        // Pause any playing video when switching
        if (galleryMainVideo) {
            galleryMainVideo.pause();
        }

        // Get the play overlay element
        const videoPlayOverlay = document.getElementById('videoPlayOverlay');

        if (mediaType === 'video') {
            // Handle video display
            const videoSrc = thumb.getAttribute('data-video');

            if (videoSrc) {
                // Set video source and load a preview
                const videoSource = galleryMainVideo.querySelector('source');
                if (videoSource) {
                    videoSource.src = videoSrc;
                }

                // Show video container, hide image
                galleryMainVideo.style.display = 'block';
                galleryMainImage.style.display = 'none';

                // Hide play overlay during preview
                if (videoPlayOverlay) {
                    videoPlayOverlay.style.display = 'none';
                }

                // Disable autoslide when showing video
                stopAutoslide();

                // Load the video metadata and show a 2-second preview
                galleryMainVideo.load();

                // After metadata is loaded, play a short preview
                galleryMainVideo.addEventListener('loadedmetadata', function previewVideo() {
                    // Remove the event listener to prevent multiple calls
                    galleryMainVideo.removeEventListener('loadedmetadata', previewVideo);

                    // Play the first 2 seconds of the video
                    galleryMainVideo.muted = true; // Mute the preview
                    galleryMainVideo.currentTime = 0;
                    galleryMainVideo.play().then(() => {
                        // After 2 seconds, pause the video and show the play button again
                        setTimeout(() => {
                            galleryMainVideo.pause();
                            galleryMainVideo.muted = false; // Unmute for when user plays

                            // Show the play overlay again
                            if (videoPlayOverlay) {
                                videoPlayOverlay.style.display = 'flex';
                            }
                        }, 2000); // 2 seconds preview
                    }).catch(e => {
                        console.log('Gallery video preview error:', e);
                        // Show the play overlay if preview fails
                        if (videoPlayOverlay) {
                            videoPlayOverlay.style.display = 'flex';
                        }
                    });
                });
            }
        } else {
            // Handle image display
            const img = thumb.querySelector('img');
            const imgSrc = thumb.getAttribute('data-image') || (img ? img.src : '');

            if (imgSrc) {
                galleryMainImage.src = imgSrc;
                galleryMainImage.alt = img ? img.alt : 'Product Image';

                // Show image, hide video and play overlay
                galleryMainImage.style.display = 'block';
                galleryMainVideo.style.display = 'none';

                if (videoPlayOverlay) {
                    videoPlayOverlay.style.display = 'none';
                }

                // Restart autoslide for images
                resetAutoslide();
            }
        }

        // Update all thumbnails to show the current active one
        galleryThumbnails.forEach(t => t.classList.remove('active'));
        thumb.classList.add('active');

        // Log the current media for debugging
        console.log(`Showing gallery item ${index}: ${mediaType}`);
    }

    function showPreviousImage() {
        showImage(currentImageIndex - 1);
    }

    function showNextImage() {
        showImage(currentImageIndex + 1);
    }

    function closeGallery() {
        galleryModal.classList.remove('show');
        document.body.classList.remove('modal-open');
        stopAutoslide(); // Stop autoslide when modal closes

        // Pause any playing video
        if (galleryMainVideo) {
            galleryMainVideo.pause();
        }
    }

    // Function to reset autoslide timer
    function resetAutoslide() {
        stopAutoslide();
        startAutoslide();
    }

    // Video play button click handler
    const videoPlayOverlay = document.getElementById('videoPlayOverlay');
    if (videoPlayOverlay) {
        videoPlayOverlay.addEventListener('click', function() {
            if (galleryMainVideo) {
                // Always stop autoslide when playing a video
                stopAutoslide();

                // Load and play the video
                galleryMainVideo.load();

                // Hide the play overlay
                videoPlayOverlay.style.display = 'none';

                // Play the video after it's loaded
                galleryMainVideo.addEventListener('loadeddata', function onceLoaded() {
                    galleryMainVideo.play().catch(e => console.log('Video play error:', e));
                    // Remove the event listener to prevent multiple calls
                    galleryMainVideo.removeEventListener('loadeddata', onceLoaded);
                });

                // Start loading the video
                galleryMainVideo.load();
            }
        });
    }

    // Add event listener for when video ends to restart autoslide
    if (galleryMainVideo) {
        galleryMainVideo.addEventListener('ended', function() {
            // Only restart autoslide if we're still in the gallery modal
            if (galleryModal.classList.contains('show')) {
                resetAutoslide();
            }
        });
    }
}); // End DOMContentLoaded
</script>

</body>
</html>
<?php
ob_end_flush();
?>