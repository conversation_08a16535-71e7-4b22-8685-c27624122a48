<?php
session_start();
include("../admin/inc/config.php");

// Set JSON header
header('Content-Type: application/json');

// Buffer output to catch any errors
ob_start();

try {
    // Get and validate inputs
    $country_id = $_POST['country_id'] ?? '';
    $shipping_fee = $_POST['shipping_fee'] ?? 0;

    // Log the inputs for debugging
    error_log("Updating shipping: Country ID: $country_id, Fee: $shipping_fee");

    // Get country name from database
    if (!empty($country_id)) {
        $stmt = $pdo->prepare("SELECT country_name FROM tbl_country WHERE country_id = ?");
        $stmt->execute([$country_id]);
        $country = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($country) {
            // Store in session
            $_SESSION['shipping_country_id'] = $country_id;
            $_SESSION['shipping_country'] = $country['country_name'];
            $_SESSION['shipping_fee'] = floatval($shipping_fee);

            // Also set a cookie for better persistence
            setcookie('selectedCountryId', $country_id, time() + 86400, '/'); // 24 hours
            setcookie('shippingFee', $shipping_fee, time() + 86400, '/'); // 24 hours
            setcookie('shippingCountry', $country['country_name'], time() + 86400, '/'); // 24 hours

            // Log the values for debugging
            error_log("Shipping values stored in session and cookies:");
            error_log("  Country ID: $country_id");
            error_log("  Country Name: {$country['country_name']}");
            error_log("  Shipping Fee: $shipping_fee");

            error_log("Shipping updated in session: Country: {$country['country_name']}, ID: $country_id, Fee: $shipping_fee");

            echo json_encode([
                'status' => 'success',
                'message' => 'Shipping information updated',
                'country_name' => $country['country_name'],
                'country_id' => $country_id,
                'shipping_fee' => $shipping_fee
            ]);
        } else {
            error_log("Country ID not found in database: $country_id");
            echo json_encode([
                'status' => 'error',
                'message' => 'Country not found'
            ]);
        }
    } else {
        // Clear shipping information
        $_SESSION['shipping_country_id'] = '';
        $_SESSION['shipping_country'] = '';
        $_SESSION['shipping_fee'] = 0;

        error_log("Shipping information cleared from session");

        echo json_encode([
            'status' => 'success',
            'message' => 'Shipping information cleared'
        ]);
    }
} catch (Exception $e) {
    error_log("Error in update_shipping.php: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => 'Server error'
    ]);
}

// Clean output buffer
ob_end_flush();