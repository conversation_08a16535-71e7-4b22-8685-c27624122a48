<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Fetch settings for footer
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© 2025 SMART LIFE. All rights reserved.";

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Forgot Password | SMART LIFE</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    /* Theme Variables */
    :root {
      --bg-primary: #f8f9fa;
      --bg-secondary: #ffffff;
      --bg-tertiary: #f3f4f6;
      --text-primary: #1f2937;
      --text-secondary: #6b7280;
      --text-tertiary: #9ca3af;
      --border-color: #e5e7eb;
      --shadow-color: rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] {
      --bg-primary: #111827;
      --bg-secondary: #1f2937;
      --bg-tertiary: #374151;
      --text-primary: #f9fafb;
      --text-secondary: #d1d5db;
      --text-tertiary: #9ca3af;
      --border-color: #374151;
      --shadow-color: rgba(0, 0, 0, 0.3);
    }

    body {
      background-color: var(--bg-primary);
      color: var(--text-primary);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Theme toggle button */
    .theme-toggle {
      position: relative;
      width: 60px;
      height: 30px;
      background: var(--bg-tertiary);
      border-radius: 15px;
      border: 1px solid var(--border-color);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .theme-toggle::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 24px;
      height: 24px;
      background: var(--bg-secondary);
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px var(--shadow-color);
    }

    [data-theme="dark"] .theme-toggle::before {
      transform: translateX(28px);
    }

    .theme-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .theme-icon.sun {
      left: 6px;
      color: #fbbf24;
    }

    .theme-icon.moon {
      right: 6px;
      color: #60a5fa;
    }

    [data-theme="dark"] .theme-icon.sun {
      opacity: 0.3;
    }

    [data-theme="light"] .theme-icon.moon {
      opacity: 0.3;
    }

    /* Auth Container Styling */
    .auth-container {
      background: var(--bg-secondary);
      border: 1px solid var(--border-color);
      box-shadow: 0 10px 25px var(--shadow-color);
      border-radius: 16px;
      padding: 2.5rem;
      max-width: 450px;
      margin: 0 auto;
      position: relative;
      backdrop-filter: blur(10px);
    }

    .auth-container h2 {
      color: var(--text-primary);
      font-size: 2rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #4f46e5, #06b6d4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .auth-container .subtitle {
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      font-size: 0.875rem;
      line-height: 1.5;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    .form-control {
      width: 100%;
      padding: 0.875rem 1rem;
      border: 2px solid var(--border-color);
      border-radius: 12px;
      font-size: 1rem;
      background: var(--bg-primary);
      color: var(--text-primary);
      transition: all 0.3s ease;
      box-sizing: border-box;
    }

    .form-control:focus {
      outline: none;
      border-color: #4f46e5;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
      background: var(--bg-secondary);
    }

    .btn {
      width: 100%;
      padding: 0.875rem 1.5rem;
      background: linear-gradient(135deg, #4f46e5, #06b6d4);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 1rem;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
    }

    .auth-links {
      text-align: center;
      margin-top: 1.5rem;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    .auth-links a {
      color: #4f46e5;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .auth-links a:hover {
      color: #06b6d4;
      text-decoration: underline;
    }

    /* Error/Success Messages */
    .error-message, .success-message {
      padding: 1rem;
      margin-bottom: 1.5rem;
      border-radius: 12px;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .error-message {
      background: linear-gradient(135deg, #fef2f2, #fee2e2);
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    .success-message {
      background: linear-gradient(135deg, #f0fdf4, #dcfce7);
      color: #16a34a;
      border: 1px solid #bbf7d0;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
      .auth-container {
        margin: 1rem;
        padding: 2rem;
      }
    }
  </style>
</head>
<body>
  <!-- Theme initialization script (must be before any content) -->
  <script>
    // Initialize theme before page renders
    (function() {
      const savedTheme = localStorage.getItem('theme');
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      const theme = savedTheme || systemTheme;

      document.documentElement.setAttribute('data-theme', theme);

      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        }
      });
    })();

    // Theme Functions
    function toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    }
  </script>

  <!-- Header (from all_products.php) -->
  <header class="fixed inset-x-0 top-0 z-50" style="background: var(--bg-secondary); box-shadow: 0 1px 3px var(--shadow-color);">
    <div class="container mx-auto px-4 flex items-center justify-between py-4">
      <a href="index.php" class="text-2xl font-bold" style="color: var(--text-primary);">
        SMART LIFE<span class="text-blue-600">.</span>
      </a>
      <nav class="hidden md:flex items-center space-x-6">
        <a href="index.php#home" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Home</a>
        <a href="index.php#about" class="hover:text-blue-600 transition" style="color: var(--text-primary);">About</a>
        <a href="index.php#products" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Products</a>
        <a href="index.php#gallery" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Best Deals</a>
        <a href="index.php#contact" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Contact</a>
        <!-- Theme Toggle -->
        <div class="theme-toggle" onclick="toggleTheme()" title="Toggle dark/light mode">
          <i class="fas fa-sun theme-icon sun"></i>
          <i class="fas fa-moon theme-icon moon"></i>
        </div>
        <!-- Cart -->
        <a href="cart.php" class="relative text-xl hover:text-blue-600 transition" style="color: var(--text-primary);">
          🛒
          <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
        </a>
      </nav>
      <!-- Mobile Menu Button -->
      <button id="mobileMenuButton" class="md:hidden flex items-center">
        <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
        </svg>
      </button>
    </div>
  </header>

  <main class="pt-24 pb-12 min-h-screen flex items-center justify-center">
    <div class="container mx-auto px-4">
      <div class="auth-container">
        <h2>Reset Password</h2>
        <p class="subtitle">
          Enter your email address and we'll send you a verification code to reset your password.
        </p>
        <?php
        // Display messages
        if (isset($_SESSION['error_message'])) {
            echo '<div class="error-message">' . $_SESSION['error_message'] . '</div>';
            unset($_SESSION['error_message']);
        }
        if (isset($_SESSION['success_message'])) {
            echo '<div class="success-message">' . $_SESSION['success_message'] . '</div>';
            unset($_SESSION['success_message']);
        }
        ?>
        <form action="forgot_password_process.php" method="POST">
          <div class="form-group">
            <label for="email">Email Address</label>
            <input type="email" id="email" name="email" required class="form-control" placeholder="Enter your registered email">
          </div>
          <button type="submit" class="btn">Send Reset Code</button>
        </form>

        <div class="auth-links">
          Remember your password? <a href="login.php">Back to Login</a>
        </div>
      </div>
    </div>
  </main>

  <script>
    // Update cart count
    document.addEventListener('DOMContentLoaded', function() {
      let cartCount = <?php
        $count = 0;
        if(isset($_SESSION['cart'])) {
          foreach($_SESSION['cart'] as $item) {
            $count += $item['quantity'];
          }
        }
        echo $count;
      ?>;

      const cartCountElements = document.querySelectorAll('.cart-count');
      cartCountElements.forEach(elem => {
        if(elem) {
          elem.textContent = cartCount;
          elem.style.display = cartCount > 0 ? 'inline-block' : 'none';
        }
      });
    });
  </script>
</body>
</html>
