

<?php
$error_message = '';
$success_message = '';

// Process form submission
if(isset($_POST['form1'])) {
    $valid = 1;
    
    // Validate caption input.
    if(empty($_POST['caption'])) {
        $valid = 0;
        $error_message .= "Photo Caption cannot be empty<br>";
    }
    
    // Validate that a photo color is selected.
    if(empty($_POST['color_id'])) {
        $valid = 0;
        $error_message .= "You must select a color for the photo<br>";
    }
    
    // Get file info.
    $path = $_FILES['photo']['name'];
    $path_tmp = $_FILES['photo']['tmp_name'];
    
    if($path != '') {
        $ext = pathinfo($path, PATHINFO_EXTENSION);
        if($ext != 'jpg' && $ext != 'png' && $ext != 'jpeg' && $ext != 'gif') {
            $valid = 0;
            $error_message .= 'You must upload a jpg, jpeg, gif or png file<br>';
        }
    }
       
    if($valid == 1) {
    
        if($path == '') {
            // No new photo is uploaded: update caption and color only.
            $statement = $pdo->prepare("UPDATE tbl_photo SET caption=?, color_id=? WHERE id=?");
            $statement->execute(array($_POST['caption'], $_POST['color_id'], $_REQUEST['id']));
        } else {
            // New photo uploaded: remove the existing one, then upload new file and update fields.
            unlink('../assets/uploads/' . $_POST['previous_photo']);
            $final_name = 'photo-' . $_REQUEST['id'] . '.' . $ext;
            move_uploaded_file($path_tmp, '../assets/uploads/' . $final_name);
            
            $statement = $pdo->prepare("UPDATE tbl_photo SET caption=?, photo=?, color_id=? WHERE id=?");
            $statement->execute(array($_POST['caption'], $final_name, $_POST['color_id'], $_REQUEST['id']));
        }
        
        $success_message = 'Photo is updated successfully.';
    }
}

// Check the id is present and valid.
if(!isset($_REQUEST['id'])) {
    header('location: logout.php');
    exit;
} else {
    // Retrieve photo record.
    $statement = $pdo->prepare("SELECT * FROM tbl_photo WHERE id=?");
    $statement->execute(array($_REQUEST['id']));
    $total = $statement->rowCount();
    $result = $statement->fetchAll(PDO::FETCH_ASSOC);
    if($total == 0) {
        header('location: logout.php');
        exit;
    }
}

// Retrieve current record's details.
foreach ($result as $row) {
    $caption = $row['caption'];
    $photo   = $row['photo'];
    $current_color_id = isset($row['color_id']) ? $row['color_id'] : '';
}
?>

<!-- Header Section -->
<section class="content-header">
    <div class="content-header-left">
        <h1>Edit Photo</h1>
    </div>
    <div class="content-header-right">
        <a href="photo.php" class="btn btn-primary btn-sm">View All</a>
    </div>
</section>

<!-- Content Section -->
<section class="content">
    <div class="row">
        <div class="col-md-12">

            <?php if($error_message): ?>
            <div class="callout callout-danger">
                <p><?php echo $error_message; ?></p>
            </div>
            <?php endif; ?>

            <?php if($success_message): ?>
            <div class="callout callout-success">
                <p><?php echo $success_message; ?></p>
            </div>
            <?php endif; ?>

            <form class="form-horizontal" action="" method="post" enctype="multipart/form-data">
                <div class="box box-info">
                    <div class="box-body">
                        <!-- Photo Caption -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Photo Caption <span>*</span></label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" name="caption" value="<?php echo $caption; ?>">
                            </div>
                        </div>
                        <!-- Existing Color Display -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Existing Photo Color</label>
                            <div class="col-sm-4" style="padding-top:6px;">
                                <?php
                                // Retrieve current color name for display.
                                $stmt = $pdo->prepare("SELECT color_name FROM tbl_color WHERE color_id=?");
                                $stmt->execute(array($current_color_id));
                                $colorRow = $stmt->fetch(PDO::FETCH_ASSOC);
                                $existing_color = isset($colorRow['color_name']) ? $colorRow['color_name'] : 'Not set';
                                echo '<strong>' . htmlspecialchars($existing_color) . '</strong>';
                                ?>
                            </div>
                        </div>
                        <!-- Color Update Selection -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Select New Color <span>*</span></label>
                            <div class="col-sm-4">
                                <select name="color_id" class="form-control">
                                    <option value="">Select Color</option>
                                    <?php
                                    // Populate color options from tbl_color.
                                    $statement = $pdo->prepare("SELECT * FROM tbl_color ORDER BY color_name ASC");
                                    $statement->execute();
                                    $colors = $statement->fetchAll(PDO::FETCH_ASSOC);
                                    foreach($colors as $color) {
                                        $selected = ($color['color_id'] == $current_color_id) ? 'selected' : '';
                                        echo '<option value="' . $color['color_id'] . '" ' . $selected . '>' . $color['color_name'] . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        <!-- Existing Photo -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Existing Photo</label>
                            <div class="col-sm-6" style="padding-top:6px;">
                                <img src="../assets/uploads/<?php echo $photo; ?>" class="existing-photo" style="width:300px;">
                                <input type="hidden" name="previous_photo" value="<?php echo $photo; ?>">
                            </div>
                        </div>
                        <!-- Upload New Photo -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Upload New Photo <span>*</span></label>
                            <div class="col-sm-4" style="padding-top:6px;">
                                <input type="file" name="photo">
                            </div>
                        </div>
                        <!-- Submit Button -->
                        <div class="form-group">
                            <label class="col-sm-2 control-label"></label>
                            <div class="col-sm-6">
                                <button type="submit" class="btn btn-success pull-left" name="form1">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<?php require_once('footer.php'); ?>
