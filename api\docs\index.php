<?php
/**
 * API Documentation
 * Interactive documentation for the Ecommerce API
 */

// Include configuration for constants
require_once __DIR__ . '/../config/config.php';

// Set content type to HTML
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ecommerce API Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 40px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .section h3 {
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 1.3rem;
        }
        
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.8rem;
            margin-right: 10px;
        }
        
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }
        
        .url {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .params {
            margin-top: 10px;
        }
        
        .param {
            margin: 5px 0;
            font-size: 0.9rem;
        }
        
        .param-name {
            font-weight: bold;
            color: #667eea;
        }
        
        .param-type {
            color: #6c757d;
            font-style: italic;
        }
        
        .example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .toc {
            background: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 5px 0;
        }
        
        .toc a {
            color: #667eea;
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .badge {
            background: #667eea;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Ecommerce API Documentation</h1>
            <p>Version <?= API_VERSION ?> - RESTful API for Flutter Mobile Application</p>
        </div>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#auth-endpoints">Authentication Endpoints</a></li>
                <li><a href="#product-endpoints">Product Endpoints</a></li>
                <li><a href="#category-endpoints">Category Endpoints</a></li>
                <li><a href="#cart-endpoints">Cart Endpoints</a></li>
                <li><a href="#order-endpoints">Order Endpoints</a></li>
                <li><a href="#user-endpoints">User Endpoints</a></li>
                <li><a href="#search-endpoints">Search Endpoints</a></li>
                <li><a href="#wishlist-endpoints">Wishlist Endpoints</a></li>
                <li><a href="#shipping-endpoints">Shipping Endpoints</a></li>
                <li><a href="#admin-endpoints">Admin Endpoints</a></li>
                <li><a href="#error-handling">Error Handling</a></li>
            </ul>
        </div>
        
        <div class="section" id="overview">
            <h2>Overview</h2>
            <p>This API provides comprehensive access to the ecommerce system for Flutter mobile applications. All endpoints return JSON responses with consistent structure.</p>
            
            <h3>Base URL</h3>
            <div class="example">
<?= $_SERVER['HTTP_HOST'] ?? 'localhost' ?>/api/v1
            </div>
            
            <h3>Response Format</h3>
            <div class="example">
{
    "status": "success|error",
    "message": "Human readable message",
    "timestamp": "2024-01-01T12:00:00+00:00",
    "api_version": "v1",
    "data": { ... } // Only present on success
}
            </div>
        </div>
        
        <div class="section" id="authentication">
            <h2>Authentication</h2>
            <p>The API uses JWT (JSON Web Token) based authentication. Include the token in the Authorization header:</p>
            
            <div class="example">
Authorization: Bearer YOUR_JWT_TOKEN
            </div>
            
            <p>Tokens expire after <?= JWT_EXPIRY / 3600 ?> hours and need to be refreshed.</p>
        </div>
        
        <div class="section" id="auth-endpoints">
            <h2>Authentication Endpoints</h2>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/auth/login</span>
                <p>Authenticate user and receive JWT token</p>
                <div class="params">
                    <div class="param">
                        <span class="param-name">email</span> 
                        <span class="param-type">(string, required)</span> - User email
                    </div>
                    <div class="param">
                        <span class="param-name">password</span> 
                        <span class="param-type">(string, required)</span> - User password
                    </div>
                    <div class="param">
                        <span class="param-name">remember_me</span> 
                        <span class="param-type">(boolean, optional)</span> - Extended session
                    </div>
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/auth/register</span>
                <p>Register new user account</p>
                <div class="params">
                    <div class="param">
                        <span class="param-name">first_name, last_name, email, password, phone, address, city, region, country</span> 
                        <span class="param-type">(required)</span>
                    </div>
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/auth/me</span>
                <span class="badge">Auth Required</span>
                <p>Get current authenticated user information</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/auth/logout</span>
                <p>Logout user (client-side token removal)</p>
            </div>
        </div>
        
        <div class="section" id="product-endpoints">
            <h2>Product Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/products</span>
                <p>Get paginated list of products with filtering</p>
                <div class="params">
                    <div class="param">
                        <span class="param-name">page</span> 
                        <span class="param-type">(int, optional)</span> - Page number (default: 1)
                    </div>
                    <div class="param">
                        <span class="param-name">limit</span> 
                        <span class="param-type">(int, optional)</span> - Items per page (max: <?= MAX_PAGE_SIZE ?>)
                    </div>
                    <div class="param">
                        <span class="param-name">category_id, search, min_price, max_price</span> 
                        <span class="param-type">(optional)</span> - Filters
                    </div>
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/products/{id}</span>
                <p>Get detailed product information including variations, colors, sizes</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/products/featured</span>
                <p>Get featured products</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/products/latest</span>
                <p>Get latest products</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/products/popular</span>
                <p>Get popular products (by view count)</p>
            </div>
        </div>
        
        <div class="section" id="cart-endpoints">
            <h2>Cart Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/cart</span>
                <p>Get cart contents with totals</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/cart/add</span>
                <p>Add item to cart</p>
                <div class="params">
                    <div class="param">
                        <span class="param-name">product_id</span> 
                        <span class="param-type">(int, required)</span> - Product ID
                    </div>
                    <div class="param">
                        <span class="param-name">quantity</span> 
                        <span class="param-type">(int, required)</span> - Quantity
                    </div>
                    <div class="param">
                        <span class="param-name">variation_id, color_id, installation</span> 
                        <span class="param-type">(optional)</span>
                    </div>
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method put">PUT</span>
                <span class="url">/cart/{cart_key}</span>
                <p>Update cart item quantity</p>
            </div>
            
            <div class="endpoint">
                <span class="method delete">DELETE</span>
                <span class="url">/cart/{cart_key}</span>
                <p>Remove item from cart</p>
            </div>
        </div>
        
        <div class="section" id="order-endpoints">
            <h2>Order Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/orders</span>
                <span class="badge">Auth Required</span>
                <p>Get user's orders with pagination</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="url">/orders/{id}</span>
                <span class="badge">Auth Required</span>
                <p>Get detailed order information with items</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="url">/orders/create</span>
                <span class="badge">Auth Required</span>
                <p>Create new order from cart items</p>
                <div class="params">
                    <div class="param">
                        <span class="param-name">items</span> 
                        <span class="param-type">(array, required)</span> - Order items
                    </div>
                    <div class="param">
                        <span class="param-name">shipping_address, shipping_country_id, phone</span> 
                        <span class="param-type">(required)</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section" id="error-handling">
            <h2>Error Handling</h2>
            <p>The API uses standard HTTP status codes and returns detailed error information:</p>
            
            <div class="example">
{
    "status": "error",
    "message": "Validation failed",
    "timestamp": "2024-01-01T12:00:00+00:00",
    "api_version": "v1",
    "error_code": "VALIDATION_ERROR",
    "details": {
        "email": "The email field is required",
        "password": "The password must be at least 6 characters"
    }
}
            </div>
            
            <h3>Common Error Codes</h3>
            <ul>
                <li><strong>VALIDATION_ERROR</strong> - Input validation failed</li>
                <li><strong>UNAUTHORIZED</strong> - Authentication required</li>
                <li><strong>FORBIDDEN</strong> - Access denied</li>
                <li><strong>NOT_FOUND</strong> - Resource not found</li>
                <li><strong>RATE_LIMIT_EXCEEDED</strong> - Too many requests</li>
                <li><strong>INSUFFICIENT_STOCK</strong> - Product out of stock</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>Rate Limiting</h2>
            <p>API requests are limited to <?= RATE_LIMIT_REQUESTS ?> requests per hour per IP address. Rate limit headers are included in responses:</p>
            
            <div class="example">
X-RateLimit-Limit: <?= RATE_LIMIT_REQUESTS ?>

X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
            </div>
        </div>
        
        <div class="section">
            <h2>Currency & Localization</h2>
            <p>All prices are returned in <?= DEFAULT_CURRENCY ?> (Tanzanian Shillings). Installation fee default is <?= number_format(DEFAULT_INSTALLATION_FEE) ?> <?= DEFAULT_CURRENCY ?>.</p>
        </div>
    </div>
</body>
</html>
<?php exit; ?>
