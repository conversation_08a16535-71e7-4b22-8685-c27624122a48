<?php
/**
 * Authentication Middleware
 * Handles authentication requirements for protected endpoints
 */

class AuthMiddleware {
    
    /**
     * Require authentication for the request
     */
    public static function requireAuth() {
        $user = Auth::getCurrentUser();
        
        if (!$user) {
            Response::unauthorized('Authentication required. Please provide a valid token.');
        }
        
        return $user;
    }
    
    /**
     * Require admin authentication
     */
    public static function requireAdmin() {
        $user = self::requireAuth();
        
        if (!in_array($user['role'], ['admin', 'super admin'])) {
            Response::forbidden('Admin access required.');
        }
        
        return $user;
    }
    
    /**
     * Optional authentication (user may or may not be logged in)
     */
    public static function optionalAuth() {
        return Auth::getCurrentUser();
    }
    
    /**
     * Check if user owns resource or is admin
     */
    public static function requireOwnershipOrAdmin($resource_user_id) {
        $user = self::requireAuth();
        
        $is_owner = (int)$user['user_id'] === (int)$resource_user_id;
        $is_admin = in_array($user['role'], ['admin', 'super admin']);
        
        if (!$is_owner && !$is_admin) {
            Response::forbidden('You can only access your own resources.');
        }
        
        return $user;
    }
}
?>
