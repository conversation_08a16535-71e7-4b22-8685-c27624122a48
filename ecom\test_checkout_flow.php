<?php
/**
 * Test Checkout Flow
 * This script tests the complete checkout process
 */

// Include session configuration before starting session
include("session_config.php");
session_start();

// Include database connection and functions
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

// Check if user is logged in
$is_logged_in = isUserLoggedIn();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout Flow Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
            cursor: pointer;
            border: none;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .test-section { 
            border: 1px solid #ddd; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 5px; 
        }
        .code { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            margin: 10px 0;
        }
        #test-results { margin-top: 20px; }
        .test-result { 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 3px; 
            border-left: 4px solid #007bff; 
            background: #f8f9fa; 
        }
        .test-result.pass { border-left-color: #28a745; background: #d4edda; }
        .test-result.fail { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Checkout Flow Test</h1>
        
        <div class="test-section">
            <h2>Authentication Status</h2>
            <?php if ($is_logged_in): ?>
                <div class="status success">✅ User is logged in</div>
                <p><strong>Customer:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_fname'] . ' ' . $_SESSION['customer']['cust_lname']) ?></p>
            <?php else: ?>
                <div class="status error">❌ User is not logged in</div>
                <p>You need to be logged in to test the checkout flow.</p>
                <a href="login.php" class="btn">Login</a>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>JavaScript Authentication Test</h2>
            <div id="js-auth-result" class="status info">⏳ Testing JavaScript authentication...</div>
            <div class="code">
                <strong>window.isUserLoggedIn:</strong> <span id="js-logged-in">Loading...</span><br>
                <strong>Cart.js logic result:</strong> <span id="cart-logic-result">Loading...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Cart Simulation</h2>
            <p>This section simulates adding items to cart and testing checkout flow.</p>
            
            <button class="btn" onclick="addTestItem()">Add Test Item to Cart</button>
            <button class="btn btn-success" onclick="testCheckoutFlow()">Test Checkout Flow</button>
            <button class="btn btn-danger" onclick="clearTestCart()">Clear Cart</button>
            
            <div id="cart-status" class="code">
                Cart items: <span id="cart-count">0</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Checkout Process Test</h2>
            <div id="checkout-results">
                <p>Click "Test Checkout Flow" above to run the checkout simulation.</p>
            </div>
        </div>
        
        <div id="test-results">
            <h2>Test Results</h2>
            <div id="results-container">
                <p>Run tests to see results here.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Quick Actions</h2>
            <a href="cart.php" class="btn">Go to Cart</a>
            <a href="test_auth_status.php" class="btn">Auth Status</a>
            <?php if ($is_logged_in): ?>
                <a href="logout.php" class="btn btn-danger">Logout</a>
            <?php else: ?>
                <a href="login.php" class="btn btn-success">Login</a>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Set authentication variables
        window.isUserLoggedIn = <?php echo json_encode($is_logged_in); ?>;
        
        // Test results array
        let testResults = [];
        
        function addTestResult(test, result, message) {
            testResults.push({ test, result, message });
            updateTestResults();
        }
        
        function updateTestResults() {
            const container = document.getElementById('results-container');
            container.innerHTML = '';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.result}`;
                div.innerHTML = `<strong>${result.test}:</strong> ${result.message}`;
                container.appendChild(div);
            });
        }
        
        function addTestItem() {
            // Simulate adding an item to cart
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            const testItem = {
                id: 1,
                product_id: 1,
                name: 'Test Product',
                price: 1000,
                quantity: 1,
                photo: 'test.jpg',
                color_id: null,
                color_name: null,
                variation_id: null,
                installation: false
            };
            
            // Check if item already exists
            const existingIndex = cart.findIndex(item => item.id === testItem.id);
            if (existingIndex >= 0) {
                cart[existingIndex].quantity += 1;
            } else {
                cart.push(testItem);
            }
            
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartStatus();
            addTestResult('Add Item', 'pass', 'Test item added to cart successfully');
        }
        
        function clearTestCart() {
            localStorage.setItem('cart', JSON.stringify([]));
            updateCartStatus();
            addTestResult('Clear Cart', 'pass', 'Cart cleared successfully');
        }
        
        function updateCartStatus() {
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            document.getElementById('cart-count').textContent = cart.length;
        }
        
        function testCheckoutFlow() {
            const checkoutResults = document.getElementById('checkout-results');
            checkoutResults.innerHTML = '<p>⏳ Testing checkout flow...</p>';
            
            // Test 1: Check authentication
            const isLoggedIn = typeof window.isUserLoggedIn !== "undefined" ? window.isUserLoggedIn : false;
            
            if (!isLoggedIn) {
                checkoutResults.innerHTML = `
                    <div class="status error">❌ Checkout Test Failed</div>
                    <p>User is not logged in. The checkout process would redirect to login page.</p>
                    <p><strong>Expected behavior:</strong> Redirect to login.php</p>
                `;
                addTestResult('Checkout Flow', 'fail', 'User not logged in - would redirect to login');
                return;
            }
            
            // Test 2: Check cart
            const cart = JSON.parse(localStorage.getItem('cart') || '[]');
            if (cart.length === 0) {
                checkoutResults.innerHTML = `
                    <div class="status warning">⚠️ Checkout Test Warning</div>
                    <p>Cart is empty. Add a test item first.</p>
                `;
                addTestResult('Checkout Flow', 'fail', 'Cart is empty');
                return;
            }
            
            // Test 3: Simulate checkout process
            checkoutResults.innerHTML = `
                <div class="status success">✅ Checkout Test Passed</div>
                <p>All conditions met for successful checkout:</p>
                <ul>
                    <li>✅ User is authenticated</li>
                    <li>✅ Cart has ${cart.length} item(s)</li>
                    <li>✅ JavaScript authentication working correctly</li>
                </ul>
                <p><strong>Expected behavior:</strong> Proceed to payment page</p>
            `;
            addTestResult('Checkout Flow', 'pass', `All conditions met - ${cart.length} items in cart, user authenticated`);
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // Test JavaScript authentication
            const jsAuthResult = document.getElementById('js-auth-result');
            const jsLoggedIn = document.getElementById('js-logged-in');
            const cartLogicResult = document.getElementById('cart-logic-result');
            
            // Check window.isUserLoggedIn
            jsLoggedIn.textContent = window.isUserLoggedIn;
            
            // Test the exact logic from cart.js
            const isLoggedIn = typeof window.isUserLoggedIn !== "undefined" ? window.isUserLoggedIn : false;
            cartLogicResult.textContent = isLoggedIn;
            
            // Update status
            if (window.isUserLoggedIn === true) {
                jsAuthResult.className = 'status success';
                jsAuthResult.innerHTML = '✅ JavaScript authentication working correctly';
                addTestResult('JavaScript Auth', 'pass', 'window.isUserLoggedIn is true and accessible');
            } else if (window.isUserLoggedIn === false) {
                jsAuthResult.className = 'status error';
                jsAuthResult.innerHTML = '❌ JavaScript shows user not logged in';
                addTestResult('JavaScript Auth', 'fail', 'window.isUserLoggedIn is false');
            } else {
                jsAuthResult.className = 'status error';
                jsAuthResult.innerHTML = '⚠️ JavaScript authentication status unclear';
                addTestResult('JavaScript Auth', 'fail', 'window.isUserLoggedIn is undefined or invalid');
            }
            
            // Update cart status
            updateCartStatus();
            
            console.log('Checkout Flow Test Results:');
            console.log('- PHP logged in:', <?php echo json_encode($is_logged_in); ?>);
            console.log('- window.isUserLoggedIn:', window.isUserLoggedIn);
            console.log('- Cart.js logic result:', isLoggedIn);
        });
    </script>
</body>
</html>
