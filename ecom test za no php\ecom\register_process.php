<?php
// registration_process.php

ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: cart.php');
    exit;
}

// Only handle POST submissions
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: register.php');
    exit;
}

// Sanitize and validate inputs
$fname            = filter_input(INPUT_POST, 'fname', FILTER_SANITIZE_SPECIAL_CHARS);
$lname            = filter_input(INPUT_POST, 'lname', FILTER_SANITIZE_SPECIAL_CHARS);
$email            = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
$phone            = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_SPECIAL_CHARS);
$password         = $_POST['password'] ?? '';
$confirm_password = $_POST['confirm_password'] ?? '';
$address_street   = filter_input(INPUT_POST, 'address_street', FILTER_SANITIZE_SPECIAL_CHARS);
$address_city     = filter_input(INPUT_POST, 'address_city', FILTER_SANITIZE_SPECIAL_CHARS);
$address_region   = filter_input(INPUT_POST, 'address_region', FILTER_SANITIZE_SPECIAL_CHARS);
$country          = filter_input(INPUT_POST, 'country', FILTER_SANITIZE_SPECIAL_CHARS);

$errors = [];

// Required fields and defaults
if (empty($fname))                $errors[] = "First name is required.";
if (empty($lname))                $errors[] = "Last name is required.";
if (!$email)                      $errors[] = "Valid email is required.";
if (empty($phone))               $errors[] = "Phone number is required.";
if (empty($password))            $errors[] = "Password is required.";
if ($password !== $confirm_password) $errors[] = "Passwords do not match.";
if (empty($address_street))      $address_street = '';
if (empty($address_city))        $errors[] = "City is required.";
if (empty($address_region))      $address_region = 'N/A';
if (empty($country))             $errors[] = "Country is required.";

// Check duplicate email
if (empty($errors)) {
    try {
        $stmt = $pdo->prepare("SELECT cust_id FROM tbl_customer WHERE cust_email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            $errors[] = "Email address is already registered.";
        }
    } catch (PDOException $e) {
        error_log("Email check error: " . $e->getMessage());
        $errors[] = "System error. Please try again later.";
    }
}

// Handle photo upload (optional)
$photo_path = '';
if (empty($errors) && !empty($_FILES['photo']['name'])) {
    if ($_FILES['photo']['error'] === UPLOAD_ERR_OK) {
        $allowed = ['image/jpeg','image/png','image/gif'];
        if (!in_array($_FILES['photo']['type'], $allowed)) {
            $errors[] = "Only JPG, PNG, and GIF images are allowed.";
        } elseif ($_FILES['photo']['size'] > 2*1024*1024) {
            $errors[] = "Image size must be under 2MB.";
        } else {
            $uploadDir = __DIR__ . '/../assets/uploads/customers/';
            if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);

            $ext = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
            $filename = uniqid('cust_', true) . '.' . $ext;
            $dest = $uploadDir . $filename;

            if (move_uploaded_file($_FILES['photo']['tmp_name'], $dest)) {
                $photo_path = 'customers/' . $filename;
            } else {
                $errors[] = "Failed to move uploaded photo.";
            }
        }
    } else {
        $errors[] = "File upload error code: " . $_FILES['photo']['error'];
    }
}

// If errors, redirect back with messages
if (!empty($errors)) {
    $_SESSION['error_message'] = implode('<br>', $errors);
    $_SESSION['form_data'] = $_POST;
    header('Location: register.php');
    exit;
}

// Store user data in session for later use after OTP verification
$_SESSION['registration_data'] = [
    'fname' => $fname,
    'lname' => $lname,
    'email' => $email,
    'phone' => $phone,
    'password' => $password,
    'address_street' => $address_street,
    'address_city' => $address_city,
    'address_region' => $address_region,
    'country' => $country,
    'photo_path' => $photo_path
];

// Generate OTP
$otp = generateOTP();
storeOTP($email, $otp);

// Prepare email content
$subject = "Your OTP for SMART HOME Registration";
$message = "
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .header { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; }
        .otp-box { font-size: 24px; font-weight: bold; text-align: center; padding: 15px; background-color: #f1f1f1; border-radius: 5px; margin: 20px 0; letter-spacing: 5px; }
        .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>SMARTLIFE TZ Registration</h2>
        </div>
        <div class='content'>
            <p>Hello $fname,</p>
            <p>Thank you for registering with SMART LIFE TZ. To complete your registration, please use the following One-Time Password (OTP):</p>
            <div class='otp-box'>$otp</div>
            <p>This OTP is valid for 10 minutes. If you did not request this registration, please ignore this email.</p>
        </div>
        <div class='footer'>
            <p>&copy; " . date('Y') . " SMART LIFE TZ. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
";

// Send OTP via email
$emailSent = sendEmailWithPHPMailer($email, $subject, $message);

if (!$emailSent) {
    $_SESSION['error_message'] = "Failed to send OTP email. Please try again.";
    header('Location: register.php');
    exit;
}

// Redirect to OTP verification page
header('Location: verify_otp.php');
exit;

ob_end_flush();
?>
