<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Redirect if no registration data is available
if (!isset($_SESSION['registration_data']) || !isset($_SESSION['otp_data'])) {
    header('Location: register.php');
    exit;
}

// Fetch settings for footer
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 Your Company. All rights reserved.";

// Get email from session
$email = $_SESSION['registration_data']['email'];
$maskedEmail = substr($email, 0, 3) . '***' . substr($email, strpos($email, '@'));

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify OTP | SMART</title>
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />

    <style>
        header {
            position: fixed;
            width: 100%;
            z-index: 1000;
            transition: all 0.4s ease;
            padding: 10px 0;
        }
        
        .otp-container {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }
        
        .otp-inputs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .otp-inputs input {
            width: 50px;
            height: 50px;
            text-align: center;
            font-size: 24px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .resend-link {
            margin-top: 15px;
            color: #666;
        }
        
        .resend-link a {
            color: #007bff;
            text-decoration: none;
        }
        
        .resend-link a:hover {
            text-decoration: underline;
        }
        
        .timer {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body class="auth-page">
    <header style="background-color: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
         <div class="container">
          <a href="index.php" class="logo">SMART HOME<span>.</span></a>
          <div class="nav-links">
            <a href="index.php">Home</a>
             <a href="index.php#about">About</a>
             <a href="index.php#products">Products</a>
             <a href="index.php#gallery">SPECIAL OFFER</a>
             <a href="index.php#contact">Contact</a>
             <div class="cart-icon">
               <a href="cart.php"><i class="fas fa-shopping-cart"></i> <span class="cart-count">0</span></a>
             </div>
            </div>
            <div class="mobile-menu">
               <div class="menu-btn"><span></span><span></span><span></span></div>
             </div>
         </div>
     </header>

    <main style="padding-top: 150px !important;">
        <div class="container">
            <div class="auth-container otp-container">
                <h2>Verify Your Email</h2>
                <p>We've sent a verification code to <strong><?php echo htmlspecialchars($maskedEmail); ?></strong></p>
                
                <?php
                // Display messages
                if (isset($_SESSION['error_message'])) {
                    echo '<div class="error-message">' . $_SESSION['error_message'] . '</div>';
                    unset($_SESSION['error_message']);
                }
                ?>
                
                <form action="verify_otp_process.php" method="POST">
                    <div class="form-group">
                        <label for="otp">Enter 6-digit OTP</label>
                        <input type="text" id="otp" name="otp" required class="form-control" 
                               maxlength="6" pattern="[0-9]{6}" inputmode="numeric" 
                               placeholder="Enter OTP">
                    </div>
                    
                    <button type="submit" class="btn">Verify & Complete Registration</button>
                </form>
                
                <div class="resend-link">
                    <p>Didn't receive the code? <a href="resend_otp.php">Resend OTP</a></p>
                    <div class="timer" id="timer">Resend available in <span id="countdown">10:00</span></div>
                </div>
                
                <p><a href="register.php">Back to Registration</a></p>
            </div>
        </div>
    </main>

    <footer>
         <div class="container">
             <div class="footer-bottom">
                 <div class="copyright"><?php echo htmlspecialchars($footer_copyright); ?></div>
             </div>
         </div>
     </footer>
     
     <script>
        // Mobile Menu Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const menuBtn = document.querySelector(".menu-btn");
            const navLinks = document.querySelector(".nav-links");
            const navOverlay = document.querySelector(".nav-overlay");

            if (menuBtn && navLinks) {
                menuBtn.addEventListener("click", function() {
                    menuBtn.classList.toggle("active");
                    navLinks.classList.toggle("active");
                });

                // Close menu when clicking overlay
                if (navOverlay) {
                    navOverlay.addEventListener("click", function() {
                        menuBtn.classList.remove("active");
                        navLinks.classList.remove("active");
                    });
                }
            }

            // Update cart count
            let cartCount = <?php
                $count = 0;
                if(isset($_SESSION['cart'])) {
                    foreach($_SESSION['cart'] as $item) {
                        $count += $item['quantity'];
                    }
                }
                echo $count;
            ?>;
            const countElem = document.querySelector('.cart-count');
            if(countElem) {
                countElem.textContent = cartCount;
                countElem.style.display = cartCount > 0 ? 'inline-block' : 'none';
            }
            
            // Countdown timer for OTP resend
            function startCountdown(duration, display) {
                let timer = duration, minutes, seconds;
                const interval = setInterval(function () {
                    minutes = parseInt(timer / 60, 10);
                    seconds = parseInt(timer % 60, 10);

                    minutes = minutes < 10 ? "0" + minutes : minutes;
                    seconds = seconds < 10 ? "0" + seconds : seconds;

                    display.textContent = minutes + ":" + seconds;

                    if (--timer < 0) {
                        clearInterval(interval);
                        document.getElementById('timer').style.display = 'none';
                    }
                }, 1000);
            }

            // Start countdown
            const display = document.querySelector('#countdown');
            startCountdown(600, display); // 10 minutes = 600 seconds
        });
     </script>
</body>
</html>
