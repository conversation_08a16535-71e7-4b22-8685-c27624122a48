<?php
ob_start();

// Include session configuration before starting session
include("session_config.php");
session_start();

// Ensure paths are correct relative to cart.php location
include("../admin/inc/config.php");
include("../admin/inc/functions.php"); // Assuming isUserLoggedIn() is defined here

// Include auto cleanup system
include("auto_cleanup.php");

// --- Define currency constants ---
define('CURRENCY_CODE', 'TZS');
define('CURRENCY_SYMBOL', 'TSH');
define('CURRENCY_FORMAT', 'en-TZ');
define('CURRENCY_SYMBOL_NEW', 'TSH'); // New currency symbol definition

// --- Fetch settings ---
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© " . date("Y") . " SMART LIFE. All rights reserved."; // Use current year

// --- Fetch products (Required by cart.js) ---
$statement = $pdo->prepare("SELECT p_id, p_name, p_featured_photo, p_current_price, p_qty FROM tbl_product WHERE p_is_active = 1");
$statement->execute();
$all_products = $statement->fetchAll(PDO::FETCH_ASSOC);

// --- Fetch colors (Required by cart.js) ---
$statement = $pdo->prepare("SELECT c.color_id, c.color_name, c.color_code, pc.p_id FROM tbl_color c JOIN tbl_product_color pc ON c.color_id = pc.color_id");
$statement->execute();
$color_data = $statement->fetchAll(PDO::FETCH_ASSOC);
$product_colors = [];
foreach ($color_data as $color) {
    $product_colors[$color['p_id']][] = [
        'color_id' => $color['color_id'],
        'color_name' => $color['color_name'],
        'color_code' => $color['color_code']
    ];
}

// --- Default Installation Fee (Required by cart.js) ---
$default_installation_fee = 15000; // Default value in TSH

// --- Fetch product-specific installation fees ---
$product_installation_fees = [];
try {
    $stmt = $pdo->prepare("SELECT p_id, installation_fee FROM tbl_product WHERE p_is_active = 1");
    $stmt->execute();
    $installation_fees_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($installation_fees_data as $fee_data) {
        // Convert to integer and store in the array
        $product_installation_fees[$fee_data['p_id']] = intval($fee_data['installation_fee']);
    }

    // Debug: Log the installation fees to the error log
    error_log("Product installation fees: " . json_encode($product_installation_fees));
} catch (PDOException $e) {
    // If there's an error, we'll use the default fee for all products
    error_log("Error fetching product installation fees: " . $e->getMessage());
}

// --- Fetch Shipping Data ---
$stmt_countries = $pdo->query("SELECT country_id, country_name FROM tbl_country ORDER BY country_name ASC");
$countries = $stmt_countries->fetchAll(PDO::FETCH_ASSOC);
$stmt_costs = $pdo->query("SELECT country_id, amount FROM tbl_shipping_cost");
$shipping_costs_db = $stmt_costs->fetchAll(PDO::FETCH_KEY_PAIR);
$stmt_default_cost = $pdo->query("SELECT amount FROM tbl_shipping_cost_all LIMIT 1");
$default_shipping_cost_db = $stmt_default_cost->fetchColumn() ?? 0;
$default_shipping_cost = floatval($default_shipping_cost_db);
$shipping_options_data = [];
foreach ($countries as $country) {
    $cost_for_country = isset($shipping_costs_db[$country['country_id']]) ? floatval($shipping_costs_db[$country['country_id']]) : null;
    $shipping_options_data[] = ['id' => $country['country_id'], 'name' => $country['country_name'], 'cost' => $cost_for_country];
}
$customer_country_id = $_SESSION['customer']['cust_country_id'] ?? null; // Example: Get user's saved country

// Initialize shipping data from session
$shipping_country_id = $_SESSION['shipping_country_id'] ?? '';
$shipping_fee = floatval($_SESSION['shipping_fee'] ?? 0);

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Shopping Cart | SMART</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" /> <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css" /> <link rel="stylesheet" href="css/cart.css" /> <script src="https://cdn.tailwindcss.com"></script>
  <script>
    // Optional: Configure Tailwind (if needed, place before closing </head>)
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            sans: ['Inter', 'sans-serif'], // Apply Inter font globally
          },
          colors: {
            primary: '#007bff', // Example primary color
            secondary: '#6c757d', // Example secondary color
            // Add custom colors if needed
          }
        }
      }
    }
  </script>
  <style>


    /* Base styles */
    body {
      font-family: 'Inter', sans-serif; /* Apply modern font */
      background-color: #f8f9fa; /* Light background */
      color: #333;
    }
    /* Keep essential styles not covered by Tailwind or that need overrides */
    .container {
        max-width: 1200px; /* Adjust as needed */
        margin-left: auto;
        margin-right: auto;
        padding-left: 1rem; /* Tailwind: px-4 */
        padding-right: 1rem; /* Tailwind: px-4 */
    }
    header {
        background-color: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        padding: 1rem 0;
        position: sticky;
        top: 0;
        z-index: 50; /* Ensure header stays on top */
    }

    /* Clear cart button */
    .clear-cart-btn {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .clear-cart-btn:hover {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    .clear-cart-btn:active {
      transform: translateY(0);
    }

    /* Clear Cart Confirmation Modal */
    .clear-cart-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      padding: 1rem;
    }

    .clear-cart-modal.show {
      display: flex;
    }

    .clear-cart-modal-content {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      max-width: 400px;
      width: 100%;
      text-align: center;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      transform: scale(0.9);
      opacity: 0;
      transition: all 0.3s ease;
    }

    .clear-cart-modal.show .clear-cart-modal-content {
      transform: scale(1);
      opacity: 1;
    }

    .clear-cart-modal-icon {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #ef4444, #dc2626);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1.5rem;
      color: white;
      font-size: 24px;
    }

    .clear-cart-modal h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 0.5rem;
    }

    .clear-cart-modal p {
      color: #6b7280;
      margin-bottom: 2rem;
      line-height: 1.5;
    }

    .clear-cart-modal-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
    }

    .clear-cart-modal-btn {
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      font-size: 0.875rem;
    }

    .clear-cart-modal-btn.cancel {
      background: #f3f4f6;
      color: #374151;
    }

    .clear-cart-modal-btn.cancel:hover {
      background: #e5e7eb;
    }

    .clear-cart-modal-btn.confirm {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;
    }

    .clear-cart-modal-btn.confirm:hover {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }
    .logo {
        font-size: 1.5rem; /* Tailwind: text-2xl */
        font-weight: 700; /* Tailwind: font-bold */
        color: #333;
        text-decoration: none;
    }
    .logo span {
        color: #007bff; /* Tailwind: text-primary */
    }
    /* Adjust existing nav link styles if needed, Tailwind can handle most */
    .nav-links { display: flex; align-items: center; gap: 1.5rem; /* Tailwind: space-x-6 */ }
    .nav-links a { color: #555; text-decoration: none; transition: color 0.2s; }
    .nav-links a:hover { color: #007bff; /* Tailwind: hover:text-primary */ }

    /* Cart Count Badge */
    .cart-icon a { position: relative; display: inline-block; }
    .cart-count {
        position: absolute;
        top: -8px;
        right: -10px;
        background-color: #dc3545; /* Tailwind: bg-red-600 */
        color: white;
        font-size: 0.7rem; /* Tailwind: text-xs */
        padding: 2px 5px;
        border-radius: 50%; /* Tailwind: rounded-full */
        font-weight: 600;
        line-height: 1;
        min-width: 18px;
        text-align: center;
    }

    /* Mobile Menu Styles */
    .mobile-menu {
      position: fixed;
      top: 0;
      left: -100%;
      width: 280px;
      height: 100vh;
      background: white;
      border-right: 1px solid #e5e7eb;
      transition: left 0.3s ease;
      z-index: 60;
      overflow-y: auto;
    }

    .mobile-menu.active {
      left: 0;
    }

    .mobile-menu-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 55;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    .mobile-menu-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      color: #1f2937;
      text-decoration: none;
      border-bottom: 1px solid #e5e7eb;
      transition: background-color 0.3s ease;
    }

    .mobile-menu-item:hover {
      background: #f3f4f6;
    }

    .mobile-menu-item i {
      width: 24px;
      margin-right: 12px;
      color: #6b7280;
    }

    .product-variation {
    margin-top: 5px;
    font-size: 14px;
    color: #666;
}

.product-variation::before {
    content: "";
    color: #999;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 5px;
}

quantity-input {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
}

.loading-quantity {
    opacity: 0.7;
    pointer-events: none;
}
    /* --- Responsive Table --- */
    /* Hide table headers on small screens */
    @media (max-width: 767px) {
        .cart-table thead {
            display: none;
        }
        .cart-table, .cart-table tbody, .cart-table tr, .cart-table td {
            display: block;
            width: 100%;
        }
        .cart-table tr {
            margin-bottom: 1.5rem; /* space between cart items */
            border: 1px solid #e5e7eb; /* border around each item */
            border-radius: 0.5rem; /* rounded corners */
            padding: 1rem; /* padding inside */
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .cart-table td {
            display: flex;
            justify-content: space-between; /* Label on left, value on right */
            align-items: center;
            padding: 0.5rem 0; /* vertical padding */
            border-bottom: 1px dashed #eee; /* separator line */
            text-align: right; /* Align value text right */
        }
         .cart-table td:last-child {
             border-bottom: none;
         }
        /* Use data-label for pseudo-element label */
        .cart-table td::before {
            content: attr(data-label); /* Get label text from data-label attribute */
            font-weight: 600; /* Tailwind: font-semibold */
            text-align: left; /* Align label text left */
            margin-right: 1rem; /* Space between label and value */
            color: #4b5563; /* Tailwind: text-gray-600 */
        }
        /* Specific adjustments for certain cells */
        .cart-table td[data-label="Product"] {
            /* Product image/details might need special layout */
            justify-content: flex-start; /* Align left */
        }
        .cart-table td[data-label="Product"]::before { display: none; } /* Hide label for product cell */
        .cart-table td[data-label="Action"] { justify-content: center; } /* Center remove button */
        .cart-table td[data-label="Action"]::before { display: none; } /* Hide label */
        .cart-table .cart-img { width: 60px; height: 60px; margin-right: 1rem; } /* Smaller image */
        .quantity-selector { justify-content: flex-end; } /* Align qty selector right */
        .color-select-wrapper { justify-content: flex-end; }
        .installation-option { justify-content: flex-end; }
    }

     /* --- Modal Styles --- */
    .modal { /* Basic modal backdrop */
      position: fixed; top: 0; left: 0; width: 100%; height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      display: none; /* Hidden by default */
      align-items: center; justify-content: center;
      z-index: 1000; padding: 1rem;
    }
    .modal-content { /* Modal box */
      background-color: white; border-radius: 0.5rem; /* Tailwind: rounded-lg */
      padding: 1.5rem; /* Tailwind: p-6 */
      max-width: 600px; width: 90%; /* Responsive width */
      max-height: 90vh; overflow-y: auto; /* Scrollable */
      position: relative; box-shadow: 0 10px 25px rgba(0,0,0,0.1); /* Tailwind: shadow-xl */
    }
    .close-modal { /* Close button */
      position: absolute; top: 10px; right: 15px;
      font-size: 1.8rem; font-weight: bold;
      color: #aaa; cursor: pointer; border: none; background: none;
    }
    .close-modal:hover { color: #333; }
    .modal h2 { margin-bottom: 1.5rem; font-size: 1.5rem; font-weight: 600; text-align: center; }
    .modal-actions { margin-top: 1.5rem; display: flex; justify-content: flex-end; gap: 0.5rem; }
    .modal-btn { padding: 0.6rem 1.2rem; border-radius: 0.375rem; font-weight: 500; cursor: pointer; transition: background-color 0.2s; }
    .cancel-btn { background-color: #e5e7eb; color: #374151; } /* Tailwind: bg-gray-200 text-gray-700 */
    .cancel-btn:hover { background-color: #d1d5db; }
    .confirm-btn { background-color: #2563eb; color: white; } /* Tailwind: bg-blue-600 text-white */
    .confirm-btn:hover { background-color: #1d4ed8; }

    /* Summary item styles within modal */
    .summary-item { display: flex; align-items: center; padding: 0.75rem 0; border-bottom: 1px solid #e5e7eb; }
    .summary-item:last-child { border-bottom: none; }
    .summary-item-image { width: 50px; height: 50px; object-fit: cover; border-radius: 0.25rem; margin-right: 1rem; }
    .summary-item-details { flex-grow: 1; }
    .summary-item-title { font-weight: 600; color: #111827; }
    .summary-item-meta { font-size: 0.8rem; color: #6b7280; margin-top: 0.25rem; display: flex; gap: 0.75rem; align-items: center;}
    .summary-color { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-left: 4px; border: 1px solid #eee; }
    .summary-item-price { font-weight: 600; color: #111827; margin-left: 1rem; white-space: nowrap; }
    .summary-totals { margin-top: 1.5rem; border-top: 1px solid #ccc; padding-top: 1rem; }
    .summary-total-row { display: flex; justify-content: space-between; padding: 0.3rem 0; font-size: 0.95rem; }
    .summary-grand-total { font-weight: 700; font-size: 1.1rem; margin-top: 0.5rem; color: #111827; }

    /* Empty Cart / Messages */
    .empty-cart { color: #6b7280; } /* Tailwind: text-gray-500 */
    .shop-btn { /* Style using Tailwind classes directly on the element if preferred */
      background-color: #007bff; color: white; padding: 0.6rem 1.2rem; border-radius: 0.375rem; text-decoration: none; display: inline-block; transition: background-color 0.2s;
    }
    .shop-btn:hover { background-color: #0056b3; }
    /* Modern Alert Styles */
    .alert-container {
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 1000;
        max-width: 400px;
        width: 100%;
    }

    .alert {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateX(120%);
        opacity: 0;
        transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.35);
        overflow: hidden;
        position: relative;
    }

    .alert.show {
        transform: translateX(0);
        opacity: 1;
    }

    .alert-icon {
        flex-shrink: 0;
        width: 24px;
        height: 24px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .alert-content {
        flex-grow: 1;
    }

    .alert-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
        font-size: 1rem;
    }

    .alert-message {
        font-size: 0.875rem;
        opacity: 0.9;
    }

    .alert-close {
        background: none;
        border: none;
        cursor: pointer;
        color: inherit;
        opacity: 0.7;
        padding: 0;
        margin-left: 12px;
        font-size: 1.25rem;
        transition: opacity 0.2s;
    }

    .alert-close:hover {
        opacity: 1;
    }

    .alert-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        width: 100%;
        transform-origin: left;
    }

    /* Alert Types */
    .alert-success {
        background-color: #ecfdf5;
        color: #065f46;
        border-left: 4px solid #10b981;
    }

    .alert-success .alert-progress {
        background-color: #10b981;
    }

    .alert-error {
        background-color: #fef2f2;
        color: #991b1b;
        border-left: 4px solid #ef4444;
    }

    .alert-error .alert-progress {
        background-color: #ef4444;
    }

    .alert-info {
        background-color: #eff6ff;
        color: #1e40af;
        border-left: 4px solid #3b82f6;
    }

    .alert-info .alert-progress {
        background-color: #3b82f6;
    }

    .alert-warning {
        background-color: #fffbeb;
        color: #92400e;
        border-left: 4px solid #f59e0b;
    }

    .alert-warning .alert-progress {
        background-color: #f59e0b;
    }

    /* Legacy alert styles for backward compatibility */
    .error-message {
        background-color: #fef2f2;
        color: #991b1b;
        border-left: 4px solid #ef4444;
        padding: 0.75rem 1.25rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .success-message {
        background-color: #ecfdf5;
        color: #065f46;
        border-left: 4px solid #10b981;
        padding: 0.75rem 1.25rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

  </style>
</head>
<body>
  <script>
    // Clear cart functions
    function clearCart() {
      // Show the styled modal instead of basic confirm
      showClearCartModal();
    }

    function showClearCartModal() {
      const modal = document.getElementById('clearCartModal');
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    }

    function closeClearCartModal() {
      const modal = document.getElementById('clearCartModal');
      modal.classList.remove('show');
      document.body.style.overflow = '';
    }

    function confirmClearCart() {
      // Close the modal first
      closeClearCartModal();

      // Use the global clearCartData function from cart.js if available
      if (typeof window.clearCartData === 'function') {
        window.clearCartData();
      } else {
        // Fallback: Clear cart manually
        localStorage.setItem('cart', JSON.stringify([]));

        // Clear server session as well
        fetch('sync_cart.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ cart: {} })
        })
        .then(response => response.json())
        .then(data => {
          console.log('Server cart cleared:', data);
        })
        .catch(error => {
          console.error('Error clearing server cart:', error);
        });

        // Update cart counter using multiple methods for compatibility
        if (typeof window.updateCartCount === 'function') {
          window.updateCartCount();
        }

        // Also try the cart.js function
        if (typeof updateCartCountHeader === 'function') {
          updateCartCountHeader();
        }

        // Fallback: manually update cart counters
        document.querySelectorAll('.cart-count').forEach(element => {
          element.textContent = '0';
        });

        // Update cart display if the function exists (from cart.js)
        if (typeof renderCart === 'function') {
          renderCart();
        }
      }

      // Show success message
      if (typeof showAlert === 'function') {
        showAlert('Cart Cleared', 'All items have been removed from your cart', 'success');
      }

      // Trigger storage event to sync across tabs
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'cart',
        newValue: JSON.stringify([]),
        oldValue: localStorage.getItem('cart')
      }));

      // Reload page to refresh the display (reduced timeout for faster response)
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
      const modal = document.getElementById('clearCartModal');
      if (e.target === modal) {
        closeClearCartModal();
      }
    });

    // Close modal with escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeClearCartModal();
      }
    });
  </script>
  <!-- Alert Container -->
  <div class="alert-container" id="alertContainer"></div>

  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="container mx-auto px-4 flex items-center justify-between h-16">
      <!-- Mobile Menu Button -->
      <button class="md:hidden p-2 rounded-lg text-gray-800" onclick="toggleMobileMenu()">
        <i class="fas fa-bars text-xl"></i>
      </button>

      <a href="index.php" class="text-2xl font-bold text-gray-800">SMART LIFE TZ<span class="text-blue-600">.</span></a>
      <nav class="hidden md:flex items-center space-x-6">
        <a href="index.php" class="text-gray-600 hover:text-blue-600">Home</a>
        <a href="index.php#about" class="text-gray-600 hover:text-blue-600">About</a>
        <a href="index.php#products" class="text-gray-600 hover:text-blue-600">Products</a>
        <a href="index.php#gallery" class="text-gray-600 hover:text-blue-600">Best Deals</a>
        <a href="all_products.php" class="text-gray-600 hover:text-blue-600">All Products</a>
        <a href="index.php#contact" class="text-gray-600 hover:text-blue-600">Contact</a>

        <div class="cart-icon">
          <a href="cart.php" class="text-gray-600 hover:text-blue-600 relative">
              <i class="fas fa-shopping-cart text-xl"></i>
              <span class="cart-count absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">0</span>
          </a>
        </div>

           <div class="user-actions ml-4 text-sm"> <?php if (isUserLoggedIn()): ?>
                 <span class="text-gray-700">Hi, <?php echo htmlspecialchars($_SESSION['customer']['cust_fname']); ?>!</span>
                 <a href="dashboard.php" class="ml-2 text-blue-600 hover:text-blue-800 font-medium">Dashboard</a>
                 <a href="logout.php" class="ml-2 text-red-600 hover:text-red-800 font-medium">Logout</a>
             <?php else: ?>
                 <a href="login.php" class="text-blue-600 hover:text-blue-800 font-medium">Login / Register</a>
             <?php endif; ?>
         </div>
      </nav>

      <!-- Mobile Cart -->
      <div class="md:hidden flex items-center mr-2">
        <a href="cart.php" class="relative text-xl text-gray-800">
          <i class="fas fa-shopping-cart"></i>
          <span class="cart-count absolute -top-1 -right-2 bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">0</span>
        </a>
      </div>
    </div>
  </header>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-menu-overlay" onclick="closeMobileMenu()"></div>

  <!-- Mobile Menu -->
  <div class="mobile-menu">
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-bold text-gray-900">Menu</h3>
        <button onclick="closeMobileMenu()" class="text-gray-400">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>

    <!-- User Profile Section -->
    <?php if (isUserLoggedIn()): ?>
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <i class="fas fa-user text-white"></i>
        </div>
        <div>
          <h4 class="font-medium text-gray-900"><?php echo htmlspecialchars($_SESSION['customer']['cust_fname'] . ' ' . $_SESSION['customer']['cust_lname']); ?></h4>
          <p class="text-sm text-gray-600"><?php echo htmlspecialchars($_SESSION['customer']['cust_email']); ?></p>
        </div>
      </div>
    </div>
    <?php endif; ?>

    <!-- Navigation Links -->
    <div class="py-2">
      <a href="index.php" class="mobile-menu-item">
        <i class="fas fa-home"></i>
        <span>Home</span>
      </a>
      <a href="all_products.php" class="mobile-menu-item">
        <i class="fas fa-shopping-bag"></i>
        <span>Shop</span>
      </a>
      <a href="cart.php" class="mobile-menu-item">
        <i class="fas fa-shopping-cart"></i>
        <span>Cart</span>
        <span class="ml-auto bg-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center cart-count">0</span>
      </a>
      <button onclick="clearCart(); closeMobileMenu();" class="mobile-menu-item w-full text-left">
        <i class="fas fa-trash-alt text-red-500"></i>
        <span>Clear Cart</span>
      </button>
      <a href="index.php#about" class="mobile-menu-item">
        <i class="fas fa-info-circle"></i>
        <span>About</span>
      </a>
      <a href="index.php#contact" class="mobile-menu-item">
        <i class="fas fa-envelope"></i>
        <span>Contact</span>
      </a>
      <?php if (isUserLoggedIn()): ?>
        <a href="dashboard.php" class="mobile-menu-item">
          <i class="fas fa-tachometer-alt"></i>
          <span>Dashboard</span>
        </a>
        <a href="logout.php" class="mobile-menu-item text-red-600">
          <i class="fas fa-sign-out-alt"></i>
          <span>Logout</span>
        </a>
      <?php else: ?>
        <a href="login.php" class="mobile-menu-item text-blue-600">
          <i class="fas fa-sign-in-alt"></i>
          <span>Login / Register</span>
        </a>
      <?php endif; ?>
    </div>
  </div>

  <!-- Clear Cart Confirmation Modal -->
  <div id="clearCartModal" class="clear-cart-modal">
    <div class="clear-cart-modal-content">
      <div class="clear-cart-modal-icon">
        <i class="fas fa-trash-alt"></i>
      </div>
      <h3>Clear Cart?</h3>
      <p>Are you sure you want to remove all items from your cart? This action cannot be undone.</p>
      <div class="clear-cart-modal-buttons">
        <button class="clear-cart-modal-btn cancel" onclick="closeClearCartModal()">Cancel</button>
        <button class="clear-cart-modal-btn confirm" onclick="confirmClearCart()">Clear All Items</button>
      </div>
    </div>
  </div>

  <main class="py-8 md:py-12"> <div class="container mx-auto px-4">
      <div class="cart-container"> <div class="cart-header mb-6 md:mb-8"> <h1 class="text-2xl md:text-3xl font-bold text-gray-800 flex items-center">
              <i class="fas fa-shopping-cart mr-3 text-blue-600"></i> Your Shopping Cart
          </h1>
            <div id="legacy-alerts" style="display: none;">
                <?php // Display messages if any (legacy method)
                if (isset($_SESSION['error_message'])) {
                    echo '<div class="error-message mt-4">' . $_SESSION['error_message'] . '</div>';
                    echo '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            showAlert("Error", "' . addslashes($_SESSION['error_message']) . '", "error");
                        });
                    </script>';
                    unset($_SESSION['error_message']);
                }
                if (isset($_SESSION['success_message'])) {
                    echo '<div class="success-message mt-4">' . $_SESSION['success_message'] . '</div>';
                    echo '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            showAlert("Success", "' . addslashes($_SESSION['success_message']) . '", "success");
                        });
                    </script>';
                    unset($_SESSION['success_message']);
                }
                ?>
            </div>
        </div>

        <div class="lg:flex lg:gap-8">

          <div class="cart-table-container lg:w-2/3 bg-white rounded-lg shadow-md overflow-hidden mb-6 lg:mb-0">
             <div class="overflow-x-auto"> <table class="cart-table w-full text-sm text-left text-gray-700">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-100 hidden md:table-header-group"> <tr>
                        <th scope="col" class="py-3 px-4">Product</th>
                        <th scope="col" class="py-3 px-4">Details</th>
                        <th scope="col" class="py-3 px-4">Price</th>
                        <th scope="col" class="py-3 px-4">Quantity</th>
                        <th scope="col" class="py-3 px-4">Color</th>
                        <th scope="col" class="py-3 px-4">Installation</th>
                        <th scope="col" class="py-3 px-4">Subtotal</th>
                        <th scope="col" class="py-3 px-4">Action</th>
                    </tr>
                    </thead>
                    <tbody id="cartItems">
                    </tbody>
                </table>
            </div>
          </div>

          <div class="cart-summary lg:w-1/3 bg-white rounded-lg shadow-md p-6 h-fit sticky top-24"> <h2 class="text-xl font-semibold mb-5 border-b border-gray-200 pb-3 text-gray-800">Order Summary</h2>

            <div class="space-y-3 text-sm text-gray-700"> <div class="price-row flex justify-between">
                    <span class="price-label text-gray-600">Products Subtotal:</span>
                    <span class="price-value font-medium text-gray-900">
                        <span class="currency-symbol mr-1"><?= CURRENCY_SYMBOL_NEW ?></span>
                        <span id="productsSubtotal" class="price-amount">0</span>
                    </span>
                </div>

                <div class="country-selector-row pt-3 border-t border-dashed"> <label for="countrySelector" class="block text-sm font-medium text-gray-600 mb-1">Shipping Location:</label>
                    <select id="countrySelector" name="shipping_country" class="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        <option value="">-- Select Location --</option>
                        </select>
                </div>

                <div class="price-row flex justify-between">
                    <span class="price-label text-gray-600">Shipping Fee:</span>
                    <span class="price-value font-medium text-gray-900">
                        <span class="currency-symbol mr-1"><?= CURRENCY_SYMBOL_NEW ?></span>
                        <span id="shippingFee" class="price-amount">0</span>
                    </span>
                </div>

                <div class="price-row installation-fee-row flex justify-between" style="display: none;"> <span class="price-label text-gray-600">Professional Installation:</span>
                    <span class="price-value font-medium text-gray-900">
                        <span class="currency-symbol mr-1"><?= CURRENCY_SYMBOL_NEW ?></span>
                        <span id="installationFeeTotal" class="price-amount">0</span>
                    </span>
                </div>

                 <div class="subtotal-row price-row flex justify-between pt-3 border-t border-dashed">
                      <span class="price-label text-gray-600 font-medium">Subtotal:</span>
                      <span class="price-value font-medium text-gray-900">
                          <span class="currency-symbol mr-1"><?= CURRENCY_SYMBOL_NEW ?></span>
                          <span id="cartSubtotal" class="price-amount">0</span>
                      </span>
                  </div>
            </div> <div class="total-row price-row flex justify-between items-center mt-5 pt-4 border-t">
                <span class="price-label text-base font-semibold text-gray-900">Total:</span>
                <span class="price-value total-price text-lg font-bold text-blue-600">
                    <span class="currency-symbol mr-1"><?= CURRENCY_SYMBOL_NEW ?></span>
                    <span id="cartTotal" class="price-amount">0</span>
                </span>
            </div>

            <div class="payment-info"> <input type="hidden" id="paymentCurrency" value="<?= CURRENCY_CODE ?>">
              <input type="hidden" id="paymentAmount" value="0">
            </div>

            <div class="cart-actions mt-6 space-y-3">
              <button id="proceedToSummaryBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-md transition duration-150 ease-in-out text-center text-base">
                 Proceed to Summary
              </button>
              <button onclick="clearCart()" class="clear-cart-btn w-full justify-center">
                <i class="fas fa-trash-alt"></i>
                <span>Clear All Items</span>
              </button>
            </div>
          </div> </div> </div> </div> </main>

  <div id="summaryModal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2><i class="fas fa-clipboard-check mr-2"></i> Order Summary</h2>
      <div id="summaryContent">
            <div style="text-align:center; padding:20px; display:none;" class="modal-empty-cart">
              <i class="fas fa-shopping-cart text-5xl text-gray-300"></i>
              <h3 class="mt-4 text-xl font-semibold text-gray-700">Your cart is empty</h3>
            </div>
            <div class="summary-items">
              </div>
            <div class="summary-totals" style="display:none;">
              </div>
      </div>
      <div class="modal-actions">
        <button type="button" class="modal-btn cancel-btn bg-gray-200 hover:bg-gray-300 text-gray-800">Continue Shopping</button>
        <button type="button" id="confirmCheckout" class="modal-btn confirm-btn bg-blue-600 hover:bg-blue-700 text-white">Confirm & Checkout</button>
      </div>
    </div>
  </div>

  <?php include 'includes/footer.php'; ?>

  <script>
      const CURRENCY = { code: '<?= CURRENCY_CODE ?>', symbol: '<?= CURRENCY_SYMBOL ?>', locale: '<?= CURRENCY_FORMAT ?>' };
      window.productData = <?php echo json_encode($all_products); ?> || [];
      window.defaultInstallationFee = <?php echo json_encode($default_installation_fee); ?> || 15000;
      window.productInstallationFees = <?php echo json_encode($product_installation_fees); ?> || {};

      // Debug: Log the installation fees
      console.log("PHP - Default installation fee:", <?php echo json_encode($default_installation_fee); ?>);
      console.log("PHP - Product installation fees:", <?php echo json_encode($product_installation_fees); ?>);
      window.productColors = <?php echo json_encode($product_colors); ?> || {};
      window.isUserLoggedIn = <?php echo json_encode(isUserLoggedIn()); ?>;
      window.shippingOptions = <?php echo json_encode($shipping_options_data); ?> || [];
      window.defaultShippingCost = <?php echo json_encode($default_shipping_cost); ?> || 0;
      window.selectedCountryId = <?php echo json_encode($customer_country_id); ?> || null;
      window.baseUploadPath = "/ecom4/assets/uploads/"; // Adjust '/ecom4/' if needed
      window.productVariationsData = {}; // Pass if needed by cart.js
      window.initialShippingFee = <?php echo json_encode($shipping_fee); ?>;
      window.initialCountryId = <?php echo json_encode($shipping_country_id); ?>;


      // Function to handle shipping data persistence
      function handleShippingData() {
          // We don't want to clear shipping data on refresh anymore
          // This allows the shipping selection to persist between page loads

          // Instead, we'll ensure the country selector is properly set
          const countrySelector = document.getElementById('countrySelector');
          if (countrySelector) {
              // Get the saved country ID from localStorage or session
              const savedCountryId = localStorage.getItem('selectedCountryId') || window.initialCountryId || '';

              // If we have a saved country ID, set the selector to that value
              if (savedCountryId && countrySelector.querySelector(`option[value="${savedCountryId}"]`)) {
                  countrySelector.value = savedCountryId;

                  // Update the label to show it's selected
                  const label = document.querySelector('label[for="countrySelector"]');
                  if (label) {
                      label.textContent = 'Shipping Country:';
                  }
              }
          }
      }

      // This function is now called from cart.js to avoid duplicate initialization

       // Helper function moved here for potential use outside cart.js too
       // Make it globally accessible by attaching to window
       window.formatPrice = function(amount) {
           const numericAmount = Number(amount);
           if (isNaN(numericAmount)) return `${CURRENCY.symbol} 0`; // Simplified display
           return numericAmount.toLocaleString(CURRENCY.locale, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
       };

       function updateShipping() {
           const country = document.getElementById('shipping_country').value;
           const subtotal = parseFloat(document.getElementById('subtotal').value);

           fetch('ajax_shipping.php', {
               method: 'POST',
               headers: {
                   'Content-Type': 'application/x-www-form-urlencoded',
               },
               body: `country=${encodeURIComponent(country)}&subtotal=${subtotal}`
           })
           .then(response => response.json())
           .then(data => {
               document.getElementById('shipping_fee').textContent = data.shipping_fee.toFixed(2);
               document.getElementById('total_amount').textContent = data.total.toFixed(2);
           })
           .catch(error => console.error('Error:', error));
       }

       // Country selector event listener is now handled in cart.js
       // This prevents duplicate event listeners and conflicts

       // Function to restore shipping selection from localStorage or session
       window.restoreShippingSelection = function() {
           const countrySelector = document.getElementById('countrySelector');
           if (!countrySelector) return;

           // Try to get the country ID from localStorage or the initial value set by PHP
           const savedCountryId = localStorage.getItem('selectedCountryId') || window.initialCountryId || null;

           if (savedCountryId && countrySelector.querySelector(`option[value="${savedCountryId}"]`)) {
               // Set the dropdown value
               countrySelector.value = savedCountryId;

               // Get the shipping fee from the selected option
               const selectedOption = countrySelector.options[countrySelector.selectedIndex];
               const shippingFee = selectedOption.dataset.cost || window.defaultShippingCost || 0;

               // Store in localStorage
               localStorage.setItem('shippingFee', shippingFee);

               // Update the display if updateCartTotals is available
               if (typeof updateCartTotals === 'function') {
                   updateCartTotals();
               }
           }
       };

       // This function is now called from cart.js to avoid duplicate initialization
  </script>

  <script src="js/script.js"></script>
  <script src="js/cart.js"></script>
  <script>
      document.addEventListener('DOMContentLoaded', function() {

          // --- Country Dropdown Population ---
          const countrySelectorElement = document.getElementById('countrySelector');
          if (countrySelectorElement && window.shippingOptions) {
              console.log("Populating country selector with options:", window.shippingOptions);

              window.shippingOptions.forEach(optionData => {
                  const optElement = document.createElement('option');
                  optElement.value = optionData.id;
                  optElement.textContent = optionData.name;

                  // Ensure cost is properly set as a data attribute
                  if (optionData.cost !== null && optionData.cost !== undefined) {
                      optElement.dataset.cost = optionData.cost;
                      console.log(`Setting data-cost=${optionData.cost} for ${optionData.name}`);
                  } else {
                      optElement.dataset.cost = window.defaultShippingCost;
                      console.log(`Using default cost ${window.defaultShippingCost} for ${optionData.name}`);
                  }

                  countrySelectorElement.appendChild(optElement);
              });

              // Set initial value if available
              const initialCountryId = window.selectedCountryId || localStorage.getItem('selectedCountryId');
              if (initialCountryId && countrySelectorElement.querySelector(`option[value="${initialCountryId}"]`)) {
                  countrySelectorElement.value = initialCountryId;
                  console.log(`Set initial country selection to ${initialCountryId}`);
              }
          } else {
              console.warn("Could not populate country selector:", {
                  element: countrySelectorElement ? "Found" : "Not found",
                  options: window.shippingOptions ? `${window.shippingOptions.length} options` : "No options"
              });
          }

          // Mobile Menu Functions (from dashboard.php)
          window.toggleMobileMenu = function() {
              const menu = document.querySelector('.mobile-menu');
              const overlay = document.querySelector('.mobile-menu-overlay');

              menu.classList.toggle('active');
              overlay.classList.toggle('active');

              // Prevent body scroll when menu is open
              if (menu.classList.contains('active')) {
                  document.body.style.overflow = 'hidden';
              } else {
                  document.body.style.overflow = '';
              }
          };

          window.closeMobileMenu = function() {
              const menu = document.querySelector('.mobile-menu');
              const overlay = document.querySelector('.mobile-menu-overlay');

              menu.classList.remove('active');
              overlay.classList.remove('active');
              document.body.style.overflow = '';
          };

          // Close mobile menu when clicking outside or pressing escape
          document.addEventListener('keydown', function(e) {
              if (e.key === 'Escape') {
                  closeMobileMenu();
              }
          });

          // Auto-close mobile menu when resizing to desktop
          window.addEventListener('resize', function() {
              if (window.innerWidth >= 768) { // md breakpoint
                  closeMobileMenu();
              }
          });
      });


      // Add this function after your existing script section


// Remove the duplicate restoreShippingSelection function and the redundant reset code
// Remove these lines:
// const countrySelector = document.getElementById('countrySelector');
// if (countrySelector) {
//     countrySelector.value = '';
//     document.querySelector('label[for="countrySelector"]').textContent = 'Shipping Country:';
// }

      // Modern Alert System
      function showAlert(title, message, type = 'info', duration = 5000) {
          const alertContainer = document.getElementById('alertContainer');
          const alertId = 'alert-' + Date.now();

          // Create alert element
          const alert = document.createElement('div');
          alert.className = `alert alert-${type}`;
          alert.id = alertId;

          // Create alert content
          alert.innerHTML = `
              <div class="alert-icon">
                  ${getAlertIcon(type)}
              </div>
              <div class="alert-content">
                  <div class="alert-title">${title}</div>
                  <div class="alert-message">${message}</div>
              </div>
              <button class="alert-close" onclick="closeAlert('${alertId}')">&times;</button>
              <div class="alert-progress"></div>
          `;

          // Add to container
          alertContainer.appendChild(alert);

          // Trigger animation
          setTimeout(() => {
              alert.classList.add('show');

              // Animate progress bar
              const progressBar = alert.querySelector('.alert-progress');
              progressBar.style.animation = `progress-bar ${duration/1000}s linear forwards`;
              progressBar.style.transformOrigin = 'left';

              // Auto close after duration
              setTimeout(() => {
                  closeAlert(alertId);
              }, duration);
          }, 10);

          // Add animation keyframes if not already added
          if (!document.getElementById('alert-keyframes')) {
              const style = document.createElement('style');
              style.id = 'alert-keyframes';
              style.textContent = `
                  @keyframes progress-bar {
                      0% { transform: scaleX(1); }
                      100% { transform: scaleX(0); }
                  }
              `;
              document.head.appendChild(style);
          }

          return alertId;
      }

      function closeAlert(alertId) {
          const alert = document.getElementById(alertId);
          if (!alert) return;

          alert.classList.remove('show');

          // Remove after animation completes
          setTimeout(() => {
              if (alert && alert.parentNode) {
                  alert.parentNode.removeChild(alert);
              }
          }, 500);
      }

      function getAlertIcon(type) {
          switch (type) {
              case 'success':
                  return '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>';
              case 'error':
                  return '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>';
              case 'warning':
                  return '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>';
              case 'info':
              default:
                  return '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
          }
      }

      // Example of how to use the alert system:
      // showAlert('Success', 'Your item has been added to cart!', 'success');
      // showAlert('Error', 'Something went wrong!', 'error');
      // showAlert('Warning', 'This action cannot be undone!', 'warning');
      // showAlert('Info', 'New features available!', 'info');
  </script>
<form id="checkoutForm" action="start_checkout.php" method="POST" style="display: none;">
    <input type="hidden" name="verified_total" id="verifiedTotal">
    <input type="hidden" name="verified_shipping" id="verifiedShipping">
    <input type="hidden" name="verified_installation" id="verifiedInstallation">
    <input type="hidden" name="products_subtotal" id="verifiedProductsTotal">
</form>


</body>
</html>
<?php ob_end_flush(); ?>