<?php
/**
 * Path Detection Helper
 * Shows the correct URLs for your API installation
 */

// Detect the correct paths
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$script_dir = dirname($_SERVER['SCRIPT_NAME']);

echo "<h1>API Path Detection</h1>";
echo "<p>This script helps you find the correct URLs for your API installation.</p>";

echo "<h2>Detected Paths:</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<strong>Protocol:</strong> {$protocol}<br>";
echo "<strong>Host:</strong> {$host}<br>";
echo "<strong>Script Directory:</strong> {$script_dir}<br>";
echo "</div>";

echo "<h2>Your API URLs:</h2>";
echo "<div style='background: #e9ecef; padding: 20px; border-radius: 5px; margin: 20px 0;'>";

$base_url = $protocol . '://' . $host . $script_dir;

echo "<strong>API Base URL:</strong> <a href='{$base_url}/v1/' target='_blank'>{$base_url}/v1/</a><br>";
echo "<strong>API Documentation:</strong> <a href='{$base_url}/v1/docs' target='_blank'>{$base_url}/v1/docs</a><br>";
echo "<strong>API Test:</strong> <a href='{$base_url}/test.php' target='_blank'>{$base_url}/test.php</a><br>";
echo "<strong>Installation:</strong> <a href='{$base_url}/install.php' target='_blank'>{$base_url}/install.php</a><br>";
echo "<strong>Migration:</strong> <a href='{$base_url}/run_migration.php' target='_blank'>{$base_url}/run_migration.php</a><br>";

// Generate monitor token
require_once __DIR__ . '/config/config.php';
$monitor_token = md5(JWT_SECRET . date('Y-m-d'));
echo "<strong>Monitor Dashboard:</strong> <a href='{$base_url}/monitor/dashboard.php?token={$monitor_token}' target='_blank'>{$base_url}/monitor/dashboard.php?token={$monitor_token}</a><br>";

echo "</div>";

echo "<h2>Test API Endpoints:</h2>";
echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";

$test_endpoints = [
    'API Root' => $base_url . '/v1/',
    'Products' => $base_url . '/v1/products?limit=5',
    'Categories' => $base_url . '/v1/categories',
    'App Settings' => $base_url . '/v1/settings/app',
    'Shipping Countries' => $base_url . '/v1/shipping/countries'
];

foreach ($test_endpoints as $name => $url) {
    echo "<strong>{$name}:</strong> <a href='{$url}' target='_blank'>{$url}</a><br>";
}

echo "</div>";

echo "<h2>For Flutter Development:</h2>";
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<strong>Local Development Base URL:</strong><br>";
echo "<code>static const String baseUrl = '{$base_url}/v1';</code><br><br>";
echo "<strong>Production Base URL (replace with your domain):</strong><br>";
echo "<code>static const String baseUrl = 'https://yourdomain.com{$script_dir}/v1';</code>";
echo "</div>";

echo "<h2>For Postman Collection:</h2>";
echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<strong>Update the base_url variable in Postman to:</strong><br>";
echo "<code>{$base_url}/v1</code>";
echo "</div>";

echo "<hr>";
echo "<p><small>You can delete this file after noting down the correct URLs.</small></p>";
?>
