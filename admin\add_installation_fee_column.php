<?php
require_once('inc/config.php');

try {
    // Check if the installation_fee column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM tbl_product LIKE 'installation_fee'");
    if($stmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $pdo->exec("ALTER TABLE tbl_product ADD COLUMN installation_fee INT NOT NULL DEFAULT 15000");
        echo "Installation fee column added successfully to tbl_product table.";
    } else {
        echo "Installation fee column already exists in tbl_product table.";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
