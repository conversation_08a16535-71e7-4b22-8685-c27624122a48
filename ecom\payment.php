<?php
// Include session configuration before starting session
include("session_config.php");
session_start();

// Include necessary files
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

// Verify user is logged in using persistent login system
if (!isUserLoggedIn()) {
    header("Location: login.php");
    exit;
}

// Verify tx_ref is provided
if (!isset($_GET['tx_ref'])) {
    header("Location: cart.php");
    exit;
}

$tx_ref = $_GET['tx_ref'];

// Fetch order details
try {
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE tx_ref = ?");
    $stmt->execute([$tx_ref]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    // Log order details for debugging
    error_log("Order details: " . json_encode($order));
} catch (PDOException $e) {
    error_log("Error fetching order: " . $e->getMessage());
    $_SESSION['error_message'] = "Error retrieving order details.";
    header("Location: cart.php");
    exit;
}

if (!$order) {
    $_SESSION['error_message'] = "Invalid order reference.";
    header("Location: cart.php");
    exit;
}

// Get customer data
$customer = $_SESSION['customer'];

// Constants
define('CURRENCY_CODE', 'TZS');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - SMART LIFE</title>
    <link rel="icon" href="/ecom4/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css" />
    <script src="https://checkout.flutterwave.com/v3.js"></script>
    <style>
        :root {
            --primary-color: #0066FF;
            --secondary-color: #1E293B;
            --background-color: #F8FAFC;
            --text-color: #1E293B;
            --border-color: #E2E8F0;
            --hover-color: #0047B3;
            --shadow-color: rgba(0, 102, 255, 0.1);
            --success-color: #10B981;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            background-image: 
                radial-gradient(circle at 100% 100%, rgba(0,102,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 0% 0%, rgba(0,102,255,0.05) 0%, transparent 50%);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .payment-container {
            max-width: 900px;
            margin: 40px auto;
            padding: 40px;
            background-color: white;
            border-radius: 24px;
            box-shadow: 
                0 10px 30px var(--shadow-color),
                0 0 0 1px rgba(0,0,0,0.02);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .payment-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--primary-color), #3385FF);
            border-radius: 24px 24px 0 0;
        }

        .payment-header {
            grid-column: 1 / -1;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .payment-header h1 {
            color: var(--secondary-color);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .payment-header h1 i {
            color: var(--primary-color);
            font-size: 28px;
        }

        .payment-header p {
            color: #64748B;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #F1F5F9;
            border-radius: 100px;
        }

        .payment-details {
            padding: 30px;
            background-color: #F8FAFC;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            transition: transform 0.3s ease;
        }

        .payment-details:hover {
            transform: translateY(-2px);
        }

        .payment-row {
            display: flex;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
            font-size: 15px;
        }

        .payment-row span:first-child {
            color: #64748B;
        }

        .payment-row:last-child {
            border-bottom: none;
            font-weight: 700;
            font-size: 20px;
            margin-top: 15px;
            padding-top: 20px;
            border-top: 2px solid var(--border-color);
            color: var(--primary-color);
        }

        .card-preview {
            background: linear-gradient(135deg, var(--primary-color), #3385FF);
            border-radius: 20px;
            padding: 30px;
            color: white;
            position: relative;
            height: 220px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            transform-style: preserve-3d;
            transition: all 0.5s ease;
            box-shadow: 0 20px 40px rgba(0,102,255,0.2);
            overflow: hidden;
        }

        .card-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
            border-radius: 20px;
        }

        .card-preview:hover {
            transform: rotateY(10deg) rotateX(5deg) translateZ(10px);
            box-shadow: 
                20px 20px 60px rgba(0,102,255,0.2),
                -20px -20px 60px rgba(255,255,255,0.1);
        }

        .card-chip {
            width: 50px;
            height: 40px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 8px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .card-chip::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -5px;
            right: -5px;
            height: 1px;
            background: rgba(0,0,0,0.2);
        }

        .card-chip::after {
            content: '';
            position: absolute;
            left: 50%;
            top: -5px;
            bottom: -5px;
            width: 1px;
            background: rgba(0,0,0,0.2);
        }

        .card-number {
            font-size: 24px;
            letter-spacing: 3px;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-name {
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 2px;
            opacity: 0.9;
        }

        .payment-actions {
            grid-column: 1 / -1;
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .payment-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 14px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }

        .payment-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: translateX(-100%);
            transition: transform 0.5s ease;
        }

        .payment-button:hover {
            background-color: var(--hover-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color);
        }

        .payment-button:hover::before {
            transform: translateX(100%);
        }

        .cancel-button {
            background-color: #F1F5F9;
            color: var(--secondary-color);
        }

        .cancel-button:hover {
            background-color: #E2E8F0;
        }

        .secure-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: var(--success-color);
            color: white;
            border-radius: 100px;
            font-size: 12px;
            position: absolute;
            top: -10px;
            right: 20px;
        }

        .card-title {
            position: absolute;
            top: 30px;
            right: 30px;
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 1px;
            opacity: 0.9;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .payment-container {
                grid-template-columns: 1fr;
                padding: 30px;
                margin: 20px;
                gap: 30px;
            }

            .payment-header {
                padding-top: 20px;
            }

            .secure-badge {
                position: static;
                margin-bottom: 15px;
                display: inline-flex;
            }

            .payment-header h1 {
                font-size: 26px;
            }

            .payment-button {
                padding: 14px 30px;
                font-size: 15px;
                width: 100%;
                justify-content: center;
            }

            .payment-actions {
                flex-direction: column;
            }

            .card-preview {
                order: -1;
            }
        }

        @keyframes cardFloat {
            0%, 100% {
                transform: translateY(0) rotateY(0) rotateX(0);
            }
            50% {
                transform: translateY(-10px) rotateY(5deg) rotateX(2deg);
            }
        }

        .floating-card {
            animation: cardFloat 4s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="payment-header">
            <div class="secure-badge">
                <i class="fas fa-shield-alt"></i>
                Secure Payment
            </div>
            <h1><i class="fas fa-lock"></i> Complete Your Payment</h1>
            <p><i class="fas fa-receipt"></i> Order Reference: <?php echo htmlspecialchars($tx_ref); ?></p>
        </div>

        <div class="payment-details">
            <?php
            // Get the total amount
            $total_amount = isset($order['total_amount']) ? $order['total_amount'] : 0;

            // Get the shipping fee
            $shipping_fee = isset($order['shipping_fee']) ? $order['shipping_fee'] : 0;

            // Get the installation fee
            $installation_fee = isset($order['installation_fee_total']) ? $order['installation_fee_total'] : 0;

            // Calculate the products subtotal
            $products_subtotal = $total_amount - $shipping_fee - $installation_fee;
            ?>
            <div class="payment-row">
                <span><i class="fas fa-shopping-cart"></i> Products Subtotal</span>
                <span>TSH <?php echo number_format($products_subtotal, 0); ?></span>
            </div>
            <div class="payment-row">
                <span><i class="fas fa-truck"></i> Shipping Fee</span>
                <span>TSH <?php echo number_format($shipping_fee, 0); ?></span>
            </div>
            <?php if ($installation_fee > 0): ?>
            <div class="payment-row">
                <span><i class="fas fa-tools"></i> Installation Fee</span>
                <span>TSH <?php echo number_format($installation_fee, 0); ?></span>
            </div>
            <?php endif; ?>
            <div class="payment-row">
                <span><i class="fas fa-tag"></i> Total Amount</span>
                <span>TSH <?php echo number_format($total_amount, 0); ?></span>
            </div>
        </div>

        <div class="card-preview floating-card">
            <div class="card-chip"></div>
            <div class="card-title">SMART LIFE TZ</div>
            <div class="card-number">•••• •••• •••• ••••</div>
            <div class="card-name">
                <i class="fas fa-wifi fa-rotate-90" style="margin-right: 8px; opacity: 0.8;"></i>
                SECURE PAYMENT
            </div>
        </div>

        <div class="payment-actions">
            <button id="payButton" class="payment-button">
                <i class="fas fa-lock"></i>
                Proceed to Payment
            </button>
            <a href="cart.php" class="payment-button cancel-button">
                <i class="fas fa-arrow-left"></i>
                Return to Cart
            </a>
        </div>
    </div>

    <script>
        document.getElementById('payButton').addEventListener('click', function() {
            makePayment();
        });

        function makePayment() {
            // Get the total amount
            const totalAmount = <?php echo json_encode($total_amount); ?>;

            // Get customer details
            const customerEmail = <?php echo json_encode($customer['cust_email'] ?? ''); ?>;
            const customerPhone = <?php echo json_encode($order['phone'] ?? ''); ?>;
            const customerName = <?php echo json_encode(($order['firstname'] ?? '') . ' ' . ($order['lastname'] ?? '')); ?>;

            console.log("Payment details:", {
                tx_ref: <?php echo json_encode($tx_ref); ?>,
                amount: totalAmount,
                customer: {
                    email: customerEmail,
                    phone: customerPhone,
                    name: customerName
                }
            });

            // Add debug logging
            if (!customerEmail || !customerPhone || !customerName) {
                console.error("Missing required customer information:", {
                    email: customerEmail,
                    phone: customerPhone,
                    name: customerName
                });
                alert("Please ensure all customer information is provided");
                return;
            }

            if (!totalAmount || totalAmount <= 0) {
                console.error("Invalid amount:", totalAmount);
                alert("Invalid payment amount");
                return;
            }

            FlutterwaveCheckout({
                public_key: "FLWPUBK_TEST-02b9b5fc6406bd4a41c3ff141cc45e93-X",
                tx_ref: "<?php echo $tx_ref; ?>",
                amount: totalAmount,
                currency: "<?php echo CURRENCY_CODE; ?>",
                payment_options: "card, mobilemoney, ussd, banktransfer, account, applepay",
                customer: {
                    email: customerEmail,
                    phone_number: customerPhone,
                    name: customerName
                },
                callback: function(data) {
                    // Ensure we have a valid transaction reference
                    if (data && data.tx_ref) {
                        window.location.href = "payment_verify.php?tx_ref=" + data.tx_ref;
                        // Clear localStorage shipping data
                        localStorage.removeItem('selectedCountryId');
                        localStorage.removeItem('shippingFee');
                    } else {
                        alert("Payment verification failed. Please contact support.");
                        window.location.href = "cart.php";
                    }
                },
                onclose: function() {
                    alert("Payment cancelled - your order has been saved");
                    window.location.href = "cart.php";
                },
                customizations: {
                    title: "SMART LIFE Order",
                    description: "Payment for your order #<?php echo $tx_ref; ?>",
                    logo: "https://smartlifetz.com/assets/uploads/logo.png"
                },
                meta: {
                    order_id: "<?php echo $tx_ref; ?>"
                }
            });
        }
    </script>
</body>
</html>
