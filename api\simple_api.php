<?php
/**
 * Simple API Router
 * Simplified version without middleware for testing
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

// Set JSON header immediately
header('Content-Type: application/json');

// Simple CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    
    // Simple path cleaning
    $clean_path = $path;
    if (strpos($path, '/ecom/api/v1') === 0) {
        $clean_path = substr($path, strlen('/ecom/api/v1'));
    } elseif (strpos($path, '/api/v1') === 0) {
        $clean_path = substr($path, strlen('/api/v1'));
    }
    
    $clean_path = trim($clean_path, '/');
    $segments = $clean_path ? explode('/', $clean_path) : [];
    $endpoint = $segments[0] ?? '';
    
    // Get input for POST/PUT requests
    $input = [];
    if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
        $json = file_get_contents('php://input');
        $input = json_decode($json, true) ?: [];
    }
    
    // Route to endpoints
    switch ($endpoint) {
        case '':
            // API root
            $response = [
                'status' => 'success',
                'message' => 'Welcome to Ecommerce API',
                'timestamp' => date('c'),
                'api_version' => API_VERSION,
                'data' => [
                    'name' => 'Ecommerce API',
                    'version' => API_VERSION,
                    'endpoints' => [
                        'auth' => 'Authentication endpoints',
                        'products' => 'Product catalog',
                        'categories' => 'Product categories',
                        'cart' => 'Shopping cart',
                        'orders' => 'Order management',
                        'users' => 'User management',
                        'search' => 'Product search',
                        'wishlist' => 'User wishlist',
                        'shipping' => 'Shipping information',
                        'settings' => 'Application settings',
                        'admin' => 'Admin operations'
                    ],
                    'documentation' => dirname($_SERVER['SCRIPT_NAME']) . '/v1/docs'
                ]
            ];
            break;
            
        case 'settings':
            // Settings endpoint
            $sub_path = $segments[1] ?? '';
            if ($sub_path === 'app' || $sub_path === '') {
                $response = [
                    'status' => 'success',
                    'message' => 'App settings retrieved successfully',
                    'timestamp' => date('c'),
                    'api_version' => API_VERSION,
                    'data' => [
                        'name' => 'Ecommerce Store',
                        'logo' => null,
                        'favicon' => null,
                        'footer_text' => 'Welcome to our online store',
                        'footer_copyright' => '© 2024 Ecommerce Store. All rights reserved.',
                        'currency' => DEFAULT_CURRENCY,
                        'installation_fee' => (int)DEFAULT_INSTALLATION_FEE,
                        'version' => API_VERSION,
                        'contact_email' => '<EMAIL>',
                        'contact_phone' => '+255 123 456 789'
                    ]
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'Settings sub-endpoint not found: ' . $sub_path,
                    'timestamp' => date('c'),
                    'api_version' => API_VERSION
                ];
                http_response_code(404);
            }
            break;
            
        case 'products':
            // Products endpoint
            try {
                $limit = min((int)($_GET['limit'] ?? 10), 50);
                $page = max(1, (int)($_GET['page'] ?? 1));
                $offset = ($page - 1) * $limit;
                
                $products = $pdo->prepare("
                    SELECT p_id, p_name, p_old_price, p_current_price, p_featured_photo, p_short_description
                    FROM tbl_product 
                    WHERE p_is_active = 1 
                    ORDER BY p_id DESC 
                    LIMIT ? OFFSET ?
                ");
                $products->execute([$limit, $offset]);
                $product_list = $products->fetchAll();
                
                $total = $pdo->query("SELECT COUNT(*) FROM tbl_product WHERE p_is_active = 1")->fetchColumn();
                
                $response = [
                    'status' => 'success',
                    'message' => 'Products retrieved successfully',
                    'timestamp' => date('c'),
                    'api_version' => API_VERSION,
                    'data' => [
                        'products' => $product_list,
                        'pagination' => [
                            'current_page' => $page,
                            'per_page' => $limit,
                            'total_items' => (int)$total,
                            'total_pages' => ceil($total / $limit)
                        ]
                    ]
                ];
            } catch (Exception $e) {
                $response = [
                    'status' => 'error',
                    'message' => 'Database error: ' . $e->getMessage(),
                    'timestamp' => date('c'),
                    'api_version' => API_VERSION
                ];
                http_response_code(500);
            }
            break;
            
        case 'categories':
            // Categories endpoint
            try {
                $categories = $pdo->query("
                    SELECT tcat_id, tcat_name, tcat_slug 
                    FROM tbl_top_category 
                    WHERE show_on_menu = 1 
                    ORDER BY tcat_name
                ")->fetchAll();
                
                $response = [
                    'status' => 'success',
                    'message' => 'Categories retrieved successfully',
                    'timestamp' => date('c'),
                    'api_version' => API_VERSION,
                    'data' => [
                        'categories' => $categories,
                        'total' => count($categories)
                    ]
                ];
            } catch (Exception $e) {
                $response = [
                    'status' => 'error',
                    'message' => 'Database error: ' . $e->getMessage(),
                    'timestamp' => date('c'),
                    'api_version' => API_VERSION
                ];
                http_response_code(500);
            }
            break;
            
        default:
            $response = [
                'status' => 'error',
                'message' => 'Endpoint not found: ' . $endpoint,
                'timestamp' => date('c'),
                'api_version' => API_VERSION
            ];
            http_response_code(404);
    }
    
    // Output response
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Internal server error: ' . $e->getMessage(),
        'timestamp' => date('c'),
        'api_version' => API_VERSION
    ], JSON_PRETTY_PRINT);
}
?>
