<?php
/**
 * Test Cart Synchronization
 * This script tests cart synchronization between JavaScript and PHP sessions
 */

// Include session configuration before starting session
include("session_config.php");
session_start();

// Include database connection and functions
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

// Check if user is logged in
$is_logged_in = isUserLoggedIn();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Synchronization Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
            cursor: pointer;
            border: none;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        .test-section { 
            border: 1px solid #ddd; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 5px; 
        }
        .code { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .test-result { 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 3px; 
            border-left: 4px solid #007bff; 
            background: #f8f9fa; 
        }
        .test-result.pass { border-left-color: #28a745; background: #d4edda; }
        .test-result.fail { border-left-color: #dc3545; background: #f8d7da; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Cart Synchronization Test</h1>
        
        <div class="test-section">
            <h2>Authentication Status</h2>
            <?php if ($is_logged_in): ?>
                <div class="status success">✅ User is logged in</div>
                <p><strong>Customer:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_fname'] . ' ' . $_SESSION['customer']['cust_lname']) ?></p>
            <?php else: ?>
                <div class="status warning">⚠️ User is not logged in</div>
                <p>Cart synchronization works for both logged in and guest users.</p>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>Current Cart Status</h2>
            <div class="grid">
                <div>
                    <h3>PHP Session Cart</h3>
                    <div class="code" id="php-cart">
                        <?php 
                        if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
                            echo "Items: " . count($_SESSION['cart']) . "\n";
                            foreach ($_SESSION['cart'] as $key => $item) {
                                echo "Key: $key\n";
                                echo "Product ID: " . ($item['product_id'] ?? 'N/A') . "\n";
                                echo "Name: " . ($item['name'] ?? 'N/A') . "\n";
                                echo "Quantity: " . ($item['quantity'] ?? 'N/A') . "\n";
                                echo "Price: " . ($item['price'] ?? 'N/A') . "\n";
                                echo "---\n";
                            }
                        } else {
                            echo "Cart is empty";
                        }
                        ?>
                    </div>
                </div>
                <div>
                    <h3>JavaScript localStorage Cart</h3>
                    <div class="code" id="js-cart">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Cart Synchronization Tests</h2>
            <div>
                <button class="btn btn-success" onclick="addTestItem()">Add Test Item to Cart</button>
                <button class="btn btn-warning" onclick="syncCartToSession()">Sync Cart to Session</button>
                <button class="btn btn-danger" onclick="clearCart()">Clear Cart</button>
                <button class="btn" onclick="refreshStatus()">Refresh Status</button>
            </div>
            
            <div id="test-results" style="margin-top: 20px;">
                <h3>Test Results</h3>
                <div id="results-container">
                    <p>Run tests to see results here.</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Checkout Flow Test</h2>
            <p>Test the complete checkout flow to verify cart synchronization:</p>
            <div>
                <button class="btn btn-warning" onclick="testCheckoutFlow()">Test Checkout Flow</button>
                <div id="checkout-results" style="margin-top: 15px;"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Debug Information</h2>
            <div class="code">
                <strong>Session ID:</strong> <?= htmlspecialchars(session_id()) ?><br>
                <strong>Session Name:</strong> <?= session_name() ?><br>
                <strong>Cart Session Key:</strong> <?= isset($_SESSION['cart']) ? 'Set' : 'Not set' ?><br>
                <strong>Cart Count (PHP):</strong> <?= isset($_SESSION['cart']) ? count($_SESSION['cart']) : 0 ?><br>
                <strong>Cart Count (JS):</strong> <span id="js-cart-count">Loading...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Quick Actions</h2>
            <a href="cart.php" class="btn">Go to Cart</a>
            <a href="test_order_flow.php" class="btn">Order Flow Test</a>
            <a href="test_auth_status.php" class="btn">Auth Status Test</a>
            <?php if ($is_logged_in): ?>
                <a href="logout.php" class="btn btn-danger">Logout</a>
            <?php else: ?>
                <a href="login.php" class="btn btn-success">Login</a>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        let testResults = [];
        
        function addTestResult(test, result, message) {
            testResults.push({ test, result, message, time: new Date().toLocaleTimeString() });
            updateTestResults();
        }
        
        function updateTestResults() {
            const container = document.getElementById('results-container');
            container.innerHTML = '';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.result}`;
                div.innerHTML = `<strong>[${result.time}] ${result.test}:</strong> ${result.message}`;
                container.appendChild(div);
            });
        }
        
        function getCartFromLocalStorage() {
            try {
                const cartData = localStorage.getItem('cart');
                return cartData ? JSON.parse(cartData) : [];
            } catch (e) {
                console.error("Error reading cart from localStorage:", e);
                return [];
            }
        }
        
        function saveCartToLocalStorage(cart) {
            try {
                localStorage.setItem('cart', JSON.stringify(cart));
                updateCartDisplay();
            } catch (e) {
                console.error("Error saving cart to localStorage:", e);
            }
        }
        
        function updateCartDisplay() {
            const cart = getCartFromLocalStorage();
            const jsCartDiv = document.getElementById('js-cart');
            const jsCartCount = document.getElementById('js-cart-count');
            
            if (cart.length > 0) {
                let cartText = `Items: ${cart.length}\n`;
                cart.forEach((item, index) => {
                    cartText += `Item ${index + 1}:\n`;
                    cartText += `Product ID: ${item.product_id || item.id || 'N/A'}\n`;
                    cartText += `Name: ${item.name || 'N/A'}\n`;
                    cartText += `Quantity: ${item.quantity || 'N/A'}\n`;
                    cartText += `Price: ${item.price || 'N/A'}\n`;
                    cartText += `---\n`;
                });
                jsCartDiv.textContent = cartText;
            } else {
                jsCartDiv.textContent = 'Cart is empty';
            }
            
            jsCartCount.textContent = cart.length;
        }
        
        function addTestItem() {
            const cart = getCartFromLocalStorage();
            const testItem = {
                id: 1,
                product_id: 1,
                name: 'Test Product',
                price: 1000,
                quantity: 1,
                photo: 'test.jpg',
                color_id: null,
                color_name: null,
                variation_id: null,
                installation: false
            };
            
            // Check if item already exists
            const existingIndex = cart.findIndex(item => item.id === testItem.id || item.product_id === testItem.product_id);
            if (existingIndex >= 0) {
                cart[existingIndex].quantity += 1;
                addTestResult('Add Item', 'pass', 'Test item quantity increased');
            } else {
                cart.push(testItem);
                addTestResult('Add Item', 'pass', 'Test item added to localStorage');
            }
            
            saveCartToLocalStorage(cart);
        }
        
        function syncCartToSession() {
            const cart = getCartFromLocalStorage();
            
            // Convert cart to PHP session format
            const cartForPhpSession = {};
            cart.forEach((item, index) => {
                const key = `${item.product_id || item.id}-${item.variation_id || 0}`;
                cartForPhpSession[key] = {
                    product_id: item.product_id || item.id,
                    variation_id: item.variation_id || null,
                    quantity: Number(item.quantity || 1),
                    price: parseFloat(item.price || 0),
                    name: item.name || '',
                    photo: item.photo || '',
                    color_id: item.color_id || null,
                    color_name: item.color_name || null,
                    installation: item.installation ? 1 : 0
                };
            });
            
            fetch('sync_cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ cart: cartForPhpSession })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    addTestResult('Sync Cart', 'pass', 'Cart synchronized successfully with PHP session');
                    setTimeout(() => location.reload(), 1000); // Reload to show updated PHP cart
                } else {
                    addTestResult('Sync Cart', 'fail', 'Failed to sync cart: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error syncing cart:', error);
                addTestResult('Sync Cart', 'fail', 'Error syncing cart: ' + error.message);
            });
        }
        
        function clearCart() {
            localStorage.setItem('cart', JSON.stringify([]));
            updateCartDisplay();
            
            // Also clear session cart
            fetch('sync_cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ cart: {} })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    addTestResult('Clear Cart', 'pass', 'Cart cleared from both localStorage and PHP session');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    addTestResult('Clear Cart', 'fail', 'Failed to clear session cart: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error clearing cart:', error);
                addTestResult('Clear Cart', 'fail', 'Error clearing cart: ' + error.message);
            });
        }
        
        function testCheckoutFlow() {
            const resultsDiv = document.getElementById('checkout-results');
            resultsDiv.innerHTML = '<p>⏳ Testing checkout flow...</p>';
            
            const cart = getCartFromLocalStorage();
            if (cart.length === 0) {
                resultsDiv.innerHTML = '<div class="status error">❌ Cart is empty. Add a test item first.</div>';
                return;
            }
            
            // Test checkout handler
            const testData = {
                products_subtotal: 1000,
                shipping_fee: 0,
                installation_fee: 0,
                final_total: 1000,
                country_id: 1
            };
            
            fetch('checkout_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Checkout response:', data);
                
                if (data.status === 'error' && data.message === 'Cart is empty') {
                    resultsDiv.innerHTML = `
                        <div class="status error">❌ Checkout Failed: Cart is empty</div>
                        <p><strong>Issue:</strong> Cart synchronization problem detected!</p>
                        <p><strong>Solution:</strong> Click "Sync Cart to Session" first, then try again.</p>
                    `;
                    addTestResult('Checkout Flow', 'fail', 'Cart synchronization issue - session cart is empty');
                } else if (data.status === 'success') {
                    resultsDiv.innerHTML = `
                        <div class="status success">✅ Checkout Flow Working</div>
                        <p><strong>Order ID:</strong> ${data.order_id}</p>
                        <p><strong>Transaction Ref:</strong> ${data.tx_ref}</p>
                        <p>Cart synchronization is working correctly!</p>
                    `;
                    addTestResult('Checkout Flow', 'pass', 'Checkout completed successfully');
                } else {
                    resultsDiv.innerHTML = `
                        <div class="status warning">⚠️ Checkout Response: ${data.message}</div>
                        <p>Check console for more details.</p>
                    `;
                    addTestResult('Checkout Flow', 'fail', 'Unexpected response: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error testing checkout:', error);
                resultsDiv.innerHTML = `
                    <div class="status error">❌ Checkout Test Failed</div>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
                addTestResult('Checkout Flow', 'fail', 'Network error: ' + error.message);
            });
        }
        
        function refreshStatus() {
            location.reload();
        }
        
        // Initialize display on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCartDisplay();
            
            console.log('Cart Sync Test initialized');
            console.log('PHP logged in:', <?php echo json_encode($is_logged_in); ?>);
            console.log('Session cart:', <?php echo json_encode($_SESSION['cart'] ?? []); ?>);
            console.log('localStorage cart:', getCartFromLocalStorage());
        });
    </script>
</body>
</html>
