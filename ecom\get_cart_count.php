<?php
// get_cart_count.php
// Include session configuration before starting session
include("session_config.php");
session_start();

// Check for cart items
$count = 0;

// Check for cart array (new format)
if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
    $count = array_sum(array_column($_SESSION['cart'], 'quantity'));
}
// Check for cart_p_id array (old format)
elseif (isset($_SESSION['cart_p_id']) && is_array($_SESSION['cart_p_id'])) {
    $count = count($_SESSION['cart_p_id']);
}

// Return just the number for direct use in HTML
echo $count;
?>