<?php
/**
 * Debug JSON Response Issues
 * Investigates why the API is returning invalid JSON
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>🔍 JSON Response Debug</h1>";

$test_email = '<EMAIL>';
$test_password = 'changawa';

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🧪 Debugging JSON Response Issues</h2>";
echo "<p>This will help identify why the API is returning invalid JSON.</p>";
echo "</div>";

// Test 1: Raw cURL response
echo "<h2>1. Raw API Response Analysis</h2>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/ecom/api/v1/auth/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'email' => $test_email,
    'password' => $test_password
]));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$error = curl_error($ch);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
echo "<p><strong>Content Type:</strong> {$content_type}</p>";
echo "<p><strong>Response Length:</strong> " . strlen($response) . " bytes</p>";

if ($error) {
    echo "<p style='color: red;'>❌ cURL Error: {$error}</p>";
} else {
    echo "<h3>📋 Raw Response (first 1000 characters):</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;'>";
    echo htmlspecialchars(substr($response, 0, 1000));
    if (strlen($response) > 1000) {
        echo "\n... (truncated)";
    }
    echo "</pre>";
    
    echo "<h3>🔍 Character Analysis:</h3>";
    echo "<p><strong>First 10 characters (hex):</strong> ";
    for ($i = 0; $i < min(10, strlen($response)); $i++) {
        echo sprintf("%02x ", ord($response[$i]));
    }
    echo "</p>";
    
    echo "<p><strong>First character:</strong> '" . htmlspecialchars($response[0] ?? '') . "' (ASCII: " . ord($response[0] ?? '') . ")</p>";
    
    // Check if response starts with HTML
    if (strpos($response, '<') === 0) {
        echo "<p style='color: red;'>❌ Response starts with HTML, not JSON!</p>";
    } elseif (strpos($response, '{') === 0) {
        echo "<p style='color: green;'>✅ Response starts with JSON bracket</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Response starts with unexpected character</p>";
    }
    
    // Try to parse JSON
    $json_data = json_decode($response, true);
    $json_error = json_last_error();
    
    if ($json_error === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✅ JSON is valid</p>";
        echo "<h3>📊 Parsed JSON:</h3>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
        echo json_encode($json_data, JSON_PRETTY_PRINT);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>❌ JSON Parse Error: " . json_last_error_msg() . "</p>";
        echo "<p><strong>JSON Error Code:</strong> {$json_error}</p>";
    }
}

// Test 2: Check for PHP errors in the response
echo "<h2>2. PHP Error Detection</h2>";

if (strpos($response, 'Fatal error') !== false) {
    echo "<p style='color: red;'>❌ Fatal PHP error detected in response</p>";
} elseif (strpos($response, 'Warning') !== false) {
    echo "<p style='color: orange;'>⚠️ PHP warning detected in response</p>";
} elseif (strpos($response, 'Notice') !== false) {
    echo "<p style='color: orange;'>⚠️ PHP notice detected in response</p>";
} elseif (strpos($response, '<br') !== false || strpos($response, '<html') !== false) {
    echo "<p style='color: red;'>❌ HTML content detected in response</p>";
} else {
    echo "<p style='color: green;'>✅ No obvious PHP errors detected</p>";
}

// Test 3: Test the API endpoint directly with error reporting
echo "<h2>3. Direct Endpoint Test with Error Reporting</h2>";

echo "<p>Testing the auth endpoint directly...</p>";

// Capture any output from the auth endpoint
ob_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Simulate the API request environment
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/ecom/api/v1/auth/login';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Set up the input data
    $input = [
        'email' => $test_email,
        'password' => $test_password
    ];
    
    // Set up segments for routing
    $segments = ['auth', 'login'];
    $method = 'POST';
    
    // Include the auth endpoint
    include __DIR__ . '/endpoints/auth.php';
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
}

$direct_output = ob_get_clean();

echo "<h3>📋 Direct Endpoint Output:</h3>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto;'>";
echo htmlspecialchars($direct_output);
echo "</pre>";

// Test 4: Check if all required classes and functions exist
echo "<h2>4. Dependencies Check</h2>";

$required_classes = ['Database', 'Auth', 'Validator', 'Response', 'SimpleJWT'];
$required_functions = ['handleLogin'];

foreach ($required_classes as $class) {
    if (class_exists($class)) {
        echo "<p style='color: green;'>✅ Class {$class} exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Class {$class} missing</p>";
    }
}

foreach ($required_functions as $function) {
    if (function_exists($function)) {
        echo "<p style='color: green;'>✅ Function {$function} exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Function {$function} missing</p>";
    }
}

// Test 5: Check database connection
echo "<h2>5. Database Connection Test</h2>";

try {
    $test_query = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✅ Database connection working</p>";
    
    $user_check = $pdo->prepare("SELECT cust_id FROM tbl_customer WHERE cust_email = ?");
    $user_check->execute([$test_email]);
    $user_exists = $user_check->fetch();
    
    if ($user_exists) {
        echo "<p style='color: green;'>✅ Test user exists in database</p>";
    } else {
        echo "<p style='color: red;'>❌ Test user not found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 6: Test with minimal API call
echo "<h2>6. Minimal API Test</h2>";

echo "<p>Testing with a simple GET request to API root...</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/ecom/api/v1/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$root_response = curl_exec($ch);
$root_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$root_error = curl_error($ch);
curl_close($ch);

echo "<p><strong>API Root HTTP Code:</strong> {$root_http_code}</p>";

if ($root_error) {
    echo "<p style='color: red;'>❌ API Root Error: {$root_error}</p>";
} else {
    echo "<p><strong>API Root Response:</strong></p>";
    echo "<pre style='background: #e3f2fd; padding: 10px; border-radius: 3px;'>";
    echo htmlspecialchars(substr($root_response, 0, 500));
    echo "</pre>";
    
    $root_json = json_decode($root_response, true);
    if ($root_json) {
        echo "<p style='color: green;'>✅ API root returns valid JSON</p>";
    } else {
        echo "<p style='color: red;'>❌ API root returns invalid JSON</p>";
    }
}

// Recommendations
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🔧 Troubleshooting Recommendations</h2>";

if (strpos($response, '<') === 0) {
    echo "<p><strong>Issue:</strong> API is returning HTML instead of JSON</p>";
    echo "<p><strong>Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check for PHP errors in the auth endpoint</li>";
    echo "<li>Ensure no HTML output before JSON response</li>";
    echo "<li>Check .htaccess URL rewriting</li>";
    echo "<li>Verify the correct endpoint is being called</li>";
    echo "</ul>";
} elseif ($json_error !== JSON_ERROR_NONE) {
    echo "<p><strong>Issue:</strong> Invalid JSON response</p>";
    echo "<p><strong>Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check for PHP warnings/notices in output</li>";
    echo "<li>Ensure proper JSON encoding</li>";
    echo "<li>Check for extra whitespace or characters</li>";
    echo "<li>Review Response class implementation</li>";
    echo "</ul>";
} else {
    echo "<p style='color: green;'><strong>JSON appears to be valid!</strong></p>";
    echo "<p>The issue might be in the Flutter/client-side parsing.</p>";
}

echo "</div>";

echo "<hr>";
echo "<p><strong>JSON debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
