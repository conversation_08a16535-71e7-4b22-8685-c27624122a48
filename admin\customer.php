<?php require_once('header.php'); ?>

<section class="content-header">
    <div class="content-header-left">
        <h1>View Customers</h1>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-body table-responsive">
                    <table id="example1" class="table table-bordered table-hover table-striped">
                        <thead>
                            <tr>
                                <th width="10">#</th>
                                <th width="180">Name</th>
                                <th width="150">Email Address</th>
                                <th width="180">Country, City, Region</th>
                                <th>Status</th>
                                <th width="100">Change Status</th>
                                <th width="120">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $i = 0;
                            // Fetch all customers from the new tbl_customer table
                            $statement = $pdo->prepare("SELECT * FROM tbl_customer");
                            $statement->execute();
                            $result = $statement->fetchAll(PDO::FETCH_ASSOC);

                            foreach ($result as $row) {
                                $i++;
                                ?>
                                <tr class="<?php echo ($row['cust_status'] == 1) ? 'bg-g' : 'bg-r'; ?>">
                                    <td><?php echo $i; ?></td>
                                    <td><?php echo $row['cust_fname'] . ' ' . $row['cust_lname']; ?></td>
                                    <td><?php echo $row['cust_email']; ?></td>
                                    <td>
                                        <?php echo $row['cust_country']; ?><br>
                                        <?php echo $row['cust_address_city']; ?><br>
                                        <?php echo $row['cust_address_region']; ?>
                                    </td>
                                    <td><?php echo ($row['cust_status'] == 1) ? 'Active' : 'Inactive'; ?></td>
                                    <td>
                                        <a href="customer-change-status.php?id=<?php echo $row['cust_id']; ?>" class="btn btn-success btn-xs">Change Status</a>
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#passwordModal<?php echo $row['cust_id']; ?>">Manage Password</a>
                                        <a href="#" class="btn btn-danger btn-xs" data-href="customer-delete.php?id=<?php echo $row['cust_id']; ?>" data-toggle="modal" data-target="#confirm-delete">Delete</a>
                                    </td>
                                </tr>
                                <!-- Modal for managing password -->
                                <div class="modal fade" id="passwordModal<?php echo $row['cust_id']; ?>" tabindex="-1" role="dialog" aria-labelledby="passwordModalLabel<?php echo $row['cust_id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                                <h4 class="modal-title" id="passwordModalLabel<?php echo $row['cust_id']; ?>">Manage Password for <?php echo htmlspecialchars($row['cust_fname'] . ' ' . $row['cust_lname']); ?></h4>
                                            </div>
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="currentPassword<?php echo $row['cust_id']; ?>">Current Password:</label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="currentPassword<?php echo $row['cust_id']; ?>" value="******** (hashed password hidden)" readonly>
                                                        <span class="input-group-btn">
                                                            <button class="btn btn-default" type="button" disabled><i class="fa fa-eye"></i></button>
                                                        </span>
                                                    </div>
                                                    <p class="help-block">For security reasons, passwords are stored as hashes and cannot be displayed. You can set a new password below.</p>
                                                </div>
                                                <div class="form-group">
                                                    <label for="newPassword<?php echo $row['cust_id']; ?>">New Password (leave blank to keep current):</label>
                                                    <input type="password" class="form-control" id="newPassword<?php echo $row['cust_id']; ?>" name="newPassword">
                                                </div>
                                                <div class="form-group">
                                                    <label for="confirmPassword<?php echo $row['cust_id']; ?>">Confirm New Password:</label>
                                                    <input type="password" class="form-control" id="confirmPassword<?php echo $row['cust_id']; ?>" name="confirmPassword">
                                                </div>
                                                <div id="passwordError<?php echo $row['cust_id']; ?>" class="alert alert-danger" style="display: none;"></div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                                                <button type="button" class="btn btn-primary" onclick="updatePassword(<?php echo $row['cust_id']; ?>)">Update Password</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div><!-- /.box-body -->
            </div><!-- /.box -->
        </div><!-- /.col-md-12 -->
    </div><!-- /.row -->
</section>

<div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">Delete Confirmation</h4>
            </div>
            <div class="modal-body">
                <p>Are you sure want to delete this item?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <a class="btn btn-danger btn-ok">Delete</a>
            </div>
        </div>
    </div>
</div>

<?php
// Check for success or error messages from URL parameters
$success_message = isset($_GET['success']) ? urldecode($_GET['success']) : '';
$error_message = isset($_GET['error']) ? urldecode($_GET['error']) : '';
?>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // Function to reveal/hide password - Disabled since passwords are hashed
    $(document).ready(function() {
        $('.reveal-password').on('click', function() {
            // Functionality disabled - passwords are hashed and cannot be revealed
        });
    });

    // Function to update password
    function updatePassword(customerId) {
        var newPassword = $('#newPassword' + customerId).val();
        var confirmPassword = $('#confirmPassword' + customerId).val();
        var errorDiv = $('#passwordError' + customerId);

        // Reset error message
        errorDiv.hide().text('');

        // Validate passwords
        if (newPassword !== '' && newPassword !== confirmPassword) {
            errorDiv.text('New passwords do not match.').show();
            return;
        }

        if (newPassword !== '' && newPassword.length < 6) {
            errorDiv.text('New password must be at least 6 characters long.').show();
            return;
        }

        // If validation passes, submit the form via AJAX
        $.ajax({
            url: 'customer-password-update.php',
            type: 'POST',
            data: {
                customer_id: customerId,
                new_password: newPassword
            },
            success: function(response) {
                // Close the modal
                $('#passwordModal' + customerId).modal('hide');
                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Password updated successfully.',
                    timer: 2000,
                    showConfirmButton: false
                }).then(function() {
                    // Optionally reload the page to reflect any changes
                    location.reload();
                });
            },
            error: function(xhr, status, error) {
                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to update password. Please try again.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    }

    // SweetAlert for success and error messages
    <?php if($success_message != ''): ?>
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: '<?php echo $success_message; ?>',
            timer: 2000,
            showConfirmButton: false
        });
    <?php endif; ?>
    
    <?php if($error_message != ''): ?>
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: '<?php echo $error_message; ?>',
            timer: 2000,
            showConfirmButton: false
        });
    <?php endif; ?>
</script>

<?php require_once('footer.php'); ?>
