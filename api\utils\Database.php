<?php
/**
 * Database Utility Class
 * Provides common database operations and query helpers
 */

class Database {

    private $pdo;

    public function __construct($pdo_instance = null) {
        global $pdo;
        $this->pdo = $pdo_instance ?: $pdo;
    }

    /**
     * Execute a prepared statement
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            throw new Exception("Database operation failed");
        }
    }

    /**
     * Fetch single row
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Fetch all rows
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Insert record and return ID
     */
    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        $sql = "INSERT INTO {$table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";

        $this->execute($sql, $data);
        return $this->pdo->lastInsertId();
    }

    /**
     * Update records
     */
    public function update($table, $data, $where, $where_params = []) {
        $set_clause = [];
        foreach (array_keys($data) as $field) {
            $set_clause[] = "{$field} = :{$field}";
        }

        // Convert positional parameters to named parameters if needed
        $named_where_params = [];
        if (!empty($where_params)) {
            // Check if where_params are positional (numeric keys) or named (string keys)
            $first_key = array_key_first($where_params);
            if (is_numeric($first_key)) {
                // Convert positional to named parameters
                $param_counter = 0;
                $where = preg_replace_callback('/\?/', function() use (&$param_counter, $where_params, &$named_where_params) {
                    $param_name = "where_param_" . $param_counter;
                    $named_where_params[$param_name] = $where_params[$param_counter];
                    $param_counter++;
                    return ":{$param_name}";
                }, $where);
            } else {
                // Already named parameters
                $named_where_params = $where_params;
            }
        }

        $sql = "UPDATE {$table} SET " . implode(', ', $set_clause) . " WHERE {$where}";
        $params = array_merge($data, $named_where_params);

        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Delete records
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Count records
     */
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) FROM {$table} WHERE {$where}";
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchColumn();
    }

    /**
     * Check if record exists
     */
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->pdo->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->pdo->rollback();
    }

    /**
     * Get paginated results
     */
    public function paginate($sql, $params = [], $page = 1, $limit = 20) {
        // Count total records
        $count_sql = "SELECT COUNT(*) FROM ({$sql}) as count_query";
        $total = $this->execute($count_sql, $params)->fetchColumn();

        // Calculate offset
        $offset = ($page - 1) * $limit;

        // Add pagination to original query
        $paginated_sql = $sql . " LIMIT {$limit} OFFSET {$offset}";
        $data = $this->fetchAll($paginated_sql, $params);

        return [
            'data' => $data,
            'total' => (int)$total,
            'page' => (int)$page,
            'limit' => (int)$limit,
            'total_pages' => ceil($total / $limit)
        ];
    }

    /**
     * Build WHERE clause from filters
     */
    public function buildWhereClause($filters, $allowed_fields = []) {
        $conditions = [];
        $params = [];

        foreach ($filters as $field => $value) {
            if (!empty($allowed_fields) && !in_array($field, $allowed_fields)) {
                continue;
            }

            if ($value !== null && $value !== '') {
                $conditions[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
        }

        $where = empty($conditions) ? '1=1' : implode(' AND ', $conditions);

        return ['where' => $where, 'params' => $params];
    }

    /**
     * Escape table/column names
     */
    public function escapeName($name) {
        return '`' . str_replace('`', '``', $name) . '`';
    }

    /**
     * Get last insert ID
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}
