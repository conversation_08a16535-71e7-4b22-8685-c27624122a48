<?php
/**
 * Flutter Authentication Test
 * Shows exactly how to use the auth API from Flutter
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>📱 Flutter Authentication Guide</h1>";

$base_url = 'http://localhost/ecom/api/v1';

// Helper function for API calls
function testAuthEndpoint($name, $endpoint, $method = 'GET', $data = null, $token = null) {
    global $base_url;
    
    $url = $base_url . '/auth/' . $endpoint;
    
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px;'>";
    echo "<h3>🧪 {$name}</h3>";
    echo "<p><strong>URL:</strong> {$url}</p>";
    echo "<p><strong>Method:</strong> {$method}</p>";
    
    $headers = ['Content-Type: application/json'];
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    if ($data) {
        echo "<p><strong>Request Body:</strong></p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px;'>";
        echo json_encode($data, JSON_PRETTY_PRINT);
        echo "</pre>";
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    
    if ($error) {
        echo "<p style='color: red;'>❌ Error: {$error}</p>";
        echo "</div>";
        return null;
    }
    
    $response_data = json_decode($response, true);
    
    if ($response_data) {
        $status = $response_data['status'] ?? 'unknown';
        $message = $response_data['message'] ?? 'No message';
        
        if ($status === 'success') {
            echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ FAILED</p>";
        }
        
        echo "<p><strong>Status:</strong> {$status}</p>";
        echo "<p><strong>Message:</strong> {$message}</p>";
        
        echo "<h4>📋 Response:</h4>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
        echo json_encode($response_data, JSON_PRETTY_PRINT);
        echo "</pre>";
        
        // Show Flutter code example
        echo "<h4>📱 Flutter Code:</h4>";
        echo "<pre style='background: #e3f2fd; padding: 10px; border-radius: 3px; font-size: 12px;'>";
        
        if ($method === 'POST' && $data) {
            echo "final response = await http.post(\n";
            echo "  Uri.parse('{$url}'),\n";
            echo "  headers: {\n";
            echo "    'Content-Type': 'application/json',\n";
            if ($token) {
                echo "    'Authorization': 'Bearer \$token',\n";
            }
            echo "  },\n";
            echo "  body: json.encode(" . json_encode($data) . "),\n";
            echo ");\n\n";
        } else {
            echo "final response = await http.get(\n";
            echo "  Uri.parse('{$url}'),\n";
            echo "  headers: {\n";
            echo "    'Content-Type': 'application/json',\n";
            if ($token) {
                echo "    'Authorization': 'Bearer \$token',\n";
            }
            echo "  },\n";
            echo ");\n\n";
        }
        
        echo "final data = json.decode(response.body);\n";
        echo "if (data['status'] == 'success') {\n";
        echo "  // Handle success\n";
        if (isset($response_data['data']['token'])) {
            echo "  String token = data['data']['token'];\n";
            echo "  // Save token for future requests\n";
        }
        echo "} else {\n";
        echo "  // Handle error\n";
        echo "  print('Error: \${data['message']}');\n";
        echo "}";
        
        echo "</pre>";
        
    } else {
        echo "<p style='color: red;'>❌ Invalid JSON response</p>";
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
    }
    
    echo "</div>";
    return $response_data;
}

// Test sequence
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🎯 Authentication Flow for Flutter</h2>";
echo "<p>This test demonstrates the complete authentication flow that your Flutter app should follow:</p>";
echo "<ol>";
echo "<li><strong>Register</strong> - Create new user account</li>";
echo "<li><strong>Login</strong> - Get authentication token</li>";
echo "<li><strong>Get User</strong> - Retrieve user data with token</li>";
echo "<li><strong>Logout</strong> - End session</li>";
echo "</ol>";
echo "</div>";

$auth_token = null;

// Test 1: Register
$register_data = [
    'first_name' => 'Flutter',
    'last_name' => 'User',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'phone' => '+************',
    'address' => '123 Flutter Street',
    'city' => 'Dar es Salaam',
    'region' => 'Dar es Salaam',
    'country' => 'Tanzania'
];

$register_response = testAuthEndpoint('Register New User', 'register', 'POST', $register_data);

if ($register_response && $register_response['status'] === 'success') {
    if (isset($register_response['data']['token'])) {
        $auth_token = $register_response['data']['token'];
    }
}

// Test 2: Login
$login_data = [
    'email' => '<EMAIL>',
    'password' => 'changawa'
];

$login_response = testAuthEndpoint('User Login', 'login', 'POST', $login_data);

if ($login_response && $login_response['status'] === 'success') {
    if (isset($login_response['data']['token'])) {
        $auth_token = $login_response['data']['token'];
    }
}

// Test 3: Get current user (authenticated)
if ($auth_token) {
    testAuthEndpoint('Get Current User (Authenticated)', 'me', 'GET', null, $auth_token);
} else {
    echo "<div style='border: 1px solid #ffc107; padding: 15px; margin: 15px 0; border-radius: 5px; background: #fff3cd;'>";
    echo "<h3>⚠️ Get Current User (Skipped)</h3>";
    echo "<p>Skipped because no authentication token was obtained from login.</p>";
    echo "</div>";
}

// Test 4: Get current user (unauthenticated)
testAuthEndpoint('Get Current User (Unauthenticated)', 'me', 'GET');

// Test 5: Invalid login
$invalid_login_data = [
    'email' => '<EMAIL>',
    'password' => 'wrongpassword'
];

testAuthEndpoint('Invalid Login Test', 'login', 'POST', $invalid_login_data);

// Test 6: Logout
testAuthEndpoint('User Logout', 'logout', 'POST');

// Complete Flutter Service Class
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📱 Complete Flutter Auth Service</h2>";
echo "<p>Copy this complete service class into your Flutter project:</p>";

echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  static const String baseUrl = 'http://localhost/ecom/api/v1';
  
  // Register new user
  static Future<Map<String, dynamic>> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String phone,
    required String address,
    required String city,
    required String region,
    required String country,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('\$baseUrl/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'first_name': firstName,
          'last_name': lastName,
          'email': email,
          'password': password,
          'phone': phone,
          'address': address,
          'city': city,
          'region': region,
          'country': country,
        }),
      );
      
      final data = json.decode(response.body);
      
      if (data['status'] == 'success' && data['data']['token'] != null) {
        // Save token
        await _saveToken(data['data']['token']);
        await _saveUser(data['data']['user']);
      }
      
      return data;
    } catch (e) {
      return {'status': 'error', 'message': 'Network error: \$e'};
    }
  }
  
  // Login user
  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('\$baseUrl/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );
      
      final data = json.decode(response.body);
      
      if (data['status'] == 'success' && data['data']['token'] != null) {
        // Save token and user data
        await _saveToken(data['data']['token']);
        await _saveUser(data['data']['user']);
      }
      
      return data;
    } catch (e) {
      return {'status': 'error', 'message': 'Network error: \$e'};
    }
  }
  
  // Get current user
  static Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      final token = await getToken();
      if (token == null) {
        return {'status': 'error', 'message': 'No authentication token'};
      }
      
      final response = await http.get(
        Uri.parse('\$baseUrl/auth/me'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer \$token',
        },
      );
      
      return json.decode(response.body);
    } catch (e) {
      return {'status': 'error', 'message': 'Network error: \$e'};
    }
  }
  
  // Logout user
  static Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('\$baseUrl/auth/logout'),
        headers: {'Content-Type': 'application/json'},
      );
      
      // Clear local storage regardless of response
      await clearAuthData();
      
      return json.decode(response.body);
    } catch (e) {
      await clearAuthData();
      return {'status': 'success', 'message': 'Logged out locally'};
    }
  }
  
  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }
  
  // Get stored token
  static Future<String?> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }
  
  // Get stored user data
  static Future<Map<String, dynamic>?> getUser() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? userData = prefs.getString('user_data');
    if (userData != null) {
      return json.decode(userData);
    }
    return null;
  }
  
  // Private helper methods
  static Future<void> _saveToken(String token) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }
  
  static Future<void> _saveUser(Map<String, dynamic> user) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_data', json.encode(user));
  }
  
  static Future<void> clearAuthData() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('user_data');
  }
}

// Usage Example:
class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  
  Future<void> _login() async {
    setState(() => _isLoading = true);
    
    final result = await AuthService.login(
      _emailController.text,
      _passwordController.text,
    );
    
    setState(() => _isLoading = false);
    
    if (result['status'] == 'success') {
      // Navigate to home screen
      Navigator.pushReplacementNamed(context, '/home');
    } else {
      // Show error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result['message'])),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _emailController,
              decoration: InputDecoration(labelText: 'Email'),
            ),
            TextField(
              controller: _passwordController,
              decoration: InputDecoration(labelText: 'Password'),
              obscureText: true,
            ),
            SizedBox(height: 20),
            _isLoading
                ? CircularProgressIndicator()
                : ElevatedButton(
                    onPressed: _login,
                    child: Text('Login'),
                  ),
          ],
        ),
      ),
    );
  }
}
");
echo "</pre>";
echo "</div>";

// Cleanup
echo "<h2>🧹 Cleanup</h2>";
try {
    $pdo->prepare("DELETE FROM tbl_customer WHERE cust_email = ?")->execute(['<EMAIL>']);
    echo "<p style='color: green;'>✅ Test user cleaned up</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Cleanup error: " . $e->getMessage() . "</p>";
}

echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🎉 Authentication System Ready!</h2>";
echo "<p>Your authentication API is working perfectly and ready for Flutter integration!</p>";
echo "<p><strong>Key Points:</strong></p>";
echo "<ul>";
echo "<li>✅ Registration creates new users and returns JWT token</li>";
echo "<li>✅ Login validates credentials and returns JWT token</li>";
echo "<li>✅ Protected endpoints require 'Authorization: Bearer TOKEN' header</li>";
echo "<li>✅ Tokens are valid for 24 hours</li>";
echo "<li>✅ All responses follow consistent JSON format</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><strong>Flutter auth test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
