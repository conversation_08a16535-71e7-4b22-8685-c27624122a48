<?php
/**
 * Products Endpoints
 * Handles product catalog operations
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path and ID
$sub_path = $segments[1] ?? '';
$product_id = $segments[2] ?? null;

switch ($method) {
    case 'GET':
        if ($product_id) {
            handleGetProduct($db, $product_id);
        } elseif ($sub_path === 'featured') {
            handleGetFeaturedProducts($db);
        } elseif ($sub_path === 'latest') {
            handleGetLatestProducts($db);
        } elseif ($sub_path === 'popular') {
            handleGetPopularProducts($db);
        } else {
            handleGetProducts($db);
        }
        break;
        
    case 'POST':
        AuthMiddleware::requireAdmin();
        handleCreateProduct($db, $input);
        break;
        
    case 'PUT':
        AuthMiddleware::requireAdmin();
        if (!$product_id) {
            Response::error('Product ID is required', 400);
        }
        handleUpdateProduct($db, $product_id, $input);
        break;
        
    case 'DELETE':
        AuthMiddleware::requireAdmin();
        if (!$product_id) {
            Response::error('Product ID is required', 400);
        }
        handleDeleteProduct($db, $product_id);
        break;
        
    default:
        Response::methodNotAllowed(['GET', 'POST', 'PUT', 'DELETE']);
}

/**
 * Get all products with filtering and pagination
 */
function handleGetProducts($db) {
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
    $category_id = $_GET['category_id'] ?? null;
    $subcategory_id = $_GET['subcategory_id'] ?? null;
    $search = $_GET['search'] ?? null;
    $min_price = $_GET['min_price'] ?? null;
    $max_price = $_GET['max_price'] ?? null;
    $sort = $_GET['sort'] ?? 'name';
    $order = $_GET['order'] ?? 'asc';
    
    // Build WHERE clause
    $where_conditions = ['p.p_is_active = 1'];
    $params = [];
    
    if ($category_id) {
        $where_conditions[] = 'p.tcat_id = ?';
        $params[] = $category_id;
    }
    
    if ($subcategory_id) {
        $where_conditions[] = 'p.mcat_id = ?';
        $params[] = $subcategory_id;
    }
    
    if ($search) {
        $where_conditions[] = '(p.p_name LIKE ? OR p.p_description LIKE ? OR p.p_short_description LIKE ?)';
        $search_term = "%{$search}%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if ($min_price) {
        $where_conditions[] = 'CAST(p.p_current_price AS DECIMAL(10,2)) >= ?';
        $params[] = $min_price;
    }
    
    if ($max_price) {
        $where_conditions[] = 'CAST(p.p_current_price AS DECIMAL(10,2)) <= ?';
        $params[] = $max_price;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Build ORDER BY clause
    $allowed_sorts = ['name', 'price', 'created', 'popularity'];
    $sort = in_array($sort, $allowed_sorts) ? $sort : 'name';
    $order = strtoupper($order) === 'DESC' ? 'DESC' : 'ASC';
    
    $order_by = match($sort) {
        'name' => "p.p_name {$order}",
        'price' => "CAST(p.p_current_price AS DECIMAL(10,2)) {$order}",
        'created' => "p.p_id {$order}",
        'popularity' => "p.p_total_view {$order}",
        default => "p.p_name {$order}"
    };
    
    // Main query
    $sql = "
        SELECT 
            p.p_id,
            p.p_name,
            p.p_old_price,
            p.p_current_price,
            p.p_qty,
            p.p_featured_photo,
            p.p_short_description,
            p.p_is_featured,
            p.p_total_view,
            p.installation_fee,
            tc.tcat_name as category_name,
            mc.mcat_name as subcategory_name,
            (SELECT COUNT(*) FROM tbl_product_variation WHERE p_id = p.p_id) as variation_count
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        LEFT JOIN tbl_mid_category mc ON p.mcat_id = mc.mcat_id
        WHERE {$where_clause}
        ORDER BY {$order_by}
    ";
    
    $result = $db->paginate($sql, $params, $page, $limit);
    
    // Format products
    $products = array_map('formatProduct', $result['data']);
    
    Response::paginated($products, $result['total'], $page, $limit, 'Products retrieved successfully');
}

/**
 * Get single product with full details
 */
function handleGetProduct($db, $product_id) {
    // Get product details
    $sql = "
        SELECT 
            p.*,
            tc.tcat_name as category_name,
            mc.mcat_name as subcategory_name
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        LEFT JOIN tbl_mid_category mc ON p.mcat_id = mc.mcat_id
        WHERE p.p_id = ? AND p.p_is_active = 1
    ";
    
    $product = $db->fetchOne($sql, [$product_id]);
    
    if (!$product) {
        Response::notFound('Product not found');
    }
    
    // Get product variations
    $variations = $db->fetchAll(
        "SELECT * FROM tbl_product_variation WHERE p_id = ? ORDER BY variation_id",
        [$product_id]
    );
    
    // Get product colors
    $colors = $db->fetchAll(
        "SELECT c.* FROM tbl_color c 
         JOIN tbl_product_color pc ON c.color_id = pc.color_id 
         WHERE pc.p_id = ?",
        [$product_id]
    );
    
    // Get product sizes
    $sizes = $db->fetchAll(
        "SELECT s.* FROM tbl_size s 
         JOIN tbl_product_size ps ON s.size_id = ps.size_id 
         WHERE ps.p_id = ?",
        [$product_id]
    );
    
    // Get additional photos
    $photos = $db->fetchAll(
        "SELECT * FROM tbl_product_photo WHERE p_id = ? ORDER BY pp_id",
        [$product_id]
    );
    
    // Update view count
    $db->execute("UPDATE tbl_product SET p_total_view = p_total_view + 1 WHERE p_id = ?", [$product_id]);
    
    // Format response
    $formatted_product = formatProduct($product);
    $formatted_product['variations'] = array_map('formatVariation', $variations);
    $formatted_product['colors'] = array_map('formatColor', $colors);
    $formatted_product['sizes'] = array_map('formatSize', $sizes);
    $formatted_product['gallery'] = array_map('formatPhoto', $photos);
    $formatted_product['description'] = $product['p_description'];
    $formatted_product['features'] = $product['p_feature'];
    $formatted_product['condition'] = $product['p_condition'];
    $formatted_product['return_policy'] = $product['p_return_policy'];
    
    Response::success($formatted_product, 'Product details retrieved successfully');
}

/**
 * Get featured products
 */
function handleGetFeaturedProducts($db) {
    $limit = min((int)($_GET['limit'] ?? 10), 20);
    
    $sql = "
        SELECT 
            p.p_id,
            p.p_name,
            p.p_old_price,
            p.p_current_price,
            p.p_qty,
            p.p_featured_photo,
            p.p_short_description,
            p.installation_fee,
            tc.tcat_name as category_name
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        WHERE p.p_is_active = 1 AND p.p_is_featured = 1
        ORDER BY p.p_id DESC
        LIMIT ?
    ";
    
    $products = $db->fetchAll($sql, [$limit]);
    $formatted_products = array_map('formatProduct', $products);
    
    Response::success($formatted_products, 'Featured products retrieved successfully');
}

/**
 * Get latest products
 */
function handleGetLatestProducts($db) {
    $limit = min((int)($_GET['limit'] ?? 10), 20);
    
    $sql = "
        SELECT 
            p.p_id,
            p.p_name,
            p.p_old_price,
            p.p_current_price,
            p.p_qty,
            p.p_featured_photo,
            p.p_short_description,
            p.installation_fee,
            tc.tcat_name as category_name
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        WHERE p.p_is_active = 1
        ORDER BY p.p_id DESC
        LIMIT ?
    ";
    
    $products = $db->fetchAll($sql, [$limit]);
    $formatted_products = array_map('formatProduct', $products);
    
    Response::success($formatted_products, 'Latest products retrieved successfully');
}

/**
 * Get popular products
 */
function handleGetPopularProducts($db) {
    $limit = min((int)($_GET['limit'] ?? 10), 20);
    
    $sql = "
        SELECT 
            p.p_id,
            p.p_name,
            p.p_old_price,
            p.p_current_price,
            p.p_qty,
            p.p_featured_photo,
            p.p_short_description,
            p.installation_fee,
            tc.tcat_name as category_name,
            p.p_total_view
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        WHERE p.p_is_active = 1
        ORDER BY p.p_total_view DESC, p.p_id DESC
        LIMIT ?
    ";
    
    $products = $db->fetchAll($sql, [$limit]);
    $formatted_products = array_map('formatProduct', $products);
    
    Response::success($formatted_products, 'Popular products retrieved successfully');
}

/**
 * Format product data for API response
 */
function formatProduct($product) {
    return [
        'id' => (int)$product['p_id'],
        'name' => $product['p_name'],
        'old_price' => (float)$product['p_old_price'],
        'current_price' => (float)$product['p_current_price'],
        'quantity' => (int)$product['p_qty'],
        'installation_fee' => (int)($product['installation_fee'] ?? DEFAULT_INSTALLATION_FEE),
        'featured_photo' => $product['p_featured_photo'] ? 
            '/assets/uploads/' . $product['p_featured_photo'] : null,
        'short_description' => $product['p_short_description'] ?? '',
        'is_featured' => (bool)($product['p_is_featured'] ?? false),
        'category' => $product['category_name'] ?? null,
        'subcategory' => $product['subcategory_name'] ?? null,
        'in_stock' => (int)$product['p_qty'] > 0,
        'variation_count' => (int)($product['variation_count'] ?? 0),
        'view_count' => (int)($product['p_total_view'] ?? 0)
    ];
}

/**
 * Format variation data
 */
function formatVariation($variation) {
    return [
        'id' => (int)$variation['variation_id'],
        'name' => $variation['variation_name'],
        'price' => (float)$variation['variation_price'],
        'quantity' => (int)$variation['variation_qty'],
        'sku' => $variation['variation_sku'],
        'image' => $variation['variation_image'] ? 
            '/assets/uploads/product_variations/' . $variation['variation_image'] : null,
        'description' => $variation['variation_description'] ?? ''
    ];
}

/**
 * Format color data
 */
function formatColor($color) {
    return [
        'id' => (int)$color['color_id'],
        'name' => $color['color_name'],
        'code' => $color['color_code']
    ];
}

/**
 * Format size data
 */
function formatSize($size) {
    return [
        'id' => (int)$size['size_id'],
        'name' => $size['size_name']
    ];
}

/**
 * Format photo data
 */
function formatPhoto($photo) {
    return [
        'id' => (int)$photo['pp_id'],
        'photo' => '/assets/uploads/product_photos/' . $photo['photo']
    ];
}

// Placeholder functions for admin operations
function handleCreateProduct($db, $input) {
    Response::success(null, 'Product creation not implemented yet');
}

function handleUpdateProduct($db, $product_id, $input) {
    Response::success(null, 'Product update not implemented yet');
}

function handleDeleteProduct($db, $product_id) {
    Response::success(null, 'Product deletion not implemented yet');
}
?>
