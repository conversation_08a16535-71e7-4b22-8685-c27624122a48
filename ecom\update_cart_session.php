<?php
// Include session configuration before starting session
include("session_config.php");
// Start session at the very beginning
session_start();


// --- IMPORTANT: Suppress direct error output for AJAX ---
// error_reporting(0); // Comment out during development if needed
// ini_set('display_errors', 0); // Comment out during development if needed
// --- IMPORTANT: Set JSON header EARLY ---
header('Content-Type: application/json');

// --- <PERSON>uffer output to catch potential stray echoes ---
ob_start();

// Adjust path if needed
require_once("../admin/inc/config.php"); // Ensure path is correct

// Default response
$response = ['status' => 'error', 'message' => 'Invalid request or parameters.'];

// Ensure cart exists in session and is properly initialized
if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Check if it's an AJAX POST request
$is_ajax = (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest');
$is_post = ($_SERVER['REQUEST_METHOD'] === 'POST');

if ($is_post && $is_ajax && isset($_POST['action'])) {
    $action = $_POST['action'];
    // Use cart_key for most actions
    $cart_key = isset($_POST['cart_key']) ? filter_var($_POST['cart_key'], FILTER_SANITIZE_STRING) : null;

    // Log the received action and key for debugging ALL actions
     error_log("[Cart Update] Received Action: {$action}, Cart Key: {$cart_key}"); // Keep this log

    try {
        switch ($action) {
            // --- ACTION: UPDATE ITEM QUANTITY ---
            case 'update_qty':
                 // ... (keep existing update_qty logic) ...
                if ($cart_key && isset($_SESSION['cart'][$cart_key]) && isset($_POST['new_qty'])) {
                    $new_qty = filter_var($_POST['new_qty'], FILTER_VALIDATE_INT);

                    if ($new_qty === false || $new_qty < 1) {
                         error_log("[Cart Update] Invalid or zero quantity ({$_POST['new_qty']}) received for key: {$cart_key}. Removing item.");
                         unset($_SESSION['cart'][$cart_key]);
                         $response = ['status' => 'success', 'message' => 'Item removed (quantity set to 0 or invalid).', 'removed_key' => $cart_key];
                    } else {
                        // Quantity is valid (>= 1), check stock (keep existing stock check logic)
                         $ids = explode('-', $cart_key);
                         if (count($ids) < 2) { // Allow key format like 'prod-var-0' or 'prod-0-color' or 'prod-0-0'
                             throw new Exception("Invalid cart key format for stock check: {$cart_key}");
                         }
                         $p_id = filter_var($ids[0], FILTER_VALIDATE_INT);
                         $v_id_str = $ids[1];
                         $v_id = ($v_id_str !== '0' && filter_var($v_id_str, FILTER_VALIDATE_INT)) ? (int)$v_id_str : null;

                         if (!$p_id) { throw new Exception("Invalid product ID parsed from cart key: {$cart_key}"); }

                         $current_stock = 0;
                         if ($v_id) {
                             $stmt = $pdo->prepare("SELECT variation_qty FROM tbl_product_variation WHERE variation_id = ? AND p_id = ?");
                             $stmt->execute([$v_id, $p_id]);
                             $variation = $stmt->fetch(PDO::FETCH_ASSOC);
                             $current_stock = $variation ? (int)$variation['variation_qty'] : 0;
                         } else {
                             $stmt = $pdo->prepare("SELECT p_qty FROM tbl_product WHERE p_id = ?");
                             $stmt->execute([$p_id]);
                             $product = $stmt->fetch(PDO::FETCH_ASSOC);
                             $current_stock = $product ? (int)$product['p_qty'] : 0;
                         }

                        if ($new_qty <= $current_stock) {
                            $_SESSION['cart'][$cart_key]['quantity'] = $new_qty;
                            $response = ['status' => 'success', 'message' => 'Quantity updated.'];
                        } else {
                            $_SESSION['cart'][$cart_key]['quantity'] = $current_stock;
                            $response = [
                                'status' => 'warning',
                                'message' => "Quantity adjusted to max available stock ({$current_stock}).",
                                'adjusted_qty' => $current_stock
                            ];
                        }
                    }
                } else {
                    $response['message'] = 'Invalid item key or quantity missing for update.';
                    error_log("[Cart Update] Update Qty Error: Key='{$cart_key}', Session Exists=".(isset($_SESSION['cart'][$cart_key])?'Yes':'No').", Qty Received='".($_POST['new_qty'] ?? 'NULL')."'");
                }
                break;

            // --- ACTION: REMOVE ITEM ---
            case 'remove_item':
                error_log("[Cart Update] Attempting removal for key: {$cart_key}");
                if ($cart_key) {
                    // Log the current cart state
                    error_log("[Cart Update] Current cart keys: " . implode(', ', array_keys($_SESSION['cart'])));

                    // Try to find the item with the exact key
                    if (isset($_SESSION['cart'][$cart_key])) {
                        error_log("[Cart Update] Found exact match for key: {$cart_key}");
                        unset($_SESSION['cart'][$cart_key]);
                        $response = ['status' => 'success', 'message' => 'Item removed.', 'removed_key' => $cart_key];
                    } else {
                        // Try to find the item with a partial match (without color)
                        $parts = explode('-', $cart_key);
                        if (count($parts) >= 2) {
                            $baseKey = $parts[0] . '-' . $parts[1];
                            error_log("[Cart Update] Trying partial match with base key: {$baseKey}");

                            foreach ($_SESSION['cart'] as $key => $item) {
                                if (strpos($key, $baseKey) === 0) {
                                    error_log("[Cart Update] Found partial match: {$key}");
                                    unset($_SESSION['cart'][$key]);
                                    $response = ['status' => 'success', 'message' => 'Item removed.', 'removed_key' => $key];
                                    break;
                                }
                            }
                        }

                        if (!isset($response['status'])) {
                            error_log("[Cart Update] No match found for key: {$cart_key}");
                            $response = ['status' => 'error', 'message' => 'Item not found in cart for removal.'];
                        }
                    }
                } else {
                    error_log("[Cart Update] Removal failed: Cart key was missing or invalid.");
                    $response = ['status' => 'error', 'message' => 'Invalid cart key provided.'];
                }
                break;

            // --- ACTION: TOGGLE INSTALLATION ---
            case 'toggle_installation':
                 // ... (keep existing toggle_installation logic) ...
                 if ($cart_key && isset($_SESSION['cart'][$cart_key]) && isset($_POST['installation_status'])) {
                    $status = filter_var($_POST['installation_status'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                    if ($status !== null) {
                        $_SESSION['cart'][$cart_key]['installation'] = $status; // Store boolean directly
                        $response = ['status' => 'success', 'message' => 'Installation preference updated.'];
                    } else {
                         $_SESSION['cart'][$cart_key]['installation'] = false;
                         error_log("[Cart Update] Invalid installation status '{$_POST['installation_status']}' for key {$cart_key}. Setting to false.");
                         $response = ['status' => 'warning', 'message' => 'Installation updated (set to false due to invalid input).'];
                    }
                } else {
                    $response['message'] = 'Invalid item key or status for installation update.';
                    error_log("[Cart Update] Toggle Install Error: Key='{$cart_key}', Session Exists=".(isset($_SESSION['cart'][$cart_key])?'Yes':'No').", Status='".($_POST['installation_status'] ?? 'NULL')."'");
                }
                break;

            // >>> NEW: ACTION: UPDATE COLOR <<<
            case 'update_color':
                 $new_color_id_input = $_POST['new_color_id'] ?? null;
                 // Validate new_color_id (allow empty string/null for 'Select', or positive integer)
                 $new_color_id = null; // Default to null
                 if ($new_color_id_input !== null && $new_color_id_input !== '') {
                     $validated_id = filter_var($new_color_id_input, FILTER_VALIDATE_INT);
                     if ($validated_id !== false && $validated_id > 0) {
                         $new_color_id = $validated_id;
                     } else {
                         error_log("[Cart Update] Invalid new_color_id received: " . $new_color_id_input);
                          $response['message'] = 'Invalid color ID provided.';
                          // Don't break yet, let the check below handle missing key
                     }
                 } // If empty string or null, $new_color_id remains null

                 if ($cart_key && isset($_SESSION['cart'][$cart_key])) {
                    // Fetch new color details
                     $new_color_name = null;
                     $new_color_code = null;
                     if ($new_color_id) {
                         $stmt_color = $pdo->prepare("SELECT color_name, color_code FROM tbl_color WHERE color_id = ?");
                         $stmt_color->execute([$new_color_id]);
                         $color_details = $stmt_color->fetch(PDO::FETCH_ASSOC);
                         if ($color_details) {
                             $new_color_name = $color_details['color_name'];
                             $new_color_code = $color_details['color_code'];
                         } else {
                             error_log("[Cart Update] Color ID {$new_color_id} not found in DB for key {$cart_key}.");
                             // Proceed with null name/code but keep the ID
                         }
                     }

                     // Update the specific item in the session
                     $_SESSION['cart'][$cart_key]['color_id'] = $new_color_id;
                     $_SESSION['cart'][$cart_key]['color_name'] = $new_color_name;
                     $_SESSION['cart'][$cart_key]['color_code'] = $new_color_code;

                     // IMPORTANT: Check if the key itself needs to change because the color_id changed
                     $original_item = $_SESSION['cart'][$cart_key]; // Get the updated item
                     unset($_SESSION['cart'][$cart_key]); // Remove item with old key

                     // Create the new key based on the updated color_id
                      $new_key = $original_item['product_id']
                               . '-' . ($original_item['variation_id'] ?? '0')
                               . '-' . ($original_item['color_id'] ?? '0');

                     // Add the item back with the potentially new key
                     $_SESSION['cart'][$new_key] = $original_item;


                     error_log("[Cart Update] Color updated for original key: {$cart_key}. New key: {$new_key}. New color ID: " . ($new_color_id ?? 'NULL'));
                     $response = ['status' => 'success', 'message' => 'Color updated.', 'updated_key' => $new_key];

                 } else {
                    $response['message'] = 'Item not found in cart for color update.';
                    error_log("[Cart Update] Update Color Error: Key='{$cart_key}', Session Exists=".(isset($_SESSION['cart'][$cart_key])?'Yes':'No').", NewColorID='".($new_color_id ?? 'NULL')."'");
                 }
                 break;
            // >>> END NEW CASE <<<


            // --- ACTION: UPDATE SHIPPING COUNTRY IN SESSION ---
            case 'update_shipping':
                // ... (keep existing update_shipping logic) ...
                 if (isset($_POST['country_id'])) {
                    $country_id = filter_var($_POST['country_id'], FILTER_SANITIZE_STRING);
                    $is_valid_format = ($country_id === '' || $country_id === '0' || (filter_var($country_id, FILTER_VALIDATE_INT) !== false && (int)$country_id > 0));

                    if ($is_valid_format) {
                        $_SESSION['shipping_country_id'] = $country_id;
                        error_log("[Cart Update] Shipping country ID set in session: {$country_id}");
                        $response = ['status' => 'success', 'message' => 'Shipping selection updated.'];
                    } else {
                        $response['message'] = 'Invalid country ID provided for shipping update.';
                        error_log("[Cart Update] Invalid country ID received for shipping update: " . $_POST['country_id']);
                    }
                } else {
                    $response['message'] = 'Country ID missing for shipping update.';
                }
                break;

            default:
                $response['message'] = 'Unknown action requested.';
                break;
        }
    } catch (PDOException $e) {
        error_log("DB Error in update_cart_session.php: Action={$action}, Key={$cart_key}, Error=" . $e->getMessage());
        $response['message'] = 'Database error during update. Please try again.';
    } catch (Exception $e) {
        error_log("General Error in update_cart_session.php: Action={$action}, Key={$cart_key}, Error=" . $e->getMessage());
        $response['message'] = 'An application error occurred: ' . $e->getMessage(); // Provide slightly more info
    }

} elseif (!$is_ajax) {
    $response['message'] = 'Direct access not allowed.';
} elseif (!$is_post) {
    $response['message'] = 'Invalid request method.';
} elseif (!isset($_POST['action'])) {
    $response['message'] = 'Action parameter missing.';
}

// --- Clean output buffer and send JSON response ---
ob_end_clean(); // Discard any accidental output or errors caught by buffer
echo json_encode($response);
exit; // Ensure no further output
?>