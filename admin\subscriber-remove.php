<?php require_once('header.php'); ?>

<?php
try {
	// Delete inactive subscribers
	$statement = $pdo->prepare("DELETE FROM tbl_subscriber WHERE status=0");
	$statement->execute();
	
	// Redirect with success message
	$_SESSION['success_message'] = "Inactive subscribers have been removed successfully.";
	header('location: subscriber.php');
	exit;
} catch (PDOException $e) {
	// Log error and redirect with error message
	error_log("Error removing inactive subscribers: " . $e->getMessage());
	$_SESSION['error_message'] = "An error occurred while removing inactive subscribers. Please try again.";
	header('location: subscriber.php');
	exit;
}
?>