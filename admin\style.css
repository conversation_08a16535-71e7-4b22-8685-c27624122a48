@import url("https://fonts.googleapis.com/css?family=Roboto:400,500,700");

body {
  font-size: 13px;
  font-family: "Roboto", sans-serif;
}

.info-box-text {
  white-space: normal !important;
}

.error {
  color: red;
  border: 0;
  background: 0;
  padding: 0;
  margin-bottom: 10px;
}

.video-iframe iframe {
  width: 100%;
  height: 200px;
}

select.form-control {
  padding-top: 2px;
}

.navbar-nav > .user-menu .user-image {
  width: 40px;
  height: 40px;
  margin-top: -10px;
}

.login-logo {
  font-size: 20px;
}
.login-logo b {
  font-size: 28px;
}

.content-header > h1,
.content-header .content-header-left h1 {
  font-family: "Roboto", sans-serif;
}
.main-header .logo {
  text-align: left;
}
.main-header .logo .logo-lg {
  font-family: "Roboto", sans-serif;
}
.sidebar-mini.sidebar-collapse .main-header .logo > .logo-mini {
  margin-left: 0;
}

.info-box-number {
  font-size: 34px;
}

.garment-label-heading {
  text-align: left;
  font-weight: bold;
  background: #eee;
  padding: 10px 15px;
  border-radius: 4px;
}

.garment-label {
  color: #d43f3a;
  display: block;
  margin-top: 10px;
  font-weight: bold;
}

.dropdown-menu {
  min-width: auto;
}

.disable-click {
  pointer-events: none !important;
}

.box-info {
  padding-top: 20px;
}

.content-header > h1 {
  position: relative;
  padding-left: 30px;
}

.content-header > h1:before {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "FontAwesome", sans-serif;
  font-size: 26px;
  content: "\f18e";
}

.sidebar-menu li.active > a > .pull-right-container > .fa-angle-left {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

.sidebar-menu li > a > .pull-right-container > .fa-angle-left:before {
  content: "\f105";
}

h4.modal-title {
  color: #3c8dbc;
  font-weight: bold;
}

.rTable {
  display: table;
  width: 100%;
  border: 1px solid #999999;
}
.rTableRow {
  display: table-row;
}
.rTableHeading {
  display: table-header-group;
  background-color: #ddd;
}
.rTableHead {
  width: 40%;
}
.rTableCell,
.rTableHead {
  display: table-cell;
  padding: 3px 10px;
  border: 1px solid #999999;
}
.rTableHeading {
  display: table-header-group;
  background-color: #ddd;
  font-weight: bold;
}
.rTableFoot {
  display: table-footer-group;
  font-weight: bold;
  background-color: #ddd;
}
.rTableBody {
  display: table-row-group;
}

.content-header {
  overflow: hidden;
}
.content-header .content-header-left {
  float: left;
  padding-top: 5px;
}
.content-header .content-header-right {
  float: right;
}
.content-header .content-header-left h1 {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 30px;
  padding-bottom: 5px;
  font-size: 24px;
  position: relative;
}
.content-header .content-header-left h1:before {
  position: absolute;
  top: 0;
  left: 0;
  font-family: "FontAwesome", sans-serif;
  font-size: 26px;
  content: "\f18e";
}
.content-header .content-header-right a {
  position: relative;
  /*padding-left: 24px;*/
  font-size: 14px;
}
.content-header .content-header-right a:before {
  /*position: absolute;
    top: 4px;
    left: 5px;
    font-family: 'FontAwesome', sans-serif;
    font-size: 16px;
    content: '\f067';*/
}

.checkbox-inline {
  padding-top: 0 !important;
}
.checkbox-content {
  float: left;
  border: 1px solid #aaa;
  margin-right: 5px;
  margin-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 3px;
}

.checkbox-content.role-access {
  float: none;
  border: 0;
}

.skin-blue .sidebar-menu > li:hover > a,
.skin-blue .sidebar-menu > li.active > a {
  color: #fff;
  background: #3c8dbc;
  border-left-color: #3c8dbc;
}

.pt10 {
  padding-top: 10px;
}
.pt20 {
  padding-top: 20px;
}
.ml10 {
  margin-left: 10px;
}

img.existing-photo {
  max-width: 500px;
}

.slimScrollBar {
  background: none repeat scroll 0 0 #9e9e9e !important;
  border-radius: 0;
  display: none;
  height: 702.936px;
  position: absolute;
  right: 1px;
  top: 145px;
  width: 10px !important;
  z-index: 99;
  opacity: 0.7 !important;
}

.seo-info {
  font-family: "Cairo", sans-serif;
  color: red;
  font-weight: 700;
  font-size: 16px;
  background: #e4e4e4;
  padding: 15px;
}

.skin-blue .main-header .logo,
.skin-blue .main-header .logo:hover,
.skin-blue .main-header .navbar,
.skin-blue .main-header .navbar .sidebar-toggle:hover {
  /* background-color: #333333; */
  background-color: #131921;
}

.skin-blue .sidebar-menu > li:hover > a,
.skin-blue .sidebar-menu > li.active > a {
  background-color: #4c4c4c;
  border-left-color: #000000;
}

.nav-tabs-custom > .nav-tabs > li.active {
  border-top-color: #333333;
}
.box.box-info {
  border-top-color: #4c4c4c;
}

.user-footer div {
  width: 100%;
}
.user-footer div:first-child {
  margin-bottom: 10px;
}
.user-footer div a {
  display: block;
}

.navbar-nav > .user-menu > .dropdown-menu {
  width: 140px;
}
.navbar-nav > .user-menu > .dropdown-menu > .user-footer {
  background-color: #d6d6d6;
}

.content-header .content-header-right a {
  background-color: #333333;
  border-color: #333333;
}
.form-control:focus {
  border-color: #333333;
}

.login-button {
  background-color: #333333;
  border-color: #333333;
}

iframe {
  /* width: 300px!important; */
}

.sidebar-menu .treeview-menu > li > a {
  font-size: 12px;
}

input,
textarea,
select {
  font-size: 13px !important;
}

.bg-g {
  background: #d3f9b4 !important;
}

.bg-r {
  background: #f9cccc !important;
}
