<?php
// logout.php

// Include session configuration
include("session_config.php");
session_start();

// Include database connection
include("../admin/inc/config.php");

// Get current customer ID before destroying session
$customer_id = $_SESSION['customer']['cust_id'] ?? null;

// Clear remember me cookie and database token if exists
$remember_token = getRememberToken();
if ($remember_token && isset($pdo)) {
    deleteRememberToken($pdo, $remember_token);
}

// Clear all remember tokens for this user (logout from all devices)
if ($customer_id && isset($pdo)) {
    deleteAllRememberTokens($pdo, $customer_id);
}

// More aggressive cookie clearing
clearRememberCookie();

// Additional cookie clearing methods to ensure it's gone
if (isset($_COOKIE[REMEMBER_COOKIE_NAME])) {
    // Try multiple methods to clear the cookie
    setcookie(REMEMBER_COOKIE_NAME, '', time() - 3600, '/');
    setcookie(REMEMBER_COOKIE_NAME, '', time() - 3600, '/', '');
    setcookie(REMEMBER_COOKIE_NAME, '', time() - 3600, '/', $_SERVER['HTTP_HOST'] ?? '');
    unset($_COOKIE[REMEMBER_COOKIE_NAME]);
}

session_unset(); // Remove all session variables
session_destroy(); // Destroy the session

// Start a new clean session to set logout flag
session_start();
session_regenerate_id(true);

// Set a logout flag to prevent auto-login
$_SESSION['just_logged_out'] = true;

// Redirect to login page after logout
header('Location: login.php?logged_out=1');
exit;