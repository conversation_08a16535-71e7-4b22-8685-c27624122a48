<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - SMART LIFE</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .policy-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }
        .policy-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .policy-header h1 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 20px;
        }
        .policy-section {
            margin-bottom: 40px;
        }
        .policy-section h2 {
            font-size: 1.8rem;
            color: var(--primary);
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        .policy-section p {
            margin-bottom: 15px;
            line-height: 1.6;
            color: #444;
        }
        .policy-section ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }
        .policy-section li {
            margin-bottom: 10px;
            line-height: 1.6;
            color: #444;
        }
        .last-updated {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-bottom: 30px;
        }
        .contact-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .contact-info h3 {
            color: var(--primary);
            margin-bottom: 15px;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
                <a href="index.php#products" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>

                <!-- Search -->
                <div class="relative">
                    <input id="searchInput" type="text" placeholder="Search products, categories..."
                           class="w-64 px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="searchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-[#00c2ff]">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
                <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
                <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
                <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>
                
                <!-- Search in Mobile Menu -->
                <div class="relative mt-4">
                    <input id="mobileSearchInput" type="text" placeholder="Search products, categories..."
                           class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="mobileSearchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <main class="pt-24 pb-12">
        <div class="policy-container">
            <div class="policy-header">
                <h1>Privacy Policy</h1>
                <p>How we collect, use, and protect your personal information</p>
                <div class="last-updated">Last Updated: January 1, 2024</div>
            </div>

            <div class="policy-section">
                <h2>Introduction</h2>
                <p>At SMART LIFE, we are committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or use our services.</p>
            </div>

            <div class="policy-section">
                <h2>Information We Collect</h2>
                <p>We collect information that you provide directly to us, including:</p>
                <ul>
                    <li>Personal information (name, email, phone number, address)</li>
                    <li>Payment information</li>
                    <li>Account credentials</li>
                    <li>Device information</li>
                    <li>Usage data</li>
                </ul>
            </div>

            <div class="policy-section">
                <h2>How We Use Your Information</h2>
                <p>We use the information we collect to:</p>
                <ul>
                    <li>Process your orders and payments</li>
                    <li>Provide customer support</li>
                    <li>Send you updates and marketing communications</li>
                    <li>Improve our products and services</li>
                    <li>Ensure the security of our services</li>
                </ul>
            </div>

            <div class="policy-section">
                <h2>Information Sharing</h2>
                <p>We may share your information with:</p>
                <ul>
                    <li>Service providers who assist in our operations</li>
                    <li>Payment processors</li>
                    <li>Shipping partners</li>
                    <li>Law enforcement when required by law</li>
                </ul>
                <p>We do not sell your personal information to third parties.</p>
            </div>

            <div class="policy-section">
                <h2>Data Security</h2>
                <p>We implement appropriate security measures to protect your personal information, including:</p>
                <ul>
                    <li>Encryption of sensitive data</li>
                    <li>Secure servers and networks</li>
                    <li>Regular security assessments</li>
                    <li>Employee training on data protection</li>
                </ul>
            </div>

            <div class="policy-section">
                <h2>Your Rights</h2>
                <p>You have the right to:</p>
                <ul>
                    <li>Access your personal information</li>
                    <li>Correct inaccurate information</li>
                    <li>Request deletion of your information</li>
                    <li>Opt-out of marketing communications</li>
                    <li>Object to certain data processing</li>
                </ul>
            </div>

            <div class="policy-section">
                <h2>Cookies and Tracking</h2>
                <p>We use cookies and similar tracking technologies to:</p>
                <ul>
                    <li>Remember your preferences</li>
                    <li>Analyze website traffic</li>
                    <li>Improve user experience</li>
                    <li>Deliver targeted advertising</li>
                </ul>
                <p>You can control cookies through your browser settings.</p>
            </div>

            <div class="policy-section">
                <h2>Children's Privacy</h2>
                <p>Our services are not intended for children under 13. We do not knowingly collect personal information from children under 13.</p>
            </div>

            <div class="policy-section">
                <h2>Changes to This Policy</h2>
                <p>We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last Updated" date.</p>
            </div>

            <div class="contact-info">
                <h3>Contact Us</h3>
                <p>If you have any questions about this Privacy Policy, please contact us:</p>
                <ul>
                    <li>Email: <EMAIL></li>
                    <li>Phone: +255712345678</li>
                    <li>Address: 123 Smart Street, Dar es Salaam, Tanzania</li>
                </ul>
            </div>
        </div>
    </main>

    <?php include 'includes/footer.php'; ?>

    <!-- External JavaScript -->
    <script src="js/script.js"></script>

    <!-- Inline Page-Specific JavaScript -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Initialize cart if not exists
            if (!localStorage.getItem('cart')) {
                localStorage.setItem('cart', JSON.stringify([]));
            }

            // Update cart count display
            updateCartCount();

            function updateCartCount() {
                const cart = JSON.parse(localStorage.getItem('cart'));
                const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
                document.querySelector('.cart-count').textContent = totalItems;
            }

            // Mobile Menu Functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function toggleMobileMenu() {
                mobileMenu.classList.toggle('translate-x-full');
                mobileMenuBackdrop.classList.toggle('hidden');
                document.body.style.overflow = mobileMenu.classList.contains('translate-x-full') ? 'auto' : 'hidden';
            }

            mobileMenuButton.addEventListener('click', toggleMobileMenu);
            closeMobileMenu.addEventListener('click', toggleMobileMenu);
            mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

            // Newsletter Form Handling
            const newsletterForm = document.querySelector('form[action="subscribe.php"]');
            const newsletterMessage = document.getElementById('newsletter-message');

            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Get form data
                    const formData = new FormData(newsletterForm);
                    
                    // Show loading state
                    const submitButton = newsletterForm.querySelector('button[type="submit"]');
                    const originalButtonText = submitButton.innerHTML;
                    submitButton.innerHTML = '...';
                    submitButton.disabled = true;
                    
                    // Send form data
                    fetch('subscribe.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Create message element
                        const messageDiv = document.createElement('div');
                        messageDiv.className = `px-4 py-2 rounded-md ${
                            data.status === 'success' 
                                ? 'bg-green-100 text-green-800 border border-green-200' 
                                : 'bg-red-100 text-red-800 border border-red-200'
                        }`;
                        messageDiv.textContent = data.message;
                        
                        // Clear previous messages and show new one
                        newsletterMessage.innerHTML = '';
                        newsletterMessage.appendChild(messageDiv);
                        
                        // Reset form if successful
                        if (data.status === 'success') {
                            newsletterForm.reset();
                        }
                    })
                    .catch(error => {
                        // Show error message
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'px-4 py-2 rounded-md bg-red-100 text-red-800 border border-red-200';
                        messageDiv.textContent = 'An error occurred. Please try again.';
                        newsletterMessage.innerHTML = '';
                        newsletterMessage.appendChild(messageDiv);
                    })
                    .finally(() => {
                        // Reset button state
                        submitButton.innerHTML = originalButtonText;
                        submitButton.disabled = false;
                    });
                });
            }
        });
    </script>
</body>
</html> 