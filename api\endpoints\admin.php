<?php
/**
 * Admin Endpoints
 * Handles admin-only operations
 */

global $pdo;
$db = new Database($pdo);

// Require admin authentication for all admin endpoints
AuthMiddleware::requireAdmin();

// Get the sub-path and ID
$sub_path = $segments[1] ?? '';
$resource_id = $segments[2] ?? null;

switch ($method) {
    case 'GET':
        switch ($sub_path) {
            case 'dashboard':
                handleGetDashboard($db);
                break;
                
            case 'orders':
                handleGetAdminOrders($db);
                break;
                
            case 'customers':
                handleGetCustomers($db);
                break;
                
            case 'products':
                handleGetAdminProducts($db);
                break;
                
            case 'analytics':
                handleGetAnalytics($db);
                break;
                
            default:
                Response::notFound('Admin endpoint not found');
        }
        break;
        
    case 'PUT':
        switch ($sub_path) {
            case 'orders':
                if (!$resource_id) {
                    Response::error('Order ID is required', 400);
                }
                handleUpdateOrderStatus($db, $resource_id, $input);
                break;
                
            case 'customers':
                if (!$resource_id) {
                    Response::error('Customer ID is required', 400);
                }
                handleUpdateCustomerStatus($db, $resource_id, $input);
                break;
                
            default:
                Response::notFound('Admin endpoint not found');
        }
        break;
        
    default:
        Response::methodNotAllowed(['GET', 'PUT']);
}

/**
 * Get admin dashboard statistics
 */
function handleGetDashboard($db) {
    // Get order statistics
    $order_stats = $db->fetchOne(
        "SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN payment_status = 'success' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_orders,
            COALESCE(SUM(CASE WHEN payment_status = 'success' THEN total_amount ELSE 0 END), 0) as total_revenue
         FROM orders"
    );
    
    // Get customer statistics
    $customer_stats = $db->fetchOne(
        "SELECT 
            COUNT(*) as total_customers,
            COUNT(CASE WHEN cust_status = 1 THEN 1 END) as active_customers,
            COUNT(CASE WHEN DATE(cust_created_at) = CURDATE() THEN 1 END) as new_today
         FROM tbl_customer"
    );
    
    // Get product statistics
    $product_stats = $db->fetchOne(
        "SELECT 
            COUNT(*) as total_products,
            COUNT(CASE WHEN p_is_active = 1 THEN 1 END) as active_products,
            COUNT(CASE WHEN p_qty = 0 THEN 1 END) as out_of_stock,
            COUNT(CASE WHEN p_is_featured = 1 THEN 1 END) as featured_products
         FROM tbl_product"
    );
    
    // Get recent orders
    $recent_orders = $db->fetchAll(
        "SELECT 
            id,
            tx_ref,
            firstname,
            lastname,
            total_amount,
            payment_status,
            created_at
         FROM orders 
         ORDER BY created_at DESC 
         LIMIT 10"
    );
    
    // Get top selling products
    $top_products = $db->fetchAll(
        "SELECT 
            p.p_id,
            p.p_name,
            p.p_current_price,
            SUM(oi.quantity) as total_sold,
            SUM(oi.total) as total_revenue
         FROM order_items oi
         JOIN orders o ON oi.order_id = o.id
         JOIN tbl_product p ON oi.product_id = p.p_id
         WHERE o.payment_status = 'success'
         GROUP BY p.p_id, p.p_name, p.p_current_price
         ORDER BY total_sold DESC
         LIMIT 5"
    );
    
    // Get monthly revenue (last 12 months)
    $monthly_revenue = $db->fetchAll(
        "SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            SUM(total_amount) as revenue,
            COUNT(*) as order_count
         FROM orders 
         WHERE payment_status = 'success' 
           AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
         GROUP BY DATE_FORMAT(created_at, '%Y-%m')
         ORDER BY month"
    );
    
    $dashboard_data = [
        'orders' => [
            'total' => (int)$order_stats['total_orders'],
            'completed' => (int)$order_stats['completed_orders'],
            'pending' => (int)$order_stats['pending_orders'],
            'failed' => (int)$order_stats['failed_orders']
        ],
        'customers' => [
            'total' => (int)$customer_stats['total_customers'],
            'active' => (int)$customer_stats['active_customers'],
            'new_today' => (int)$customer_stats['new_today']
        ],
        'products' => [
            'total' => (int)$product_stats['total_products'],
            'active' => (int)$product_stats['active_products'],
            'out_of_stock' => (int)$product_stats['out_of_stock'],
            'featured' => (int)$product_stats['featured_products']
        ],
        'revenue' => [
            'total' => (float)$order_stats['total_revenue'],
            'currency' => DEFAULT_CURRENCY
        ],
        'recent_orders' => array_map(function($order) {
            return [
                'id' => (int)$order['id'],
                'tx_ref' => $order['tx_ref'],
                'customer_name' => trim($order['firstname'] . ' ' . $order['lastname']),
                'total_amount' => (float)$order['total_amount'],
                'payment_status' => $order['payment_status'],
                'created_at' => $order['created_at']
            ];
        }, $recent_orders),
        'top_products' => array_map(function($product) {
            return [
                'id' => (int)$product['p_id'],
                'name' => $product['p_name'],
                'price' => (float)$product['p_current_price'],
                'total_sold' => (int)$product['total_sold'],
                'total_revenue' => (float)$product['total_revenue']
            ];
        }, $top_products),
        'monthly_revenue' => array_map(function($month) {
            return [
                'month' => $month['month'],
                'revenue' => (float)$month['revenue'],
                'order_count' => (int)$month['order_count']
            ];
        }, $monthly_revenue)
    ];
    
    Response::success($dashboard_data, 'Dashboard data retrieved successfully');
}

/**
 * Get admin orders with filtering
 */
function handleGetAdminOrders($db) {
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
    $status = $_GET['status'] ?? null;
    $search = $_GET['search'] ?? null;
    
    // Build WHERE clause
    $where_conditions = ['1=1'];
    $params = [];
    
    if ($status) {
        $where_conditions[] = 'payment_status = ?';
        $params[] = $status;
    }
    
    if ($search) {
        $where_conditions[] = '(tx_ref LIKE ? OR firstname LIKE ? OR lastname LIKE ? OR email LIKE ?)';
        $search_term = "%{$search}%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            id,
            tx_ref,
            firstname,
            lastname,
            email,
            phone,
            total_amount,
            shipping_fee,
            currency,
            payment_status,
            verification_status,
            shipping_status,
            created_at,
            updated_at
        FROM orders
        WHERE {$where_clause}
        ORDER BY created_at DESC
    ";
    
    $result = $db->paginate($sql, $params, $page, $limit);
    
    // Format orders
    $orders = array_map(function($order) {
        return [
            'id' => (int)$order['id'],
            'tx_ref' => $order['tx_ref'],
            'customer_name' => trim($order['firstname'] . ' ' . $order['lastname']),
            'email' => $order['email'],
            'phone' => $order['phone'],
            'total_amount' => (float)$order['total_amount'],
            'shipping_fee' => (float)$order['shipping_fee'],
            'currency' => $order['currency'],
            'payment_status' => $order['payment_status'],
            'verification_status' => $order['verification_status'],
            'shipping_status' => $order['shipping_status'],
            'created_at' => $order['created_at'],
            'updated_at' => $order['updated_at']
        ];
    }, $result['data']);
    
    Response::paginated($orders, $result['total'], $page, $limit, 'Admin orders retrieved successfully');
}

/**
 * Get customers list
 */
function handleGetCustomers($db) {
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
    $status = $_GET['status'] ?? null;
    $search = $_GET['search'] ?? null;
    
    // Build WHERE clause
    $where_conditions = ['1=1'];
    $params = [];
    
    if ($status !== null) {
        $where_conditions[] = 'cust_status = ?';
        $params[] = $status;
    }
    
    if ($search) {
        $where_conditions[] = '(cust_fname LIKE ? OR cust_lname LIKE ? OR cust_email LIKE ?)';
        $search_term = "%{$search}%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            cust_id,
            cust_fname,
            cust_lname,
            cust_email,
            cust_phone,
            cust_status,
            cust_created_at,
            cust_last_login,
            (SELECT COUNT(*) FROM orders WHERE user_id = cust_id) as total_orders,
            (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = cust_id AND payment_status = 'success') as total_spent
        FROM tbl_customer
        WHERE {$where_clause}
        ORDER BY cust_created_at DESC
    ";
    
    $result = $db->paginate($sql, $params, $page, $limit);
    
    // Format customers
    $customers = array_map(function($customer) {
        return [
            'id' => (int)$customer['cust_id'],
            'first_name' => $customer['cust_fname'],
            'last_name' => $customer['cust_lname'],
            'email' => $customer['cust_email'],
            'phone' => $customer['cust_phone'],
            'status' => (int)$customer['cust_status'],
            'total_orders' => (int)$customer['total_orders'],
            'total_spent' => (float)$customer['total_spent'],
            'created_at' => $customer['cust_created_at'],
            'last_login' => $customer['cust_last_login']
        ];
    }, $result['data']);
    
    Response::paginated($customers, $result['total'], $page, $limit, 'Customers retrieved successfully');
}

/**
 * Get admin products list
 */
function handleGetAdminProducts($db) {
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
    $status = $_GET['status'] ?? null;
    $search = $_GET['search'] ?? null;
    
    // Build WHERE clause
    $where_conditions = ['1=1'];
    $params = [];
    
    if ($status !== null) {
        $where_conditions[] = 'p_is_active = ?';
        $params[] = $status;
    }
    
    if ($search) {
        $where_conditions[] = '(p_name LIKE ? OR p_description LIKE ?)';
        $search_term = "%{$search}%";
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            p.p_id,
            p.p_name,
            p.p_current_price,
            p.p_qty,
            p.p_is_active,
            p.p_is_featured,
            p.p_total_view,
            tc.tcat_name as category_name,
            (SELECT SUM(oi.quantity) FROM order_items oi JOIN orders o ON oi.order_id = o.id WHERE oi.product_id = p.p_id AND o.payment_status = 'success') as total_sold
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        WHERE {$where_clause}
        ORDER BY p.p_id DESC
    ";
    
    $result = $db->paginate($sql, $params, $page, $limit);
    
    // Format products
    $products = array_map(function($product) {
        return [
            'id' => (int)$product['p_id'],
            'name' => $product['p_name'],
            'price' => (float)$product['p_current_price'],
            'quantity' => (int)$product['p_qty'],
            'is_active' => (bool)$product['p_is_active'],
            'is_featured' => (bool)$product['p_is_featured'],
            'view_count' => (int)$product['p_total_view'],
            'category' => $product['category_name'],
            'total_sold' => (int)($product['total_sold'] ?? 0),
            'stock_status' => (int)$product['p_qty'] > 0 ? 'in_stock' : 'out_of_stock'
        ];
    }, $result['data']);
    
    Response::paginated($products, $result['total'], $page, $limit, 'Admin products retrieved successfully');
}

/**
 * Update order status
 */
function handleUpdateOrderStatus($db, $order_id, $input) {
    $validator = new Validator($input);
    $validator->in('payment_status', ['pending', 'success', 'failed'])
             ->in('shipping_status', ['processing', 'shipped', 'delivered', 'cancelled']);
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    // Check if order exists
    $order = $db->fetchOne("SELECT * FROM orders WHERE id = ?", [$order_id]);
    
    if (!$order) {
        Response::notFound('Order not found');
    }
    
    $update_data = [];
    $allowed_fields = ['payment_status', 'verification_status', 'shipping_status', 'tracking_number', 'carrier'];
    
    foreach ($allowed_fields as $field) {
        if (isset($input[$field])) {
            $update_data[$field] = $input[$field];
        }
    }
    
    if (empty($update_data)) {
        Response::error('No valid fields to update', 400);
    }
    
    $update_data['updated_at'] = date('Y-m-d H:i:s');
    
    $db->update('orders', $update_data, 'id = ?', [$order_id]);
    
    Response::success(null, 'Order status updated successfully');
}

/**
 * Update customer status
 */
function handleUpdateCustomerStatus($db, $customer_id, $input) {
    $validator = new Validator($input);
    $validator->required('status')->in('status', ['0', '1']);
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    // Check if customer exists
    if (!$db->exists('tbl_customer', 'cust_id = ?', [$customer_id])) {
        Response::notFound('Customer not found');
    }
    
    $db->update('tbl_customer', ['cust_status' => $input['status']], 'cust_id = ?', [$customer_id]);
    
    Response::success(null, 'Customer status updated successfully');
}

/**
 * Get analytics data
 */
function handleGetAnalytics($db) {
    // This is a placeholder for more detailed analytics
    Response::success(['message' => 'Analytics endpoint not implemented yet'], 'Analytics data');
}
?>
