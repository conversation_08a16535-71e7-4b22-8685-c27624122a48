<?php
require_once('header.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_id = isset($_POST['customer_id']) ? (int)$_POST['customer_id'] : 0;
    $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';

    if ($customer_id > 0 && !empty($new_password)) {
        // Hash the new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        // Update the customer's password
        $statement = $pdo->prepare("UPDATE tbl_customer SET cust_password = ? WHERE cust_id = ?");
        $statement->execute(array($hashed_password, $customer_id));
        
        // Redirect back with success message
        $success_message = 'Customer password updated successfully.';
        header('Location: customer.php?success=' . urlencode($success_message));
        exit;
    } else {
        // Redirect back with error message if data is invalid
        $error_message = 'Invalid data provided. Password not updated.';
        header('Location: customer.php?error=' . urlencode($error_message));
        exit;
    }
}

// If not a POST request, redirect back with error
$error_message = 'Invalid request method.';
header('Location: customer.php?error=' . urlencode($error_message));
exit;
?>
