<?php
/**
 * Debug Checkout Process
 * This script helps debug checkout issues by testing all components
 */

// Include session configuration before starting session
include("session_config.php");
session_start();

// Include database connection and functions
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

// Check if user is logged in
$is_logged_in = isUserLoggedIn();

// Handle AJAX requests for testing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'test_checkout':
            try {
                // Simulate checkout process
                $test_data = [
                    'products_subtotal' => 1000,
                    'shipping_fee' => 0,
                    'installation_fee' => 0,
                    'final_total' => 1000,
                    'country_id' => 1,
                    'cart' => [
                        '1-0' => [
                            'product_id' => 1,
                            'variation_id' => null,
                            'quantity' => 1,
                            'price' => 1000,
                            'name' => 'Test Product',
                            'photo' => 'test.jpg',
                            'color_id' => null,
                            'color_name' => null,
                            'installation' => 0
                        ]
                    ]
                ];
                
                // Make internal request to checkout_handler.php
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://localhost/ecom/ecom/checkout_handler.php');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Cookie: ' . $_SERVER['HTTP_COOKIE']
                ]);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo json_encode([
                    'status' => 'success',
                    'http_code' => $http_code,
                    'response' => $response,
                    'parsed_response' => json_decode($response, true)
                ]);
                
            } catch (Exception $e) {
                echo json_encode([
                    'status' => 'error',
                    'message' => $e->getMessage()
                ]);
            }
            exit;
            
        case 'check_database':
            try {
                // Check database tables
                $tables = ['orders', 'order_items', 'tbl_customer', 'tbl_product'];
                $results = [];
                
                foreach ($tables as $table) {
                    try {
                        $stmt = $pdo->query("DESCRIBE $table");
                        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        $results[$table] = [
                            'exists' => true,
                            'columns' => $columns
                        ];
                    } catch (PDOException $e) {
                        $results[$table] = [
                            'exists' => false,
                            'error' => $e->getMessage()
                        ];
                    }
                }
                
                echo json_encode([
                    'status' => 'success',
                    'tables' => $results
                ]);
                
            } catch (Exception $e) {
                echo json_encode([
                    'status' => 'error',
                    'message' => $e->getMessage()
                ]);
            }
            exit;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout Debug Tool</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
            cursor: pointer;
            border: none;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        .test-section { 
            border: 1px solid #ddd; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 5px; 
        }
        .code { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        .test-result { 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 3px; 
            border-left: 4px solid #007bff; 
            background: #f8f9fa; 
        }
        .test-result.pass { border-left-color: #28a745; background: #d4edda; }
        .test-result.fail { border-left-color: #dc3545; background: #f8d7da; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .loading { 
            display: inline-block; 
            width: 20px; 
            height: 20px; 
            border: 3px solid #f3f3f3; 
            border-top: 3px solid #3498db; 
            border-radius: 50%; 
            animation: spin 1s linear infinite; 
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Checkout Debug Tool</h1>
        
        <div class="test-section">
            <h2>System Status</h2>
            <?php if ($is_logged_in): ?>
                <div class="status success">✅ User is logged in</div>
                <p><strong>Customer:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_fname'] . ' ' . $_SESSION['customer']['cust_lname']) ?></p>
                <p><strong>Customer ID:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_id']) ?></p>
            <?php else: ?>
                <div class="status error">❌ User is not logged in</div>
                <p>Please login to test the checkout process.</p>
                <a href="login.php" class="btn">Login</a>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>Database Tests</h2>
            <button class="btn btn-warning" onclick="testDatabase()">Test Database Tables</button>
            <div id="database-results"></div>
        </div>
        
        <div class="test-section">
            <h2>Checkout Process Test</h2>
            <button class="btn btn-success" onclick="testCheckout()">Test Checkout Process</button>
            <div id="checkout-results"></div>
        </div>
        
        <div class="test-section">
            <h2>Session Information</h2>
            <div class="code">
                <strong>Session ID:</strong> <?= htmlspecialchars(session_id()) ?><br>
                <strong>Session Name:</strong> <?= session_name() ?><br>
                <strong>Customer Session:</strong> <?= isset($_SESSION['customer']) ? 'Set' : 'Not set' ?><br>
                <strong>Cart Session:</strong> <?= isset($_SESSION['cart']) ? 'Set (' . count($_SESSION['cart']) . ' items)' : 'Not set' ?><br>
                <strong>Remember Token:</strong> <?= isset($_COOKIE['smartlife_remember']) ? 'Present' : 'Not present' ?><br>
                <strong>PHP Version:</strong> <?= PHP_VERSION ?><br>
                <strong>PDO Available:</strong> <?= class_exists('PDO') ? 'Yes' : 'No' ?>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Error Logs</h2>
            <button class="btn" onclick="showErrorLogs()">Show Recent Error Logs</button>
            <div id="error-logs"></div>
        </div>
        
        <div class="test-section">
            <h2>Quick Actions</h2>
            <a href="cart.php" class="btn">Go to Cart</a>
            <a href="test_cart_sync.php" class="btn">Cart Sync Test</a>
            <a href="test_order_flow.php" class="btn">Order Flow Test</a>
            <a href="test_auth_status.php" class="btn">Auth Status Test</a>
        </div>
    </div>
    
    <script>
        function testDatabase() {
            const resultsDiv = document.getElementById('database-results');
            resultsDiv.innerHTML = '<div class="loading"></div> Testing database tables...';
            
            fetch('debug_checkout.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=check_database'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    let html = '<h3>Database Table Status</h3>';
                    for (const [table, info] of Object.entries(data.tables)) {
                        if (info.exists) {
                            html += `<div class="test-result pass">✅ ${table}: ${info.columns.length} columns</div>`;
                            html += `<div class="code">${info.columns.join(', ')}</div>`;
                        } else {
                            html += `<div class="test-result fail">❌ ${table}: ${info.error}</div>`;
                        }
                    }
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="test-result fail">❌ Database test failed: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="test-result fail">❌ Error: ${error.message}</div>`;
            });
        }
        
        function testCheckout() {
            const resultsDiv = document.getElementById('checkout-results');
            resultsDiv.innerHTML = '<div class="loading"></div> Testing checkout process...';
            
            fetch('debug_checkout.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=test_checkout'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    let html = '<h3>Checkout Test Results</h3>';
                    html += `<div class="test-result ${data.http_code === 200 ? 'pass' : 'fail'}">HTTP Code: ${data.http_code}</div>`;
                    
                    if (data.parsed_response) {
                        const response = data.parsed_response;
                        if (response.status === 'success') {
                            html += `<div class="test-result pass">✅ Checkout successful</div>`;
                            html += `<div class="code">Order ID: ${response.order_id}\nTX Ref: ${response.tx_ref}\nRedirect: ${response.redirect}</div>`;
                        } else {
                            html += `<div class="test-result fail">❌ Checkout failed: ${response.message}</div>`;
                            if (response.debug_info) {
                                html += `<div class="code">Debug Info: ${JSON.stringify(response.debug_info, null, 2)}</div>`;
                            }
                        }
                    } else {
                        html += `<div class="test-result fail">❌ Invalid response format</div>`;
                        html += `<div class="code">Raw Response: ${data.response}</div>`;
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="test-result fail">❌ Test failed: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="test-result fail">❌ Error: ${error.message}</div>`;
            });
        }
        
        function showErrorLogs() {
            const resultsDiv = document.getElementById('error-logs');
            resultsDiv.innerHTML = '<div class="info status">Error logs are typically found in your server\'s error log file. Check your PHP error log or XAMPP error logs for detailed information about checkout failures.</div>';
        }
        
        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Checkout Debug Tool loaded');
            console.log('User logged in:', <?php echo json_encode($is_logged_in); ?>);
            console.log('Session data:', <?php echo json_encode($_SESSION ?? []); ?>);
        });
    </script>
</body>
</html>
