<?php
/**
 * Cart Endpoints
 * Handles shopping cart operations
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path
$sub_path = $segments[1] ?? '';
$item_id = $segments[2] ?? null;

switch ($method) {
    case 'GET':
        if ($sub_path === 'count') {
            handleGetCartCount($db);
        } else {
            handleGetCart($db);
        }
        break;
        
    case 'POST':
        if ($sub_path === 'add') {
            handleAddToCart($db, $input);
        } elseif ($sub_path === 'sync') {
            handleSyncCart($db, $input);
        } else {
            Response::notFound('Cart endpoint not found');
        }
        break;
        
    case 'PUT':
        if ($item_id) {
            handleUpdateCartItem($db, $item_id, $input);
        } else {
            Response::error('Item ID is required', 400);
        }
        break;
        
    case 'DELETE':
        if ($sub_path === 'clear') {
            handleClearCart($db);
        } elseif ($item_id) {
            handleRemoveFromCart($db, $item_id);
        } else {
            Response::error('Item ID is required', 400);
        }
        break;
        
    default:
        Response::methodNotAllowed(['GET', 'POST', 'PUT', 'DELETE']);
}

/**
 * Get cart contents
 */
function handleGetCart($db) {
    // For mobile API, we'll use a session-based approach
    // In a real implementation, you might want to store cart in database for logged-in users
    
    session_start();
    $cart = $_SESSION['cart'] ?? [];
    
    if (empty($cart)) {
        Response::success([
            'items' => [],
            'total_items' => 0,
            'subtotal' => 0,
            'installation_total' => 0,
            'total' => 0,
            'currency' => DEFAULT_CURRENCY
        ], 'Cart is empty');
    }
    
    $cart_items = [];
    $subtotal = 0;
    $installation_total = 0;
    $total_items = 0;
    
    foreach ($cart as $key => $item) {
        // Verify product still exists and get current data
        $product = $db->fetchOne(
            "SELECT p_id, p_name, p_current_price, p_qty, p_featured_photo, installation_fee 
             FROM tbl_product WHERE p_id = ? AND p_is_active = 1",
            [$item['product_id']]
        );
        
        if (!$product) {
            // Remove invalid items from cart
            unset($_SESSION['cart'][$key]);
            continue;
        }
        
        $item_price = $item['price'] ?? $product['p_current_price'];
        $item_total = $item_price * $item['quantity'];
        $installation_fee = $item['installation'] ? ($product['installation_fee'] ?? DEFAULT_INSTALLATION_FEE) : 0;
        
        $cart_items[] = [
            'cart_key' => $key,
            'product_id' => (int)$item['product_id'],
            'variation_id' => $item['variation_id'] ?? null,
            'color_id' => $item['color_id'] ?? null,
            'name' => $item['name'] ?? $product['p_name'],
            'variation_name' => $item['variation_name'] ?? null,
            'color_name' => $item['color_name'] ?? null,
            'color_code' => $item['color_code'] ?? null,
            'size_name' => $item['size_name'] ?? null,
            'price' => (float)$item_price,
            'quantity' => (int)$item['quantity'],
            'subtotal' => (float)$item_total,
            'installation_fee' => (float)$installation_fee,
            'installation_selected' => (bool)($item['installation'] ?? false),
            'image' => $item['photo'] ? '/assets/uploads/' . $item['photo'] : 
                      ($product['p_featured_photo'] ? '/assets/uploads/' . $product['p_featured_photo'] : null),
            'max_quantity' => (int)$product['p_qty']
        ];
        
        $subtotal += $item_total;
        $installation_total += $installation_fee;
        $total_items += $item['quantity'];
    }
    
    $total = $subtotal + $installation_total;
    
    Response::success([
        'items' => $cart_items,
        'total_items' => $total_items,
        'subtotal' => $subtotal,
        'installation_total' => $installation_total,
        'total' => $total,
        'currency' => DEFAULT_CURRENCY
    ], 'Cart retrieved successfully');
}

/**
 * Add item to cart
 */
function handleAddToCart($db, $input) {
    $validator = new Validator($input);
    $validator->required('product_id')->integer('product_id')
             ->required('quantity')->integer('quantity')->min('quantity', 1);
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    $product_id = $input['product_id'];
    $quantity = $input['quantity'];
    $variation_id = $input['variation_id'] ?? null;
    $color_id = $input['color_id'] ?? null;
    $installation = $input['installation'] ?? false;
    
    // Get product details
    $product = $db->fetchOne(
        "SELECT * FROM tbl_product WHERE p_id = ? AND p_is_active = 1",
        [$product_id]
    );
    
    if (!$product) {
        Response::notFound('Product not found');
    }
    
    // Check stock
    $available_stock = (int)$product['p_qty'];
    if ($variation_id) {
        $variation = $db->fetchOne(
            "SELECT * FROM tbl_product_variation WHERE variation_id = ? AND p_id = ?",
            [$variation_id, $product_id]
        );
        if ($variation) {
            $available_stock = (int)$variation['variation_qty'];
        }
    }
    
    if ($quantity > $available_stock) {
        Response::error("Only {$available_stock} items available in stock", 400, 'INSUFFICIENT_STOCK');
    }
    
    // Start session and get cart
    session_start();
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    
    // Create cart key
    $cart_key = $product_id . '-' . ($variation_id ?? '0') . '-' . ($color_id ?? '0');
    
    // Check if item already in cart
    $existing_quantity = $_SESSION['cart'][$cart_key]['quantity'] ?? 0;
    $total_quantity = $existing_quantity + $quantity;
    
    if ($total_quantity > $available_stock) {
        $can_add = $available_stock - $existing_quantity;
        Response::error("Can only add {$can_add} more items. {$existing_quantity} already in cart.", 400, 'INSUFFICIENT_STOCK');
    }
    
    // Get additional item details
    $item_name = $product['p_name'];
    $item_price = $product['p_current_price'];
    $item_photo = $product['p_featured_photo'];
    $variation_name = null;
    $color_name = null;
    $color_code = null;
    $size_name = null;
    
    if ($variation_id) {
        $variation = $db->fetchOne(
            "SELECT * FROM tbl_product_variation WHERE variation_id = ?",
            [$variation_id]
        );
        if ($variation) {
            $variation_name = $variation['variation_name'];
            $item_price = $variation['variation_price'] ?: $item_price;
            $item_photo = $variation['variation_image'] ?: $item_photo;
        }
    }
    
    if ($color_id) {
        $color = $db->fetchOne("SELECT * FROM tbl_color WHERE color_id = ?", [$color_id]);
        if ($color) {
            $color_name = $color['color_name'];
            $color_code = $color['color_code'];
        }
    }
    
    // Add/update item in cart
    if (isset($_SESSION['cart'][$cart_key])) {
        $_SESSION['cart'][$cart_key]['quantity'] += $quantity;
        $_SESSION['cart'][$cart_key]['installation'] = $installation;
    } else {
        $_SESSION['cart'][$cart_key] = [
            'product_id' => $product_id,
            'variation_id' => $variation_id,
            'color_id' => $color_id,
            'quantity' => $quantity,
            'price' => $item_price,
            'name' => $item_name,
            'variation_name' => $variation_name,
            'photo' => $item_photo,
            'installation' => $installation,
            'color_name' => $color_name,
            'color_code' => $color_code,
            'size_name' => $size_name
        ];
    }
    
    // Calculate cart totals
    $cart_count = 0;
    foreach ($_SESSION['cart'] as $cart_item) {
        $cart_count += $cart_item['quantity'];
    }
    
    Response::success([
        'cart_key' => $cart_key,
        'quantity' => $_SESSION['cart'][$cart_key]['quantity'],
        'cart_count' => $cart_count,
        'message' => 'Item added to cart successfully'
    ], 'Item added to cart successfully');
}

/**
 * Update cart item quantity
 */
function handleUpdateCartItem($db, $cart_key, $input) {
    $validator = new Validator($input);
    $validator->required('quantity')->integer('quantity')->min('quantity', 1);
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    session_start();
    
    if (!isset($_SESSION['cart'][$cart_key])) {
        Response::notFound('Cart item not found');
    }
    
    $new_quantity = $input['quantity'];
    $cart_item = $_SESSION['cart'][$cart_key];
    
    // Check stock availability
    $product = $db->fetchOne(
        "SELECT p_qty FROM tbl_product WHERE p_id = ?",
        [$cart_item['product_id']]
    );
    
    $available_stock = (int)$product['p_qty'];
    if ($cart_item['variation_id']) {
        $variation = $db->fetchOne(
            "SELECT variation_qty FROM tbl_product_variation WHERE variation_id = ?",
            [$cart_item['variation_id']]
        );
        if ($variation) {
            $available_stock = (int)$variation['variation_qty'];
        }
    }
    
    if ($new_quantity > $available_stock) {
        Response::error("Only {$available_stock} items available in stock", 400, 'INSUFFICIENT_STOCK');
    }
    
    // Update quantity
    $_SESSION['cart'][$cart_key]['quantity'] = $new_quantity;
    
    Response::success([
        'cart_key' => $cart_key,
        'quantity' => $new_quantity
    ], 'Cart item updated successfully');
}

/**
 * Remove item from cart
 */
function handleRemoveFromCart($db, $cart_key) {
    session_start();
    
    if (!isset($_SESSION['cart'][$cart_key])) {
        Response::notFound('Cart item not found');
    }
    
    unset($_SESSION['cart'][$cart_key]);
    
    Response::success(null, 'Item removed from cart successfully');
}

/**
 * Clear entire cart
 */
function handleClearCart($db) {
    session_start();
    $_SESSION['cart'] = [];
    
    Response::success(null, 'Cart cleared successfully');
}

/**
 * Get cart item count
 */
function handleGetCartCount($db) {
    session_start();
    $cart = $_SESSION['cart'] ?? [];
    
    $count = 0;
    foreach ($cart as $item) {
        $count += $item['quantity'];
    }
    
    Response::success(['count' => $count], 'Cart count retrieved successfully');
}

/**
 * Sync cart with client data
 */
function handleSyncCart($db, $input) {
    $validator = new Validator($input);
    $validator->required('items');
    
    if ($validator->fails()) {
        Response::validationError($validator->getErrors());
    }
    
    session_start();
    $_SESSION['cart'] = [];
    
    foreach ($input['items'] as $item) {
        // Validate each item and add to cart
        $item_validator = new Validator($item);
        $item_validator->required('product_id')->integer('product_id')
                       ->required('quantity')->integer('quantity')->min('quantity', 1);
        
        if ($item_validator->passes()) {
            $cart_key = $item['product_id'] . '-' . ($item['variation_id'] ?? '0') . '-' . ($item['color_id'] ?? '0');
            $_SESSION['cart'][$cart_key] = $item;
        }
    }
    
    Response::success(null, 'Cart synchronized successfully');
}
?>
