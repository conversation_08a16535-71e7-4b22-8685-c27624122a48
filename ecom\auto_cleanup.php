<?php
/**
 * Automatic Cleanup System for Remember Tokens
 * This file should be included in frequently accessed pages to ensure automatic cleanup
 */

// Prevent direct access
if (!defined('SESSION_CONFIG_LOADED')) {
    // Include session configuration if not already loaded
    $session_config_path = __DIR__ . '/session_config.php';
    if (file_exists($session_config_path)) {
        include_once $session_config_path;
    }
}

/**
 * Initialize automatic cleanup system
 * This function should be called on frequently accessed pages
 * @param PDO $pdo Database connection
 */
function initAutoCleanup($pdo) {
    if (!isset($pdo)) {
        return;
    }

    try {
        // Method 1: Time-based cleanup (runs once every 24 hours)
        if (shouldRunCleanup($pdo)) {
            $result = performScheduledCleanup($pdo);
            if ($result['success'] && $result['deleted_count'] > 0) {
                error_log("Auto-cleanup: Removed {$result['deleted_count']} expired tokens at {$result['timestamp']}");
            }
        }

        // Method 2: Probabilistic cleanup (1% chance on each page load)
        // This ensures cleanup happens even if the time-based method fails
        autoCleanupTokens($pdo, 0.01);

    } catch (Exception $e) {
        error_log("Auto-cleanup error: " . $e->getMessage());
    }
}

/**
 * Lightweight cleanup check - runs on every authentication check
 * Only cleans up if there are many expired tokens to avoid performance impact
 * @param PDO $pdo Database connection
 */
function lightweightCleanup($pdo) {
    if (!isset($pdo)) {
        return;
    }

    try {
        // Only run if there are more than 100 expired tokens
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_remember_tokens WHERE expires_at < NOW()");
        $stmt->execute();
        $expired_count = $stmt->fetch()['count'];

        if ($expired_count > 100) {
            cleanupExpiredTokens($pdo);
            error_log("Lightweight cleanup: Removed $expired_count expired tokens due to high count");
        }
    } catch (PDOException $e) {
        // Silently fail to avoid disrupting user experience
        error_log("Lightweight cleanup error: " . $e->getMessage());
    }
}

/**
 * Emergency cleanup - removes tokens older than 60 days regardless of expiry
 * This is a safety measure to prevent token table from growing too large
 * @param PDO $pdo Database connection
 */
function emergencyCleanup($pdo) {
    if (!isset($pdo)) {
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE created_at < DATE_SUB(NOW(), INTERVAL 60 DAY)");
        $stmt->execute();
        $deleted_count = $stmt->rowCount();

        if ($deleted_count > 0) {
            error_log("Emergency cleanup: Removed $deleted_count very old tokens (60+ days)");
        }

        return $deleted_count;
    } catch (PDOException $e) {
        error_log("Emergency cleanup error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get cleanup statistics
 * @param PDO $pdo Database connection
 * @return array|null Cleanup statistics or null on error
 */
function getCleanupStats($pdo) {
    if (!isset($pdo)) {
        return [
            'active_tokens' => 0,
            'expired_tokens' => 0,
            'last_cleanup' => 'Never',
            'last_cleanup_count' => 0,
            'next_cleanup' => 'Now',
            'cleanup_due' => true
        ];
    }

    try {
        $stats = [];

        // Count active tokens
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_remember_tokens WHERE expires_at > NOW()");
        $stmt->execute();
        $stats['active_tokens'] = $stmt->fetch()['count'];

        // Count expired tokens
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_remember_tokens WHERE expires_at <= NOW()");
        $stmt->execute();
        $stats['expired_tokens'] = $stmt->fetch()['count'];

        // Get last cleanup info
        $stmt = $pdo->prepare("SELECT last_cleanup, tokens_deleted FROM tbl_cleanup_log WHERE cleanup_type = 'remember_tokens' ORDER BY last_cleanup DESC LIMIT 1");
        $stmt->execute();
        $last_cleanup = $stmt->fetch();

        $stats['last_cleanup'] = $last_cleanup ? $last_cleanup['last_cleanup'] : 'Never';
        $stats['last_cleanup_count'] = $last_cleanup ? $last_cleanup['tokens_deleted'] : 0;

        // Calculate next cleanup time
        if ($last_cleanup) {
            $next_cleanup = strtotime($last_cleanup['last_cleanup']) + 24 * 60 * 60;
            $stats['next_cleanup'] = date('Y-m-d H:i:s', $next_cleanup);
            $stats['cleanup_due'] = time() > $next_cleanup;
        } else {
            $stats['next_cleanup'] = 'Now';
            $stats['cleanup_due'] = true;
        }

        return $stats;
    } catch (PDOException $e) {
        error_log("Error getting cleanup stats: " . $e->getMessage());
        return [
            'active_tokens' => 0,
            'expired_tokens' => 0,
            'last_cleanup' => 'Error',
            'last_cleanup_count' => 0,
            'next_cleanup' => 'Error',
            'cleanup_due' => false
        ];
    }
}

// Auto-initialize cleanup if database connection is available
if (isset($pdo) && $pdo instanceof PDO) {
    // Run lightweight cleanup on every include
    lightweightCleanup($pdo);

    // Run full cleanup check (time-based and probabilistic)
    initAutoCleanup($pdo);
}
