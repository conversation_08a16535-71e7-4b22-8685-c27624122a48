
// Wrap entire JS in a DOMContentLoaded listener
document.addEventListener('DOMContentLoaded', function() {

    // --- DOM Element References ---
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');
    const thumbnailContainer = document.querySelector('.thumbnail-container');
    const productPriceElement = document.getElementById('productPrice');
    const availabilityElement = document.getElementById('productAvailability');
    const skuElement = document.getElementById('productSku');
    const quantityInput = document.getElementById('quantity');
    const addToCartBtn = document.getElementById('addToCartBtn');
    const addToCartBtnIcon = addToCartBtn?.querySelector('.btn-icon');
    const addToCartBtnText = addToCartBtn?.querySelector('.btn-text');
    const totalPriceElement = document.getElementById('totalPrice');
    const variationNameSelectorContainer = document.getElementById('variationNameSelector');
    const variationDescriptionElement = document.getElementById('variationDescription');
    const variationColorDisplay = document.getElementById('variationColorDisplay');
    const variationColorSwatch = variationColorDisplay?.querySelector('.color-swatch');
    const variationColorName = document.getElementById('variationColorName');
    const variationSizeDisplay = document.getElementById('variationSizeDisplay');
    const variationSizeName = document.getElementById('variationSizeName');
    const selectedVariationIdInput = document.getElementById('selectedVariationId');
    const shippingCountryDropdown = document.getElementById('shippingCountry');
    const shippingCostDisplayElement = document.getElementById('shippingCostDisplay');
    const installationCheckbox = document.getElementById('installation');
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const navLinks = document.getElementById('navLinks');
    const cartCountElement = document.getElementById('cartCount');

    // Check if essential elements exist before proceeding
    if (!document.querySelector('.product-detail-container')) {
        console.error("Product detail container not found. Aborting script.");
        return; // Stop script execution if main container is missing
    }

    // --- Data from PHP ---
    let variationsData = {};
    try {
        const variationsJsonString = '<?php echo json_encode($variations_json ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE); ?>';
        if (variationsJsonString) {
            variationsData = JSON.parse(variationsJsonString) || {};
        }
    } catch (e) {
        console.error("Error parsing variations JSON:", e);
        displayErrorMessage("Error loading product options.");
    }

   

    // --- State Variables ---
    let currentSelectedVariation = variationsData[selectedVariationIdInput?.value] || null;
    let currentShippingCost = 0;

    // --- Helper Functions ---

    function displayErrorMessage(message) {
         if(document.querySelector('.product-info')) {
             document.querySelector('.product-info').insertAdjacentHTML('afterbegin', `<p style="color: red; font-weight: bold;">${message}</p>`);
         }
    }

    function formatPrice(amount) {
        const numericAmount = Number(amount);
        if (isNaN(numericAmount)) {
            console.warn("formatPrice received non-numeric value:", amount);
            return 'Tsh --';
        }
        return 'Tsh ' + numericAmount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
    }

    function updateVariationDisplay(variation) {
        currentSelectedVariation = variation; // Update global state first

        if (variation && typeof variation === 'object' && variation.id !== undefined) {
            if (selectedVariationIdInput) selectedVariationIdInput.value = variation.id;

            const newImageSrc = variation.image || baseProductFeaturedImage;
            if (mainImage && mainImage.src !== newImageSrc) {
                mainImage.src = newImageSrc;
                thumbnails?.forEach(thumb => thumb.classList.toggle('active', thumb.dataset.image === newImageSrc));
            }

            if (productPriceElement) productPriceElement.textContent = formatPrice(variation.price);

            const stock = variation.quantity;
            const isInStock = stock > 0;
            if (availabilityElement) {
                availabilityElement.textContent = isInStock ? 'In Stock' : 'Out of Stock';
                availabilityElement.style.color = isInStock ? '#1cc88a' : '#e74a3b';
            }
            if (quantityInput) {
                quantityInput.max = stock > 0 ? stock : 1;
                quantityInput.disabled = !isInStock;
                quantityInput.min = isInStock ? 1 : 0;
                let currentQty = parseInt(quantityInput.value);
                if (!isInStock || isNaN(currentQty) || currentQty < 1 || currentQty > stock) {
                    quantityInput.value = isInStock ? 1 : 0;
                }
                if (isInStock && parseInt(quantityInput.value) < 1) quantityInput.value = 1;
                 else if (!isInStock) quantityInput.value = 0;
            }

            if (skuElement) skuElement.textContent = 'SKU: ' + (variation.sku !== 'N/A' && variation.sku ? variation.sku : baseSku);
            if (variationDescriptionElement) variationDescriptionElement.innerHTML = variation.description || '';

             if (variationColorDisplay && variationColorName && variationColorSwatch) {
                  if (variation.color_name) {
                      variationColorName.textContent = variation.color_name;
                      variationColorSwatch.style.backgroundColor = variation.color_code || 'transparent';
                      variationColorSwatch.style.display = variation.color_code ? 'inline-block' : 'none';
                      variationColorDisplay.style.display = 'inline-flex';
                  } else {
                      variationColorDisplay.style.display = 'none';
                  }
             }
             if (variationSizeDisplay && variationSizeName) {
                  if (variation.size_name) {
                      variationSizeName.textContent = variation.size_name;
                      variationSizeDisplay.style.display = 'inline-flex';
                  } else {
                      variationSizeDisplay.style.display = 'none';
                  }
              }

        } else { // Handle NO variation selected or invalid data
             currentSelectedVariation = null;
             if (selectedVariationIdInput) selectedVariationIdInput.value = '';

             if (mainImage && mainImage.src !== baseProductFeaturedImage) {
                 mainImage.src = baseProductFeaturedImage;
                 thumbnails?.forEach(thumb => thumb.classList.toggle('active', thumb.dataset.image === baseProductFeaturedImage));
             }
             if (productPriceElement) productPriceElement.textContent = formatPrice(baseProductPrice);
             if (availabilityElement) {
                  const baseInStock = baseProductQuantity > 0;
                  availabilityElement.textContent = baseInStock ? 'In Stock' : 'Out of Stock';
                  availabilityElement.style.color = baseInStock ? '#1cc88a' : '#e74a3b';
             }
             if (skuElement) skuElement.textContent = 'SKU: ' + baseSku;
             if (variationDescriptionElement) variationDescriptionElement.innerHTML = '';
             if (quantityInput) {
                 quantityInput.max = baseProductQuantity > 0 ? baseProductQuantity : 1;
                 quantityInput.disabled = baseProductQuantity <= 0;
                 quantityInput.value = baseProductQuantity > 0 ? 1 : 0;
                 quantityInput.min = baseProductQuantity > 0 ? 1 : 0;
             }
             if(variationColorDisplay) variationColorDisplay.style.display = 'none';
             if(variationSizeDisplay) variationSizeDisplay.style.display = 'none';
        }

        // Always update button state based on current selection (or lack thereof)
        const stockAvailable = currentSelectedVariation ? currentSelectedVariation.quantity > 0 : (!hasVariations && baseProductQuantity > 0);
        const alreadyInCart = checkItemInCart(baseProductId, currentSelectedVariation?.id);
        updateAddToCartButton(stockAvailable, alreadyInCart);

        calculateAndUpdateTotal(); // Always update total price
    }

    function updateAddToCartButton(isInStock, isAlreadyInCart) {
        if (!addToCartBtn || !addToCartBtnText || !addToCartBtnIcon) return;
        let text = '', iconHtml = '', disabled = true;

        if (isAlreadyInCart) {
            text = 'Already in Cart'; iconHtml = '<i class="fas fa-check"></i>'; disabled = true;
        } else if (!isInStock) {
            text = 'Out of Stock'; iconHtml = '<i class="fas fa-times-circle"></i>'; disabled = true;
        } else if (hasVariations && !currentSelectedVariation) {
             text = 'Select Option'; iconHtml = '<i class="fas fa-info-circle"></i>'; disabled = true;
        } else {
            text = 'Add to Cart'; iconHtml = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>'; disabled = false;
        }

        addToCartBtnText.textContent = text;
        addToCartBtnIcon.innerHTML = iconHtml;
        addToCartBtn.disabled = disabled;
    }

    function updateShippingCost() {
         const selectedCountryId = shippingCountryDropdown?.value;
         let cost = 0;

         if (selectedCountryId === "") { cost = 0; }
         else if (selectedCountryId === "0") { cost = defaultShippingCost; }
         else {
             const countryKey = Number(selectedCountryId);
             cost = shippingCostsData[countryKey] !== undefined ? shippingCostsData[countryKey] : defaultShippingCost;
         }

         currentShippingCost = Number(cost) || 0;

         if (shippingCostDisplayElement) {
              // Display cost only if > 0
              shippingCostDisplayElement.textContent = currentShippingCost > 0 ? '+ ' + formatPrice(currentShippingCost) : '';
         }

         calculateAndUpdateTotal();
     }

    function calculateAndUpdateTotal() {
        if (!totalPriceElement || !quantityInput) return;

        let quantity = parseInt(quantityInput.value);
        if (quantityInput.disabled || isNaN(quantity) || quantity < 0) { quantity = 0; }
        if (!quantityInput.disabled && quantity < 1) { quantity = 1; }

        let unitPrice = currentSelectedVariation ? currentSelectedVariation.price : baseProductPrice;
        let currentTotal = unitPrice * quantity;
        currentTotal += currentShippingCost;

        if (installationCheckbox && installationCheckbox.checked) { currentTotal += installationFee; }

        totalPriceElement.textContent = formatPrice(currentTotal);
    }

    function getCartFromLocalStorage() {
        try {
            const cartData = localStorage.getItem('cart');
            return cartData ? JSON.parse(cartData) : [];
        } catch (e) { console.error("LS Cart Read Error:", e); return []; }
    }
     function saveCartToLocalStorage(cart) {
         try { localStorage.setItem('cart', JSON.stringify(cart)); }
         catch (e) { console.error("LS Cart Save Error:", e); }
     }
     function updateCartCountDisplay() {
         if (cartCountElement) {
             const cart = getCartFromLocalStorage();
             cartCountElement.textContent = cart.length;
         }
     }

    function checkItemInCart(productId, variationId = null) {
        let cart = getCartFromLocalStorage();
        const productIdStr = String(productId);
        const varIdToCheck = variationId ? parseInt(variationId) : null;

        return cart.some(item => {
             const itemProductIdStr = String(item.product_id);
             const itemVariationId = item.variation_id ? parseInt(item.variation_id) : null;
             return itemProductIdStr === productIdStr && itemVariationId === varIdToCheck;
         });
    }

    // --- Event Listener Setup ---

    if (variationNameSelectorContainer) {
        variationNameSelectorContainer.addEventListener('change', function(event) {
            if (event.target.type === 'radio' && event.target.classList.contains('variation-name-radio')) {
                const selectedVarId = event.target.value;
                const selectedVariation = variationsData[selectedVarId];
                updateVariationDisplay(selectedVariation);
            }
        });
    } else if (hasVariations) {
         console.warn("Variation name selector container not found (#variationNameSelector), but variations exist.");
    }

    if (thumbnailContainer) {
        thumbnailContainer.addEventListener('click', function(event) {
            const targetThumbnail = event.target.closest('.thumbnail');
            if (!targetThumbnail) return;

            const newImageSrc = targetThumbnail.dataset.image;
            const variationId = targetThumbnail.dataset.variationId;

            if (mainImage && newImageSrc && mainImage.src !== newImageSrc) {
                 mainImage.src = newImageSrc;
                 thumbnails.forEach(t => t.classList.remove('active'));
                 targetThumbnail.classList.add('active');
            }

            if (variationId && variationsData[variationId]) {
                const clickedVariation = variationsData[variationId];
                const correspondingRadio = variationNameSelectorContainer?.querySelector(`input[value="${variationId}"]`);
                if (correspondingRadio && !correspondingRadio.checked) {
                    correspondingRadio.checked = true;
                }
                updateVariationDisplay(clickedVariation);
            }
        });
    }

    quantityInput?.addEventListener('input', calculateAndUpdateTotal);
    installationCheckbox?.addEventListener('change', calculateAndUpdateTotal);
    shippingCountryDropdown?.addEventListener('change', updateShippingCost);

    addToCartBtn?.addEventListener('click', function() {
        const variationId = currentSelectedVariation?.id || null;
        const quantity = parseInt(quantityInput?.value);

        if (hasVariations && !variationId) {
             Swal.fire('Selection Required', 'Please select a product option.', 'warning');
             return;
        }
        const stockToCheck = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
         if (isNaN(quantity) || quantity < 1 || quantity > stockToCheck) {
             Swal.fire('Invalid Quantity', `Please enter a quantity between 1 and ${stockToCheck}.`, 'warning');
             return;
         }
         if (stockToCheck <= 0) {
             Swal.fire('Out of Stock', 'This item is currently out of stock.', 'error');
             return;
         }

        const formData = new FormData();
        formData.append('product_id', baseProductId);
        if (variationId) { formData.append('variation_id', variationId); }
        formData.append('quantity', quantity);
        formData.append('installation', (installationCheckbox && installationCheckbox.checked) ? '1' : '0');
        // formData.append('csrf_token', '<?php // echo $csrf->getToken(); ?>');

        addToCartBtn.disabled = true;
        if (addToCartBtnText && addToCartBtnIcon) {
            addToCartBtnIcon.innerHTML = '<span class="loading-spinner"><i class="fas fa-spinner"></i></span>';
            addToCartBtnText.textContent = 'Adding...';
        }

        const addToCartURL = 'add_to_cart.php'; // !!! VERIFY THIS PATH !!!
        console.log("Attempting to add to cart via:", addToCartURL);
        console.log("Form Data:", Object.fromEntries(formData));

        fetch(addToCartURL, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log("Add to Cart Response Status:", response.status);
            const contentType = response.headers.get("content-type");
            console.log("Add to Cart Response Content-Type:", contentType);
            if (response.ok && contentType && contentType.includes("application/json")) {
                return response.json();
            } else {
                return response.text().then(text => {
                    console.error("Server Response (Non-JSON or Error):", text);
                    let errorMsg = `Server error (${response.status}). Check server logs.`;
                     if (text.toLowerCase().includes('<html')) {
                         errorMsg = `Server returned an HTML page instead of JSON (${response.status}). Check add_to_cart.php for PHP errors.`;
                     } else if (text) {
                         errorMsg = `Server error (${response.status}): ${text.substring(0, 100)}...`;
                     }
                    throw new Error(errorMsg);
                });
            }
         })
        .then(data => {
             console.log("Parsed JSON response from server:", data);
            if (data && data.status === 'success' && data.added_item) {
                const addedItem = data.added_item;
                 try {
                     let cart = getCartFromLocalStorage();
                     const addedProductIdStr = String(addedItem.product_id);
                     const addedVariationId = addedItem.variation_id ? parseInt(addedItem.variation_id) : null;

                     let foundItemIndex = cart.findIndex(item =>
                         String(item.product_id) === addedProductIdStr &&
                         (item.variation_id ? parseInt(item.variation_id) : null) === addedVariationId
                     );

                     if (foundItemIndex > -1) {
                         cart[foundItemIndex].quantity = addedItem.quantity;
                         cart[foundItemIndex].installation = addedItem.installation;
                         console.log("LocalStorage item updated:", cart[foundItemIndex]);
                     } else {
                         const newItemForLocalStorage = {
                             product_id: addedItem.product_id,
                             variation_id: addedVariationId,
                             quantity: addedItem.quantity,
                             price: addedItem.price ?? 0,
                             name: addedItem.name ?? 'Product Name Missing',
                             variation_name: addedItem.variation_name || null,
                             photo: addedItem.photo ?? baseProductFeaturedImage,
                             color_name: addedItem.color_name || null,
                             size_name: addedItem.size_name || null,
                             installation: addedItem.installation ?? false
                         };
                         cart.push(newItemForLocalStorage);
                         console.log("LocalStorage item added:", newItemForLocalStorage);
                     }
                     saveCartToLocalStorage(cart);
                     updateCartCountDisplay();
                 } catch (e) {
                     console.error("Error updating localStorage:", e);
                     Swal.fire('Cart Sync Error', 'Item added, but there was an issue updating the local cart display.', 'warning');
                 }
                Swal.fire({ title: 'Added to Cart!', text: data.message || 'Item added successfully.', icon: 'success', timer: 2000, showConfirmButton: false });
                updateAddToCartButton(true, true); // Update button state immediately
            } else {
                Swal.fire('Error', data.message || 'Could not add item to cart. Unexpected response.', 'error');
                 const stillInStock = currentSelectedVariation ? currentSelectedVariation.quantity > 0 : (!hasVariations && baseProductQuantity > 0);
                 updateAddToCartButton(stillInStock, false); // Reset button state (not in cart)
            }
        })
        .catch(error => {
            console.error('Add to Cart Fetch Error:', error);
            Swal.fire('Request Failed', `Could not add item to cart. ${error.message}`, 'error');
             const stillInStock = currentSelectedVariation ? currentSelectedVariation.quantity > 0 : (!hasVariations && baseProductQuantity > 0);
             const alreadyInCart = checkItemInCart(baseProductId, currentSelectedVariation?.id);
             updateAddToCartButton(stillInStock, alreadyInCart); // Reset to pre-attempt state
        });
    });

    mobileMenuBtn?.addEventListener('click', () => {
        navLinks?.classList.toggle('active');
        mobileMenuBtn.querySelector('.menu-btn')?.classList.toggle('active');
    });

    // --- Initial Page Setup ---
    function initializePage() {
         console.log("Initializing page...");
         // 1. Update display based on the initial variation (if any)
         updateVariationDisplay(currentSelectedVariation);

         // 2. Set initial shipping cost and update total
         updateShippingCost();

         // 3. Update cart count from localStorage on load
         updateCartCountDisplay();
         console.log("Page initialization complete.");
    }

    // Call initialization function
    initializePage();

}); // End DOMContentLoaded Wrapper
