<?php
/**
 * Sanitizes input data to prevent XSS and SQL injection
 * @param string $data The input data to sanitize
 * @return string The sanitized data
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Generates a random token for CSRF protection
 * @return string The generated token
 */
function generateToken() {
    return bin2hex(random_bytes(32));
}

/**
 * Validates an email address
 * @param string $email The email address to validate
 * @return bool True if valid, false otherwise
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Sends an email using <PERSON><PERSON>'s mail function
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email message
 * @param string $from Sender email address
 * @return bool True if email was sent successfully, false otherwise
 */
function sendEmail($to, $subject, $message, $from) {
    $headers = "From: $from\r\n";
    $headers .= "Reply-To: $from\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    return mail($to, $subject, $message, $headers);
}

/**
 * Sends an email using PHPMailer
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email message (HTML format)
 * @return bool True if email was sent successfully, false otherwise
 */
function sendEmailWithPHPMailer($to, $subject, $message) {
    try {
        // Include PHPMailer library
        require_once '../admin/vendor/PHPMailer/src/Exception.php';
        require_once '../admin/vendor/PHPMailer/src/PHPMailer.php';
        require_once '../admin/vendor/PHPMailer/src/SMTP.php';

        // Create a new PHPMailer instance
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = 'mail.smartlifetz.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'W;.7Ya5kQb5I8h';
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port = 465;

        // Additional settings to help with connection issues
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Set timeout
        $mail->Timeout = 60;

        // Set sender name
        $mail->setFrom('<EMAIL>', 'Smart Life Support');

        // Recipients
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $message;

        // Send the email
        $mail->send();
        return true;
    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Message could not be sent. Mailer Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generates a random OTP (One-Time Password)
 * @param int $length Length of the OTP (default: 6)
 * @return string The generated OTP
 */
function generateOTP($length = 6) {
    // Generate a random numeric OTP
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= mt_rand(0, 9);
    }
    return $otp;
}

/**
 * Stores OTP in session with expiration time
 * @param string $email User's email address
 * @param string $otp The OTP to store
 * @param int $expiry Expiration time in seconds (default: 10 minutes)
 */
function storeOTP($email, $otp, $expiry = 600) {
    $_SESSION['otp_data'] = [
        'email' => $email,
        'otp' => $otp,
        'expires' => time() + $expiry
    ];
}

/**
 * Verifies if the provided OTP is valid and not expired
 * @param string $otp The OTP to verify
 * @return bool True if valid, false otherwise
 */
function verifyOTP($otp) {
    if (!isset($_SESSION['otp_data'])) {
        return false;
    }

    $otpData = $_SESSION['otp_data'];

    // Check if OTP has expired
    if (time() > $otpData['expires']) {
        return false;
    }

    // Verify OTP
    return $otp === $otpData['otp'];
}

/**
 * Logs an error message to the error log
 * @param string $message The error message to log
 * @param string $context Additional context information
 */
function logError($message, $context = '') {
    $logMessage = date('[Y-m-d H:i:s] ') . $message;
    if ($context) {
        $logMessage .= ' Context: ' . $context;
    }
    error_log($logMessage);
}

/**
 * Gets the current customer ID
 * @return int|null Customer ID if logged in, null otherwise
 */
function getCurrentCustomerId() {
    return $_SESSION['customer']['cust_id'] ?? null;
}
?>