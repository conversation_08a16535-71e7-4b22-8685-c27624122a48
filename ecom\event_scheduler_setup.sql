-- =====================================================
-- Event Scheduler Setup for Persistent Login System
-- =====================================================
-- This file contains all SQL commands needed to set up
-- automatic cleanup of expired remember tokens using
-- MySQL Event Scheduler
-- =====================================================

-- 1. CHECK IF EVENT SCHEDULER IS ENABLED
-- Run this query to check current status
SELECT @@event_scheduler;
-- Expected results:
-- 'ON' = Event scheduler is enabled
-- 'OFF' = Event scheduler is disabled
-- 'DISABLED' = Event scheduler is disabled at startup

-- 2. ENABLE EVENT SCHEDULER (if not already enabled)
-- This enables the event scheduler for the current session
SET GLOBAL event_scheduler = ON;

-- 3. VERIFY EVENT SCHEDULER STATUS
SHOW VARIABLES LIKE 'event_scheduler';

-- 4. CREATE THE CLEANUP EVENT
-- This event will run daily at 2:00 AM to clean up expired tokens
DELIMITER $$

CREATE EVENT IF NOT EXISTS `cleanup_expired_remember_tokens`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
COMMENT 'Daily cleanup of expired remember me tokens'
DO
BEGIN
    -- Declare variables for logging
    DECLARE deleted_count INT DEFAULT 0;
    DECLARE cleanup_time DATETIME DEFAULT NOW();
    
    -- Delete expired tokens
    DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
    
    -- Get the number of deleted rows
    SET deleted_count = ROW_COUNT();
    
    -- Log the cleanup activity (if cleanup log table exists)
    INSERT IGNORE INTO `tbl_cleanup_log` 
    (`cleanup_type`, `tokens_deleted`, `last_cleanup`) 
    VALUES ('remember_tokens', deleted_count, cleanup_time);
    
    -- Optional: Log to MySQL general log (if enabled)
    -- This will appear in MySQL logs for monitoring
    IF deleted_count > 0 THEN
        SELECT CONCAT('Event Scheduler: Cleaned up ', deleted_count, ' expired remember tokens at ', cleanup_time) AS cleanup_log;
    END IF;
END$$

DELIMITER ;

-- 5. VERIFY THE EVENT WAS CREATED
SHOW EVENTS;

-- 6. CHECK SPECIFIC EVENT DETAILS
SHOW CREATE EVENT `cleanup_expired_remember_tokens`;

-- 7. VIEW ALL EVENTS IN THE DATABASE
SELECT 
    EVENT_NAME,
    EVENT_DEFINITION,
    INTERVAL_VALUE,
    INTERVAL_FIELD,
    STATUS,
    STARTS,
    ENDS,
    LAST_EXECUTED,
    EVENT_COMMENT
FROM information_schema.EVENTS 
WHERE EVENT_SCHEMA = DATABASE();

-- =====================================================
-- OPTIONAL: ADVANCED EVENT CONFIGURATIONS
-- =====================================================

-- 8. CREATE A MORE FREQUENT CLEANUP EVENT (every 6 hours)
-- Uncomment if you want more frequent cleanup
/*
DELIMITER $$

CREATE EVENT IF NOT EXISTS `cleanup_expired_tokens_frequent`
ON SCHEDULE EVERY 6 HOUR
STARTS CURRENT_TIMESTAMP
COMMENT 'Frequent cleanup of expired remember me tokens (every 6 hours)'
DO
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
    SET deleted_count = ROW_COUNT();
    
    INSERT IGNORE INTO `tbl_cleanup_log` 
    (`cleanup_type`, `tokens_deleted`, `last_cleanup`) 
    VALUES ('remember_tokens_frequent', deleted_count, NOW());
END$$

DELIMITER ;
*/

-- 9. CREATE EMERGENCY CLEANUP EVENT (for very old tokens)
-- This runs weekly to remove tokens older than 60 days
DELIMITER $$

CREATE EVENT IF NOT EXISTS `emergency_cleanup_old_tokens`
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_TIMESTAMP
COMMENT 'Weekly emergency cleanup of very old tokens (60+ days)'
DO
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- Delete tokens older than 60 days regardless of expiry
    DELETE FROM `tbl_remember_tokens` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 60 DAY);
    
    SET deleted_count = ROW_COUNT();
    
    INSERT IGNORE INTO `tbl_cleanup_log` 
    (`cleanup_type`, `tokens_deleted`, `last_cleanup`) 
    VALUES ('emergency_cleanup', deleted_count, NOW());
END$$

DELIMITER ;

-- =====================================================
-- EVENT MANAGEMENT COMMANDS
-- =====================================================

-- 10. ENABLE/DISABLE SPECIFIC EVENTS
-- Enable an event
-- ALTER EVENT `cleanup_expired_remember_tokens` ENABLE;

-- Disable an event
-- ALTER EVENT `cleanup_expired_remember_tokens` DISABLE;

-- 11. MODIFY EVENT SCHEDULE
-- Change to run every 12 hours instead of daily
-- ALTER EVENT `cleanup_expired_remember_tokens` 
-- ON SCHEDULE EVERY 12 HOUR;

-- 12. DROP AN EVENT (if needed)
-- DROP EVENT IF EXISTS `cleanup_expired_remember_tokens`;

-- =====================================================
-- MONITORING AND TROUBLESHOOTING
-- =====================================================

-- 13. CHECK EVENT EXECUTION HISTORY
-- View recent cleanup activities
SELECT * FROM `tbl_cleanup_log` 
WHERE `cleanup_type` LIKE '%remember_tokens%' 
ORDER BY `last_cleanup` DESC 
LIMIT 10;

-- 14. CHECK FOR EVENT ERRORS
-- View MySQL error log for event-related issues
-- (This requires access to MySQL error log file)
-- SHOW VARIABLES LIKE 'log_error';

-- 15. MANUAL EVENT EXECUTION (for testing)
-- You can manually trigger the cleanup logic:
/*
DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
SELECT ROW_COUNT() as 'Manually deleted tokens';
*/

-- =====================================================
-- PERSISTENT EVENT SCHEDULER SETUP
-- =====================================================

-- 16. MAKE EVENT SCHEDULER PERSISTENT ACROSS RESTARTS
-- Add this to your MySQL configuration file (my.cnf or my.ini):
-- [mysqld]
-- event_scheduler = ON

-- 17. VERIFY PERSISTENT CONFIGURATION
-- Check if event_scheduler is set to start automatically
SHOW VARIABLES LIKE 'event_scheduler';

-- =====================================================
-- SECURITY CONSIDERATIONS
-- =====================================================

-- 18. GRANT NECESSARY PRIVILEGES
-- Ensure the database user has EVENT privileges:
-- GRANT EVENT ON your_database_name.* TO 'your_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 19. CHECK USER PRIVILEGES
-- SHOW GRANTS FOR CURRENT_USER();

-- =====================================================
-- BACKUP AND RESTORE EVENTS
-- =====================================================

-- 20. BACKUP EVENT DEFINITIONS
-- Use mysqldump to backup events:
-- mysqldump --events --no-data your_database_name > events_backup.sql

-- =====================================================
-- COMPLETION VERIFICATION
-- =====================================================

-- 21. FINAL VERIFICATION SCRIPT
-- Run this to verify everything is working:
SELECT 
    'Event Scheduler Status' as Check_Type,
    @@event_scheduler as Status
UNION ALL
SELECT 
    'Total Events Created' as Check_Type,
    COUNT(*) as Status
FROM information_schema.EVENTS 
WHERE EVENT_SCHEMA = DATABASE()
UNION ALL
SELECT 
    'Cleanup Events Active' as Check_Type,
    COUNT(*) as Status
FROM information_schema.EVENTS 
WHERE EVENT_SCHEMA = DATABASE() 
AND EVENT_NAME LIKE '%cleanup%' 
AND STATUS = 'ENABLED';

-- =====================================================
-- NOTES
-- =====================================================
-- 1. Event Scheduler requires SUPER or EVENT privileges
-- 2. Events are database-specific
-- 3. Event Scheduler must be enabled globally
-- 4. Events will not run if Event Scheduler is disabled
-- 5. Check MySQL error logs if events don't execute
-- 6. Events are stored in information_schema.EVENTS table
-- =====================================================
