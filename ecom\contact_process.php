<?php
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

header('Content-Type: application/json');

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $subject = trim($_POST['subject']);
    $message = trim($_POST['message']);

    // Validate input
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        echo json_encode(['status' => 'error', 'message' => 'All fields are required.']);
        exit;
    }

    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['status' => 'error', 'message' => 'Please enter a valid email address.']);
        exit;
    }

    try {
        // Prepare SQL statement
        $stmt = $pdo->prepare("INSERT INTO tbl_contact_message (name, email, subject, message) VALUES (?, ?, ?, ?)");
        
        // Execute the statement
        $stmt->execute([$name, $email, $subject, $message]);

        // Return success message
        echo json_encode(['status' => 'success', 'message' => 'Thank you for your message. We will get back to you soon.']);
        exit;

    } catch (PDOException $e) {
        // Log error and return user-friendly message
        error_log("Contact form error: " . $e->getMessage());
        echo json_encode(['status' => 'error', 'message' => 'Sorry, there was an error sending your message. Please try again later.']);
        exit;
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method.']);
    exit;
} 