<?php
/**
 * Endpoint Testing Script
 * Tests API endpoints using GET parameters (bypassing URL rewriting)
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>API Endpoint Testing</h1>";

// Get endpoint from URL parameter
$endpoint = $_GET['endpoint'] ?? '';
$action = $_GET['action'] ?? '';

if (empty($endpoint)) {
    echo "<h2>Available Endpoints</h2>";
    echo "<p>Click on any endpoint to test it:</p>";
    echo "<ul>";
    echo "<li><a href='?endpoint=settings&action=app'>Settings - App Config</a></li>";
    echo "<li><a href='?endpoint=products&limit=5'>Products - List (5 items)</a></li>";
    echo "<li><a href='?endpoint=categories'>Categories - List</a></li>";
    echo "<li><a href='?endpoint=shipping&action=countries'>Shipping - Countries</a></li>";
    echo "<li><a href='?endpoint=api_info'>API - Information</a></li>";
    echo "</ul>";
    exit;
}

// Set up variables for endpoints
$method = 'GET';
$segments = [$endpoint];
if ($action) {
    $segments[] = $action;
}
$input = [];

// Set content type to JSON
header('Content-Type: application/json');

echo "<h2>Testing Endpoint: {$endpoint}" . ($action ? "/{$action}" : "") . "</h2>";

try {
    switch ($endpoint) {
        case 'api_info':
            // Manual API info response
            $response = [
                'status' => 'success',
                'message' => 'API is working',
                'data' => [
                    'name' => 'Ecommerce API',
                    'version' => API_VERSION,
                    'timestamp' => date('c'),
                    'endpoints' => [
                        'auth', 'products', 'categories', 'cart', 
                        'orders', 'users', 'search', 'wishlist', 
                        'shipping', 'settings', 'admin'
                    ]
                ]
            ];
            echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
            break;

        case 'settings':
            echo "<h3>Settings Endpoint Response:</h3>";
            ob_start();
            include __DIR__ . '/endpoints/settings.php';
            $output = ob_get_clean();
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            break;

        case 'products':
            echo "<h3>Products Endpoint Response:</h3>";
            $_GET['limit'] = $_GET['limit'] ?? 5;
            ob_start();
            include __DIR__ . '/endpoints/products.php';
            $output = ob_get_clean();
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            break;

        case 'categories':
            echo "<h3>Categories Endpoint Response:</h3>";
            ob_start();
            include __DIR__ . '/endpoints/categories.php';
            $output = ob_get_clean();
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            break;

        case 'shipping':
            echo "<h3>Shipping Endpoint Response:</h3>";
            ob_start();
            include __DIR__ . '/endpoints/shipping.php';
            $output = ob_get_clean();
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            break;

        default:
            echo "<p style='color: red;'>Unknown endpoint: {$endpoint}</p>";
            echo "<p><a href='?'>← Back to endpoint list</a></p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre style='color: red; font-size: 12px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='?'>← Back to endpoint list</a></p>";
echo "<p><small>This test bypasses URL rewriting and tests endpoints directly.</small></p>";
?>
