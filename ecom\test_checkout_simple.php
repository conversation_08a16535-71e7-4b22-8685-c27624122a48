<?php
/**
 * Simple Checkout Test
 * This script tests the checkout process with minimal setup
 */

// Include session configuration before starting session
include("session_config.php");
session_start();

// Include database connection and functions
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

// Check if user is logged in
$is_logged_in = isUserLoggedIn();

if (!$is_logged_in) {
    echo "Please login first: <a href='login.php'>Login</a>";
    exit;
}

// Handle test checkout request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_checkout'])) {
    // Set up test cart in session
    $_SESSION['cart'] = [
        '1-0' => [
            'product_id' => 1,
            'variation_id' => null,
            'quantity' => 1,
            'price' => 1000,
            'name' => 'Test Product',
            'photo' => 'test.jpg',
            'color_id' => null,
            'color_name' => null,
            'installation' => 0
        ]
    ];
    
    // Test data for checkout
    $test_data = [
        'products_subtotal' => 1000,
        'shipping_fee' => 0,
        'installation_fee' => 0,
        'final_total' => 1000,
        'country_id' => 1,
        'cart' => $_SESSION['cart']
    ];
    
    // Make request to checkout_handler.php
    $url = 'http://localhost/ecom/checkout_handler.php';
    $data = json_encode($test_data);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Cookie: ' . $_SERVER['HTTP_COOKIE']
            ],
            'content' => $data
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    $result = json_decode($response, true);
    
    echo "<h2>Checkout Test Result:</h2>";
    echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>";
    
    if ($result && $result['status'] === 'success') {
        echo "<p><strong>✅ Checkout successful!</strong></p>";
        echo "<p>Order ID: " . $result['order_id'] . "</p>";
        echo "<p>TX Ref: " . $result['tx_ref'] . "</p>";
        echo "<p><a href='" . $result['redirect'] . "'>Go to Payment</a></p>";
    } else {
        echo "<p><strong>❌ Checkout failed!</strong></p>";
        if ($result && isset($result['message'])) {
            echo "<p>Error: " . htmlspecialchars($result['message']) . "</p>";
        }
        if ($result && isset($result['debug_info'])) {
            echo "<h3>Debug Info:</h3>";
            echo "<pre>" . htmlspecialchars(json_encode($result['debug_info'], JSON_PRETTY_PRINT)) . "</pre>";
        }
    }
    
    echo "<hr>";
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Checkout Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
            cursor: pointer;
            border: none;
        }
        .btn:hover { background: #0056b3; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Checkout Test</h1>
        
        <div class="status success">✅ User is logged in</div>
        <p><strong>Customer:</strong> <?= htmlspecialchars($_SESSION['customer']['cust_fname'] . ' ' . $_SESSION['customer']['cust_lname']) ?></p>
        
        <h2>Test Checkout Process</h2>
        <p>This will test the checkout process with a simple test product.</p>
        
        <form method="POST">
            <button type="submit" name="test_checkout" class="btn">Run Checkout Test</button>
        </form>
        
        <h3>Current Session Data</h3>
        <pre><?php 
        $session_data = $_SESSION;
        // Remove sensitive data for display
        if (isset($session_data['customer']['cust_password'])) {
            $session_data['customer']['cust_password'] = '[HIDDEN]';
        }
        echo htmlspecialchars(json_encode($session_data, JSON_PRETTY_PRINT)); 
        ?></pre>
        
        <h3>Database Connection Test</h3>
        <?php
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<div class='status success'>✅ Database connection working. Orders table has " . $result['count'] . " records.</div>";
        } catch (PDOException $e) {
            echo "<div class='status error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ?>
        
        <h3>Orders Table Structure</h3>
        <?php
        try {
            $stmt = $pdo->query("DESCRIBE orders");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<pre>";
            foreach ($columns as $column) {
                echo $column['Field'] . " (" . $column['Type'] . ")\n";
            }
            echo "</pre>";
        } catch (PDOException $e) {
            echo "<div class='status error'>❌ Error getting table structure: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ?>
        
        <p><a href="cart.php" class="btn">Go to Cart</a></p>
        <p><a href="debug_checkout.php" class="btn">Advanced Debug Tool</a></p>
    </div>
</body>
</html>
