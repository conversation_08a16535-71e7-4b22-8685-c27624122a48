<?php
/**
 * API Monitoring Dashboard
 * Simple dashboard to monitor API health and statistics
 */

// Include configuration
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../utils/Logger.php';

// Simple authentication (change this in production)
$auth_token = $_GET['token'] ?? '';
$valid_token = 'monitor_' . md5(JWT_SECRET . date('Y-m-d'));

if ($auth_token !== $valid_token) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized. Use token: ' . $valid_token]);
    exit;
}

// Get statistics
try {
    $db = new Database($pdo);
    
    // API Statistics
    $api_stats = Logger::getStats(7);
    
    // Recent errors
    $recent_errors = $db->fetchAll(
        "SELECT endpoint, method, response_code, COUNT(*) as count, MAX(created_at) as last_occurrence
         FROM api_logs 
         WHERE response_code >= 400 AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
         GROUP BY endpoint, method, response_code
         ORDER BY count DESC, last_occurrence DESC
         LIMIT 10"
    );
    
    // System health
    $system_health = [
        'database_status' => 'connected',
        'disk_space' => round(disk_free_space('.') / 1024 / 1024 / 1024, 2) . ' GB',
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
        'php_version' => PHP_VERSION,
        'api_version' => API_VERSION
    ];
    
    // Recent activity
    $recent_activity = $db->fetchAll(
        "SELECT endpoint, method, response_code, response_time, created_at
         FROM api_logs 
         ORDER BY created_at DESC 
         LIMIT 20"
    );
    
    // Top users (by request count)
    $top_users = $db->fetchAll(
        "SELECT user_id, COUNT(*) as request_count, MAX(created_at) as last_request
         FROM api_logs 
         WHERE user_id IS NOT NULL AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
         GROUP BY user_id
         ORDER BY request_count DESC
         LIMIT 10"
    );
    
    // Database table sizes
    $table_sizes = $db->fetchAll(
        "SELECT 
            table_name,
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb',
            table_rows
         FROM information_schema.TABLES 
         WHERE table_schema = ?
         ORDER BY (data_length + index_length) DESC
         LIMIT 10",
        [DB_NAME]
    );
    
    $dashboard_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'api_stats' => $api_stats,
        'recent_errors' => $recent_errors,
        'system_health' => $system_health,
        'recent_activity' => $recent_activity,
        'top_users' => $top_users,
        'table_sizes' => $table_sizes
    ];
    
    // Return JSON if requested
    if (isset($_GET['format']) && $_GET['format'] === 'json') {
        header('Content-Type: application/json');
        echo json_encode($dashboard_data, JSON_PRETTY_PRINT);
        exit;
    }
    
} catch (Exception $e) {
    $error_message = 'Dashboard error: ' . $e->getMessage();
    if (isset($_GET['format']) && $_GET['format'] === 'json') {
        header('Content-Type: application/json');
        echo json_encode(['error' => $error_message]);
        exit;
    }
    $dashboard_data = ['error' => $error_message];
}

// HTML Dashboard
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Monitoring Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { color: #333; margin-bottom: 15px; border-bottom: 2px solid #667eea; padding-bottom: 5px; }
        .stat { display: flex; justify-content: space-between; margin: 10px 0; }
        .stat-value { font-weight: bold; color: #667eea; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .table th, .table td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background: #f8f9fa; }
        .refresh-btn { background: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .refresh-btn:hover { background: #5a6fd8; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>API Monitoring Dashboard</h1>
            <p>Real-time monitoring for Ecommerce API v<?= API_VERSION ?></p>
            <p class="timestamp">Last updated: <?= $dashboard_data['timestamp'] ?? date('Y-m-d H:i:s') ?></p>
            <button class="refresh-btn" onclick="location.reload()">Refresh</button>
        </div>
        
        <?php if (isset($dashboard_data['error'])): ?>
            <div class="card error">
                <h3>Error</h3>
                <p><?= htmlspecialchars($dashboard_data['error']) ?></p>
            </div>
        <?php else: ?>
            
            <div class="grid">
                <!-- API Statistics -->
                <div class="card">
                    <h3>API Statistics (7 days)</h3>
                    <?php if ($api_stats): ?>
                        <div class="stat">
                            <span>Total Requests:</span>
                            <span class="stat-value"><?= number_format($api_stats['total_requests']) ?></span>
                        </div>
                        <div class="stat">
                            <span>Average Response Time:</span>
                            <span class="stat-value"><?= $api_stats['avg_response_time'] ?>ms</span>
                        </div>
                        <div class="stat">
                            <span>Success Rate:</span>
                            <span class="stat-value success">
                                <?php
                                $success_count = 0;
                                foreach ($api_stats['status_codes'] as $status) {
                                    if ($status['response_code'] >= 200 && $status['response_code'] < 400) {
                                        $success_count += $status['count'];
                                    }
                                }
                                $success_rate = $api_stats['total_requests'] > 0 ? round(($success_count / $api_stats['total_requests']) * 100, 1) : 0;
                                echo $success_rate . '%';
                                ?>
                            </span>
                        </div>
                    <?php else: ?>
                        <p>No statistics available</p>
                    <?php endif; ?>
                </div>
                
                <!-- System Health -->
                <div class="card">
                    <h3>System Health</h3>
                    <div class="stat">
                        <span>Database:</span>
                        <span class="stat-value success"><?= $system_health['database_status'] ?></span>
                    </div>
                    <div class="stat">
                        <span>Disk Space:</span>
                        <span class="stat-value"><?= $system_health['disk_space'] ?></span>
                    </div>
                    <div class="stat">
                        <span>Memory Usage:</span>
                        <span class="stat-value"><?= $system_health['memory_usage'] ?></span>
                    </div>
                    <div class="stat">
                        <span>PHP Version:</span>
                        <span class="stat-value"><?= $system_health['php_version'] ?></span>
                    </div>
                </div>
                
                <!-- Recent Errors -->
                <div class="card">
                    <h3>Recent Errors (24h)</h3>
                    <?php if (!empty($recent_errors)): ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Endpoint</th>
                                    <th>Code</th>
                                    <th>Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($recent_errors, 0, 5) as $error): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($error['endpoint']) ?></td>
                                        <td class="error"><?= $error['response_code'] ?></td>
                                        <td><?= $error['count'] ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p class="success">No errors in the last 24 hours!</p>
                    <?php endif; ?>
                </div>
                
                <!-- Top Endpoints -->
                <div class="card">
                    <h3>Top Endpoints (7 days)</h3>
                    <?php if ($api_stats && !empty($api_stats['top_endpoints'])): ?>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Endpoint</th>
                                    <th>Requests</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($api_stats['top_endpoints'], 0, 5) as $endpoint): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($endpoint['endpoint']) ?></td>
                                        <td><?= number_format($endpoint['count']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <p>No endpoint data available</p>
                    <?php endif; ?>
                </div>
                
                <!-- Database Tables -->
                <div class="card">
                    <h3>Database Table Sizes</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Table</th>
                                <th>Size (MB)</th>
                                <th>Rows</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($table_sizes, 0, 5) as $table): ?>
                                <tr>
                                    <td><?= htmlspecialchars($table['table_name']) ?></td>
                                    <td><?= $table['size_mb'] ?></td>
                                    <td><?= number_format($table['table_rows']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Recent Activity -->
                <div class="card">
                    <h3>Recent Activity</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Endpoint</th>
                                <th>Method</th>
                                <th>Code</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($recent_activity, 0, 10) as $activity): ?>
                                <tr>
                                    <td><?= htmlspecialchars($activity['endpoint']) ?></td>
                                    <td><?= $activity['method'] ?></td>
                                    <td class="<?= $activity['response_code'] >= 400 ? 'error' : 'success' ?>">
                                        <?= $activity['response_code'] ?>
                                    </td>
                                    <td class="timestamp"><?= date('H:i:s', strtotime($activity['created_at'])) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 20px; text-align: center; color: #666;">
            <p>Access token: <code><?= $valid_token ?></code></p>
            <p>Add <code>?format=json</code> for JSON output</p>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>
