# Persistent Login System - Implementation Guide

## Overview
This implementation adds "Remember Me" functionality to the SMART LIFE ecommerce login system, allowing users to stay logged in across browser sessions for up to 30 days. The system features **FULLY AUTOMATED CLEANUP** that requires zero manual intervention.

## Features Implemented

### 🔐 Security Features
- **Secure Cookies**: HttpOnly, Secure (HTTPS), SameSite protection
- **Token Hashing**: SHA-256 hashed tokens stored in database
- **Session Regeneration**: New session ID on each login
- **Automatic Cleanup**: Expired tokens are automatically removed
- **Logout Protection**: Clear all tokens on logout

### 🚀 User Experience
- **Remember Me Checkbox**: Optional 30-day persistent login
- **Seamless Auto-login**: Automatic login via secure cookies
- **Cross-browser Support**: Works across different browsers
- **Mobile Friendly**: Optimized for mobile devices

### 🤖 Fully Automated Cleanup System
- **Time-based Cleanup**: Runs automatically every 24 hours
- **Probabilistic Cleanup**: 1% chance on each page load
- **Lightweight Cleanup**: Triggers when 100+ expired tokens exist
- **Emergency Cleanup**: Removes tokens older than 60 days
- **Activity Logging**: All cleanup activities are logged
- **Zero Maintenance**: No manual intervention required

## Files Modified/Created

### New Files
1. `ecom/session_config.php` - Session configuration and token management
2. `ecom/auto_cleanup.php` - **Fully automated cleanup system**
3. `ecom/setup_remember_tokens.php` - Database setup script
4. `ecom/cleanup_dashboard.php` - **Automated cleanup monitoring dashboard**
5. `ecom/test_persistent_login.php` - Comprehensive testing interface
6. `ecom/cleanup_expired_tokens.php` - Manual cleanup tool (backup)
7. `ecom/create_remember_tokens_table.sql` - SQL schema
8. `ecom/PERSISTENT_LOGIN_README.md` - This documentation

### Modified Files
1. `ecom/login.php` - Added remember me checkbox and session config
2. `ecom/login_process.php` - Handle persistent sessions and token creation
3. `ecom/logout.php` - Clear persistent cookies and database tokens
4. `ecom/dashboard.php` - Include session config and auto-cleanup
5. `ecom/cart.php` - Include session config and auto-cleanup
6. `ecom/index.php` - Include session config and auto-cleanup
7. `admin/inc/functions.php` - Enhanced `isUserLoggedIn()` with auto-cleanup

## Installation Steps

### 1. Database Setup
Run the setup script to create the required database table:
```
http://yourdomain.com/ecom/setup_remember_tokens.php
```

Or manually execute the SQL:
```sql
CREATE TABLE IF NOT EXISTS `tbl_remember_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `token_hash` varchar(64) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_hash` (`token_hash`),
  KEY `customer_id` (`customer_id`),
  KEY `expires_at` (`expires_at`),
  FOREIGN KEY (`customer_id`) REFERENCES `tbl_customer` (`cust_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. Configuration
The system is pre-configured with secure defaults:
- **Token Expiry**: 30 days
- **Cookie Name**: `smartlife_remember`
- **Token Length**: 64 characters
- **Session Name**: `SMARTLIFE_SESSION`

### 3. Testing
1. Go to the login page
2. Enter valid credentials
3. Check "Remember me for 30 days"
4. Login successfully
5. Close browser completely
6. Reopen browser and visit any protected page
7. You should be automatically logged in

## How It Works

### Login Process
1. User checks "Remember Me" checkbox
2. System generates secure random token
3. Token is hashed (SHA-256) and stored in database
4. Original token is stored in secure cookie
5. User is logged in normally

### Auto-login Process
1. User visits site without active session
2. System checks for remember token cookie
3. Token is verified against database
4. If valid, user is automatically logged in
5. Session is regenerated for security
6. **Automatic cleanup is triggered**

### Logout Process
1. User clicks logout
2. Remember token is deleted from database
3. Cookie is cleared from browser
4. All tokens for user are optionally cleared
5. User is redirected to login page

### 🤖 Automated Cleanup Process
The system uses multiple automated cleanup methods that require **ZERO manual intervention**:

#### 1. Time-based Cleanup (Primary)
- Runs once every 24 hours automatically
- Triggered on any page load if 24+ hours have passed
- Logs all activity for monitoring

#### 2. Probabilistic Cleanup (Backup)
- 1% chance of running on each page load
- Ensures cleanup happens even if time-based fails
- Lightweight and non-intrusive

#### 3. Lightweight Cleanup (Performance)
- Triggers when 100+ expired tokens exist
- Runs during authentication checks
- Prevents token table from growing too large

#### 4. Emergency Cleanup (Safety)
- Removes tokens older than 60 days regardless of expiry
- Safety measure against database bloat
- Runs during manual cleanup operations

#### 5. Activity Logging
- All cleanup activities are logged in `tbl_cleanup_log`
- Tracks when cleanup ran and how many tokens were removed
- Enables monitoring and troubleshooting

## Security Considerations

### Token Security
- Tokens are cryptographically secure (random_bytes)
- Only hashed versions stored in database
- Tokens expire automatically after 30 days
- Invalid tokens are immediately cleared

### Cookie Security
- HttpOnly: Prevents JavaScript access
- Secure: Only sent over HTTPS (when available)
- SameSite: CSRF protection
- Proper expiration handling

### Session Security
- Session ID regeneration on login
- Strict session mode enabled
- Secure session configuration
- Proper session cleanup

## Maintenance

### Automatic Cleanup
The system includes automatic cleanup of expired tokens:
- Database event runs daily
- Removes tokens older than expiry date
- No manual intervention required

### Manual Cleanup
If needed, manually clean expired tokens:
```sql
DELETE FROM tbl_remember_tokens WHERE expires_at < NOW();
```

### Monitoring
Monitor the remember tokens table:
```sql
-- Check active tokens
SELECT COUNT(*) as active_tokens FROM tbl_remember_tokens WHERE expires_at > NOW();

-- Check tokens per user
SELECT customer_id, COUNT(*) as token_count
FROM tbl_remember_tokens
WHERE expires_at > NOW()
GROUP BY customer_id;
```

## Troubleshooting

### Common Issues

**Users not staying logged in:**
- Check if database table exists
- Verify cookie settings in browser
- Ensure HTTPS for secure cookies (production)

**Automatic cleanup not working:**
- Check if MySQL events are enabled
- Verify user has EVENT privileges
- Run manual cleanup if needed

**Session conflicts:**
- Clear browser cookies
- Check for multiple session_start() calls
- Verify session configuration

### Debug Mode
Enable debug logging by adding to session_config.php:
```php
// Add after other constants
define('DEBUG_PERSISTENT_LOGIN', true);

// Add logging function
function debugLog($message) {
    if (defined('DEBUG_PERSISTENT_LOGIN') && DEBUG_PERSISTENT_LOGIN) {
        error_log("[Persistent Login] " . $message);
    }
}
```

## Configuration Options

### Customizing Token Expiry
Edit `session_config.php`:
```php
// Change from 30 days to 7 days
define('REMEMBER_TOKEN_EXPIRY', 7 * 24 * 60 * 60);
```

### Customizing Cookie Name
Edit `session_config.php`:
```php
// Change cookie name
define('REMEMBER_COOKIE_NAME', 'your_custom_name');
```

### Disabling Auto-cleanup
Remove or comment out the event creation in setup script.

## Testing Tools

### Test Interface
Access the comprehensive test page at:
```
http://yourdomain.com/ecom/test_persistent_login.php
```

This page provides:
- Authentication status check
- Cookie verification
- Database table status
- Session configuration review
- Step-by-step testing instructions

### Automated Cleanup Dashboard
Access the real-time monitoring dashboard at:
```
http://yourdomain.com/ecom/cleanup_dashboard.php
```

This dashboard provides:
- **Real-time Statistics**: Active/expired token counts
- **Cleanup History**: Last 10 cleanup activities with timestamps
- **System Status**: All automation features status
- **Manual Controls**: Force cleanup, emergency cleanup, system testing
- **Auto-refresh**: Updates every 30 seconds automatically

### Cleanup Tool (Backup)
Access the manual cleanup interface at:
```
http://yourdomain.com/ecom/cleanup_expired_tokens.php
```

This tool allows you to:
- View current token statistics
- Clean up expired tokens manually
- Monitor token usage
- Set up maintenance schedules

## PHP Version Compatibility

The system automatically detects your PHP version and uses the appropriate cookie setting method:

- **PHP 7.3+**: Uses modern options array for `setcookie()`
- **PHP < 7.3**: Uses path workaround for SameSite attribute

This ensures compatibility across different PHP versions while maintaining security.

## Production Deployment

### HTTPS Considerations
- Secure cookies are automatically enabled when HTTPS is detected
- For production, ensure SSL certificate is properly configured
- Test the system on your production domain before going live

### Performance Optimization
- Database indexes are automatically created for optimal performance
- Consider setting up automatic cleanup via cron jobs
- Monitor token table size in high-traffic environments

### Security Checklist
- [ ] Database table created with proper foreign keys
- [ ] HTTPS enabled in production
- [ ] Automatic cleanup configured
- [ ] Session configuration reviewed
- [ ] Test persistent login functionality
- [ ] Verify logout clears all tokens

## Support
For issues or questions about the persistent login system, check:
1. **Test Page**: Use `test_persistent_login.php` for diagnostics
2. **Browser Console**: Check for JavaScript errors
3. **PHP Error Logs**: Review server-side issues
4. **Database Logs**: Check for SQL errors
5. **Network Tab**: Monitor cookie/session behavior
6. **Cleanup Tool**: Use `cleanup_expired_tokens.php` for maintenance
