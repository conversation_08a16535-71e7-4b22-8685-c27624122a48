<?php
include 'inc/config.php';

header('Content-Type: application/json'); // Set header for JSON response

if (isset($_GET['tcat_id']) && is_numeric($_GET['tcat_id'])) { // Basic validation
    $tcat_id = (int)$_GET['tcat_id']; // Sanitize input

    $statement = $pdo->prepare("SELECT mcat_id, mcat_name FROM tbl_mid_category WHERE tcat_id = ?");
    $statement->execute(array($tcat_id));
    $result = $statement->fetchAll(PDO::FETCH_ASSOC);

    if ($result) {
        echo json_encode($result);
    } else {
        echo json_encode([]); // Return empty array if no categories
    }
} else {
    echo json_encode(['error' => 'Invalid Top Category ID']); // Error message
}
?>