<?php
// Enable comprehensive error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'sku_errors.log');

require_once('header.php');

// Function to redirect with message parameters (Post-Redirect-Get pattern)
function redirectWithMessage($type, $message) {
    $url = 'sku-management.php?' . $type . '=' . urlencode($message);
    header('Location: ' . $url);
    exit();
}

// Get messages from URL parameters
$success_message = isset($_GET['success']) ? $_GET['success'] : '';
$error_message = isset($_GET['error']) ? $_GET['error'] : '';

// Debug function to log errors and display them
function debugLog($message, $data = null) {
    $logMessage = "[" . date('Y-m-d H:i:s') . "] " . $message;
    if ($data !== null) {
        $logMessage .= " | Data: " . print_r($data, true);
    }
    error_log($logMessage, 3, 'sku_debug.log');

    // Also display in development mode
    if (isset($_GET['debug']) || $_SERVER['HTTP_HOST'] === 'localhost' || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false) {
        echo "<div class='alert alert-info'><strong>DEBUG:</strong> " . htmlspecialchars($logMessage) . "</div>";
    }
}

// Test database connection
try {
    if (!isset($pdo) || !$pdo) {
        throw new Exception("Database connection not established");
    }

    // Test query
    $test = $pdo->query("SELECT 1");
    if (!$test) {
        throw new Exception("Database query test failed");
    }
    debugLog("Database connection successful");
} catch (Exception $e) {
    debugLog("Database connection error: " . $e->getMessage());
    die("Database connection failed. Please check your database configuration.");
}

// Validate SKU code format
function validateSKUCode($skuCode) {
    // SKU must start with SLTZ- followed by user text
    return preg_match('/^SLTZ-.+/', $skuCode) && strlen($skuCode) >= 6;
}

// CRUD Operations
// Handle Delete SKU
if(isset($_POST['delete_sku_confirm'])) {
    $delete_id = $_POST['delete_sku_id'];

    try {
        $statement = $pdo->prepare("DELETE FROM tbl_sku WHERE sku_id = ?");
        $statement->execute(array($delete_id));

        redirectWithMessage('success', 'SKU deleted successfully!');
    } catch(Exception $e) {
        redirectWithMessage('error', 'Error deleting SKU: ' . $e->getMessage());
    }
}

// Handle Load Edit SKU
if(isset($_POST['load_edit_sku'])) {
    $edit_id = $_POST['edit_sku_id'];

    $statement = $pdo->prepare("SELECT * FROM tbl_sku WHERE sku_id = ?");
    $statement->execute(array($edit_id));
    $edit_sku_data = $statement->fetch(PDO::FETCH_ASSOC);

    if($edit_sku_data) {
        // Set flag to show edit modal
        $show_edit_modal = true;
    }
}

// Handle Quick Stock Update
if(isset($_POST['update_stock_quick'])) {
    $sku_id = $_POST['quick_stock_sku_id'];
    $new_quantity = $_POST['new_stock_quantity'];

    if($new_quantity >= 0) {
        try {
            $statement = $pdo->prepare("UPDATE tbl_sku SET quantity = ? WHERE sku_id = ?");
            $statement->execute(array($new_quantity, $sku_id));

            redirectWithMessage('success', 'Stock quantity updated successfully!');
        } catch(Exception $e) {
            redirectWithMessage('error', 'Error updating stock: ' . $e->getMessage());
        }
    } else {
        redirectWithMessage('error', 'Stock quantity cannot be negative!');
    }
}

if(isset($_POST['add_sku'])) {
    debugLog("Add SKU form submitted", $_POST);

    try {
        $valid = 1;
        $error_message = ''; // Reset error message

        // Validate required POST data exists
        $required_fields = ['sku_code', 'product_id', 'price', 'quantity', 'status'];
        foreach($required_fields as $field) {
            if(!isset($_POST[$field])) {
                debugLog("Missing required field: " . $field);
                $valid = 0;
                $error_message .= "Missing required field: " . $field . "<br>";
            }
        }

        if($valid == 0) {
            debugLog("Validation failed - missing required fields", $error_message);
            redirectWithMessage('error', $error_message);
        } else {
            // Auto-add SLTZ- prefix to SKU code
            $sku_code = 'SLTZ-' . trim($_POST['sku_code']);
            debugLog("Generated SKU code: " . $sku_code);

            // Validation
            if(empty(trim($_POST['sku_code']))) {
                $valid = 0;
                $error_message .= "SKU Code cannot be empty<br>";
                debugLog("SKU code is empty");
            } else {
                if(strlen(trim($_POST['sku_code'])) < 3) {
                    $valid = 0;
                    $error_message .= "SKU Code must be at least 3 characters long<br>";
                    debugLog("SKU code too short: " . $_POST['sku_code']);
                } else {
                    // Duplicate SKU checking
                    try {
                        $statement = $pdo->prepare("SELECT * FROM tbl_sku WHERE sku_code=?");
                        $statement->execute([$sku_code]);
                        if($statement->rowCount()) {
                            $valid = 0;
                            $error_message .= "SKU Code already exists<br>";
                            debugLog("Duplicate SKU code found: " . $sku_code);
                        }
                    } catch(PDOException $e) {
                        debugLog("Database error checking SKU duplicate: " . $e->getMessage());
                        $valid = 0;
                        $error_message .= "Database error checking SKU duplicate<br>";
                    }
                }
            }

            if(empty($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
                $valid = 0;
                $error_message .= "Product selection is required<br>";
                debugLog("Invalid product_id: " . ($_POST['product_id'] ?? 'null'));
            } else {
                // Check if product is already assigned to another SKU (uniqueness constraint)
                try {
                    $statement = $pdo->prepare("SELECT sku_code FROM tbl_sku WHERE product_id=?");
                    $statement->execute([$_POST['product_id']]);
                    if($statement->rowCount()) {
                        $existing_sku = $statement->fetch(PDO::FETCH_ASSOC);
                        $valid = 0;
                        $error_message .= "This product is already assigned to SKU: " . $existing_sku['sku_code'] . ". Each product can only be assigned to one SKU.<br>";
                        debugLog("Product already assigned to SKU: " . $existing_sku['sku_code']);
                    }
                } catch(PDOException $e) {
                    debugLog("Database error checking product assignment: " . $e->getMessage());
                    $valid = 0;
                    $error_message .= "Database error checking product assignment<br>";
                }
            }

            if(empty($_POST['price']) || !is_numeric($_POST['price']) || $_POST['price'] <= 0) {
                $valid = 0;
                $error_message .= "Valid price is required<br>";
                debugLog("Invalid price: " . ($_POST['price'] ?? 'null'));
            }

            if(!isset($_POST['quantity']) || !is_numeric($_POST['quantity']) || $_POST['quantity'] < 0) {
                $valid = 0;
                $error_message .= "Valid quantity is required<br>";
                debugLog("Invalid quantity: " . ($_POST['quantity'] ?? 'null'));
            }

            if($valid == 1) {
                debugLog("Validation passed, attempting to save SKU");

                // Auto-generate barcode if not provided
                $barcode = !empty($_POST['barcode']) ? trim($_POST['barcode']) : $sku_code;

                // Prepare data for insertion - handle empty decimal values properly
                $cost_price = (!empty($_POST['cost_price']) && is_numeric($_POST['cost_price'])) ? $_POST['cost_price'] : 0;
                $reorder_level = (!empty($_POST['reorder_level']) && is_numeric($_POST['reorder_level'])) ? $_POST['reorder_level'] : 5;

                $insert_data = [
                    $sku_code,
                    $_POST['product_id'],
                    $_POST['variant_details'] ?? '',
                    $_POST['price'],
                    $cost_price,
                    $_POST['quantity'],
                    $reorder_level,
                    $barcode,
                    $_POST['status']
                ];

                debugLog("Processed insert data with proper decimal handling", $insert_data);

                debugLog("Insert data prepared", $insert_data);

                // Saving data
                try {
                    $statement = $pdo->prepare("INSERT INTO tbl_sku
                        (sku_code, product_id, variant_details, price, cost_price, quantity, reorder_level, barcode, status)
                        VALUES (?,?,?,?,?,?,?,?,?)");

                    $result = $statement->execute($insert_data);

                    if($result) {
                        $new_sku_id = $pdo->lastInsertId();
                        debugLog("SKU added successfully with ID: " . $new_sku_id);
                        redirectWithMessage('success', 'SKU added successfully with ID: ' . $new_sku_id);
                    } else {
                        debugLog("Failed to insert SKU - execute returned false");
                        redirectWithMessage('error', 'Failed to insert SKU into database');
                    }
                } catch(PDOException $e) {
                    debugLog("Database error during SKU insertion: " . $e->getMessage());
                    redirectWithMessage('error', 'Database error: ' . $e->getMessage());
                }
            } else {
                debugLog("Validation failed", $error_message);
                redirectWithMessage('error', $error_message);
            }
        }
    } catch(Exception $e) {
        debugLog("Unexpected error in add_sku: " . $e->getMessage());
        redirectWithMessage('error', 'Unexpected error: ' . $e->getMessage());
    }
}

if(isset($_POST['update_sku'])) {
    debugLog("Update SKU form submitted", $_POST);

    try {
        $valid = 1;
        $error_message = ''; // Reset error message

        // Validate required POST data exists
        $required_fields = ['sku_id', 'sku_code', 'product_id', 'price', 'quantity', 'status'];
        foreach($required_fields as $field) {
            if(!isset($_POST[$field])) {
                debugLog("Missing required field for update: " . $field);
                $valid = 0;
                $error_message .= "Missing required field: " . $field . "<br>";
            }
        }

        if($valid == 0) {
            debugLog("Update validation failed - missing required fields", $error_message);
            redirectWithMessage('error', $error_message);
        } else {
            // Validation (similar to add but exclude SKU code from duplicate check for same record)
            if(empty($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
                $valid = 0;
                $error_message .= "Product selection is required<br>";
                debugLog("Invalid product_id for update: " . ($_POST['product_id'] ?? 'null'));
            } else {
                // Check if product is already assigned to another SKU (uniqueness constraint)
                // Allow the current SKU to keep its product assignment
                try {
                    $statement = $pdo->prepare("SELECT sku_code FROM tbl_sku WHERE product_id=? AND sku_id != ?");
                    $statement->execute([$_POST['product_id'], $_POST['sku_id']]);
                    if($statement->rowCount()) {
                        $existing_sku = $statement->fetch(PDO::FETCH_ASSOC);
                        $valid = 0;
                        $error_message .= "This product is already assigned to SKU: " . $existing_sku['sku_code'] . ". Each product can only be assigned to one SKU.<br>";
                        debugLog("Product already assigned to another SKU during update: " . $existing_sku['sku_code']);
                    }
                } catch(PDOException $e) {
                    debugLog("Database error checking product assignment for update: " . $e->getMessage());
                    $valid = 0;
                    $error_message .= "Database error checking product assignment<br>";
                }
            }

            if(empty($_POST['price']) || !is_numeric($_POST['price']) || $_POST['price'] <= 0) {
                $valid = 0;
                $error_message .= "Valid price is required<br>";
                debugLog("Invalid price for update: " . ($_POST['price'] ?? 'null'));
            }

            if(!isset($_POST['quantity']) || !is_numeric($_POST['quantity']) || $_POST['quantity'] < 0) {
                $valid = 0;
                $error_message .= "Valid quantity is required<br>";
                debugLog("Invalid quantity for update: " . ($_POST['quantity'] ?? 'null'));
            }

            if(empty($_POST['sku_id']) || !is_numeric($_POST['sku_id'])) {
                $valid = 0;
                $error_message .= "Invalid SKU ID<br>";
                debugLog("Invalid sku_id for update: " . ($_POST['sku_id'] ?? 'null'));
            }

            if($valid == 1) {
                debugLog("Update validation passed, attempting to update SKU");

                $barcode = !empty($_POST['barcode']) ? trim($_POST['barcode']) : trim($_POST['sku_code']);

                // Prepare data for update - handle empty decimal values properly
                $cost_price = (!empty($_POST['cost_price']) && is_numeric($_POST['cost_price'])) ? $_POST['cost_price'] : 0;
                $reorder_level = (!empty($_POST['reorder_level']) && is_numeric($_POST['reorder_level'])) ? $_POST['reorder_level'] : 5;

                $update_data = [
                    trim($_POST['sku_code']),
                    $_POST['product_id'],
                    $_POST['variant_details'] ?? '',
                    $_POST['price'],
                    $cost_price,
                    $_POST['quantity'],
                    $reorder_level,
                    $barcode,
                    $_POST['status'],
                    $_POST['sku_id']
                ];

                debugLog("Processed update data with proper decimal handling", $update_data);

                debugLog("Update data prepared", $update_data);

                try {
                    $statement = $pdo->prepare("UPDATE tbl_sku SET
                        sku_code=?, product_id=?, variant_details=?, price=?, cost_price=?, quantity=?,
                        reorder_level=?, barcode=?, status=?, updated_at=NOW()
                        WHERE sku_id=?");

                    $result = $statement->execute($update_data);

                    if($result) {
                        $affected_rows = $statement->rowCount();
                        debugLog("SKU updated successfully, affected rows: " . $affected_rows);
                        redirectWithMessage('success', 'SKU updated successfully (affected rows: ' . $affected_rows . ')');
                    } else {
                        debugLog("Failed to update SKU - execute returned false");
                        redirectWithMessage('error', 'Failed to update SKU in database');
                    }
                } catch(PDOException $e) {
                    debugLog("Database error during SKU update: " . $e->getMessage());
                    redirectWithMessage('error', 'Database error: ' . $e->getMessage());
                }
            } else {
                debugLog("Update validation failed", $error_message);
                redirectWithMessage('error', $error_message);
            }
        }
    } catch(Exception $e) {
        debugLog("Unexpected error in update_sku: " . $e->getMessage());
        redirectWithMessage('error', 'Unexpected error: ' . $e->getMessage());
    }
}

if(isset($_POST['delete_sku'])) {
    $statement = $pdo->prepare("DELETE FROM tbl_sku WHERE sku_id=?");
    $statement->execute(array($_POST['delete_id']));
    redirectWithMessage('success', 'SKU deleted successfully');
}

if(isset($_POST['bulk_delete'])) {
    if(!empty($_POST['sku_ids'])) {
        $ids = implode(',', array_map('intval', $_POST['sku_ids']));
        $statement = $pdo->prepare("DELETE FROM tbl_sku WHERE sku_id IN ($ids)");
        $statement->execute();
        redirectWithMessage('success', count($_POST['sku_ids']) . ' SKU(s) deleted successfully');
    }
}

if(isset($_POST['bulk_status_update'])) {
    if(!empty($_POST['sku_ids']) && isset($_POST['new_status'])) {
        $ids = implode(',', array_map('intval', $_POST['sku_ids']));
        $statement = $pdo->prepare("UPDATE tbl_sku SET status=?, updated_at=NOW() WHERE sku_id IN ($ids)");
        $statement->execute(array($_POST['new_status']));
        redirectWithMessage('success', count($_POST['sku_ids']) . ' SKU(s) status updated successfully');
    }
}

if(isset($_POST['update_stock_modal'])) {
    $sku_id = $_POST['stock_sku_id'];
    $new_quantity = $_POST['stock_quantity'];
    $reason = $_POST['stock_reason'];

    if($new_quantity >= 0) {
        $statement = $pdo->prepare("UPDATE tbl_sku SET quantity=?, updated_at=NOW() WHERE sku_id=?");
        $statement->execute(array($new_quantity, $sku_id));

        // Get SKU code for success message
        $stmt = $pdo->prepare("SELECT sku_code FROM tbl_sku WHERE sku_id=?");
        $stmt->execute(array($sku_id));
        $sku_data = $stmt->fetch(PDO::FETCH_ASSOC);

        $success_message = 'Stock quantity updated successfully for SKU: ' . $sku_data['sku_code'];
        if($reason) {
            $success_message .= ' (Reason: ' . ucfirst(str_replace('_', ' ', $reason)) . ')';
        }
        redirectWithMessage('success', $success_message);
    } else {
        redirectWithMessage('error', 'Stock quantity cannot be negative!');
    }
}

if(isset($_POST['bulk_import']) && isset($_FILES['import_file'])) {
    $file = $_FILES['import_file'];
    $updateExisting = isset($_POST['update_existing']);

    if($file['error'] === UPLOAD_ERR_OK) {
        $handle = fopen($file['tmp_name'], 'r');

        if($handle !== FALSE) {
            $headers = fgetcsv($handle); // Skip header row
            $imported = 0;
            $updated = 0;
            $errors = [];

            while(($data = fgetcsv($handle)) !== FALSE) {
                if(count($data) >= 9) {
                    $skuCode = trim($data[0]);
                    $productId = intval($data[1]);
                    $variantDetails = trim($data[2]);
                    $price = floatval($data[3]);
                    $costPrice = floatval($data[4]);
                    $quantity = intval($data[5]);
                    $reorderLevel = intval($data[6]);
                    $barcode = trim($data[7]);
                    $status = intval($data[8]);

                    // Validate required fields
                    if(empty($skuCode) || $productId <= 0 || $price <= 0) {
                        $errors[] = "Invalid data in row: " . implode(',', $data);
                        continue;
                    }

                    // Check if SKU exists
                    $checkStmt = $pdo->prepare("SELECT sku_id FROM tbl_sku WHERE sku_code = ?");
                    $checkStmt->execute([$skuCode]);
                    $existingSku = $checkStmt->fetch();

                    if($existingSku && $updateExisting) {
                        // Update existing SKU
                        $updateStmt = $pdo->prepare("UPDATE tbl_sku SET
                            product_id=?, variant_details=?, price=?, cost_price=?,
                            quantity=?, reorder_level=?, barcode=?, status=?, updated_at=NOW()
                            WHERE sku_code=?");
                        $updateStmt->execute([
                            $productId, $variantDetails, $price, $costPrice,
                            $quantity, $reorderLevel, $barcode, $status, $skuCode
                        ]);
                        $updated++;
                    } elseif(!$existingSku) {
                        // Insert new SKU
                        $insertStmt = $pdo->prepare("INSERT INTO tbl_sku
                            (sku_code, product_id, variant_details, price, cost_price, quantity, reorder_level, barcode, status)
                            VALUES (?,?,?,?,?,?,?,?,?)");
                        $insertStmt->execute([
                            $skuCode, $productId, $variantDetails, $price, $costPrice,
                            $quantity, $reorderLevel, $barcode, $status
                        ]);
                        $imported++;
                    } else {
                        $errors[] = "SKU $skuCode already exists (skipped)";
                    }
                }
            }

            fclose($handle);

            $success_message = "Import completed: $imported new SKUs imported, $updated SKUs updated";
            if(!empty($errors)) {
                $error_message = "Some errors occurred:<br>" . implode('<br>', array_slice($errors, 0, 10));
                if(count($errors) > 10) {
                    $error_message .= "<br>... and " . (count($errors) - 10) . " more errors";
                }
                redirectWithMessage('error', $error_message);
            } else {
                redirectWithMessage('success', $success_message);
            }
        } else {
            redirectWithMessage('error', 'Could not read the uploaded file');
        }
    } else {
        redirectWithMessage('error', 'File upload error: ' . $file['error']);
    }
}
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>SKU Management</h1>
    </div>
    <div class="content-header-right">
        <button class="btn btn-success btn-sm" data-toggle="modal" data-target="#addModal">
            <i class="fa fa-plus"></i> Add New SKU
        </button>
        <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#importModal">
            <i class="fa fa-upload"></i> Bulk Import
        </button>
        <a href="export-sku.php" class="btn btn-info btn-sm">
            <i class="fa fa-download"></i> Export
        </a>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <!-- Debug Information Panel -->
            <?php if(isset($_GET['debug']) || $_SERVER['HTTP_HOST'] === 'localhost' || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false): ?>
            <div class="callout callout-info">
                <h4><i class="fa fa-info-circle"></i> Debug Information</h4>
                <p><strong>Server:</strong> <?php echo $_SERVER['HTTP_HOST']; ?></p>
                <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                <p><strong>Database Connected:</strong> <?php echo isset($pdo) && $pdo ? 'Yes' : 'No'; ?></p>
                <p><strong>Session Active:</strong> <?php echo isset($_SESSION['user']) ? 'Yes (' . $_SESSION['user']['full_name'] . ')' : 'No'; ?></p>
                <p><strong>Request Method:</strong> <?php echo $_SERVER['REQUEST_METHOD']; ?></p>
                <?php if($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                <p><strong>POST Data:</strong> <?php echo count($_POST); ?> fields submitted</p>
                <?php endif; ?>
                <p><a href="sku-debug.php" class="btn btn-sm btn-info">View Full Debug Page</a></p>
            </div>
            <?php endif; ?>

            <!-- Error Messages -->
            <?php if($error_message): ?>
            <div class="callout callout-danger">
                <h4><i class="fa fa-exclamation-triangle"></i> Error</h4>
                <p><?php echo $error_message; ?></p>
                <?php if(isset($_GET['debug'])): ?>
                <p><small><strong>Debug:</strong> Check the log files for more details</small></p>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Success Messages -->
            <?php if($success_message): ?>
            <div class="callout callout-success">
                <h4><i class="fa fa-check-circle"></i> Success</h4>
                <p><?php echo $success_message; ?></p>
            </div>
            <?php endif; ?>

            <!-- System Status Check -->
            <?php
            $system_errors = [];

            // Check database connection
            try {
                $test_query = $pdo->query("SELECT COUNT(*) FROM tbl_sku LIMIT 1");
                if(!$test_query) {
                    $system_errors[] = "Database query test failed";
                }
            } catch(Exception $e) {
                $system_errors[] = "Database error: " . $e->getMessage();
            }

            // Check if products exist
            try {
                $product_count = $pdo->query("SELECT COUNT(*) as count FROM tbl_product WHERE p_is_active=1")->fetch()['count'];
                if($product_count == 0) {
                    $system_errors[] = "No active products found. Please add products first.";
                }
            } catch(Exception $e) {
                $system_errors[] = "Error checking products: " . $e->getMessage();
            }

            if(!empty($system_errors)):
            ?>
            <div class="callout callout-warning">
                <h4><i class="fa fa-warning"></i> System Issues Detected</h4>
                <ul>
                    <?php foreach($system_errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- Low Stock Warning -->
            <?php
            $statement = $pdo->prepare("SELECT * FROM tbl_sku WHERE quantity <= reorder_level AND quantity > 0");
            $statement->execute();
            $low_stock_count = $statement->rowCount();

            if($low_stock_count > 0): ?>
            <div class="callout callout-warning">
                <p><i class="fa fa-exclamation-triangle"></i> You have <?php echo $low_stock_count; ?> SKU(s) with low inventory. <a href="#" id="showLowStock">View List</a></p>
            </div>
            <?php endif; ?>

            <!-- Out of Stock Warning -->
            <?php
            $statement = $pdo->prepare("SELECT * FROM tbl_sku WHERE quantity = 0");
            $statement->execute();
            $out_of_stock_count = $statement->rowCount();

            if($out_of_stock_count > 0): ?>
            <div class="callout callout-danger">
                <p><i class="fa fa-times-circle"></i> You have <?php echo $out_of_stock_count; ?> SKU(s) out of stock. <a href="#" id="showOutOfStock">View List</a></p>
            </div>
            <?php endif; ?>

            <!-- Bulk Actions Bar -->
            <div class="box box-default">
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Bulk Actions:</label>
                                <div class="input-group">
                                    <select class="form-control" id="bulkAction">
                                        <option value="">Select Action</option>
                                        <option value="delete">Delete Selected</option>
                                        <option value="activate">Activate Selected</option>
                                        <option value="deactivate">Deactivate Selected</option>
                                    </select>
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" onclick="submitBulkAction()">Apply</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Quick Filters:</label>
                                <div class="btn-group btn-group-sm" role="group" id="quickFilters">
                                    <button type="button" class="btn btn-primary active" onclick="applyFilter('all', this)">
                                        <i class="fa fa-list"></i> All SKUs
                                    </button>
                                    <button type="button" class="btn btn-default" onclick="applyFilter('low-stock', this)">
                                        <i class="fa fa-exclamation-triangle"></i> Low Stock
                                    </button>
                                    <button type="button" class="btn btn-default" onclick="applyFilter('out-of-stock', this)">
                                        <i class="fa fa-times-circle"></i> Out of Stock
                                    </button>
                                    <button type="button" class="btn btn-default" onclick="applyFilter('active', this)">
                                        <i class="fa fa-check-circle"></i> Active
                                    </button>
                                    <button type="button" class="btn btn-default" onclick="applyFilter('inactive', this)">
                                        <i class="fa fa-ban"></i> Inactive
                                    </button>
                                </div>
                                <div class="text-muted" style="margin-top: 5px;">
                                    <small id="filterStatus">Showing all SKUs</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box box-info">
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table id="skuTable" class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th width="3%"><input type="checkbox" id="selectAll"></th>
                                            <th width="5%">#</th>
                                            <th width="15%">SKU Code</th>
                                            <th width="20%">Product</th>
                                            <th width="15%">Variant</th>
                                            <th width="10%">Price</th>
                                            <th width="10%">Stock</th>
                                            <th width="10%">Status</th>
                                            <th width="15%">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $i = 0;
                                        $statement = $pdo->prepare("SELECT
                                            s.*,
                                            p.p_name
                                            FROM tbl_sku s
                                            LEFT JOIN tbl_product p ON s.product_id = p.p_id
                                            ORDER BY s.sku_id DESC");
                                        $statement->execute();
                                        $result = $statement->fetchAll(PDO::FETCH_ASSOC);
                                        foreach ($result as $row) {
                                            $i++;
                                            ?>
                                            <tr class="<?php
                                                if($row['quantity'] == 0) echo 'danger';
                                                elseif($row['quantity'] <= $row['reorder_level']) echo 'warning';
                                            ?>" data-status="<?php echo $row['status']; ?>" data-stock="<?php echo $row['quantity']; ?>" data-reorder="<?php echo $row['reorder_level']; ?>">
                                                <td><input type="checkbox" class="sku-checkbox" value="<?php echo $row['sku_id']; ?>"></td>
                                                <td><?php echo $i; ?></td>
                                                <td><?php echo $row['sku_code']; ?></td>
                                                <td><?php echo $row['p_name']; ?></td>
                                                <td><?php echo $row['variant_details']; ?></td>
                                                <td><?php echo 'TZS '.number_format($row['price'], 0); ?></td>
                                                <td>
                                                    <span class="stock-quantity"><?php echo $row['quantity']; ?></span>
                                                    <?php if($row['quantity'] <= $row['reorder_level'] && $row['quantity'] > 0): ?>
                                                    <span class="label label-warning">Low</span>
                                                    <?php elseif($row['quantity'] == 0): ?>
                                                    <span class="label label-danger">Out</span>
                                                    <?php endif; ?>
                                                    <button class="btn btn-xs btn-info"
                                                            onclick="openStockModal(<?php echo $row['sku_id']; ?>, '<?php echo addslashes($row['sku_code']); ?>', <?php echo $row['quantity']; ?>)"
                                                            title="Edit Stock Quantity">
                                                        <i class="fa fa-edit"></i>
                                                    </button>
                                                </td>
                                                <td>
                                                    <?php if($row['status'] == 1): ?>
                                                    <span class="label label-success">Active</span>
                                                    <?php else: ?>
                                                    <span class="label label-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <!-- Edit Button -->
                                                    <button type="button" class="btn btn-primary btn-xs"
                                                            onclick="openEditModal(<?php echo $row['sku_id']; ?>, '<?php echo addslashes($row['sku_code']); ?>', <?php echo $row['product_id']; ?>, '<?php echo addslashes($row['variant_details']); ?>', <?php echo $row['price']; ?>, <?php echo $row['cost_price']; ?>, <?php echo $row['quantity']; ?>, <?php echo $row['reorder_level']; ?>, '<?php echo addslashes($row['barcode']); ?>', <?php echo $row['status']; ?>)"
                                                            title="Edit SKU">
                                                        <i class="fa fa-edit"></i>
                                                    </button>

                                                    <!-- Delete Button -->
                                                    <button type="button" class="btn btn-danger btn-xs"
                                                            onclick="confirmDeleteSKU(<?php echo $row['sku_id']; ?>, '<?php echo addslashes($row['sku_code']); ?>')"
                                                            title="Delete SKU">
                                                        <i class="fa fa-trash"></i>
                                                    </button>

                                                    <!-- Barcode Button -->
                                                    <button class="btn btn-info btn-xs" data-toggle="modal" data-target="#barcodeModal"
                                                            onclick="document.getElementById('barcode_sku_code').textContent='<?php echo $row['sku_code']; ?>'; document.getElementById('barcode_display_code').textContent='<?php echo $row['sku_code']; ?>';"
                                                            title="View Barcode">
                                                        <i class="fa fa-barcode"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <?php
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add SKU Modal -->
<div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="addModalLabel">Add New SKU</h4>
            </div>
            <form action="" method="post" id="addSkuForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="sku_code">SKU Code *</label>
                                <div class="input-group">
                                    <span class="input-group-addon">SLTZ-</span>
                                    <input type="text" class="form-control" id="sku_code" name="sku_code" required
                                           placeholder="YOURTEXT (e.g., REDLARGE, 2024WINTER)">
                                </div>
                                <small class="text-muted">Enter your custom text (minimum 3 characters). SLTZ- prefix will be added automatically.</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status">Status *</label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="product_id">Product *</label>
                        <select class="form-control" id="product_id" name="product_id" required>
                            <option value="">Select Product</option>
                            <?php
                            // Get products that are already assigned to SKUs
                            $assigned_stmt = $pdo->prepare("SELECT DISTINCT product_id FROM tbl_sku");
                            $assigned_stmt->execute();
                            $assigned_products = $assigned_stmt->fetchAll(PDO::FETCH_COLUMN);

                            // Get all active products
                            $statement = $pdo->prepare("SELECT * FROM tbl_product WHERE p_is_active=1 ORDER BY p_name ASC");
                            $statement->execute();
                            $products = $statement->fetchAll(PDO::FETCH_ASSOC);

                            foreach ($products as $product) {
                                // Only show products that are not already assigned to SKUs
                                if (!in_array($product['p_id'], $assigned_products)) {
                                    echo '<option value="'.$product['p_id'].'" data-price="'.$product['p_current_price'].'" data-stock="'.$product['p_qty'].'">'.$product['p_name'].'</option>';
                                }
                            }
                            ?>
                        </select>
                        <small class="text-muted">Only products not already assigned to SKUs are shown</small>
                    </div>

                    <!-- Product Info Display -->
                    <div id="productInfo" class="alert alert-info" style="display: none;">
                        <h5>Product Information:</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Current Price:</strong> $<span id="productPrice">0.00</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Available Stock:</strong> <span id="productStock">0</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Variations:</strong> <span id="productVariationCount">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Variant Selection Section -->
                    <div id="variantSection" style="display: none;">
                        <div class="form-group">
                            <label>Select Product Variants</label>

                            <!-- Existing Product Variations -->
                            <div id="existingVariations" style="display: none;">
                                <h6 class="text-primary"><i class="fa fa-list"></i> Existing Product Variations:</h6>
                                <div id="existingVariationsList" class="well well-sm" style="max-height: 200px; overflow-y: auto;">
                                    <!-- Populated by JavaScript -->
                                </div>
                            </div>

                            <!-- Custom Variant Builder -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h6 class="panel-title">
                                        <a data-toggle="collapse" href="#variantBuilder" aria-expanded="false">
                                            <i class="fa fa-plus"></i> Create Custom Variant Combination
                                        </a>
                                    </h6>
                                </div>
                                <div id="variantBuilder" class="panel-collapse collapse">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Available Colors:</label>
                                                    <div id="colorOptions">
                                                        <!-- Populated by JavaScript -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Available Sizes:</label>
                                                    <div id="sizeOptions">
                                                        <!-- Populated by JavaScript -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Custom Attributes:</label>
                                            <input type="text" class="form-control" id="customAttributes"
                                                   placeholder="e.g., Material: Cotton, Style: Casual">
                                            <small class="text-muted">Add any additional variant attributes</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-primary" id="buildVariant">
                                            <i class="fa fa-magic"></i> Build Variant Description
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Previously Used Variants -->
                            <div id="previousVariants" style="display: none;">
                                <h6 class="text-success"><i class="fa fa-history"></i> Previously Used SKU Variants:</h6>
                                <div id="previousVariantsList" class="well well-sm" style="max-height: 150px; overflow-y: auto;">
                                    <!-- Populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="variant_details">Variant Details</label>
                        <textarea class="form-control" id="variant_details" name="variant_details" rows="2"
                            placeholder="e.g., Size: XL, Color: Red, Material: Cotton"></textarea>
                        <small class="text-muted">
                            <i class="fa fa-info-circle"></i>
                            Select from existing variants above or enter custom variant details manually
                        </small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="price">Selling Price *</label>
                                <div class="input-group">
                                    <span class="input-group-addon">TZS</span>
                                    <input type="number" step="1" class="form-control" id="price" name="price" required min="0">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-default" id="useProductPrice" title="Use Product Price">
                                            <i class="fa fa-copy"></i>
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cost_price">Cost Price</label>
                                <div class="input-group">
                                    <span class="input-group-addon">TZS</span>
                                    <input type="number" step="1" class="form-control" id="cost_price" name="cost_price" min="0">
                                </div>
                                <small class="text-muted">Optional: For profit margin calculation</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quantity">Initial Quantity *</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" required min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="reorder_level">Reorder Level</label>
                                <input type="number" class="form-control" id="reorder_level" name="reorder_level" min="0" value="5">
                                <small class="text-muted">Alert when stock falls below this level</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="barcode">Barcode/UPC</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="barcode" name="barcode" placeholder="Leave empty to auto-generate">
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default" id="generateBarcode" title="Generate Barcode">
                                    <i class="fa fa-barcode"></i>
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" name="add_sku">
                        <i class="fa fa-save"></i> Save SKU
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit SKU Modal -->
<div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="editModalLabel">Edit SKU</h4>
            </div>
            <form action="" method="post" id="editSkuForm">
                <input type="hidden" id="edit_sku_id" name="sku_id" value="<?php echo isset($edit_sku_data) ? $edit_sku_data['sku_id'] : ''; ?>">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="edit_sku_code">SKU Code *</label>
                                <input type="text" class="form-control" id="edit_sku_code" name="sku_code" value="<?php echo isset($edit_sku_data) ? $edit_sku_data['sku_code'] : ''; ?>" required>
                                <small class="text-muted">You can modify the SKU code if needed</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_status">Status *</label>
                                <select class="form-control" id="edit_status" name="status" required>
                                    <option value="1" <?php echo (isset($edit_sku_data) && $edit_sku_data['status'] == 1) ? 'selected' : ''; ?>>Active</option>
                                    <option value="0" <?php echo (isset($edit_sku_data) && $edit_sku_data['status'] == 0) ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_product_id">Product *</label>
                        <select class="form-control" id="edit_product_id" name="product_id" required>
                            <option value="">Select Product</option>
                            <?php
                            // Get all active products for edit modal
                            $statement = $pdo->prepare("SELECT * FROM tbl_product WHERE p_is_active=1 ORDER BY p_name ASC");
                            $statement->execute();
                            $all_products = $statement->fetchAll(PDO::FETCH_ASSOC);

                            foreach ($all_products as $product) {
                                echo '<option value="'.$product['p_id'].'" data-price="'.$product['p_current_price'].'" data-stock="'.$product['p_qty'].'">'.$product['p_name'].'</option>';
                            }
                            ?>
                        </select>
                        <small class="text-muted">You can keep the current product or change to any unassigned product</small>
                    </div>

                    <!-- Edit Variant Selection Section -->
                    <div id="editVariantSection" style="display: none;">
                        <div class="form-group">
                            <label>Update Product Variants</label>

                            <!-- Existing Product Variations for Edit -->
                            <div id="editExistingVariations" style="display: none;">
                                <h6 class="text-primary"><i class="fa fa-list"></i> Available Product Variations:</h6>
                                <div id="editExistingVariationsList" class="well well-sm" style="max-height: 150px; overflow-y: auto;">
                                    <!-- Populated by JavaScript -->
                                </div>
                            </div>

                            <!-- Previously Used Variants for Edit -->
                            <div id="editPreviousVariants" style="display: none;">
                                <h6 class="text-success"><i class="fa fa-history"></i> Previously Used SKU Variants:</h6>
                                <div id="editPreviousVariantsList" class="well well-sm" style="max-height: 100px; overflow-y: auto;">
                                    <!-- Populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_variant_details">Variant Details</label>
                        <textarea class="form-control" id="edit_variant_details" name="variant_details" rows="2"
                            placeholder="e.g., Size: XL, Color: Red, Material: Cotton"><?php echo isset($edit_sku_data) ? $edit_sku_data['variant_details'] : ''; ?></textarea>
                        <small class="text-muted">
                            <i class="fa fa-info-circle"></i>
                            Select from available variants above or modify manually
                        </small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_price">Selling Price *</label>
                                <div class="input-group">
                                    <span class="input-group-addon">TZS</span>
                                    <input type="number" step="1" class="form-control" id="edit_price" name="price" required min="0" value="<?php echo isset($edit_sku_data) ? $edit_sku_data['price'] : ''; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_cost_price">Cost Price</label>
                                <div class="input-group">
                                    <span class="input-group-addon">TZS</span>
                                    <input type="number" step="1" class="form-control" id="edit_cost_price" name="cost_price" min="0" value="<?php echo isset($edit_sku_data) ? $edit_sku_data['cost_price'] : ''; ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_quantity">Quantity *</label>
                                <input type="number" class="form-control" id="edit_quantity" name="quantity" required min="0" value="<?php echo isset($edit_sku_data) ? $edit_sku_data['quantity'] : ''; ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_reorder_level">Reorder Level</label>
                                <input type="number" class="form-control" id="edit_reorder_level" name="reorder_level" min="0" value="<?php echo isset($edit_sku_data) ? $edit_sku_data['reorder_level'] : ''; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_barcode">Barcode/UPC</label>
                        <input type="text" class="form-control" id="edit_barcode" name="barcode" value="<?php echo isset($edit_sku_data) ? $edit_sku_data['barcode'] : ''; ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" name="update_sku">
                        <i class="fa fa-save"></i> Update SKU
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<!-- Barcode Modal -->
<div class="modal fade" id="barcodeModal" tabindex="-1" role="dialog" aria-labelledby="barcodeModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="barcodeModalLabel">Barcode/QR Code</h4>
            </div>
            <div class="modal-body text-center">
                <h3>SKU Code: <span id="barcode_sku_code"></span></h3>
                <div style="border: 2px solid #333; padding: 20px; margin: 20px 0; font-family: monospace; font-size: 24px; letter-spacing: 3px; background: #fff;">
                    ||||| <span id="barcode_display_code"></span> |||||
                </div>
                <p class="text-muted">Scan this code for inventory management</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Stock Edit Modal -->
<div class="modal fade" id="stockModal" tabindex="-1" role="dialog" aria-labelledby="stockModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="stockModalLabel">Edit Stock Quantity</h4>
            </div>
            <form action="" method="post" id="stockEditForm">
                <input type="hidden" id="stock_sku_id" name="stock_sku_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        Editing stock for SKU: <strong id="stock_sku_code"></strong>
                    </div>

                    <div class="form-group">
                        <label for="stock_quantity">Current Stock Quantity *</label>
                        <div class="input-group">
                            <span class="input-group-addon"><i class="fa fa-cubes"></i></span>
                            <input type="number" class="form-control" id="stock_quantity" name="stock_quantity"
                                   required min="0" step="1" placeholder="Enter new quantity">
                            <span class="input-group-addon">units</span>
                        </div>
                        <small class="text-muted">Enter the new stock quantity (must be 0 or greater)</small>
                    </div>

                    <div class="form-group">
                        <label for="stock_reason">Reason for Change (Optional)</label>
                        <select class="form-control" id="stock_reason" name="stock_reason">
                            <option value="">Select reason...</option>
                            <option value="inventory_count">Inventory Count Adjustment</option>
                            <option value="damaged_goods">Damaged Goods</option>
                            <option value="returned_items">Returned Items</option>
                            <option value="manual_correction">Manual Correction</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    <button type="submit" name="update_stock_modal" class="btn btn-primary">
                        <i class="fa fa-save"></i> Update Stock
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="importModalLabel">Bulk Import SKUs</h4>
            </div>
            <form action="" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h5><i class="fa fa-info-circle"></i> Import Instructions:</h5>
                        <ul>
                            <li>Upload a CSV file with the following columns: SKU Code, Product ID, Variant Details, Price, Cost Price, Quantity, Reorder Level, Barcode, Status</li>
                            <li>First row should contain column headers</li>
                            <li>Product ID must exist in the products table</li>
                            <li>Status should be 1 for Active, 0 for Inactive</li>
                        </ul>
                        <a href="sample-sku-import.csv" class="btn btn-sm btn-info">
                            <i class="fa fa-download"></i> Download Sample CSV
                        </a>
                    </div>

                    <div class="form-group">
                        <label for="import_file">Select CSV File *</label>
                        <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv" required>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="update_existing" value="1">
                            Update existing SKUs if duplicate codes found
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" name="bulk_import">
                        <i class="fa fa-upload"></i> Import SKUs
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<!-- SweetAlert2 for modern alerts -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Global error handler for JavaScript
window.onerror = function(msg, url, lineNo, columnNo, error) {
    console.error('JavaScript Error:', {
        message: msg,
        source: url,
        line: lineNo,
        column: columnNo,
        error: error
    });

    // Log to server if possible
    if(typeof $ !== 'undefined') {
        $.post('sku-ajax.php?action=log_js_error', {
            message: msg,
            source: url,
            line: lineNo,
            column: columnNo,
            stack: error ? error.stack : 'No stack trace'
        }).fail(function() {
            console.log('Failed to log JavaScript error to server');
        });
    }

    return false;
};

$(document).ready(function() {
    console.log('SKU Management page loaded');

    try {
        // Initialize DataTable
        var skuTable = $('#skuTable').DataTable({
            "responsive": true,
            "pageLength": 25,
            "order": [[ 1, "desc" ]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] }
            ]
        });
        console.log('DataTable initialized successfully');
    } catch(e) {
        console.error('Error initializing DataTable:', e);
        alert('Error initializing data table: ' + e.message);
    }

    // Select All checkbox
    $('#selectAll').change(function() {
        $('.sku-checkbox').prop('checked', this.checked);
    });

    // COMPLETELY NEW SIMPLE QUICK FILTERS SYSTEM
    $('#quickFilters button').click(function(e) {
        e.preventDefault(); // Prevent any default behavior
        console.log('Filter button clicked!'); // Debug log

        var filter = $(this).data('filter');
        var $this = $(this);

        console.log('Filter type:', filter); // Debug log

        // jQuery event handler (backup - not used since we use onclick)
        // This code is kept as backup but onclick handlers take precedence

        // Update button states
        $('#quickFilters button').removeClass('btn-primary btn-warning btn-danger btn-success btn-info active')
                                 .addClass('btn-default');

        // Set active button with appropriate color
        $this.removeClass('btn-default').addClass('active');
        switch(filter) {
            case 'all':
                $this.addClass('btn-primary');
                break;
            case 'low-stock':
                $this.addClass('btn-warning');
                break;
            case 'out-of-stock':
                $this.addClass('btn-danger');
                break;
            case 'active':
                $this.addClass('btn-success');
                break;
            case 'inactive':
                $this.addClass('btn-info');
                break;
        }

        console.log('Button state updated'); // Debug log

        // Simple DOM-based filtering - much more reliable
        var totalRows = 0;
        var visibleRows = 0;

        $('#skuTable tbody tr').each(function() {
            var $row = $(this);
            totalRows++;

            var status = parseInt($row.data('status'));
            var stock = parseInt($row.data('stock'));
            var reorder = parseInt($row.data('reorder'));
            var showRow = false;

            console.log('Row data - Status:', status, 'Stock:', stock, 'Reorder:', reorder); // Debug log

            switch(filter) {
                case 'all':
                    showRow = true;
                    break;
                case 'low-stock':
                    showRow = (stock <= reorder && stock > 0);
                    break;
                case 'out-of-stock':
                    showRow = (stock === 0);
                    break;
                case 'active':
                    showRow = (status === 1);
                    break;
                case 'inactive':
                    showRow = (status === 0);
                    break;
                default:
                    showRow = true;
            }

            if(showRow) {
                $row.show();
                visibleRows++;
            } else {
                $row.hide();
            }
        });

        console.log('Filter applied - Total rows:', totalRows, 'Visible rows:', visibleRows); // Debug log

        // Update filter status display
        var statusText = '';
        switch(filter) {
            case 'all':
                statusText = 'Showing all ' + totalRows + ' SKUs';
                break;
            case 'low-stock':
                statusText = 'Showing ' + visibleRows + ' low stock SKUs';
                break;
            case 'out-of-stock':
                statusText = 'Showing ' + visibleRows + ' out of stock SKUs';
                break;
            case 'active':
                statusText = 'Showing ' + visibleRows + ' active SKUs';
                break;
            case 'inactive':
                statusText = 'Showing ' + visibleRows + ' inactive SKUs';
                break;
        }
        $('#filterStatus').text(statusText);

        // Update DataTable display (redraw to handle pagination)
        skuTable.draw(false);
    });

    // Use Product Price button
    $('#useProductPrice').click(function() {
        var selectedOption = $('#product_id option:selected');
        var productPrice = selectedOption.data('price');
        if(productPrice) {
            $('#price').val(Math.round(parseFloat(productPrice)));
            Swal.fire({
                icon: 'success',
                title: 'Price Updated',
                text: 'Product price has been applied successfully',
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'No Product Selected',
                text: 'Please select a product first',
                confirmButtonColor: '#3085d6'
            });
        }
    });

    // Product selection handler
    $('#product_id, #edit_product_id').change(function() {
        var productId = $(this).val();
        var selectedOption = $(this).find('option:selected');
        var price = selectedOption.data('price');
        var stock = selectedOption.data('stock');

        if ($(this).attr('id') === 'product_id') {
            $('#productPrice').text(Math.round(parseFloat(price)));
            $('#productStock').text(stock);
            $('#productInfo').show();
            $('#price').val(Math.round(parseFloat(price)));

            // Fetch detailed product variant information
            if (productId) {
                fetchProductVariants(productId);
            } else {
                hideVariantSection();
            }
        } else if ($(this).attr('id') === 'edit_product_id') {
            // Handle edit modal product selection
            if (price && price !== '') {
                $('#edit_price').val(Math.round(parseFloat(price)));
            }

            // Fetch detailed product variant information for edit modal
            if (productId) {
                fetchEditProductVariants(productId);
            } else {
                hideEditVariantSection();
            }
        }
    });

    // Enhanced Form validation with comprehensive error handling
    $('#addSkuForm, #editSkuForm').on('submit', function(e) {
        console.log('Form submission started');

        try {
            var formId = $(this).attr('id');
            var isAddForm = formId === 'addSkuForm';
            console.log('Validating form:', formId);

            // Get form data
            var formData = {
                sku_code: $(this).find('input[name="sku_code"]').val(),
                product_id: $(this).find('select[name="product_id"]').val(),
                price: $(this).find('input[name="price"]').val(),
                quantity: $(this).find('input[name="quantity"]').val(),
                status: $(this).find('select[name="status"]').val()
            };

            console.log('Form data:', formData);

            var errors = [];

            // Validate SKU code
            if(isAddForm) {
                if(!formData.sku_code || formData.sku_code.trim().length < 3) {
                    errors.push('SKU Code must be at least 3 characters long');
                }
            }

            // Validate product selection
            if(!formData.product_id || formData.product_id === '') {
                errors.push('Please select a product');
            }

            // Validate price
            if(!formData.price || isNaN(parseFloat(formData.price)) || parseFloat(formData.price) <= 0) {
                errors.push('Price must be a valid number greater than 0');
            }

            // Validate quantity
            if(formData.quantity === '' || isNaN(parseInt(formData.quantity)) || parseInt(formData.quantity) < 0) {
                errors.push('Quantity must be a valid number (0 or greater)');
            }

            // Validate status
            if(!formData.status || (formData.status !== '0' && formData.status !== '1')) {
                errors.push('Please select a valid status');
            }

            if(errors.length > 0) {
                console.log('Validation errors:', errors);
                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    html: 'Please fix the following errors:<br><ul><li>' + errors.join('</li><li>') + '</li></ul>',
                    confirmButtonColor: '#d33'
                });
                e.preventDefault();
                return false;
            }

            console.log('Form validation passed');

            // Show loading indicator
            Swal.fire({
                title: 'Processing...',
                text: 'Please wait while we save your SKU',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

        } catch(error) {
            console.error('Error during form validation:', error);
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'An error occurred during form validation: ' + error.message,
                confirmButtonColor: '#d33'
            });
            e.preventDefault();
            return false;
        }
    });



    // Auto-show edit modal if edit data is loaded
    <?php if(isset($show_edit_modal) && $show_edit_modal): ?>
        $('#editModal').modal('show');
    <?php endif; ?>

    // Show success/error messages with SweetAlert2
    <?php if($success_message != ''): ?>
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: '<?php echo addslashes($success_message); ?>',
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    <?php endif; ?>

    <?php if($error_message != ''): ?>
        Swal.fire({
            icon: 'error',
            title: 'Error',
            html: '<?php echo addslashes($error_message); ?>',
            confirmButtonColor: '#d33'
        });
    <?php endif; ?>
});

// Global functions that can be called from onclick handlers
function openEditModal(skuId, skuCode, productId, variantDetails, price, costPrice, quantity, reorderLevel, barcode, status) {
    console.log('Opening edit modal for SKU ID:', skuId, 'Product ID:', productId);

    // Populate edit form
    $('#edit_sku_id').val(skuId);
    $('#edit_sku_code').val(skuCode);
    $('#edit_variant_details').val(variantDetails);
    $('#edit_price').val(price);
    $('#edit_cost_price').val(costPrice);
    $('#edit_quantity').val(quantity);
    $('#edit_reorder_level').val(reorderLevel);
    $('#edit_barcode').val(barcode);
    $('#edit_status').val(status);

    // Simply set the product dropdown to the current product
    $('#edit_product_id').val(productId);
    console.log('Product dropdown set to:', productId, 'Current value:', $('#edit_product_id').val());

    // Trigger change event to update price if needed
    $('#edit_product_id').trigger('change');

    // Show the modal
    $('#editModal').modal('show');
}

function saveStockUpdate(skuId, button) {
    var newQuantity = $(button).siblings('.stock-input').val();

    console.log('Saving stock update for SKU ID:', skuId, 'New quantity:', newQuantity);

    if(newQuantity < 0) {
        alert('Quantity cannot be negative');
        return;
    }

    // Create and submit form
    var form = $('<form method="post" action="">' +
        '<input type="hidden" name="update_stock_quick" value="1">' +
        '<input type="hidden" name="quick_stock_sku_id" value="' + skuId + '">' +
        '<input type="hidden" name="new_stock_quantity" value="' + newQuantity + '">' +
        '</form>');
    $('body').append(form);
    form.submit();
}

function openStockModal(skuId, skuCode, currentQuantity) {
    console.log('Opening stock modal for SKU ID:', skuId);

    // Populate stock modal
    $('#stock_sku_id').val(skuId);
    $('#stock_sku_code').text(skuCode);
    $('#stock_quantity').val(currentQuantity);
    $('#stock_reason').val('');

    // Show the modal
    $('#stockModal').modal('show');
}

// GLOBAL FILTER FUNCTION - WORKING PERFECTLY
function applyFilter(filter, button) {
    console.log('Applying filter:', filter);

    // Update button states
    $('#quickFilters button').removeClass('btn-primary btn-warning btn-danger btn-success btn-info active')
                             .addClass('btn-default');

    // Set active button with appropriate color
    $(button).removeClass('btn-default').addClass('active');
    switch(filter) {
        case 'all':
            $(button).addClass('btn-primary');
            break;
        case 'low-stock':
            $(button).addClass('btn-warning');
            break;
        case 'out-of-stock':
            $(button).addClass('btn-danger');
            break;
        case 'active':
            $(button).addClass('btn-success');
            break;
        case 'inactive':
            $(button).addClass('btn-info');
            break;
    }

    console.log('Button state updated for filter:', filter);

    // Apply filter to table rows
    var totalRows = 0;
    var visibleRows = 0;

    $('#skuTable tbody tr').each(function() {
        var $row = $(this);
        totalRows++;

        var status = parseInt($row.data('status'));
        var stock = parseInt($row.data('stock'));
        var reorder = parseInt($row.data('reorder'));
        var showRow = false;

        console.log('Processing row - Status:', status, 'Stock:', stock, 'Reorder:', reorder);

        switch(filter) {
            case 'all':
                showRow = true;
                break;
            case 'low-stock':
                showRow = (stock <= reorder && stock > 0);
                break;
            case 'out-of-stock':
                showRow = (stock === 0);
                break;
            case 'active':
                showRow = (status === 1);
                break;
            case 'inactive':
                showRow = (status === 0);
                break;
            default:
                showRow = true;
        }

        if(showRow) {
            $row.show();
            visibleRows++;
            console.log('Showing row');
        } else {
            $row.hide();
            console.log('Hiding row');
        }
    });

    console.log('Filter completed - Total:', totalRows, 'Visible:', visibleRows);

    // Update status text
    var statusText = '';
    switch(filter) {
        case 'all':
            statusText = 'Showing all ' + totalRows + ' SKUs';
            break;
        case 'low-stock':
            statusText = 'Showing ' + visibleRows + ' low stock SKUs';
            break;
        case 'out-of-stock':
            statusText = 'Showing ' + visibleRows + ' out of stock SKUs';
            break;
        case 'active':
            statusText = 'Showing ' + visibleRows + ' active SKUs';
            break;
        case 'inactive':
            statusText = 'Showing ' + visibleRows + ' inactive SKUs';
            break;
    }
    $('#filterStatus').text(statusText);

    console.log('Status updated:', statusText);
}

// Modern delete confirmation with SweetAlert2
function confirmDeleteSKU(skuId, skuCode) {
    Swal.fire({
        title: 'Delete SKU?',
        html: `Are you sure you want to delete SKU: <strong>${skuCode}</strong>?<br><br>This action cannot be undone.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Create and submit delete form
            var form = $('<form method="post" action="">' +
                '<input type="hidden" name="delete_sku" value="1">' +
                '<input type="hidden" name="delete_id" value="' + skuId + '">' +
                '</form>');
            $('body').append(form);
            form.submit();
        }
    });
}

function submitBulkAction() {
    var action = $('#bulkAction').val();
    var selectedIds = [];

    $('.sku-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
    });

    if(selectedIds.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'No Selection',
            text: 'Please select at least one SKU',
            confirmButtonColor: '#3085d6'
        });
        return;
    }

    if(!action) {
        Swal.fire({
            icon: 'warning',
            title: 'No Action Selected',
            text: 'Please select an action to perform',
            confirmButtonColor: '#3085d6'
        });
        return;
    }

    var confirmMessage = '';
    var actionName = '';
    var iconType = 'warning';
    var confirmButtonColor = '#3085d6';

    switch(action) {
        case 'delete':
            confirmMessage = `Are you sure you want to delete <strong>${selectedIds.length}</strong> selected SKU(s)?<br><br>This action cannot be undone.`;
            actionName = 'bulk_delete';
            iconType = 'warning';
            confirmButtonColor = '#d33';
            break;
        case 'activate':
            confirmMessage = `Are you sure you want to activate <strong>${selectedIds.length}</strong> selected SKU(s)?`;
            actionName = 'bulk_status_update';
            iconType = 'question';
            confirmButtonColor = '#28a745';
            break;
        case 'deactivate':
            confirmMessage = `Are you sure you want to deactivate <strong>${selectedIds.length}</strong> selected SKU(s)?`;
            actionName = 'bulk_status_update';
            iconType = 'question';
            confirmButtonColor = '#ffc107';
            break;
    }

    Swal.fire({
        title: 'Confirm Bulk Action',
        html: confirmMessage,
        icon: iconType,
        showCancelButton: true,
        confirmButtonColor: confirmButtonColor,
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, proceed!',
        cancelButtonText: 'Cancel',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Create and submit form
            var form = $('<form method="post" action="">');

            if(action === 'delete') {
                form.append('<input type="hidden" name="bulk_delete" value="1">');
            } else {
                form.append('<input type="hidden" name="bulk_status_update" value="1">');
                form.append('<input type="hidden" name="new_status" value="' + (action === 'activate' ? '1' : '0') + '">');
            }

            selectedIds.forEach(function(id) {
                form.append('<input type="hidden" name="sku_ids[]" value="' + id + '">');
            });

            $('body').append(form);
            form.submit();
        }
    });
}





    // Form validation
    $('#addSkuForm, #editSkuForm').on('submit', function(e) {
        var price = $(this).find('input[name="price"]').val();
        var quantity = $(this).find('input[name="quantity"]').val();

        if(parseFloat(price) <= 0) {
            alert('Price must be greater than 0');
            e.preventDefault();
            return false;
        }

        if(parseInt(quantity) < 0) {
            alert('Quantity cannot be negative');
            e.preventDefault();
            return false;
        }
    });

    // Product selection handler
    $('#product_id, #edit_product_id').change(function() {
        var productId = $(this).val();
        var selectedOption = $(this).find('option:selected');
        var price = selectedOption.data('price');
        var stock = selectedOption.data('stock');

        if ($(this).attr('id') === 'product_id') {
            $('#productPrice').text(parseFloat(price).toFixed(2));
            $('#productStock').text(stock);
            $('#productInfo').show();
            $('#price').val(price);

            // Fetch detailed product variant information
            if (productId) {
                fetchProductVariants(productId);
            } else {
                hideVariantSection();
            }
        } else if ($(this).attr('id') === 'edit_product_id') {
            // Handle edit modal product selection
            if (price && price !== '') {
                $('#edit_price').val(parseFloat(price).toFixed(2));
            }

            // Fetch detailed product variant information for edit modal
            if (productId) {
                fetchEditProductVariants(productId);
            } else {
                hideEditVariantSection();
            }
        }
    });

    // Function to fetch product variants via AJAX
    function fetchProductVariants(productId) {
        $.get('sku-ajax.php?action=get_product_details&product_id=' + productId, function(response) {
            try {
                var data = JSON.parse(response);
                if (data.success) {
                    displayProductVariants(data);
                } else {
                    console.error('Error fetching variants:', data.error);
                    hideVariantSection();
                }
            } catch (e) {
                console.error('Error parsing variant response:', e);
                hideVariantSection();
            }
        }).fail(function() {
            console.error('Failed to fetch product variants');
            hideVariantSection();
        });
    }

    // Function to display product variants
    function displayProductVariants(data) {
        var hasVariants = false;

        // Show variant section
        $('#variantSection').show();

        // Display existing product variations
        if (data.existing_variations && data.existing_variations.length > 0) {
            hasVariants = true;
            $('#existingVariations').show();
            var variationsHtml = '';

            data.existing_variations.forEach(function(variation) {
                var variantText = '';
                var variantParts = [];

                if (variation.color_name) {
                    variantParts.push('Color: ' + variation.color_name);
                }
                if (variation.size_name) {
                    variantParts.push('Size: ' + variation.size_name);
                }
                if (variation.variation_name) {
                    variantParts.push('Name: ' + variation.variation_name);
                }

                variantText = variantParts.join(', ');
                if (!variantText) variantText = 'Standard Variation';

                variationsHtml += '<div class="variant-option" style="margin-bottom: 5px;">' +
                    '<button type="button" class="btn btn-sm btn-default select-variant" ' +
                    'data-variant="' + variantText + '" ' +
                    'data-price="' + (variation.variation_price || '') + '">' +
                    '<i class="fa fa-tag"></i> ' + variantText +
                    (variation.variation_price ? ' ($' + parseFloat(variation.variation_price).toFixed(2) + ')' : '') +
                    '</button></div>';
            });

            $('#existingVariationsList').html(variationsHtml);
            $('#productVariationCount').text(data.existing_variations.length);
        } else {
            $('#existingVariations').hide();
            $('#productVariationCount').text('0');
        }

        // Display available colors
        if (data.available_colors && data.available_colors.length > 0) {
            hasVariants = true;
            var colorsHtml = '';
            data.available_colors.forEach(function(color) {
                colorsHtml += '<label class="checkbox-inline">' +
                    '<input type="checkbox" class="color-option" value="' + color.color_name + '" ' +
                    'data-color-id="' + color.color_id + '"> ' +
                    '<span class="color-swatch" style="background-color: ' + color.color_code + '; ' +
                    'width: 15px; height: 15px; display: inline-block; margin-right: 5px; border: 1px solid #ccc;"></span>' +
                    color.color_name + '</label>';
            });
            $('#colorOptions').html(colorsHtml);
        } else {
            $('#colorOptions').html('<p class="text-muted">No colors configured for this product</p>');
        }

        // Display available sizes
        if (data.available_sizes && data.available_sizes.length > 0) {
            hasVariants = true;
            var sizesHtml = '';
            data.available_sizes.forEach(function(size) {
                sizesHtml += '<label class="checkbox-inline">' +
                    '<input type="checkbox" class="size-option" value="' + size.size_name + '" ' +
                    'data-size-id="' + size.size_id + '"> ' + size.size_name + '</label>';
            });
            $('#sizeOptions').html(sizesHtml);
        } else {
            $('#sizeOptions').html('<p class="text-muted">No sizes configured for this product</p>');
        }

        // Display previously used SKU variants
        if (data.existing_sku_variants && data.existing_sku_variants.length > 0) {
            $('#previousVariants').show();
            var previousHtml = '';
            data.existing_sku_variants.forEach(function(variant) {
                previousHtml += '<div class="variant-option" style="margin-bottom: 3px;">' +
                    '<button type="button" class="btn btn-xs btn-success select-variant" ' +
                    'data-variant="' + variant + '">' +
                    '<i class="fa fa-history"></i> ' + variant + '</button></div>';
            });
            $('#previousVariantsList').html(previousHtml);
        } else {
            $('#previousVariants').hide();
        }

        if (!hasVariants) {
            $('#variantSection').hide();
        }
    }

    // Function to hide variant section
    function hideVariantSection() {
        $('#variantSection').hide();
        $('#existingVariations').hide();
        $('#previousVariants').hide();
        $('#productVariationCount').text('0');
    }

    // Function to fetch product variants for edit modal
    function fetchEditProductVariants(productId) {
        $.get('sku-ajax.php?action=get_product_details&product_id=' + productId, function(response) {
            try {
                var data = JSON.parse(response);
                if (data.success) {
                    displayEditProductVariants(data);
                } else {
                    console.error('Error fetching edit variants:', data.error);
                    hideEditVariantSection();
                }
            } catch (e) {
                console.error('Error parsing edit variant response:', e);
                hideEditVariantSection();
            }
        }).fail(function() {
            console.error('Failed to fetch edit product variants');
            hideEditVariantSection();
        });
    }

    // Function to display product variants in edit modal
    function displayEditProductVariants(data) {
        var hasVariants = false;

        // Show edit variant section
        $('#editVariantSection').show();

        // Display existing product variations for edit
        if (data.existing_variations && data.existing_variations.length > 0) {
            hasVariants = true;
            $('#editExistingVariations').show();
            var variationsHtml = '';

            data.existing_variations.forEach(function(variation) {
                var variantText = '';
                var variantParts = [];

                if (variation.color_name) {
                    variantParts.push('Color: ' + variation.color_name);
                }
                if (variation.size_name) {
                    variantParts.push('Size: ' + variation.size_name);
                }
                if (variation.variation_name) {
                    variantParts.push('Name: ' + variation.variation_name);
                }

                variantText = variantParts.join(', ');
                if (!variantText) variantText = 'Standard Variation';

                variationsHtml += '<div class="variant-option" style="margin-bottom: 5px;">' +
                    '<button type="button" class="btn btn-sm btn-default select-edit-variant" ' +
                    'data-variant="' + variantText + '" ' +
                    'data-price="' + (variation.variation_price || '') + '">' +
                    '<i class="fa fa-tag"></i> ' + variantText +
                    (variation.variation_price ? ' ($' + parseFloat(variation.variation_price).toFixed(2) + ')' : '') +
                    '</button></div>';
            });

            $('#editExistingVariationsList').html(variationsHtml);
        } else {
            $('#editExistingVariations').hide();
        }

        // Display previously used SKU variants for edit
        if (data.existing_sku_variants && data.existing_sku_variants.length > 0) {
            $('#editPreviousVariants').show();
            var previousHtml = '';
            data.existing_sku_variants.forEach(function(variant) {
                previousHtml += '<div class="variant-option" style="margin-bottom: 3px;">' +
                    '<button type="button" class="btn btn-xs btn-success select-edit-variant" ' +
                    'data-variant="' + variant + '">' +
                    '<i class="fa fa-history"></i> ' + variant + '</button></div>';
            });
            $('#editPreviousVariantsList').html(previousHtml);
        } else {
            $('#editPreviousVariants').hide();
        }

        if (!hasVariants) {
            $('#editVariantSection').hide();
        }
    }

    // Function to hide edit variant section
    function hideEditVariantSection() {
        $('#editVariantSection').hide();
        $('#editExistingVariations').hide();
        $('#editPreviousVariants').hide();
    }

    // Variant selection handlers
    $(document).on('click', '.select-variant', function() {
        var variantText = $(this).data('variant');
        var variantPrice = $(this).data('price');

        $('#variant_details').val(variantText);

        // If variant has a specific price, update the price field
        if (variantPrice && variantPrice !== '') {
            $('#price').val(parseFloat(variantPrice).toFixed(2));
        }

        // Visual feedback
        $('.select-variant').removeClass('btn-primary').addClass('btn-default');
        $(this).removeClass('btn-default btn-success').addClass('btn-primary');
    });

    // Edit variant selection handlers
    $(document).on('click', '.select-edit-variant', function() {
        var variantText = $(this).data('variant');
        var variantPrice = $(this).data('price');

        $('#edit_variant_details').val(variantText);

        // If variant has a specific price, update the price field
        if (variantPrice && variantPrice !== '') {
            $('#edit_price').val(parseFloat(variantPrice).toFixed(2));
        }

        // Visual feedback
        $('.select-edit-variant').removeClass('btn-primary').addClass('btn-default btn-success');
        $(this).removeClass('btn-default btn-success').addClass('btn-primary');
    });

    // Build custom variant functionality
    $('#buildVariant').click(function() {
        var variantParts = [];

        // Get selected colors
        $('.color-option:checked').each(function() {
            variantParts.push('Color: ' + $(this).val());
        });

        // Get selected sizes
        $('.size-option:checked').each(function() {
            variantParts.push('Size: ' + $(this).val());
        });

        // Get custom attributes
        var customAttrs = $('#customAttributes').val().trim();
        if (customAttrs) {
            variantParts.push(customAttrs);
        }

        if (variantParts.length > 0) {
            var variantText = variantParts.join(', ');
            $('#variant_details').val(variantText);

            // Collapse the builder
            $('#variantBuilder').collapse('hide');

            // Show success message
            var successMsg = '<div class="alert alert-success alert-dismissible" style="margin-top: 10px;">' +
                '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                '<i class="fa fa-check"></i> Variant built: <strong>' + variantText + '</strong></div>';
            $('#variantBuilder').after(successMsg);

            // Auto-remove success message after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 3000);
        } else {
            alert('Please select at least one variant option or add custom attributes.');
        }
    });

    // Clear variant selections when building new variant
    $('#variantBuilder').on('show.bs.collapse', function() {
        $('.color-option, .size-option').prop('checked', false);
        $('#customAttributes').val('');
        $('.select-variant').removeClass('btn-primary').addClass('btn-default');
    });

    // Use product price button
    $('#useProductPrice').click(function() {
        var productPrice = $('#productPrice').text();
        $('#price').val(productPrice);
    });

    // Generate barcode button
    $('#generateBarcode').click(function() {
        var skuCode = $('#sku_code').val();
        if (skuCode) {
            $('#barcode').val(skuCode);
        }
    });

    // Filter buttons - use event delegation
    $(document).on('click', '.filter-btn', function() {
        var filter = $(this).data('filter');
        $('.filter-btn').removeClass('active btn-primary').addClass('btn-default');
        $(this).removeClass('btn-default').addClass('active btn-primary');

        // Clear all filters first
        skuTable.search('').columns().search('').draw();

        switch(filter) {
            case 'all':
                // Show all rows
                skuTable.search('').draw();
                break;
            case 'low-stock':
                // Filter for rows with "Low" label
                skuTable.column(6).search('Low').draw();
                break;
            case 'out-of-stock':
                // Filter for rows with "Out" label
                skuTable.column(6).search('Out').draw();
                break;
            case 'active':
                // Filter for Active status
                skuTable.column(7).search('Active').draw();
                break;
            case 'inactive':
                // Filter for Inactive status
                skuTable.column(7).search('Inactive').draw();
                break;
        }
    });

    // Show low/out of stock items
    $(document).on('click', '#showLowStock', function(e) {
        e.preventDefault();
        $('.filter-btn[data-filter="low-stock"]').trigger('click');
    });

    $(document).on('click', '#showOutOfStock', function(e) {
        e.preventDefault();
        $('.filter-btn[data-filter="out-of-stock"]').trigger('click');
    });



    // Print barcode
    $('#printBarcode').click(function() {
        var printContent = $('#barcodeDisplay').html();
        var printWindow = window.open('', '', 'height=400,width=600');
        printWindow.document.write('<html><head><title>Print Barcode</title></head><body>' + printContent + '</body></html>');
        printWindow.document.close();
        printWindow.print();
    });

    // Download barcode
    $('#downloadBarcode').click(function() {
        var skuCode = $('#barcodeModal').data('current-sku');
        if (skuCode) {
            // Create a simple text file download with barcode info
            var element = document.createElement('a');
            element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent('SKU Code: ' + skuCode + '\nBarcode: ||||| ' + skuCode + ' |||||'));
            element.setAttribute('download', 'barcode_' + skuCode + '.txt');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'No barcode available for download',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });



    // Reset add form on modal close
    $('#addModal').on('hidden.bs.modal', function() {
        $('#addSkuForm')[0].reset();
        $('#productInfo').hide();
        hideVariantSection();
        $('#sku_code').removeClass('has-error');
        $('#sku-error').remove();
        $('.alert-success').remove(); // Remove any success messages
    });

    // Reset edit form on modal close
    $('#editModal').on('hidden.bs.modal', function() {
        hideEditVariantSection();
        $('.select-edit-variant').removeClass('btn-primary').addClass('btn-default btn-success');
    });
    // Auto-show edit modal if edit data is loaded
    <?php if(isset($show_edit_modal) && $show_edit_modal): ?>
        $('#editModal').modal('show');
    <?php endif; ?>
</script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // SweetAlert for success and error messages
    <?php if($success_message != ''): ?>
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: '<?php echo addslashes($success_message); ?>',
            timer: 3000,
            showConfirmButton: false
        });
    <?php endif; ?>

    <?php if($error_message != ''): ?>
        Swal.fire({
            icon: 'error',
            title: 'Error',
            html: '<?php echo addslashes($error_message); ?>',
            timer: 5000,
            showConfirmButton: true
        });
    <?php endif; ?>
</script>

<?php require_once('footer.php'); ?>