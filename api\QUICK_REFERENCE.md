# 🚀 API Quick Reference Guide

**For Junior Developers - Essential Information at a Glance**

## 📍 Base URL
```
Local Development: http://localhost/ecom/api/v1
Production: https://yourdomain.com/api/v1
```

## 🔑 Authentication
```dart
// Login
POST /auth/login
Body: {"email": "<EMAIL>", "password": "password123"}

// Use token in headers
headers: {"Authorization": "Bearer YOUR_JWT_TOKEN"}
```

## 📱 Essential Endpoints

### Products
```dart
GET /products                    // List products
GET /products?limit=10          // Limit results
GET /products?page=2            // Pagination
GET /products?category=5        // Filter by category
GET /products?search=phone      // Search products
GET /products/{id}              // Single product
```

### Cart (Requires Auth)
```dart
GET /cart                       // Get cart
POST /cart/add                  // Add to cart
Body: {"product_id": 123, "quantity": 2}
```

### Orders (Requires Auth)
```dart
GET /orders                     // User's orders
POST /orders/create             // Create order
GET /orders/{id}                // Order details
```

## 📊 Response Format
```json
{
  "status": "success",           // "success" or "error"
  "message": "Data retrieved",   // Human message
  "timestamp": "2024-01-15T10:30:00Z",
  "api_version": "1.0.0",
  "data": {                      // Your data here
    "products": [...],
    "pagination": {...}
  }
}
```

## 🚨 HTTP Status Codes
- **200** ✅ Success
- **400** ❌ Bad Request (check your data)
- **401** ❌ Unauthorized (login required)
- **404** ❌ Not Found
- **429** ⚠️ Rate Limited (slow down)
- **500** ❌ Server Error

## 🔧 Flutter Setup
```dart
class ApiService {
  static const String baseUrl = 'http://localhost/ecom/api/v1';
  
  static Future<Map<String, String>> _getHeaders({bool auth = false}) async {
    Map<String, String> headers = {'Content-Type': 'application/json'};
    
    if (auth) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('auth_token');
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }
    
    return headers;
  }
  
  static Future<List<Product>> getProducts() async {
    final response = await http.get(
      Uri.parse('$baseUrl/products'),
      headers: await _getHeaders(),
    );
    
    final data = json.decode(response.body);
    if (data['status'] == 'success') {
      return (data['data']['products'] as List)
          .map((json) => Product.fromJson(json))
          .toList();
    }
    throw Exception(data['message']);
  }
}
```

## 🐛 Common Issues

**"Failed to connect"**
- ✅ Check XAMPP is running
- ✅ Verify URL is correct
- ✅ Check internet connection

**"401 Unauthorized"**
- ✅ Check token is being sent
- ✅ Token might be expired (24h)
- ✅ Re-login to get new token

**"Invalid JSON"**
- ✅ Check response format
- ✅ Look for PHP errors in response
- ✅ Verify API is working: `/api/test.php`

## 🧪 Testing URLs
```
API Test:        /api/test.php
API Root:        /api/v1/
Documentation:   /api/v1/docs
Monitor:         /api/monitor/dashboard.php
```

## 💡 Pro Tips

1. **Always handle errors**:
```dart
try {
  final products = await ApiService.getProducts();
  // Success
} catch (e) {
  // Handle error
  print('Error: $e');
}
```

2. **Check authentication**:
```dart
if (await ApiService.isLoggedIn()) {
  // Make authenticated request
} else {
  // Redirect to login
}
```

3. **Use pagination**:
```dart
// Load more products
final products = await ApiService.getProducts(page: currentPage);
```

4. **Show loading states**:
```dart
bool isLoading = true;
// Make API call
setState(() => isLoading = false);
```

5. **Cache responses**:
```dart
// Save to SharedPreferences for offline use
SharedPreferences prefs = await SharedPreferences.getInstance();
await prefs.setString('cached_products', json.encode(products));
```

## 📞 Need Help?
- 📖 Full Documentation: `README.md`
- 🧪 Test API: `/api/test.php`
- 📊 Monitor: `/api/monitor/dashboard.php`
- 🔍 Debug: Add `print()` statements to see responses

---

**Happy Coding! 🎉**
