<?php
// Improved Malware and Vulnerability Scanner for PHP Files

set_time_limit(0);
ini_set('memory_limit', '512M');

$directory = __DIR__; // Corrected magic constant
$extensions = ['php', 'html', 'js', 'txt'];

$suspicious_patterns = [
    '/eval\s*\(/i',
    '/base64_decode\s*\(/i',
    '/gzinflate\s*\(/i',
    '/str_rot13\s*\(/i',
    '/shell_exec\s*\(/i',
    '/system\s*\(/i',
    '/passthru\s*\(/i',
    '/proc_open\s*\(/i',
    '/popen\s*\(/i',
    '/assert\s*\(/i',
    '/file_put_contents\s*\(/i',
    '/curl_exec\s*\(/i',
    '/\$_(GET|POST|REQUEST|COOKIE|SERVER|FILES|ENV)\s*\[\s*[\'"]?.+?[\'"]?\s*\]/i',
    '/preg_replace\s*\(\s*[\'"].*?e[\'"].*?,/i',
    '/\binclude(_once)?\b\s*[\'"]/i',
    '/\brequire(_once)?\b\s*[\'"]/i',
    '/\$\w+\s*=\s*\$_(GET|POST|REQUEST|COOKIE)\s*\[/i'
];

function highlight($str) {
    return '<span style="color:#c00;font-weight:bold;">' . htmlspecialchars($str) . '</span>';
}

function scan($dir, $extensions, $patterns) {
    $flagged = [];
    $rii = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));

    foreach ($rii as $file) {
        if ($file->isDir()) continue;

        $path = $file->getPathname();
        $ext = pathinfo($path, PATHINFO_EXTENSION);

        if (!in_array(strtolower($ext), $extensions)) continue;

        $content = @file_get_contents($path);
        if ($content === false) continue;

        $lines = explode("\n", $content);
        $line_num = 0;
        $match_found = false;

        foreach ($lines as $line) {
            $line_num++;
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $line)) {
                    $flagged[] = [
                        'file' => $path,
                        'line' => $line_num,
                        'pattern' => $pattern,
                        'code' => trim($line)
                    ];
                    $match_found = true;
                    break;
                }
            }
        }

        if (!$match_found && strlen($content) > 3000 && substr_count($content, "\n") < 5) {
            $flagged[] = [
                'file' => $path,
                'line' => 'n/a',
                'pattern' => 'Obfuscated (very long lines)',
                'code' => '[Dense or obfuscated content]'
            ];
        }
    }

    return $flagged;
}

// Run scan
$results = scan($directory, $extensions, $suspicious_patterns);

// Output
echo "<h2>🔍 PHP Malware & Vulnerability Scan Results</h2>";
if (!empty($results)) {
    echo "<p><strong style='color:red'>⚠ Found " . count($results) . " suspicious matches.</strong></p>";
    echo "<table border='1' cellpadding='6'><tr><th>File</th><th>Line</th><th>Pattern</th><th>Code</th></tr>";
    foreach ($results as $r) {
        echo "<tr>
                <td style='color:blue'>{$r['file']}</td>
                <td align='center'>{$r['line']}</td>
                <td><code>{$r['pattern']}</code></td>
                <td><code>" . highlight($r['code']) . "</code></td>
              </tr>";
    }
    echo "</table><p><strong>⚠ Review the files carefully before deleting anything.</strong></p>";
} else {
    echo "<p style='color:green;'>✅ No suspicious code found. All files appear clean.</p>";
}
?>
