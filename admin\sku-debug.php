<?php
// Enable comprehensive error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

require_once('header.php');

echo "<h1>SKU Management Debug Page</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    if (!isset($pdo) || !$pdo) {
        throw new Exception("Database connection not established");
    }
    
    $test = $pdo->query("SELECT 1 as test");
    if (!$test) {
        throw new Exception("Database query test failed");
    }
    
    $result = $test->fetch();
    echo "<div class='alert alert-success'>✓ Database connection successful. Test query result: " . $result['test'] . "</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>✗ Database connection failed: " . $e->getMessage() . "</div>";
}

// Test 2: Check if SKU table exists
echo "<h2>2. SKU Table Structure Test</h2>";
try {
    $stmt = $pdo->query("DESCRIBE tbl_sku");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<div class='alert alert-success'>✓ SKU table exists with " . count($columns) . " columns:</div>";
    echo "<table class='table table-bordered'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>✗ SKU table check failed: " . $e->getMessage() . "</div>";
}

// Test 3: Check if products exist
echo "<h2>3. Products Table Test</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tbl_product WHERE p_is_active=1");
    $result = $stmt->fetch();
    echo "<div class='alert alert-success'>✓ Found " . $result['count'] . " active products</div>";
    
    if($result['count'] > 0) {
        $stmt = $pdo->query("SELECT p_id, p_name, p_current_price FROM tbl_product WHERE p_is_active=1 LIMIT 5");
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<table class='table table-bordered'>";
        echo "<tr><th>ID</th><th>Name</th><th>Price</th></tr>";
        foreach($products as $product) {
            echo "<tr>";
            echo "<td>" . $product['p_id'] . "</td>";
            echo "<td>" . $product['p_name'] . "</td>";
            echo "<td>" . $product['p_current_price'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>✗ Products table check failed: " . $e->getMessage() . "</div>";
}

// Test 4: Session Check
echo "<h2>4. Session Test</h2>";
if(isset($_SESSION['user'])) {
    echo "<div class='alert alert-success'>✓ User session active: " . $_SESSION['user']['full_name'] . "</div>";
} else {
    echo "<div class='alert alert-danger'>✗ No user session found</div>";
}

// Test 5: POST Data Test
echo "<h2>5. POST Data Test</h2>";
if($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<div class='alert alert-info'>POST data received:</div>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
} else {
    echo "<div class='alert alert-warning'>No POST data. Submit the form below to test:</div>";
}

// Test 6: Simple Form Test
echo "<h2>6. Simple Form Test</h2>";
echo '<form method="POST" action="">
    <div class="form-group">
        <label>Test SKU Code:</label>
        <input type="text" name="test_sku" class="form-control" value="TEST123">
    </div>
    <div class="form-group">
        <label>Test Product ID:</label>
        <input type="number" name="test_product_id" class="form-control" value="1">
    </div>
    <div class="form-group">
        <label>Test Price:</label>
        <input type="number" name="test_price" class="form-control" value="100">
    </div>
    <div class="form-group">
        <label>Test Quantity:</label>
        <input type="number" name="test_quantity" class="form-control" value="10">
    </div>
    <button type="submit" name="test_submit" class="btn btn-primary">Test Form Submission</button>
</form>';

if(isset($_POST['test_submit'])) {
    echo "<div class='alert alert-success'>✓ Form submitted successfully!</div>";
    echo "<div class='alert alert-info'>Received data:</div>";
    echo "<ul>";
    foreach($_POST as $key => $value) {
        echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . htmlspecialchars($value) . "</li>";
    }
    echo "</ul>";
}

// Test 7: File Permissions
echo "<h2>7. File Permissions Test</h2>";
$files_to_check = [
    'sku-management.php',
    'sku-ajax.php',
    'inc/config.php'
];

foreach($files_to_check as $file) {
    if(file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file) ? '✓' : '✗';
        $writable = is_writable($file) ? '✓' : '✗';
        echo "<div class='alert alert-info'>";
        echo "<strong>$file:</strong> Readable: $readable, Writable: $writable, Permissions: " . substr(sprintf('%o', $perms), -4);
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>✗ File not found: $file</div>";
    }
}

// Test 8: PHP Version and Extensions
echo "<h2>8. PHP Environment Test</h2>";
echo "<div class='alert alert-info'>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>PDO Available:</strong> " . (extension_loaded('pdo') ? '✓' : '✗') . "<br>";
echo "<strong>PDO MySQL Available:</strong> " . (extension_loaded('pdo_mysql') ? '✓' : '✗') . "<br>";
echo "<strong>Session Started:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? '✓' : '✗') . "<br>";
echo "</div>";

// Test 9: Debug Log Files
echo "<h2>9. Debug Log Files</h2>";
$log_files = ['sku_debug.log', 'sku_ajax_debug.log', 'sku_errors.log', 'sku_ajax_errors.log'];
foreach($log_files as $log_file) {
    if(file_exists($log_file)) {
        $size = filesize($log_file);
        echo "<div class='alert alert-success'>✓ $log_file exists (Size: $size bytes)</div>";
        if($size > 0 && $size < 10000) {
            echo "<div class='alert alert-info'>";
            echo "<strong>Last 10 lines of $log_file:</strong><br>";
            echo "<pre>" . htmlspecialchars(implode('', array_slice(file($log_file), -10))) . "</pre>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠ $log_file does not exist yet</div>";
    }
}

echo "<hr>";
echo "<p><a href='sku-management.php' class='btn btn-primary'>Go to SKU Management</a></p>";
echo "<p><small>Debug URL: Add ?debug=1 to any page URL to see debug messages</small></p>";

require_once('footer.php');
?>
