<?php
// Ensures header.php is included, which should contain:
// - Database connection ($pdo)
// - Session start (if needed for admin login)
// - Opening HTML, HEAD (with CSS links), BODY tags
require_once('header.php');

// Create table for additional product photos/videos if it doesn't exist
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS tbl_product_photo (
        photo_id INT PRIMARY KEY AUTO_INCREMENT,
        p_id INT NOT NULL,
        photo_name VARCHAR(255) NOT NULL,
        photo_order INT DEFAULT 0,
        media_type VARCHAR(10) DEFAULT 'photo',
        FOREIGN KEY (p_id) REFERENCES tbl_product(p_id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    // Log error but continue - table might already exist
    error_log("Error creating product photo table: " . $e->getMessage());
}

// --- Database Queries ---
$color_options = '';
$top_categories = [];
$mid_category_map = [];
$size_options = '';
$color_result = []; // Initialize color result

// --- Try to fetch initial data ---
try {
    // Retrieve color options for the select inputs and checkboxes.
    $statement = $pdo->prepare("SELECT * FROM tbl_color ORDER BY color_name ASC");
    $statement->execute();
    $color_result = $statement->fetchAll(PDO::FETCH_ASSOC); // Store for later use in checkboxes too
    foreach ($color_result as $color) {
        $color_options .= '<option value="' . $color['color_id'] . '">' . htmlspecialchars($color['color_name']) . '</option>';
    }

    // Retrieve top categories
    $statement = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC");
    $statement->execute();
    $top_categories = $statement->fetchAll(PDO::FETCH_ASSOC);

    // Retrieve all mid categories and map them
    $statement = $pdo->prepare("SELECT * FROM tbl_mid_category ORDER BY mcat_name ASC");
    $statement->execute();
    $all_mid_categories = $statement->fetchAll(PDO::FETCH_ASSOC);
    $mid_category_map = [];
    foreach ($all_mid_categories as $mcat) {
        $mid_category_map[$mcat['tcat_id']][] = $mcat;
    }

     // Retrieve sizes for variations
     $stmt_sizes = $pdo->prepare("SELECT * FROM tbl_size ORDER BY size_name ASC");
     $stmt_sizes->execute();
     $sizes = $stmt_sizes->fetchAll(PDO::FETCH_ASSOC);
     foreach ($sizes as $size) {
         $size_options .= '<option value="'.$size['size_id'].'">'.htmlspecialchars($size['size_name']).'</option>';
     }

} catch (PDOException $e) {
    // Handle potential database errors during initial data fetch
    $error_message = "Database error fetching initial data: " . $e->getMessage();
    // Log this error or display a user-friendly message
    // Let the form display with potentially empty dropdowns if queries fail
    $color_options = '';
    $top_categories = [];
    $mid_category_map = [];
    $size_options = '';
    $color_result = [];
}


// --- Form Submission Logic ---
$error_message = '';
$success_message = '';

// --- Check if form was submitted ---
if (isset($_POST['form1'])) {
    $valid = 1; // Assume valid initially

    // --- Basic Field Validations ---
    if (empty($_POST['tcat_id'])) {
        $valid = 0;
        $error_message .= "You must select a top level category.<br>";
    }
    if (empty($_POST['p_name'])) {
        $valid = 0;
        $error_message .= "Product name cannot be empty.<br>";
    } else {
        // Check if product name already exists
        $statement = $pdo->prepare("SELECT COUNT(*) as total FROM tbl_product WHERE p_name = ?");
        $statement->execute(array($_POST['p_name']));
        $result = $statement->fetch(PDO::FETCH_ASSOC);
        if ($result['total'] > 0) {
            $valid = 0;
            $error_message .= "Product name already exists. Please choose a different name.<br>";
        }
    }
     // Validate prices are numeric (allow decimals)
    if (!empty($_POST['p_old_price']) && !is_numeric($_POST['p_old_price'])) {
        $valid = 0;
        $error_message .= "Old Price must be a number if provided.<br>";
    }
     if (empty($_POST['p_current_price']) || !is_numeric($_POST['p_current_price'])) {
        $valid = 0;
        $error_message .= "Current Price cannot be empty and must be a number.<br>";
    }
     // Validate base quantity is numeric (integer >= 0)
    if (!isset($_POST['p_qty']) || !ctype_digit((string)$_POST['p_qty'])) { // Also check if set
        $valid = 0;
        $error_message .= "Base Quantity cannot be empty and must be a whole number (0 or more).<br>";
    }

    // --- Validate Featured Photo Upload ---
    $final_featured_photo = ''; // Initialize variable
    $allowed_photo_ext = array('jpg', 'jpeg', 'png', 'gif');
    $allowed_video_ext = array('mp4', 'webm', 'ogg', 'mov');
    $allowed_ext = array_merge($allowed_photo_ext, $allowed_video_ext);
    $upload_path = "../assets/uploads/"; // Define upload path BEFORE checking file

    if (empty($_FILES['p_featured_photo']['name'])) {
        $valid = 0;
        $error_message .= "You must select a featured photo.<br>";
    } else {
        $path = $_FILES['p_featured_photo']['name'];
        $path_tmp = $_FILES['p_featured_photo']['tmp_name'];
        $ext = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        // Check for upload errors
        if ($_FILES['p_featured_photo']['error'] != 0) {
             $valid = 0;
             // Provide more specific error messages if possible
             switch ($_FILES['p_featured_photo']['error']) {
                case UPLOAD_ERR_INI_SIZE:
                case UPLOAD_ERR_FORM_SIZE:
                    $error_message .= "Featured photo file is too large.<br>";
                    break;
                case UPLOAD_ERR_PARTIAL:
                    $error_message .= "Featured photo was only partially uploaded.<br>";
                    break;
                case UPLOAD_ERR_NO_FILE: // Should have been caught by empty() check, but good practice
                    $error_message .= "No featured photo was uploaded.<br>";
                    break;
                // case UPLOAD_ERR_NO_TMP_DIR:
                // case UPLOAD_ERR_CANT_WRITE:
                // case UPLOAD_ERR_EXTENSION:
                //     $error_message .= "Server configuration error prevented photo upload.<br>";
                //     // Log the specific error internally
                //     error_log("Photo upload error: " . $_FILES['p_featured_photo']['error']);
                //     break;
                default:
                    $error_message .= "Error uploading featured photo. Code: " . $_FILES['p_featured_photo']['error'] . "<br>";
            }
        }
        // Check file extension
        elseif (!in_array($ext, $allowed_ext)) {
            $valid = 0;
            $error_message .= "Featured photo must be in jpg, jpeg, png, or gif format.<br>";
        }
        // Check if upload path is writable (optional but good practice)
        // elseif (!is_writable($upload_path)) {
        //     $valid = 0;
        //     $error_message .= "Server error: Upload directory is not writable.<br>";
        //     error_log("Upload path not writable: " . $upload_path);
        // }
        else {
             // File seems okay for now, generate a temporary unique name part
             // The final name using the product ID will be set inside the transaction
             $final_featured_photo_temp_ext = $ext; // Store extension separately
        }
    }

    // --- Validate Featured Photo Colors (Checkboxes) ---
    if (empty($_POST['p_featured_colors']) || !is_array($_POST['p_featured_colors'])) {
        $valid = 0;
        $error_message .= "You must select at least one available color for the product.<br>";
    } else {
        // Optional: Validate submitted color IDs against database
        $available_color_ids = array_column($color_result, 'color_id');
        foreach ($_POST['p_featured_colors'] as $submitted_color_id) {
           if (!in_array($submitted_color_id, $available_color_ids)) {
               $valid = 0;
               $error_message .= "Invalid available color selected.<br>";
               break; // Stop checking after first invalid color
           }
        }
    }


    // --- Validation for Variations (if any submitted) ---
    if (isset($_POST['variation_price'])) { // Check price array presence as indicator
        foreach($_POST['variation_price'] as $index => $price) {
            // Get values for this variation row
            $var_color_id = isset($_POST['variation_color'][$index]) ? $_POST['variation_color'][$index] : null;
            $var_size_id = isset($_POST['variation_size'][$index]) ? $_POST['variation_size'][$index] : null;
            $var_qty = isset($_POST['variation_qty'][$index]) ? $_POST['variation_qty'][$index] : null;
            $var_name = isset($_POST['variation_name'][$index]) ? trim($_POST['variation_name'][$index]) : null;
            $var_desc = isset($_POST['variation_description'][$index]) ? trim($_POST['variation_description'][$index]) : null;
            $var_img_name = isset($_FILES['variation_image']['name'][$index]) ? $_FILES['variation_image']['name'][$index] : null;
            $var_img_error = isset($_FILES['variation_image']['error'][$index]) ? $_FILES['variation_image']['error'][$index] : null;

            // --- Process this variation row ONLY if Price AND Quantity are provided ---
            // This avoids validating/saving empty rows added by the user.
            if (($price !== '' && $price !== null) && ($var_qty !== '' && $var_qty !== null)) {

                 // Variation *required* fields validation (Price & Quantity)
                 if (!is_numeric($price) || $price < 0) { // Price must be a non-negative number
                    $valid = 0;
                    $error_message .= "Variation #" . ($index+1) . ": Price must be a valid non-negative number.<br>";
                 }
                 if (!ctype_digit((string)$var_qty)) { // Quantity must be a whole number (0 or more)
                     $valid = 0;
                     $error_message .= "Variation #" . ($index+1) . ": Quantity must be a whole number (0 or more).<br>";
                 }

                 // --- Variation Image Validation (only if an image was submitted for this variation) ---
                 if (!empty($var_img_name)) {
                     $var_path = $var_img_name;
                     $var_ext = strtolower(pathinfo($var_path, PATHINFO_EXTENSION));

                     // Check for upload errors
                     if ($var_img_error != 0) {
                         $valid = 0;
                         // Provide specific error messages if possible
                         switch ($var_img_error) {
                            case UPLOAD_ERR_INI_SIZE:
                            case UPLOAD_ERR_FORM_SIZE:
                                $error_message .= "Variation #" . ($index+1) . " image file is too large.<br>";
                                break;
                            case UPLOAD_ERR_PARTIAL:
                                $error_message .= "Variation #" . ($index+1) . " image was only partially uploaded.<br>";
                                break;
                            // case UPLOAD_ERR_NO_TMP_DIR: etc. - handle server errors
                            default:
                                $error_message .= "Variation #" . ($index+1) . ": Error uploading image. Code: " . $var_img_error . "<br>";
                        }
                     }
                     // Check extension
                     elseif(!in_array($var_ext, $allowed_ext)) {
                         $valid = 0;
                         $error_message .= "Variation #" . ($index+1) . ": Image must be jpg, jpeg, png, or gif.<br>";
                     }
                 }
                 // --- End Variation Image Validation ---

            }
             // --- End check for Price and Quantity presence ---
        } // --- End foreach loop for variations ---
    } // --- End isset check for variations ---


    // --- If All Validations Pass, Proceed with Database Operations ---
    if ($valid == 1) {
        $pdo->beginTransaction(); // Start transaction
        $p_id = null; // Initialize product ID
        $moved_featured_photo_path = null; // Track moved photo for potential rollback cleanup

        try {
            // Get the next auto-increment value for tbl_product (Do this *inside* the transaction for safety)
            // Note: This can have race conditions on highly concurrent systems. Using lastInsertId() is generally preferred.
            // We'll use it here mainly to construct the filename *before* insert.
            $statement = $pdo->prepare("SHOW TABLE STATUS LIKE 'tbl_product'");
            $statement->execute();
            $result = $statement->fetch(PDO::FETCH_ASSOC);
            if (!$result || !isset($result['Auto_increment'])) {
                throw new Exception("Could not determine the next product ID.");
            }
            $ai_id = $result['Auto_increment']; // This is the *next* ID that *will* be used

            // --- Finalize featured photo name and move the file ---
            // Add timestamp and unique ID to prevent overwriting existing files
            $final_featured_photo = 'product-featured-' . $ai_id . '-' . time() . '-' . uniqid() . '.' . $final_featured_photo_temp_ext;
            $moved_featured_photo_path = $upload_path . $final_featured_photo; // Full path

            if (!move_uploaded_file($_FILES['p_featured_photo']['tmp_name'], $moved_featured_photo_path)) {
                 throw new Exception("Failed to move uploaded featured photo to destination.");
            }


            // --- Insert product details into tbl_product (including installation_fee) ---
            $statement = $pdo->prepare("INSERT INTO tbl_product(
                p_name, p_old_price, p_current_price, p_qty, p_featured_photo,
                p_featured_color, p_description, p_short_description, p_feature, p_condition,
                p_return_policy, p_total_view, p_is_featured, p_is_active, tcat_id,
                mcat_id, installation_fee
            ) VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?, ?,?, ?)"); // Added p_featured_color and installation_fee placeholders

            // Process installation fee - use default if empty or not numeric
            $installation_fee = 15000; // Default value
            if (!empty($_POST['installation_fee']) && is_numeric($_POST['installation_fee'])) {
                $installation_fee = floatval($_POST['installation_fee']);
            }

            // Get the first selected color ID for p_featured_color (default to 0 if none)
            $p_featured_color = 0; // Default value
            if (!empty($_POST['p_featured_colors']) && is_array($_POST['p_featured_colors']) && count($_POST['p_featured_colors']) > 0) {
                $p_featured_color = (int)$_POST['p_featured_colors'][0]; // Use the first selected color
            }

            $statement->execute(array(
                $_POST['p_name'],
                $_POST['p_old_price'],
                $_POST['p_current_price'],
                $_POST['p_qty'],
                $final_featured_photo, // Use the final generated name
                $p_featured_color, // Add p_featured_color value
                $_POST['p_description'],
                $_POST['p_short_description'],
                $_POST['p_feature'],
                $_POST['p_condition'],
                $_POST['p_return_policy'],
                0, // initial total view
                $_POST['p_is_featured'],
                $_POST['p_is_active'],
                $_POST['tcat_id'],
                empty($_POST['mcat_id']) ? null : $_POST['mcat_id'], // Handle optional mid-category
                $installation_fee // Add installation fee
            ));

            // Get the actual ID of the inserted product
            $p_id = $pdo->lastInsertId();
            if (!$p_id) {
                throw new Exception("Failed to retrieve the new product ID after insertion.");
            }

            // --- Insert selected featured colors into the linking table (using tbl_product_color) ---
            if (!empty($_POST['p_featured_colors']) && is_array($_POST['p_featured_colors'])) {
                 // Prepare statement using the user-specified table name: tbl_product_color
                 $stmt_feat_color = $pdo->prepare("INSERT INTO tbl_product_color (p_id, color_id) VALUES (?, ?)");
                 foreach ($_POST['p_featured_colors'] as $featured_color_id) {
                     // Ensure it's a valid integer before inserting
                     if (filter_var($featured_color_id, FILTER_VALIDATE_INT)) {
                         // Execute the insert for the tbl_product_color table
                         $stmt_feat_color->execute([$p_id, $featured_color_id]);
                     } else {
                         // Optional: Log a warning if an invalid ID was somehow submitted
                         error_log("Invalid featured color ID submitted: " . $featured_color_id . " for product ID: " . $p_id);
                     }
                 }
            }


            // --- Process product variations, if provided. ---
            if (isset($_POST['variation_price'])) { // Check price array again
                $variation_images = isset($_FILES['variation_image']) ? $_FILES['variation_image'] : null;
                $variation_upload_path = "../assets/uploads/product_variations/";
                $moved_variation_files = []; // Keep track of moved files for potential rollback

                // Check if the variation upload directory exists, create if not
                if (!is_dir($variation_upload_path)) {
                    if (!mkdir($variation_upload_path, 0755, true)) {
                        throw new Exception("Failed to create variation upload directory: " . $variation_upload_path);
                    }
                }
                // Check if writable
                // elseif (!is_writable($variation_upload_path)) {
                //     throw new Exception("Variation upload directory is not writable: " . $variation_upload_path);
                // }


                // Prepare variation insert statement once
                $stmt_variation = $pdo->prepare("INSERT INTO tbl_product_variation(
                             p_id, variation_color, variation_size, variation_price, variation_qty,
                             variation_name, variation_description, variation_image
                         ) VALUES (?,?,?,?,?,?,?,?)");

                for ($i = 0; $i < count($_POST['variation_price']); $i++) {
                     // --- Get values for this variation row ---
                     $var_price = isset($_POST['variation_price'][$i]) ? $_POST['variation_price'][$i] : null;
                     $var_qty = isset($_POST['variation_qty'][$i]) ? $_POST['variation_qty'][$i] : null;

                     // --- Process this row ONLY if Price AND Quantity are valid non-negative numbers/digits ---
                     if ($var_price !== null && $var_price !== '' && is_numeric($var_price) && $var_price >= 0 &&
                         $var_qty !== null && $var_qty !== '' && ctype_digit((string)$var_qty))
                     {
                         // Get optional fields, setting to null if empty
                         $var_color_id = isset($_POST['variation_color'][$i]) && $_POST['variation_color'][$i] !== '' ? $_POST['variation_color'][$i] : null;
                         $var_size_id = isset($_POST['variation_size'][$i]) && $_POST['variation_size'][$i] !== '' ? $_POST['variation_size'][$i] : null;
                         $var_name = isset($_POST['variation_name'][$i]) && trim($_POST['variation_name'][$i]) !== '' ? trim($_POST['variation_name'][$i]) : null;
                         $var_desc = isset($_POST['variation_description'][$i]) && trim($_POST['variation_description'][$i]) !== '' ? trim($_POST['variation_description'][$i]) : null;

                         $final_variation_image = null; // Assume no image initially
                         $moved_variation_image_path = null;

                         // --- Process variation image upload if present and valid ---
                         if ($variation_images && !empty($variation_images['name'][$i]) && $variation_images['error'][$i] == 0) {
                             $var_path = $variation_images['name'][$i];
                             $var_tmp_path = $variation_images['tmp_name'][$i];
                             $var_ext = strtolower(pathinfo($var_path, PATHINFO_EXTENSION));

                             // Double-check extension just before moving
                             if (in_array($var_ext, $allowed_ext)) {
                                 // Generate a unique file name for this variation image.
                                 $final_variation_image = 'product-variation-' . $p_id . '-' . ($i+1) . '-' . time() . uniqid() . '.' . $var_ext;
                                 $moved_variation_image_path = $variation_upload_path . $final_variation_image;

                                 if (!move_uploaded_file($var_tmp_path, $moved_variation_image_path)) {
                                     // If move fails, don't save the image name to DB, log error, but continue (unless critical)
                                     $final_variation_image = null; // Reset image name
                                     $moved_variation_image_path = null;
                                     $error_message .= "Warning: Failed to move image for variation #" . ($i+1) . ". Variation saved without image.<br>";
                                     error_log("Failed move variation image: " . $var_tmp_path . " to " . $moved_variation_image_path);
                                     // If variation images are absolutely critical, you could throw an exception here:
                                     // throw new Exception("Critical error: Failed to move image for variation #" . ($i+1));
                                 } else {
                                     // Add successfully moved file to list for potential cleanup on rollback
                                     $moved_variation_files[] = $moved_variation_image_path;
                                 }
                             } else {
                                 // Invalid extension detected during processing (should have been caught in validation, but good failsafe)
                                 $error_message .= "Warning: Invalid file type for variation #" . ($i+1) . " image. Skipped image.<br>";
                             }
                         } elseif ($variation_images && $variation_images['error'][$i] != 0 && $variation_images['error'][$i] != UPLOAD_ERR_NO_FILE) {
                             // Log upload error if it's not simply 'no file uploaded'
                             $error_message .= "Warning: Error with variation #" . ($i+1) . " image upload (Code: " . $variation_images['error'][$i] . "). Skipped image.<br>";
                         }
                         // --- End Variation Image Processing ---

                         // --- Execute the prepared statement for variation insert ---
                         $stmt_variation->execute(array(
                             $p_id,
                             $var_color_id, // Null if empty/not set
                             $var_size_id,  // Null if empty/not set
                             $var_price,
                             $var_qty,
                             $var_name,     // Null if empty/not set
                             $var_desc,     // Null if empty/not set
                             $final_variation_image // Null if no image, upload failed, or invalid type
                         ));
                    } else {
                         // Optional: Log or notify if a variation row was skipped due to missing/invalid price or quantity during the processing phase
                         // This might happen if JS allows submitting such rows but validation catches them later.
                         // error_log("Skipping variation row #".($i+1)." due to missing/invalid price or quantity during final processing.");
                    }
                } // end for loop for variations
            } // end if isset variation_price

            // --- Process Additional Photos/Videos ---
            if (isset($_FILES['additional_photos'])) {
                $additional_photos = $_FILES['additional_photos'];
                $photo_order = 0;

                // Prepare statement for photo/video insertion
                $stmt_photo = $pdo->prepare("INSERT INTO tbl_product_photo (p_id, photo_name, photo_order, media_type) VALUES (?, ?, ?, ?)");

                // Process each additional photo/video
                for ($i = 0; $i < count($additional_photos['name']); $i++) {
                    if ($additional_photos['error'][$i] == 0 && !empty($additional_photos['name'][$i])) {
                        $photo_path = $additional_photos['name'][$i];
                        $photo_tmp = $additional_photos['tmp_name'][$i];
                        $photo_ext = strtolower(pathinfo($photo_path, PATHINFO_EXTENSION));

                        // Get media type from form
                        $media_type = isset($_POST['additional_media_type'][$i]) ? $_POST['additional_media_type'][$i] : 'photo';

                        // Determine allowed extensions based on media type
                        $valid_extensions = ($media_type === 'video') ? $allowed_video_ext : $allowed_photo_ext;

                        // Validate file type
                        if (in_array($photo_ext, $valid_extensions)) {
                            // Generate unique filename with media type prefix
                            $prefix = ($media_type === 'video') ? 'product-video-' : 'product-photo-';
                            $final_photo_name = $prefix . $p_id . '-' . ($i+1) . '-' . time() . uniqid() . '.' . $photo_ext;
                            $final_photo_path = $upload_path . $final_photo_name;

                            // Move uploaded file
                            if (move_uploaded_file($photo_tmp, $final_photo_path)) {
                                // Insert into database with media type
                                $stmt_photo->execute([$p_id, $final_photo_name, $photo_order, $media_type]);
                                $photo_order++;

                                // Add to cleanup list for potential rollback
                                $moved_variation_files[] = $final_photo_path;
                            } else {
                                $error_message .= "Warning: Failed to move additional " . $media_type . " #" . ($i+1) . ".<br>";
                            }
                        } else {
                            $error_message .= "Warning: Invalid file type for additional " . $media_type . " #" . ($i+1) . ".<br>";
                        }
                    }
                }
            }

            // --- If we reach here without exceptions, commit the transaction ---
            $pdo->commit();
            $success_message = "Product added successfully!";

             // --- Clear POST data after successful submission to prevent re-submission on refresh ---
             // Be cautious if you need the POST data for displaying success messages or keeping form populated
             // $_POST = array(); // Uncomment if needed

             // --- OR Redirect after success ---
             // header('Location: product.php?status=add_success');
             // exit;

        } catch (Exception $e) {
            // --- An error occurred, rollback the transaction ---
            if ($pdo->inTransaction()) {
                 $pdo->rollBack();
            }
            $error_message .= "An error occurred during saving: " . $e->getMessage() . "<br>";

             // --- Clean up any files that were successfully moved before the error ---
             // Cleanup featured photo
             if (!empty($moved_featured_photo_path) && file_exists($moved_featured_photo_path)) {
                  @unlink($moved_featured_photo_path); // Use @ to suppress errors if unlink fails
             }
             // Cleanup variation photos
             if (!empty($moved_variation_files)) {
                 foreach ($moved_variation_files as $file_path) {
                     if (file_exists($file_path)) {
                         @unlink($file_path);
                     }
                 }
             }
        } // --- End Try-Catch block ---
    } // --- End if $valid == 1 ---
} // --- End of form submission logic (isset($_POST['form1'])) ---
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Add Product</h1>
    </div>
    <div class="content-header-right">
        <a href="product.php" class="btn btn-primary btn-sm">View All Products</a>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <?php if ($error_message): ?>
            <div class="callout callout-danger">
                <h4><i class="fa fa-exclamation-triangle"></i> Errors Found:</h4>
                <p><?php echo $error_message; /* Contains HTML breaks */ ?></p>
            </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
            <div class="callout callout-success">
                 <h4><i class="fa fa-check-circle"></i> Success!</h4>
                <p><?php echo htmlspecialchars($success_message); ?></p>
            </div>
            <?php endif; ?>

            <form class="form-horizontal" action="" method="post" enctype="multipart/form-data" id="product-form">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title">Product Details</h3>
                    </div>
                    <div class="box-body">
                        <div class="form-group <?php echo (!empty($error_message) && empty($_POST['tcat_id'])) ? 'has-error' : ''; ?>">
                            <label for="tcat_id" class="col-sm-2 control-label">Main Category <span class="text-danger">*</span></label>
                            <div class="col-sm-4">
                                <select name="tcat_id" id="tcat_id" class="form-control select2" required>
                                    <option value="">Select Main Category</option>
                                    <?php foreach ($top_categories as $cat): ?>
                                        <option value="<?php echo $cat['tcat_id']; ?>" <?php echo (isset($_POST['tcat_id']) && $_POST['tcat_id'] == $cat['tcat_id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['tcat_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="mcat_id" class="col-sm-2 control-label">Sub Category</label>
                            <div class="col-sm-4">
                                <select name="mcat_id" id="mcat_id" class="form-control select2">
                                    <option value="">-- Select Main Category First --</option>
                                    </select>
                            </div>
                        </div>
                        <div class="form-group <?php echo (!empty($error_message) && empty($_POST['p_name'])) ? 'has-error' : ''; ?>">
                            <label for="p_name" class="col-sm-2 control-label">Product Name <span class="text-danger">*</span></label>
                            <div class="col-sm-6">
                                <input type="text" name="p_name" id="p_name" class="form-control" value="<?php echo isset($_POST['p_name']) ? htmlspecialchars($_POST['p_name']) : ''; ?>" required>
                            </div>
                        </div>
                         <div class="form-group <?php echo (!empty($error_message) && !empty($_POST['p_old_price']) && !is_numeric($_POST['p_old_price'])) ? 'has-error' : ''; ?>">
                            <label for="p_old_price" class="col-sm-2 control-label">Old Price</label>
                            <div class="col-sm-4">
                                <div class="input-group">
                                    <span class="input-group-addon">TSH</span>
                                    <input type="text" name="p_old_price" id="p_old_price" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Enter a valid price (e.g., 10.99)" placeholder="e.g., 2000" value="<?php echo isset($_POST['p_old_price']) ? htmlspecialchars($_POST['p_old_price']) : '0'; ?>">
                                </div>
                                <p class="help-block">Optional. Leave empty or set to 0 if no old price.</p>
                            </div>
                        </div>
                        <div class="form-group <?php echo (!empty($error_message) && (empty($_POST['p_current_price']) || !is_numeric($_POST['p_current_price']))) ? 'has-error' : ''; ?>">
                            <label for="p_current_price" class="col-sm-2 control-label">Current Price <span class="text-danger">*</span></label>
                            <div class="col-sm-4">
                                <div class="input-group">
                                    <span class="input-group-addon">TSH</span>
                                    <input type="text" name="p_current_price" id="p_current_price" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Enter a valid price (e.g., 9.99)" placeholder="e.g., 2000" value="<?php echo isset($_POST['p_current_price']) ? htmlspecialchars($_POST['p_current_price']) : ''; ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group <?php echo (!empty($error_message) && (!isset($_POST['p_qty']) || !ctype_digit((string)$_POST['p_qty']))) ? 'has-error' : ''; ?>">
                            <label for="p_qty" class="col-sm-2 control-label">Base Quantity <span class="text-danger">*</span></label>
                            <div class="col-sm-4">
                                <input type="number" name="p_qty" id="p_qty" class="form-control" min="0" step="1" value="<?php echo isset($_POST['p_qty']) ? htmlspecialchars($_POST['p_qty']) : '0'; ?>" required>
                                <p class="help-block">Overall stock count. Variation quantities are separate.</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="installation_fee" class="col-sm-2 control-label">Installation Fee</label>
                            <div class="col-sm-4">
                                <div class="input-group">
                                    <span class="input-group-addon">TSH</span>
                                    <input type="text" name="installation_fee" id="installation_fee" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Enter a valid installation fee" placeholder="Default: 15000" value="<?php echo isset($_POST['installation_fee']) ? htmlspecialchars($_POST['installation_fee']) : '15000'; ?>">
                                </div>
                                <p class="help-block">Installation fee for this product. Leave at 15000 to use the default value.</p>
                            </div>
                        </div>
                        <div class="form-group <?php echo (!empty($error_message) && empty($_FILES['p_featured_photo']['name']) && !isset($_POST['form1'])) ? 'has-error' : ''; // Only show error if no file and form not yet submitted or failed validation without file ?>">
                            <label for="p_featured_photo" class="col-sm-2 control-label">Featured Photo <span class="text-danger">*</span></label>
                            <div class="col-sm-4" style="padding-top: 6px;">
                                <input type="file" name="p_featured_photo" id="p_featured_photo" accept=".jpg,.jpeg,.png,.gif" required> (jpg, jpeg, png, gif)
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">Additional Photos/Videos</label>
                            <div class="col-sm-6">
                                <div id="additional-photos-container">
                                    <div class="additional-photo-item" style="margin-bottom: 10px;">
                                        <div class="form-group" style="margin-bottom: 5px;">
                                            <select name="additional_media_type[]" class="form-control input-sm media-type-selector">
                                                <option value="photo">Photo</option>
                                                <option value="video">Video</option>
                                            </select>
                                        </div>
                                        <div class="input-group">
                                            <input type="file" name="additional_photos[]" class="form-control media-file" accept=".jpg,.jpeg,.png,.gif">
                                            <span class="input-group-btn">
                                                <button type="button" class="btn btn-danger remove-photo"><i class="fa fa-times"></i></button>
                                            </span>
                                        </div>
                                        <span class="text-muted file-type-hint" style="font-size:11px;">(Optional: jpg, jpeg, png, gif)</span>
                                    </div>
                                </div>
                                <button type="button" id="add-photo" class="btn btn-info btn-sm" style="margin-top: 10px;">
                                    <i class="fa fa-plus"></i> Add Another Media
                                </button>
                                <p class="help-block">Add more photos or videos of the product. You can add as many as needed.</p>
                            </div>
                        </div>

                        <div class="form-group <?php echo (!empty($error_message) && (empty($_POST['p_featured_colors']) || !is_array($_POST['p_featured_colors']))) ? 'has-error' : ''; ?>">
                            <label class="col-sm-2 control-label">Available Colors <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <?php if (!empty($color_result)): ?>
                                    <?php foreach ($color_result as $color): ?>
                                        <div class="checkbox checkbox-inline" style="margin-top: 0; margin-bottom: 5px; margin-right: 15px;">
                                            <label>
                                                <input type="checkbox"
                                                       name="p_featured_colors[]"
                                                       value="<?php echo $color['color_id']; ?>"
                                                       <?php
                                                       // Re-check boxes on validation error
                                                       if (isset($_POST['p_featured_colors']) && is_array($_POST['p_featured_colors']) && in_array($color['color_id'], $_POST['p_featured_colors'])) {
                                                           echo ' checked';
                                                       }
                                                       ?>
                                                >
                                                <span style="padding-left: 5px;"><?php echo htmlspecialchars($color['color_name']); ?></span>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p class="text-warning">No colors found in the database. Please add colors first.</p>
                                <?php endif; ?>
                                <p class="help-block">Select all colors this product is generally available in.</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="editor1" class="col-sm-2 control-label">Description</label>
                            <div class="col-sm-9">
                                <textarea name="p_description" class="form-control ckeditor-instance" id="editor1" rows="8"><?php echo isset($_POST['p_description']) ? htmlspecialchars($_POST['p_description']) : ''; ?></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editor2" class="col-sm-2 control-label">Key Features</label>
                            <div class="col-sm-9">
                                <textarea name="p_short_description" class="form-control ckeditor-instance" id="editor2" rows="4"><?php echo isset($_POST['p_short_description']) ? htmlspecialchars($_POST['p_short_description']) : ''; ?></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editor3" class="col-sm-2 control-label">Extra Info</label>
                            <div class="col-sm-9">
                                <textarea name="p_feature" class="form-control ckeditor-instance" id="editor3" rows="5"><?php echo isset($_POST['p_feature']) ? htmlspecialchars($_POST['p_feature']) : ''; ?></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editor4" class="col-sm-2 control-label">Technical Features</label>
                            <div class="col-sm-9">
                                <textarea name="p_condition" class="form-control ckeditor-instance" id="editor4" rows="5"><?php echo isset($_POST['p_condition']) ? htmlspecialchars($_POST['p_condition']) : ''; ?></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editor5" class="col-sm-2 control-label">Return Policy</label>
                            <div class="col-sm-9">
                                <textarea name="p_return_policy" class="form-control ckeditor-instance" id="editor5" rows="5"><?php echo isset($_POST['p_return_policy']) ? htmlspecialchars($_POST['p_return_policy']) : ''; ?></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="p_is_featured" class="col-sm-2 control-label">Is Featured?</label>
                            <div class="col-sm-4">
                                <select name="p_is_featured" id="p_is_featured" class="form-control">
                                    <option value="0" <?php echo (isset($_POST['p_is_featured']) && $_POST['p_is_featured'] == 0) ? 'selected' : ''; ?>>No</option>
                                    <option value="1" <?php echo (isset($_POST['p_is_featured']) && $_POST['p_is_featured'] == 1) ? 'selected' : ''; ?>>Yes</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="p_is_active" class="col-sm-2 control-label">Is Active?</label>
                            <div class="col-sm-4">
                                <select name="p_is_active" id="p_is_active" class="form-control">
                                     <option value="1" <?php echo (!isset($_POST['p_is_active']) || (isset($_POST['p_is_active']) && $_POST['p_is_active'] == 1)) ? 'selected' : ''; ?>>Yes</option>
                                     <option value="0" <?php echo (isset($_POST['p_is_active']) && $_POST['p_is_active'] == 0) ? 'selected' : ''; ?>>No</option>
                                </select>
                            </div>
                        </div>

                    </div></div><div class="box box-warning">
                    <div class="box-header with-border">
                        <h3 class="box-title">Product Variations (Optional)</h3>
                    </div>
                    <div class="box-body">
                         <div class="col-md-12"> <p class="help-block">Add specific variations like color, size, etc., with their own price, quantity, and image. Price and Quantity are required for each variation you add.</p>
                            <div id="variation-container" style="margin-top: 15px;">
                                 <div class="variation-item well well-sm" style="margin-bottom:15px; padding:15px; background-color: #fdfdfd; border: 1px solid #eee;">
                                     <div class="row">
                                         <div class="col-md-2">
                                             <div class="form-group" style="margin-bottom: 5px;">
                                                <label>Color</label>
                                                <select name="variation_color[]" class="form-control input-sm">
                                                    <option value="">-- Optional --</option>
                                                    <?php echo $color_options; ?>
                                                </select>
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group" style="margin-bottom: 5px;">
                                                <label>Size</label>
                                                <select name="variation_size[]" class="form-control input-sm">
                                                    <option value="">-- Optional --</option>
                                                    <?php echo $size_options; ?>
                                                </select>
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group" style="margin-bottom: 5px;">
                                                <label>Price <span class="text-danger">*</span></label>
                                                <input type="text" name="variation_price[]" class="form-control input-sm" placeholder="9.99" pattern="^\d+(\.\d{1,2})?$" title="Enter a valid price (e.g., 9.99)">
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group" style="margin-bottom: 5px;">
                                                <label>Quantity <span class="text-danger">*</span></label>
                                                <input type="number" name="variation_qty[]" class="form-control input-sm" min="0" step="1" value="0" placeholder="0">
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                              <div class="form-group" style="margin-bottom: 5px;">
                                                <label>Name</label>
                                                <input type="text" name="variation_name[]" class="form-control input-sm" placeholder="e.g., Red-Large">
                                              </div>
                                          </div>
                                         <div class="col-md-2">
                                             <div class="form-group" style="margin-bottom: 5px;">
                                                <label>Description</label>
                                                <textarea name="variation_description[]" class="form-control input-sm" rows="1" placeholder="Notes..."></textarea>
                                             </div>
                                         </div>
                                     </div><div class="row" style="margin-top:5px;">
                                         <div class="col-md-5">
                                             <div class="form-group" style="margin-bottom: 5px;">
                                                <label>Image</label>
                                                <input type="file" name="variation_image[]" class="form-control input-sm" accept=".jpg,.jpeg,.png,.gif">
                                                <span class="text-muted" style="font-size:11px;">(Optional: jpg, jpeg, png, gif)</span>
                                             </div>
                                         </div>
                                         <div class="col-md-7 text-right">
                                             <button type="button" class="btn btn-danger btn-xs remove-variation" style="margin-top:25px;"><i class="fa fa-trash"></i> Remove</button>
                                         </div>
                                     </div></div><?php
                                   // --- START: Re-populate variations on validation error ---
                                   if (isset($_POST['variation_price']) && $valid == 0 && !empty($error_message)) { // Use variation_price as primary check and ensure there was an error
                                       // Remove the initial template displayed by default ONLY if POST data exists AND there were errors
                                       echo '<script>$(document).ready(function() { $("#variation-container .variation-item").first().remove(); });</script>';

                                       foreach ($_POST['variation_price'] as $index => $posted_price) {
                                           // Get submitted values for this index
                                           $posted_qty = isset($_POST['variation_qty'][$index]) ? htmlspecialchars($_POST['variation_qty'][$index]) : '0';
                                           $posted_color = isset($_POST['variation_color'][$index]) ? $_POST['variation_color'][$index] : '';
                                           $posted_size = isset($_POST['variation_size'][$index]) ? $_POST['variation_size'][$index] : '';
                                           $posted_name = isset($_POST['variation_name'][$index]) ? htmlspecialchars($_POST['variation_name'][$index]) : '';
                                           $posted_desc = isset($_POST['variation_description'][$index]) ? htmlspecialchars($_POST['variation_description'][$index]) : '';
                                           // Note: Cannot repopulate file inputs for security reasons

                                           // Determine if this specific variation row had errors (more advanced, requires tracking errors per row)
                                           // Simple approach: Just repopulate all submitted rows if *any* error occurred.

                                           // Regenerate color options with selected value
                                           $current_color_options = '<option value="">-- Optional --</option>';
                                           foreach ($color_result as $color) {
                                               $selected = ($posted_color == $color['color_id']) ? 'selected' : '';
                                               $current_color_options .= '<option value="' . $color['color_id'] . '" ' . $selected . '>' . htmlspecialchars($color['color_name']) . '</option>';
                                           }
                                            // Regenerate size options with selected value
                                           $current_size_options = '<option value="">-- Optional --</option>';
                                            foreach ($sizes as $size) {
                                                $selected = ($posted_size == $size['size_id']) ? 'selected' : '';
                                                $current_size_options .= '<option value="'.$size['size_id'].'" '.$selected.'>'.htmlspecialchars($size['size_name']).'</option>';
                                            }

                                           // --- Output the HTML for the repopulated variation item ---
                                           echo '<div class="variation-item well well-sm" style="margin-bottom:15px; padding:15px; background-color: #fdfdfd; border: 1px solid #eee;">';
                                           echo '  <div class="row">';
                                           // Color
                                           echo '      <div class="col-md-2"><div class="form-group" style="margin-bottom: 5px;"><label>Color</label><select name="variation_color[]" class="form-control input-sm">'.$current_color_options.'</select></div></div>';
                                           // Size
                                           echo '      <div class="col-md-2"><div class="form-group" style="margin-bottom: 5px;"><label>Size</label><select name="variation_size[]" class="form-control input-sm">'.$current_size_options.'</select></div></div>';
                                           // Price
                                           echo '      <div class="col-md-2"><div class="form-group" style="margin-bottom: 5px;"><label>Price <span class="text-danger">*</span></label><input type="text" name="variation_price[]" class="form-control input-sm" placeholder="9.99" pattern="^\d+(\.\d{1,2})?$" title="Enter a valid price (e.g., 9.99)" value="'.htmlspecialchars($posted_price).'"></div></div>';
                                           // Quantity
                                           echo '      <div class="col-md-2"><div class="form-group" style="margin-bottom: 5px;"><label>Quantity <span class="text-danger">*</span></label><input type="number" name="variation_qty[]" class="form-control input-sm" min="0" step="1" value="'.$posted_qty.'" placeholder="0"></div></div>';
                                           // Name
                                           echo '      <div class="col-md-2"><div class="form-group" style="margin-bottom: 5px;"><label>Name</label><input type="text" name="variation_name[]" class="form-control input-sm" placeholder="e.g., Red-Large" value="'.$posted_name.'"></div></div>';
                                           // Description
                                           echo '      <div class="col-md-2"><div class="form-group" style="margin-bottom: 5px;"><label>Description</label><textarea name="variation_description[]" class="form-control input-sm" rows="1" placeholder="Notes...">'.$posted_desc.'</textarea></div></div>';
                                           echo '  </div>'; // end row
                                           echo '  <div class="row" style="margin-top:5px;">';
                                           // Image
                                           echo '      <div class="col-md-5"><div class="form-group" style="margin-bottom: 5px;"><label>Image</label><input type="file" name="variation_image[]" class="form-control input-sm" accept=".jpg,.jpeg,.png,.gif"><span class="text-muted" style="font-size:11px;">(Previous selection lost)</span></div></div>';
                                           // Remove button
                                           echo '      <div class="col-md-7 text-right"><button type="button" class="btn btn-danger btn-xs remove-variation" style="margin-top:25px;"><i class="fa fa-trash"></i> Remove</button></div>';
                                           echo '  </div>'; // end row
                                           echo '</div>'; // end variation-item

                                       } // end foreach loop for repopulating
                                   } elseif (!isset($_POST['variation_price']) && !$success_message) {
                                        // If form hasn't been submitted at all OR submission failed but had no variation data,
                                        // ensure the initial template isn't removed by the script above.
                                        // (The script only removes the first item if POST data exists AND there was an error)
                                        // No explicit action needed here unless the template was hidden by default.
                                   }
                                   // --- END: Re-populate variations ---
                                   ?>
                             </div> <button type="button" id="add-variation" class="btn btn-info btn-sm" style="margin-top: 10px;"><i class="fa fa-plus"></i> Add Another Variation</button>
                         </div> </div></div><div class="box box-info">
                     <div class="box-body">
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-6">
                                <button type="submit" class="btn btn-success pull-left" name="form1"><i class="fa fa-save"></i> Add Product</button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>
        </div></div></section>


<?php
// This should include necessary JS libraries (like maybe jQuery if not in header),
// closing BODY and HTML tags.
require_once('footer.php');
?>

<script src="https://cdn.ckeditor.com/4.25.1-lts/standard/ckeditor.js"></script>
<script>
$(document).ready(function() {

    // Initialize the first media type selector
    $('.media-type-selector').first().trigger('change');

    // --- Initialize CKEditor Instances ---
    // Use a class to target all textareas needing CKEditor
    if (typeof CKEDITOR !== 'undefined') {
        $('.ckeditor-instance').each(function() {
            CKEDITOR.replace(this.id);
        });
    } else {
        console.error("CKEditor library not loaded. Textareas will be standard.");
    }


    // --- Initialize Select2 Dropdowns ---
     if (typeof $.fn.select2 !== 'undefined') {
         $('.select2').select2({
             width: '100%' // Ensure select2 takes full width of its container
         });
     } else {
         console.warn("Select2 library not loaded. Dropdowns will be standard.");
     }


    // --- Mid Category Loading Logic ---
    var midCategoryMap = <?php echo json_encode($mid_category_map); ?>;
    var previouslySelectedMcat = '<?php echo isset($_POST['mcat_id']) ? $_POST['mcat_id'] : ''; ?>'; // For repopulating on error

    function populateMidCategories() {
        var tcatId = $('#tcat_id').val();
        var mcatSelect = $('#mcat_id');
        var currentMcatValue = mcatSelect.val(); // Store current value before clearing

        mcatSelect.html('<option value="">-- Optional --</option>'); // Clear existing options, set default

        if (tcatId && midCategoryMap[tcatId]) {
             $.each(midCategoryMap[tcatId], function(i, mcat) {
                  var option = $('<option></option>').val(mcat.mcat_id).text(mcat.mcat_name);
                  // Select if it matches the previously submitted value (on page load after error)
                  if (mcat.mcat_id == previouslySelectedMcat) {
                       option.prop('selected', true);
                       previouslySelectedMcat = ''; // Clear after first use to prevent re-selecting on user change
                  }
                  mcatSelect.append(option);
             });
        } else {
             // If no top category selected or no mid-categories found
             mcatSelect.html('<option value="">-- Select Main Category First --</option>');
        }

        // Trigger change for Select2 to update its display
        if (mcatSelect.hasClass('select2')) {
             mcatSelect.trigger('change.select2');
        }
    }

    // Populate on initial page load (handles cases where tcat_id is pre-selected)
    populateMidCategories();

    // Add change event listener
    $('#tcat_id').on('change', function() {
        populateMidCategories();
    });


    // --- Product Variation Management ---

    // Get the HTML template for a new variation item from the first one on the page
    // Ensure this runs *after* PHP might have removed the template during error repopulation
    var variationTemplateHtml = $('#variation-container .variation-item').first().prop('outerHTML');
    if (!variationTemplateHtml) {
         console.error("Could not find the variation template HTML. 'Add Variation' button might not work.");
         // You could potentially define a fallback HTML string here if needed, but it's better to ensure the template exists in the HTML.
    }


    // Add new variation block
    $('#add-variation').on('click', function() {
        if (!variationTemplateHtml) {
            alert("Error: Could not create new variation item. Template missing.");
            return;
        }
        // Append a clean copy of the template HTML
        var newBlock = $(variationTemplateHtml); // Create jQuery object from HTML string

        // Clear values in the new block before appending
        newBlock.find('input[type="text"], input[type="number"], input[type="file"], textarea').val('');
        newBlock.find('input[type="number"][name="variation_qty[]"]').val('0'); // Default qty to 0
        newBlock.find('input[type="text"][name="variation_price[]"]').attr('placeholder', '9.99');
        newBlock.find('select').prop('selectedIndex', 0); // Reset selects to the first option ("-- Optional --")

        // Append the cleaned new block
        $('#variation-container').append(newBlock);

        // Optional: Scroll to the new variation item
        // newBlock.get(0).scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    });

    // Remove variation block (using event delegation for dynamically added items)
    $('#variation-container').on('click', '.remove-variation', function() {
         // Optional: Add confirmation before removing
         if (confirm('Are you sure you want to remove this variation?')) {
              $(this).closest('.variation-item').fadeOut(300, function() { $(this).remove(); }); // Fade out and remove
         }
    });

    // --- Additional Photos/Videos Management ---
    $('#add-photo').on('click', function() {
        var newPhotoField = `
            <div class="additional-photo-item" style="margin-bottom: 10px;">
                <div class="form-group" style="margin-bottom: 5px;">
                    <select name="additional_media_type[]" class="form-control input-sm media-type-selector">
                        <option value="photo">Photo</option>
                        <option value="video">Video</option>
                    </select>
                </div>
                <div class="input-group">
                    <input type="file" name="additional_photos[]" class="form-control media-file" accept=".jpg,.jpeg,.png,.gif">
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-danger remove-photo"><i class="fa fa-times"></i></button>
                    </span>
                </div>
                <span class="text-muted file-type-hint" style="font-size:11px;">(Optional: jpg, jpeg, png, gif)</span>
            </div>
        `;
        $('#additional-photos-container').append(newPhotoField);
    });

    // Remove additional photo/video field
    $('#additional-photos-container').on('click', '.remove-photo', function() {
        $(this).closest('.additional-photo-item').fadeOut(300, function() {
            $(this).remove();
        });
    });

    // Handle media type change
    $('#additional-photos-container').on('change', '.media-type-selector', function() {
        var mediaType = $(this).val();
        var fileInput = $(this).closest('.additional-photo-item').find('.media-file');
        var fileTypeHint = $(this).closest('.additional-photo-item').find('.file-type-hint');

        if (mediaType === 'photo') {
            fileInput.attr('accept', '.jpg,.jpeg,.png,.gif');
            fileTypeHint.text('(Optional: jpg, jpeg, png, gif)');
        } else if (mediaType === 'video') {
            fileInput.attr('accept', '.mp4,.webm,.ogg,.mov');
            fileTypeHint.text('(Optional: mp4, webm, ogg, mov)');
        }
    });

});
</script>
