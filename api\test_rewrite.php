<?php
/**
 * URL Rewrite Test
 * Tests if URL rewriting is working correctly
 */

echo "<h1>URL Rewrite Test</h1>";

echo "<h2>Current Request Information</h2>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>PATH_INFO:</strong> " . ($_SERVER['PATH_INFO'] ?? 'Not set') . "</p>";
echo "<p><strong>QUERY_STRING:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "</p>";
echo "<p><strong>REQUEST_METHOD:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'Not set') . "</p>";

echo "<h2>Test Links</h2>";
echo "<p>Click these links to test URL rewriting:</p>";
echo "<ul>";
echo "<li><a href='/ecom/api/v1/' target='_blank'>Direct API Root (/ecom/api/v1/)</a></li>";
echo "<li><a href='/ecom/api/v1/settings/app' target='_blank'>Settings Endpoint (/ecom/api/v1/settings/app)</a></li>";
echo "<li><a href='/ecom/api/v1/products?limit=3' target='_blank'>Products Endpoint (/ecom/api/v1/products?limit=3)</a></li>";
echo "<li><a href='/ecom/api/index.php' target='_blank'>Direct index.php</a></li>";
echo "</ul>";

echo "<h2>cURL Tests</h2>";

$test_urls = [
    'API Root' => 'http://localhost/ecom/api/v1/',
    'Settings' => 'http://localhost/ecom/api/v1/settings/app',
    'Products' => 'http://localhost/ecom/api/v1/products?limit=3'
];

foreach ($test_urls as $name => $url) {
    echo "<h3>Testing: {$name}</h3>";
    echo "<p><strong>URL:</strong> {$url}</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>cURL Error: {$error}</p>";
        continue;
    }
    
    $headers = substr($response, 0, $header_size);
    $body = substr($response, $header_size);
    
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    
    echo "<h4>Response Headers:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo htmlspecialchars($headers);
    echo "</pre>";
    
    echo "<h4>Response Body:</h4>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars($body);
    echo "</pre>";
    
    // Validate JSON
    $data = json_decode($body, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✓ Valid JSON response</p>";
        if (isset($data['status'])) {
            echo "<p><strong>Status:</strong> " . $data['status'] . "</p>";
            echo "<p><strong>Message:</strong> " . ($data['message'] ?? 'No message') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Invalid JSON: " . json_last_error_msg() . "</p>";
    }
    
    echo "<hr>";
}

echo "<h2>Server Environment</h2>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";

// Check if mod_rewrite is enabled
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✓ mod_rewrite is enabled</p>";
    } else {
        echo "<p style='color: red;'>✗ mod_rewrite is not enabled</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ Cannot detect mod_rewrite status</p>";
}

echo "<hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
