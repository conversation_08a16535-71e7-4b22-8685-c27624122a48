<?php
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

header('Content-Type: application/json');

if (!isset($_GET['q']) || strlen($_GET['q']) < 2) {
    echo json_encode([]);
    exit;
}

$search_term = $_GET['q'];
$results = [];

// Search in products
$stmt_products = $pdo->prepare("
    SELECT p_id as id, p_name as name, 'product' as type 
    FROM tbl_product 
    WHERE p_name LIKE ? AND p_is_active = 1 
    LIMIT 5
");
$stmt_products->execute(["%$search_term%"]);
$products = $stmt_products->fetchAll(PDO::FETCH_ASSOC);
$results = array_merge($results, $products);

// Search in top categories
$stmt_top_cat = $pdo->prepare("
    SELECT tcat_id as id, tcat_name as name, 'category' as type 
    FROM tbl_top_category 
    WHERE tcat_name LIKE ? AND show_on_menu = 1 
    LIMIT 3
");
$stmt_top_cat->execute(["%$search_term%"]);
$top_categories = $stmt_top_cat->fetchAll(PDO::FETCH_ASSOC);
$results = array_merge($results, $top_categories);

// Search in mid categories
$stmt_mid_cat = $pdo->prepare("
    SELECT mcat_id as id, mcat_name as name, 'category' as type 
    FROM tbl_mid_category 
    WHERE mcat_name LIKE ? 
    LIMIT 3
");
$stmt_mid_cat->execute(["%$search_term%"]);
$mid_categories = $stmt_mid_cat->fetchAll(PDO::FETCH_ASSOC);
$results = array_merge($results, $mid_categories);

// Sort results by relevance (exact matches first, then partial matches)
usort($results, function($a, $b) use ($search_term) {
    $a_exact = stripos($a['name'], $search_term) === 0;
    $b_exact = stripos($b['name'], $search_term) === 0;
    
    if ($a_exact && !$b_exact) return -1;
    if (!$a_exact && $b_exact) return 1;
    
    return strcasecmp($a['name'], $b['name']);
});

echo json_encode($results);
?> 