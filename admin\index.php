<?php require_once('header.php'); ?>

<section class="content-header">
  <h1>Dashboard</h1>
</section>

<?php
// --- Fetch Dashboard Metrics ---

// Total Top Categories
$statement = $pdo->prepare("SELECT * FROM tbl_top_category");
$statement->execute();
$total_top_category = $statement->rowCount();

// Total Products
$statement = $pdo->prepare("SELECT * FROM tbl_product");
$statement->execute();
$total_product = $statement->rowCount();

// Active Customers
$statement = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_status='1'");
$statement->execute();
$total_customers = $statement->rowCount();

// Subscribers
$statement = $pdo->prepare("SELECT * FROM tbl_subscriber WHERE status='1'");
$statement->execute();
$total_subscriber = $statement->rowCount();

// Available Shippings
$statement = $pdo->prepare("SELECT * FROM tbl_shipping_cost");
$statement->execute();
$available_shipping = $statement->rowCount();

// --- Orders Data using 'orders' table ---
// Total completed orders and total sales amount for orders that are successful
$statement = $pdo->prepare("SELECT COUNT(*) as order_count, SUM(total_amount) as total_sales FROM orders WHERE payment_status = 'success'");
$statement->execute();
$order_data = $statement->fetch(PDO::FETCH_ASSOC);
$total_order_completed = $order_data['order_count'];
$total_sales_amount = $order_data['total_sales'] ?? 0; // fallback to 0 if NULL

// --- Sales Graph Data ---
// Aggregate monthly sales (using created_at) from orders with successful payments
$statement = $pdo->prepare("SELECT DATE_FORMAT(created_at, '%Y-%m') as month, SUM(total_amount) as monthly_sales FROM orders WHERE payment_status = 'success' GROUP BY month ORDER BY month ASC");
$statement->execute();
$sales_graph_data = $statement->fetchAll(PDO::FETCH_ASSOC);
$months = [];
$monthly_sales = [];
foreach ($sales_graph_data as $row) {
    $months[] = $row['month'];
    $monthly_sales[] = $row['monthly_sales'];
}
?>

<section class="content">
  <div class="row">
    <!-- Products Box -->
    <div class="col-lg-3 col-xs-6">
      <div class="small-box bg-primary">
        <div class="inner">
          <h3><?php echo $total_product; ?></h3>
          <p>Products</p>
        </div>
        <div class="icon">
          <i class="ionicons ion-android-cart"></i>
        </div>
      </div>
    </div>
    <!-- Completed Orders Box -->
    <div class="col-lg-3 col-xs-6">
      <div class="small-box bg-green">
        <div class="inner">
          <h3><?php echo $total_order_completed; ?></h3>
          <p>Completed Orders</p>
        </div>
        <div class="icon">
          <i class="ionicons ion-android-checkbox-outline"></i>
        </div>
      </div>
    </div>
    <!-- Total Sales Box -->
    <div class="col-lg-3 col-xs-6">
      <div class="small-box bg-aqua">
        <div class="inner">
          <h3>Tsh <?php echo number_format($total_sales_amount, 0); ?></h3>
          <p>Total Sales</p>
        </div>
        <div class="icon">
          <i class="ionicons ion-cash"></i>
        </div>
      </div>
    </div>
    <!-- Active Customers Box -->
    <div class="col-lg-3 col-xs-6">
      <div class="small-box bg-red">
        <div class="inner">
          <h3><?php echo $total_customers; ?></h3>
          <p>Active Customers</p>
        </div>
        <div class="icon">
          <i class="ionicons ion-person-stalker"></i>
        </div>
      </div>
    </div>
    <!-- Subscribers Box -->
    <div class="col-lg-3 col-xs-6">
      <div class="small-box bg-yellow">
        <div class="inner">
          <h3><?php echo $total_subscriber; ?></h3>
          <p>Subscribers</p>
        </div>
        <div class="icon">
          <i class="ionicons ion-person-add"></i>
        </div>
      </div>
    </div>
    <!-- Available Shippings Box -->
    <div class="col-lg-3 col-xs-6">
      <div class="small-box bg-teal">
        <div class="inner">
          <h3><?php echo $available_shipping; ?></h3>
          <p>Available Shippings</p>
        </div>
        <div class="icon">
          <i class="ionicons ion-location"></i>
        </div>
      </div>
    </div>
    <!-- Top Categories Box -->
    <div class="col-lg-3 col-xs-6">
      <div class="small-box bg-olive">
        <div class="inner">
          <h3><?php echo $total_top_category; ?></h3>
          <p>Top Categories</p>
        </div>
        <div class="icon">
          <i class="ionicons ion-arrow-up-b"></i>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Sales Graph Section -->
  <div class="row">
    <div class="col-xs-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title">Monthly Sales Graph</h3>
        </div>
        <div class="box-body">
          <!-- Canvas for Chart.js -->
          <canvas id="salesChart" style="max-height:400px;"></canvas>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Additional dashboard features can be added here such as recent orders, top-selling products, etc. -->
</section>

<?php require_once('footer.php'); ?>

<!-- Include Chart.js via CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Convert PHP arrays for labels and data into JavaScript arrays
  const months = <?php echo json_encode($months); ?>;
  const monthlySales = <?php echo json_encode($monthly_sales); ?>;
  
  const ctx = document.getElementById('salesChart').getContext('2d');
  const salesChart = new Chart(ctx, {
      type: 'line',
      data: {
          labels: months,
          datasets: [{
              label: 'Sales (Tsh)',
              data: monthlySales,
              borderColor: 'rgba(75, 192, 192, 1)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              fill: true,
              tension: 0.1
          }]
      },
      options: {
          responsive: true,
          plugins: {
              legend: {
                  display: true,
              },
              tooltip: {
                  mode: 'index',
                  intersect: false,
              }
          },
          scales: {
              x: {
                  title: {
                      display: true,
                      text: 'Month'
                  }
              },
              y: {
                  title: {
                      display: true,
                      text: 'Total Sales'
                  },
                  beginAtZero: true
              }
          }
      }
  });
</script>
