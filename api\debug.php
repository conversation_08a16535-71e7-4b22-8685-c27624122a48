<?php
/**
 * API Debug Script
 * Helps diagnose API routing and configuration issues
 */

echo "<h1>API Debug Information</h1>";

// Check if we can include the configuration
echo "<h2>1. Configuration Test</h2>";
try {
    require_once __DIR__ . '/config/config.php';
    echo "<p style='color: green;'>✓ Configuration loaded successfully</p>";
    echo "<p>Database: " . DB_NAME . "</p>";
    echo "<p>API Version: " . API_VERSION . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Configuration error: " . $e->getMessage() . "</p>";
}

// Check database connection
echo "<h2>2. Database Connection Test</h2>";
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if API tables exist
    $tables = ['tbl_product', 'tbl_customer', 'orders', 'api_logs'];
    foreach ($tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
            echo "<p style='color: green;'>✓ Table {$table} exists ({$count} records)</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ Table {$table} missing or error: " . $e->getMessage() . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Check server environment
echo "<h2>3. Server Environment</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";

// Check if mod_rewrite is available
echo "<h2>4. URL Rewriting Test</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✓ mod_rewrite is enabled</p>";
    } else {
        echo "<p style='color: red;'>✗ mod_rewrite is not enabled</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ Cannot detect mod_rewrite status</p>";
}

// Check .htaccess file
echo "<h2>5. .htaccess File Test</h2>";
$htaccess_path = __DIR__ . '/.htaccess';
if (file_exists($htaccess_path)) {
    echo "<p style='color: green;'>✓ .htaccess file exists</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars(file_get_contents($htaccess_path));
    echo "</pre>";
} else {
    echo "<p style='color: red;'>✗ .htaccess file missing</p>";
}

// Test direct API access
echo "<h2>6. Direct API Test</h2>";
echo "<p>Testing direct access to API endpoints:</p>";

$test_urls = [
    'Direct Index' => '/ecom/api/index.php',
    'API Root' => '/ecom/api/v1/',
    'Products' => '/ecom/api/v1/products',
    'Settings' => '/ecom/api/v1/settings/app'
];

foreach ($test_urls as $name => $url) {
    $full_url = 'http://' . $_SERVER['HTTP_HOST'] . $url;
    echo "<p><strong>{$name}:</strong> <a href='{$full_url}' target='_blank'>{$full_url}</a></p>";
}

// Test API components
echo "<h2>7. API Components Test</h2>";

// Test Response class
try {
    if (class_exists('Response')) {
        echo "<p style='color: green;'>✓ Response class available</p>";
    } else {
        echo "<p style='color: red;'>✗ Response class not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Response class error: " . $e->getMessage() . "</p>";
}

// Test Database class
try {
    $db = new Database();
    echo "<p style='color: green;'>✓ Database class working</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database class error: " . $e->getMessage() . "</p>";
}

// Test JWT
try {
    $token = SimpleJWT::encode(['test' => true], JWT_SECRET);
    $decoded = SimpleJWT::decode($token, JWT_SECRET);
    if ($decoded['test']) {
        echo "<p style='color: green;'>✓ JWT working</p>";
    } else {
        echo "<p style='color: red;'>✗ JWT test failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ JWT error: " . $e->getMessage() . "</p>";
}

// Show request information
echo "<h2>8. Current Request Information</h2>";
echo "<p><strong>Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p><strong>Query String:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'None') . "</p>";
echo "<p><strong>Content Type:</strong> " . ($_SERVER['CONTENT_TYPE'] ?? 'Not set') . "</p>";

// Show all $_SERVER variables for debugging
echo "<h2>9. Server Variables (for debugging)</h2>";
echo "<details><summary>Click to expand $_SERVER variables</summary>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
foreach ($_SERVER as $key => $value) {
    echo htmlspecialchars($key . ' = ' . (is_array($value) ? print_r($value, true) : $value)) . "\n";
}
echo "</pre>";
echo "</details>";

echo "<hr>";
echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><small>You can delete this file after debugging.</small></p>";
?>
