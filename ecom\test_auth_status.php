<?php
/**
 * Test Authentication Status
 * This script helps debug authentication issues
 */

// Include session configuration before starting session
include("session_config.php");
session_start();

// Include database connection and functions
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Status Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
        }
        .btn:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Status Test</h1>
        
        <h2>PHP Authentication Check</h2>
        <?php
        $is_logged_in_php = isUserLoggedIn();
        if ($is_logged_in_php) {
            echo '<div class="status success">✅ PHP: User is logged in</div>';
            if (isset($_SESSION['customer'])) {
                $customer = $_SESSION['customer'];
                echo '<p><strong>Customer ID:</strong> ' . htmlspecialchars($customer['cust_id']) . '</p>';
                echo '<p><strong>Name:</strong> ' . htmlspecialchars($customer['cust_fname'] . ' ' . $customer['cust_lname']) . '</p>';
                echo '<p><strong>Email:</strong> ' . htmlspecialchars($customer['cust_email']) . '</p>';
            }
        } else {
            echo '<div class="status error">❌ PHP: User is not logged in</div>';
        }
        ?>
        
        <h2>Session Data</h2>
        <table>
            <tr>
                <th>Session Variable</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Session ID</td>
                <td><?= htmlspecialchars(session_id()) ?></td>
            </tr>
            <tr>
                <td>Customer Session</td>
                <td><?= isset($_SESSION['customer']) ? 'Set' : 'Not set' ?></td>
            </tr>
            <?php if (isset($_SESSION['customer'])): ?>
            <tr>
                <td>Customer Data</td>
                <td><pre><?= htmlspecialchars(print_r($_SESSION['customer'], true)) ?></pre></td>
            </tr>
            <?php endif; ?>
            <tr>
                <td>Remember Token Cookie</td>
                <td><?= isset($_COOKIE['smartlife_remember']) ? 'Present' : 'Not present' ?></td>
            </tr>
        </table>
        
        <h2>JavaScript Authentication Check</h2>
        <div id="js-auth-status" class="status info">⏳ Checking JavaScript authentication...</div>
        
        <h2>JavaScript Variables</h2>
        <div class="code">
            <strong>window.isUserLoggedIn:</strong> <span id="js-logged-in">Loading...</span><br>
            <strong>typeof isUserLoggedIn:</strong> <span id="js-type">Loading...</span><br>
            <strong>isUserLoggedIn variable:</strong> <span id="js-variable">Loading...</span>
        </div>
        
        <h2>Test Actions</h2>
        <a href="login.php" class="btn">Go to Login</a>
        <a href="logout.php" class="btn">Logout</a>
        <a href="cart.php" class="btn">Go to Cart</a>
        <a href="dashboard.php" class="btn">Go to Dashboard</a>
        
        <h2>Debug Information</h2>
        <div class="info status">
            <strong>PHP Version:</strong> <?= PHP_VERSION ?><br>
            <strong>Session Status:</strong> <?= session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive' ?><br>
            <strong>Session Name:</strong> <?= session_name() ?><br>
            <strong>Cookie Settings:</strong><br>
            - Lifetime: <?= ini_get('session.cookie_lifetime') ?><br>
            - Secure: <?= ini_get('session.cookie_secure') ? 'Yes' : 'No' ?><br>
            - HttpOnly: <?= ini_get('session.cookie_httponly') ? 'Yes' : 'No' ?><br>
            - SameSite: <?= ini_get('session.cookie_samesite') ?>
        </div>
    </div>
    
    <script>
        // Set the JavaScript variables that cart.js expects
        window.isUserLoggedIn = <?php echo json_encode($is_logged_in_php); ?>;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Test JavaScript authentication
            const jsAuthStatus = document.getElementById('js-auth-status');
            const jsLoggedIn = document.getElementById('js-logged-in');
            const jsType = document.getElementById('js-type');
            const jsVariable = document.getElementById('js-variable');
            
            // Check if the variable is properly set
            jsLoggedIn.textContent = window.isUserLoggedIn;
            jsType.textContent = typeof window.isUserLoggedIn;
            
            // Check if the variable used in cart.js is available
            if (typeof isUserLoggedIn !== 'undefined') {
                jsVariable.textContent = isUserLoggedIn;
            } else {
                jsVariable.textContent = 'undefined';
            }
            
            // Update status based on JavaScript check
            if (window.isUserLoggedIn === true) {
                jsAuthStatus.className = 'status success';
                jsAuthStatus.innerHTML = '✅ JavaScript: User is logged in';
            } else if (window.isUserLoggedIn === false) {
                jsAuthStatus.className = 'status error';
                jsAuthStatus.innerHTML = '❌ JavaScript: User is not logged in';
            } else {
                jsAuthStatus.className = 'status error';
                jsAuthStatus.innerHTML = '⚠️ JavaScript: Authentication status unclear (value: ' + window.isUserLoggedIn + ')';
            }
            
            // Test the same logic that cart.js uses
            console.log('Authentication Test Results:');
            console.log('- window.isUserLoggedIn:', window.isUserLoggedIn);
            console.log('- typeof isUserLoggedIn:', typeof isUserLoggedIn);
            console.log('- isUserLoggedIn variable:', typeof isUserLoggedIn !== 'undefined' ? isUserLoggedIn : 'undefined');
            
            // Test the exact condition from cart.js
            var isLoggedIn = typeof isUserLoggedIn !== "undefined" ? isUserLoggedIn : false;
            console.log('- cart.js logic result:', isLoggedIn);
            
            if (isLoggedIn !== window.isUserLoggedIn) {
                console.warn('⚠️ Mismatch between window.isUserLoggedIn and cart.js logic!');
                jsAuthStatus.innerHTML += '<br><strong>⚠️ Warning:</strong> Mismatch detected in authentication logic!';
            }
        });
    </script>
</body>
</html>
