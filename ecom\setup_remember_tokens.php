<?php
/**
 * Setup script for remember tokens functionality
 * Run this script once to create the required database table
 */

// Include database configuration
include("../admin/inc/config.php");

try {
    // Create the remember tokens table
    $sql = "
    CREATE TABLE IF NOT EXISTS `tbl_remember_tokens` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `customer_id` int(11) NOT NULL,
      `token_hash` varchar(64) NOT NULL,
      `expires_at` datetime NOT NULL,
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `token_hash` (`token_hash`),
      <PERSON>EY `customer_id` (`customer_id`),
      KEY `expires_at` (`expires_at`),
      FOREIGN KEY (`customer_id`) REFERENCES `tbl_customer` (`cust_id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql);
    echo "✓ Remember tokens table created successfully.<br>";

    // Create index for better performance
    $index_sql = "CREATE INDEX IF NOT EXISTS `idx_customer_expires` ON `tbl_remember_tokens` (`customer_id`, `expires_at`)";
    $pdo->exec($index_sql);
    echo "✓ Performance index created successfully.<br>";

    // Create cleanup log table
    $cleanup_log_sql = "
    CREATE TABLE IF NOT EXISTS `tbl_cleanup_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `cleanup_type` varchar(50) NOT NULL,
        `last_cleanup` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `tokens_deleted` int(11) DEFAULT 0,
        PRIMARY KEY (`id`),
        KEY `cleanup_type` (`cleanup_type`),
        KEY `last_cleanup` (`last_cleanup`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($cleanup_log_sql);
    echo "✓ Cleanup log table created successfully.<br>";

    // Try to create cleanup event (may fail if user doesn't have EVENT privileges)
    try {
        $event_sql = "
        CREATE EVENT IF NOT EXISTS `cleanup_expired_remember_tokens`
        ON SCHEDULE EVERY 1 DAY
        STARTS CURRENT_TIMESTAMP
        DO
        BEGIN
          DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
        END
        ";
        $pdo->exec($event_sql);
        echo "✓ Automatic cleanup event created successfully.<br>";

        // Enable event scheduler
        $pdo->exec("SET GLOBAL event_scheduler = ON");
        echo "✓ Event scheduler enabled.<br>";

    } catch (PDOException $e) {
        echo "⚠ Warning: Could not create automatic cleanup event. This is normal if your database user doesn't have EVENT privileges.<br>";
        echo "You can manually clean up expired tokens by running: DELETE FROM tbl_remember_tokens WHERE expires_at < NOW();<br>";
    }

    echo "<br><strong>Setup completed successfully!</strong><br>";
    echo "The persistent login functionality is now ready to use.<br><br>";
    echo "Features enabled:<br>";
    echo "• Remember Me checkbox on login form<br>";
    echo "• 30-day persistent sessions<br>";
    echo "• Secure token-based authentication<br>";
    echo "• <strong>Fully automated cleanup system</strong><br>";
    echo "• Multiple cleanup methods (time-based, probabilistic, lightweight)<br>";
    echo "• Cleanup activity logging and monitoring<br>";
    echo "• Emergency cleanup for very old tokens<br>";
    echo "• Logout from all devices functionality<br>";

} catch (PDOException $e) {
    echo "Error setting up remember tokens: " . $e->getMessage() . "<br>";
    echo "Please check your database connection and permissions.<br>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Remember Tokens Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Remember Tokens Setup</h1>
    <p>This script sets up the database table required for persistent login functionality.</p>

    <div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>Security Features Implemented:</h3>
        <ul>
            <li><strong>Secure Cookies:</strong> HttpOnly, Secure (HTTPS), SameSite protection</li>
            <li><strong>Token Hashing:</strong> SHA-256 hashed tokens stored in database</li>
            <li><strong>Expiration:</strong> 30-day automatic expiration</li>
            <li><strong>Session Regeneration:</strong> New session ID on each login</li>
            <li><strong>Cleanup:</strong> Automatic removal of expired tokens</li>
            <li><strong>Logout Protection:</strong> Clear all tokens on logout</li>
        </ul>
    </div>

    <div style="background: #e8f4fd; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>How to Use:</h3>
        <ol>
            <li>Run this setup script once to create the database table</li>
            <li>Users can now check "Remember me for 30 days" when logging in</li>
            <li>They will stay logged in even after closing the browser</li>
            <li>Logout will clear all persistent sessions</li>
        </ol>
    </div>

    <p><a href="login.php">← Back to Login</a></p>
</body>
</html>
