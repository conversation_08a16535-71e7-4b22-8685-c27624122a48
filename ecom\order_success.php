<?php
// ecom/order_success.php
// Include session configuration before starting session
include("session_config.php");
session_start();

// Include necessary files
include("../admin/inc/config.php"); // Ensure correct path
include("../admin/inc/functions.php"); // Ensure correct path
include("auto_cleanup.php");

// Get order ID from URL
$order_id = $_GET['order_id'] ?? null;

// Validate order ID and ensure it belongs to the logged-in user
if (!$order_id || !isUserLoggedIn()) {
    header("Location: index.php");
    exit();
}

$customer_id = $_SESSION['customer']['cust_id'];

// Fetch order details securely
$statement = $pdo->prepare("SELECT * FROM orders WHERE order_id = ? AND customer_id = ? AND payment_status = 'success'");
$statement->execute([$order_id, $customer_id]);
$order = $statement->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    // Order not found, not successful, or doesn't belong to user
    $_SESSION['error_message'] = "Order confirmation not available or invalid.";
    header("Location: index.php"); // Or an order history page
    exit();
}

// Fetch order items
$statement = $pdo->prepare("SELECT oi.*, p.p_featured_photo
                            FROM order_items oi
                            LEFT JOIN tbl_product p ON oi.product_id = p.p_id
                            WHERE oi.order_id = ?");
$statement->execute([$order_id]);
$order_items = $statement->fetchAll(PDO::FETCH_ASSOC);


// Fetch settings for footer etc. (if using includes/header.php pattern)
$settingsStmt = $pdo->query("SELECT footer_copyright FROM tbl_settings WHERE id=1");
$settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© " . date("Y") . " SMART LIFE";


// Set page title for header include
$page_title = "Order Confirmed - " . htmlspecialchars($order_id);

// --- Start HTML ---
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="css/style.css"> <link rel="stylesheet" href="css/cart.css"> <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
     <style>
        /* Add specific styles for the confirmation page */
        .order-confirmation { padding: 40px; background-color: #fff; border-radius: 8px; box-shadow: 0 2px 15px rgba(0,0,0,0.07); margin-bottom: 30px; }
        .confirmation-header { text-align: center; margin-bottom: 30px; }
        .confirmation-header h1 { color: #1cc88a; /* Green */ margin-bottom: 10px; font-size: 2.5rem; }
        .confirmation-header p { font-size: 1.1rem; color: #555; }
        .order-details, .order-items, .order-actions { margin-bottom: 30px; }
        .order-details .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px dashed #eee; }
        .order-details .detail-label { font-weight: 600; color: #333; }
        .order-details .detail-value { color: #555; }
        .order-items h3 { margin-bottom: 15px; font-weight: 600; border-bottom: 1px solid #eee; padding-bottom: 10px;}
        .order-items-table { width: 100%; border-collapse: collapse; }
        .order-items-table th, .order-items-table td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        .order-items-table th { background-color: #f8f9fa; font-weight: 600; }
        .order-items-table .product-info { display: flex; align-items: center; }
        .order-items-table .product-image { width: 50px; height: 50px; object-fit: cover; border-radius: 4px; margin-right: 15px; }
         .order-items-table .product-details div { margin-bottom: 3px;}
        .installation-badge { font-size: 0.8rem; color: #007bff; display: block; margin-top: 4px;}
        .order-actions { text-align: center; }
        .order-actions .btn { margin: 0 10px; }
        .btn-primary { background-color: var(--secondary); border-color: var(--secondary); }
        .btn-primary:hover { background-color: var(--accent); border-color: var(--accent); }
        .btn-secondary { background-color: #6c757d; border-color: #6c757d; color: #fff;}
        .btn-secondary:hover { background-color: #5a6268; border-color: #545b62;}
     </style>
</head>
<body>
    <?php // If you have a standard header include, add it here
        // include("includes/header.php");
    ?>
     <header>
         <div class="container">
           <a href="index.php" class="logo">NOIR<span>.</span></a>
             </div>
     </header>

    <main>
        <div class="container">
            <div class="order-confirmation">
                <div class="confirmation-header">
                    <h1><i class="fas fa-check-circle"></i> Order Confirmed!</h1>
                    <p>Thank you for your purchase, <?php echo htmlspecialchars($_SESSION['customer']['cust_fname']); ?>!</p>
                     <?php
                        if (isset($_SESSION['success_message'])) {
                            echo '<div class="success-message" style="margin-top: 15px; background-color: #e8f5e9; color: #155724; border: 1px solid #c3e6cb; padding: 10px; border-radius: 4px;">' . $_SESSION['success_message'] . '</div>';
                            unset($_SESSION['success_message']);
                        }
                    ?>
                </div>

                <div class="order-details">
                    <h3>Order Summary</h3>
                    <div class="detail-row">
                        <span class="detail-label">Order Number:</span>
                        <span class="detail-value"><?php echo htmlspecialchars($order['order_id']); ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Order Date:</span>
                        <span class="detail-value"><?php echo date('F j, Y, g:i a', strtotime($order['created_at'])); ?></span>
                    </div>
                     <div class="detail-row">
                        <span class="detail-label">Payment Method:</span>
                        <span class="detail-value"><?php echo htmlspecialchars(ucfirst($order['payment_method'] ?? 'Flutterwave')); ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Total Paid:</span>
                        <span class="detail-value"><strong>Tsh <?php echo number_format($order['total_amount'], 0); ?></strong></span>
                    </div>
                    </div>

                <div class="order-items">
                    <h3>Items Ordered</h3>
                    <table class="order-items-table">
                        <thead>
                            <tr>
                                <th colspan="2">Product</th>
                                <th>Quantity</th>
                                <th>Total Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($order_items as $item): ?>
                            <tr>
                                 <td style="width: 60px;">
                                    <img src="<?php echo $item['p_featured_photo'] ? '../assets/uploads/' . htmlspecialchars($item['p_featured_photo']) : 'images/placeholder.png'; ?>" alt="" class="product-image">
                                </td>
                                <td>
                                    <div class="product-details">
                                        <div><strong><?php echo htmlspecialchars($item['product_name']); ?></strong></div>
                                         <?php if ($item['color_name']): ?>
                                            <div style="font-size: 0.9em; color: #555;">Color: <?php echo htmlspecialchars($item['color_name']); ?></div>
                                        <?php endif; ?>
                                        <?php if ($item['installation']): ?>
                                            <span class="installation-badge"><i class="fas fa-tools"></i> Incl. Installation</span>
                                        <?php endif; ?>
                                     </div>
                                </td>
                                <td><?php echo $item['quantity']; ?></td>
                                <td>Tsh <?php echo number_format($item['total_price'], 0); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="order-actions">
                    <a href="index.php" class="btn btn-primary">Continue Shopping</a>
                    <a href="order_history.php" class="btn btn-secondary">View Order History</a> </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
          <div class="footer-bottom">
            <div class="copyright"><?php echo htmlspecialchars($footer_copyright); ?></div>
          </div>
        </div>
    </footer>


    <?php
    // Check if the flag is set by payment_verify.php
    if (isset($_SESSION['clear_local_cart']) && $_SESSION['clear_local_cart'] === true) {
        echo "<script>
            console.log('Clearing local storage cart...');
            localStorage.removeItem('cart');
            // Optionally update header count immediately if needed via JS
            const countElem = document.querySelector('.cart-count');
            if(countElem) { countElem.textContent = '0'; }
         </script>";
        unset($_SESSION['clear_local_cart']); // Clear the flag after outputting script
    }
    ?>
    <script src="js/script.js"></script> </body> </body>
</html>