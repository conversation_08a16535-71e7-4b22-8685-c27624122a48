<?php
/**
 * Simple Login Endpoint
 * A standalone login endpoint that bypasses routing issues
 */

// Prevent any output before JSON
ob_start();
error_reporting(0);
ini_set('display_errors', 0);

// Set JSON header immediately
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method not allowed. Use POST.',
        'timestamp' => date('c'),
        'api_version' => 'v1'
    ]);
    exit();
}

try {
    // Include only essential files
    require_once __DIR__ . '/config/database.php';
    
    // Simple JWT implementation
    class SimpleAuth {
        public static function hashPassword($password) {
            return password_hash($password, PASSWORD_DEFAULT);
        }
        
        public static function verifyPassword($password, $hash) {
            return password_verify($password, $hash);
        }
        
        public static function generateToken($user) {
            $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
            $payload = json_encode([
                'user_id' => $user['cust_id'],
                'email' => $user['cust_email'],
                'first_name' => $user['cust_fname'],
                'last_name' => $user['cust_lname'],
                'iat' => time(),
                'exp' => time() + 86400 // 24 hours
            ]);
            
            $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
            $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
            
            $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, 'your-secret-key', true);
            $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
            
            return $base64Header . "." . $base64Payload . "." . $base64Signature;
        }
    }
    
    // Get input data
    $input_json = file_get_contents('php://input');
    $input = json_decode($input_json, true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    if (empty($input['email']) || empty($input['password'])) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'Email and password are required',
            'timestamp' => date('c'),
            'api_version' => 'v1'
        ]);
        exit();
    }
    
    $email = $input['email'];
    $password = $input['password'];
    
    // Database connection
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Find user
    $stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_email = ? AND cust_status = 1");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid email or password',
            'timestamp' => date('c'),
            'api_version' => 'v1',
            'error_code' => 'INVALID_CREDENTIALS'
        ]);
        exit();
    }
    
    // Verify password
    if (!SimpleAuth::verifyPassword($password, $user['cust_password'])) {
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid email or password',
            'timestamp' => date('c'),
            'api_version' => 'v1',
            'error_code' => 'INVALID_CREDENTIALS'
        ]);
        exit();
    }
    
    // Update last login
    $update_stmt = $pdo->prepare("UPDATE tbl_customer SET cust_last_login = NOW() WHERE cust_id = ?");
    $update_stmt->execute([$user['cust_id']]);
    
    // Generate token
    $token = SimpleAuth::generateToken($user);
    
    // Prepare response
    $response = [
        'status' => 'success',
        'message' => 'Login successful',
        'timestamp' => date('c'),
        'api_version' => 'v1',
        'data' => [
            'token' => $token,
            'token_type' => 'Bearer',
            'expires_in' => 86400,
            'user' => [
                'id' => (int)$user['cust_id'],
                'first_name' => $user['cust_fname'],
                'last_name' => $user['cust_lname'],
                'email' => $user['cust_email'],
                'phone' => $user['cust_phone'] ?? null,
                'photo' => $user['cust_photo'] ?? null,
                'created_at' => $user['cust_created_at'] ?? null,
                'last_login' => date('c')
            ]
        ]
    ];
    
    // Clear any buffered output and send JSON
    ob_clean();
    http_response_code(200);
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed',
        'timestamp' => date('c'),
        'api_version' => 'v1',
        'error_code' => 'DATABASE_ERROR'
    ]);
    
} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'An unexpected error occurred',
        'timestamp' => date('c'),
        'api_version' => 'v1',
        'error_code' => 'INTERNAL_ERROR',
        'debug' => $e->getMessage()
    ]);
}

exit();
?>
