<?php
/**
 * Response Format Test
 * Tests the Response class and API output format
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>Response Format Test</h1>";

// Test 1: Direct Response class test
echo "<h2>1. Testing Response Class Directly</h2>";

try {
    echo "<h3>Success Response:</h3>";
    ob_start();
    Response::success(['test' => 'data'], 'Test message');
    $output = ob_get_clean();
    echo "<pre style='background: #d4edda; padding: 10px;'>" . htmlspecialchars($output) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Response class error: " . $e->getMessage() . "</p>";
}

// Test 2: Test API root endpoint manually
echo "<h2>2. Testing API Root Response</h2>";

try {
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    
    $api_info = [
        'name' => 'Ecommerce API',
        'version' => API_VERSION,
        'timestamp' => date('c'),
        'endpoints' => [
            'auth' => 'Authentication endpoints',
            'products' => 'Product catalog',
            'categories' => 'Product categories',
            'cart' => 'Shopping cart',
            'orders' => 'Order management',
            'users' => 'User management',
            'search' => 'Product search',
            'wishlist' => 'User wishlist',
            'shipping' => 'Shipping information',
            'settings' => 'Application settings',
            'admin' => 'Admin operations'
        ],
        'documentation' => $script_dir . '/v1/docs'
    ];
    
    echo "<h3>Manual API Root Response:</h3>";
    ob_start();
    Response::success($api_info, 'Welcome to Ecommerce API');
    $output = ob_get_clean();
    echo "<pre style='background: #d4edda; padding: 10px;'>" . htmlspecialchars($output) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>API root error: " . $e->getMessage() . "</p>";
}

// Test 3: Check if settings table exists
echo "<h2>3. Database Table Check</h2>";

try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>Available tables:</strong></p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>{$table}</li>";
    }
    echo "</ul>";
    
    // Check if tbl_settings exists
    if (in_array('tbl_settings', $tables)) {
        echo "<p style='color: green;'>✓ tbl_settings table exists</p>";
        $count = $pdo->query("SELECT COUNT(*) FROM tbl_settings")->fetchColumn();
        echo "<p>Records in tbl_settings: {$count}</p>";
    } else {
        echo "<p style='color: red;'>✗ tbl_settings table missing</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

// Test 4: Test settings endpoint with fallback
echo "<h2>4. Settings Endpoint with Fallback</h2>";

try {
    // Create fallback settings
    $fallback_settings = [
        'name' => 'Ecommerce Store',
        'logo' => null,
        'favicon' => null,
        'footer_text' => 'Welcome to our store',
        'footer_copyright' => '© 2024 Ecommerce Store',
        'currency' => DEFAULT_CURRENCY,
        'installation_fee' => (int)DEFAULT_INSTALLATION_FEE,
        'version' => API_VERSION
    ];
    
    echo "<h3>Fallback Settings Response:</h3>";
    ob_start();
    Response::success($fallback_settings, 'App settings retrieved successfully');
    $output = ob_get_clean();
    echo "<pre style='background: #d4edda; padding: 10px;'>" . htmlspecialchars($output) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Settings fallback error: " . $e->getMessage() . "</p>";
}

// Test 5: Raw JSON output
echo "<h2>5. Raw JSON Test</h2>";

$raw_response = [
    'status' => 'success',
    'message' => 'Raw JSON test',
    'timestamp' => date('c'),
    'api_version' => API_VERSION,
    'data' => [
        'test' => true,
        'format' => 'valid'
    ]
];

echo "<h3>Raw JSON Output:</h3>";
echo "<pre style='background: #fff3cd; padding: 10px;'>";
echo json_encode($raw_response, JSON_PRETTY_PRINT);
echo "</pre>";

echo "<hr>";
echo "<p>If you see properly formatted JSON above, the Response class is working correctly.</p>";
?>
