:root {
    --primary: #333;
    --secondary: #00c2ff;
    --accent: #0099cc;
    --light-bg: #f9f9f9;
    --border: #e1e1e1;
    --error: #ff6b6b;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html, body {
    min-height: 100%;
    margin: 0;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--primary);
    background-color: #f5f5f5;
  }

  main {
    flex: 1;
    padding: 20px 0;
  }

  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }

  /* Header Styles */
  header {
    background: #fff;
    border-bottom: 1px solid var(--border);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  }

  header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  header .logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary);
    text-decoration: none;
  }

  header .logo span {
    color: var(--secondary);
  }

  header .nav-links {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  header .nav-links a {
    text-decoration: none;
    color: var(--primary);
    font-size: 1rem;
    transition: color 0.3s;
  }

  header .nav-links a:hover {
    color: var(--secondary);
  }

  header .cart-icon a {
    text-decoration: none;
    font-size: 1.2rem;
    position: relative;
    display: inline-block;
    color: var(--primary);
  }

  header .cart-count {
    background: var(--secondary);
    color: #fff;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.8rem;
    position: absolute;
    top: -10px;
    right: -10px;
  }

  /* Cart Container */
  .cart-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    margin-bottom: 30px;
  }

  .cart-header {
    padding: 20px;
    border-bottom: 1px solid var(--border);
  }

  .cart-header h1 {
    font-size: 1.8rem;
    color: var(--primary);
    margin: 0;
  }

  /* Cart Table */
  .cart-table {
    width: 100%;
    border-collapse: collapse;
  }

  .cart-table thead {
    background-color: var(--light-bg);
  }

  .cart-table th {
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: var(--primary);
    border-bottom: 2px solid var(--border);
  }

  .cart-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border);
    vertical-align: middle;
  }

  .cart-table tr:last-child td {
    border-bottom: none;
  }

  .cart-table tr:hover {
    background-color: rgba(0, 194, 255, 0.03);
  }

  .cart-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .product-name {
    font-weight: 600;
    color: var(--primary);
    text-decoration: none;
    transition: color 0.3s;
  }

  .product-name:hover {
    color: var(--secondary);
  }

  .product-color {
    display: inline-block;
    margin-top: 5px;
    font-size: 0.9rem;
    color: #666;
  }

  /* Color Selector */
  .color-select-wrapper {
    position: relative;
    display: inline-block;
    min-width: 150px;
  }

  .color-select {
    width: 100%;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border);
    background-color: white;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
  }

  .color-select:focus {
    outline: none;
    border-color: var(--secondary);
    box-shadow: 0 0 0 2px rgba(0, 194, 255, 0.2);
  }

  .color-select-wrapper::after {
    content: "▼";
    font-size: 0.7rem;
    color: #666;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }

  .color-preview {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-left: 8px;
    vertical-align: middle;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    border: 1px solid rgba(0,0,0,0.1);
  }

  /* Quantity Selector */
  .quantity-selector {
    display: inline-flex;
    align-items: center;
    border: 1px solid var(--border);
    border-radius: 4px;
    overflow: hidden;
  }

  .quantity-btn {
    background-color: var(--light-bg);
    border: none;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
  }

  .quantity-btn:hover {
    background-color: #e1e1e1;
  }

  .quantity-input {
    width: 40px;
    text-align: center;
    border: none;
    border-left: 1px solid var(--border);
    border-right: 1px solid var(--border);
    padding: 5px;
    font-size: 0.9rem;
  }

  /* Installation Option */
  .installation-option {
    display: flex;
    align-items: center;
  }

  .installation-option input {
    margin-right: 8px;
    cursor: pointer;
  }

  .installation-option label {
    cursor: pointer;
    font-size: 0.9rem;
  }

  /* Action Buttons */
  .action-btn {
    background-color: transparent;
    color: var(--primary);
    border: 1px solid var(--border);
    padding: 6px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
    font-size: 0.9rem;
  }

  .action-btn:hover {
    background-color: var(--light-bg);
  }

  .remove-btn {
    color: var(--error);
    border-color: var(--error);
  }

  .remove-btn:hover {
    background-color: rgba(255, 107, 107, 0.1);
  }

  /* Price Summary */
  .price-summary {
    padding: 20px;
    background-color: var(--light-bg);
    border-top: 1px solid var(--border);
  }

  .price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
  }

  .price-label {
    color: #666;
  }

  .price-value {
    font-weight: 500;
  }

  .installation-fee-row {
    display: none;
  }

  .subtotal-row {
    padding-top: 12px;
    border-top: 1px dashed var(--border);
    margin-top: 10px;
    font-size: 1.1rem;
  }

  .total-row {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 20px 0 10px;
    padding-top: 15px;
    border-top: 1px solid var(--primary);
  }

  .total-price {
    color: var(--secondary);
  }

  /* Checkout Button */
  .checkout-btn {
    display: inline-block;
    background-color: var(--secondary);
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s;
    font-weight: 600;
    border: none;
    cursor: pointer;
    text-align: center;
    width: 100%;
    max-width: 250px;
    margin-left: auto;
  }

  .checkout-btn:hover {
    background-color: var(--accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 194, 255, 0.2);
  }

  /* Modal Styles */
  .modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
  }

  .modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    width: 90%;
    max-width: 700px;
    position: relative;
  }

  .close-modal {
    position: absolute;
    right: 20px;
    top: 15px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
  }

  .close-modal:hover {
    color: var(--primary);
  }

  .modal h2 {
    margin-bottom: 20px;
    color: var(--primary);
    padding-right: 30px;
  }

  /* Summary Item Styles */
  .summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border);
    align-items: center;
  }

  .summary-item-details {
    flex: 2;
    display: flex;
    align-items: center;
  }

  .summary-item-image {
    width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .summary-item-text {
    flex: 1;
  }

  .summary-item-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--primary);
  }

  .summary-item-meta {
    font-size: 0.9rem;
    color: #666;
  }

  .summary-color {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-left: 8px;
    vertical-align: middle;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    border: 1px solid rgba(0,0,0,0.1);
  }

  .summary-item-price {
    flex: 1;
    text-align: right;
    font-weight: 600;
    color: var(--primary);
  }

  .summary-totals {
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid var(--border);
  }

  .summary-total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .summary-grand-total {
    font-size: 1.2rem;
    font-weight: 700;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid var(--primary);
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
  }

  .modal-btn {
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
  }

  .confirm-btn {
    background-color: var(--secondary);
    color: white;
    border: none;
  }

  .confirm-btn:hover {
    background-color: var(--accent);
  }

  .cancel-btn {
    background-color: #f1f1f1;
    color: var(--primary);
    border: none;
  }

  .cancel-btn:hover {
    background-color: #e1e1e1;
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .cart-table {
      display: block;
      overflow-x: auto;
    }
    
    .cart-table thead {
      display: none;
    }
    
    .cart-table tr {
      display: grid;
      grid-template-columns: 250px 1fr;
      grid-template-areas: 
        "image name"
        "price price"
        "quantity color"
        "installation subtotal"
        "action action";
      gap: 10px;
      padding: 15px 10px;
      border-bottom: 1px solid var(--border);
      position: relative;
    }
    
    .cart-table td {
      display: block;
      text-align: left;
      
      padding: 5px;
      border: none;
    }
    
    .cart-table td:first-child {
      grid-area: image;
      padding: 0;
    }
    
    .cart-table td:nth-child(2) {
      grid-area: name;
      align-self: center;
    }
    
    .cart-table td:nth-child(3) {
      grid-area: price;
      font-weight: bold;
    }
    
    .cart-table td:nth-child(4) {
      grid-area: quantity;
    }
    
    .cart-table td:nth-child(5) {
      grid-area: color;
    }
    
    .cart-table td:nth-child(6) {
      grid-area: installation;
    }
    
    .cart-table td:nth-child(7) {
      grid-area: subtotal;
      font-weight: bold;
    }
    
    .cart-table td:nth-child(8) {
      grid-area: action;
      text-align: right;
    }
    
    .cart-table td::before {
      content: attr(data-label);
      font-weight: bold;
      display: block;
      margin-bottom: 5px;
      color: #666;
    }
    
    .cart-img {
      width: 60%;
      height: auto;
    }
    
    .color-select-wrapper {
      min-width: 100%;
    }
    
    .checkout-btn {
      max-width: 100%;
    }
    
    /* Modal adjustments */
    .modal-content {
      width: 95%;
      margin: 20px auto;
      padding: 15px;
    }
    
    .summary-item {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .summary-item-details {
      width: 100%;
      margin-bottom: 10px;
    }
    
    .summary-item-price {
      width: 100%;
      text-align: left;
      padding-top: 10px;
      border-top: 1px dashed var(--border);
    }
    
    .modal-actions {
      flex-direction: column;
    }
    
    .modal-actions .modal-btn {
      width: 100%;
      margin-bottom: 10px;
    }
  }

  /* Empty Cart State */
  .empty-cart {
    text-align: center;
    padding: 40px 20px;
  }
  
  .empty-cart i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 20px;
  }
  
  .empty-cart h3 {
    margin-bottom: 15px;
    color: var(--primary);
  }
  
  .empty-cart p {
    color: #666;
    margin-bottom: 20px;
  }
  
  .shop-btn {
    display: inline-block;
    background-color: var(--secondary);
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s;
  }
  
  .shop-btn:hover {
    background-color: var(--accent);
  }


 


