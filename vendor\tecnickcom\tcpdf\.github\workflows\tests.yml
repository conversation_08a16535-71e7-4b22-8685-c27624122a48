name: Run tests

env:
  XDEBUG_MODE: coverage

permissions:
  contents: read

on:
  push:
    branches:
      - 'main'
  pull_request:
    branches:
      - 'main'

jobs:
    test-php:
        name: Test on php ${{ matrix.php-version }} and ${{ matrix.os }}
        runs-on: ${{ matrix.os }}
        continue-on-error: ${{ matrix.experimental }}
        strategy:
            fail-fast: false
            matrix:
                php-version: ["7.1", "7.2", "7.3", "7.4", "8.0", "8.1", "8.2", "8.3"]
                os: [ubuntu-latest]
                experimental: [false]
                php-extensions: ["bcmath, curl, imagick, gd"]
                coverage-extension: ["none"]
                # Add more specific tests
                include:
                  #- { php-version: '8.2', experimental: false, os: macos-latest, php-extensions: 'bcmath, curl, imagick, gd', coverage-extension: 'none' }
                  - { php-version: '8.2', experimental: false, os: windows-latest, php-extensions: 'bcmath, curl, imagick, gd', coverage-extension: 'none' }
                  - { php-version: '8.4', experimental: true, os: ubuntu-latest, php-extensions: 'bcmath, curl, imagick, gd', coverage-extension: 'pcov' }
                  - { php-version: 'nightly', experimental: true, os: ubuntu-latest, php-extensions: 'bcmath, curl, imagick, gd', coverage-extension: 'pcov' }
        env:
            PDFINFO_BINARY: ${{ (matrix.os == 'ubuntu-latest') && '/usr/bin/pdfinfo' || ((matrix.os == 'macos-latest') && '/usr/local/bin/pdfinfo' || 'C:\ProgramData\Chocolatey\bin\pdfinfo.exe') }}
        steps:
            - uses: actions/checkout@v4
            - name: Install pdfinfo, pdftopng or pdftoppm
              uses: ConorMacBride/install-package@v1
              with:
                apt: poppler-utils
                brew: poppler
                choco: xpdf-utils
            - name: Install magick
              if: runner.os == 'Linux'
              uses: mfinelli/setup-imagemagick@v2
            - name: Install magick
              uses: ConorMacBride/install-package@v1
              with:
                brew: imagemagick@6
                choco: imagemagick
            - name: Use php ${{ matrix.php-version }}
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php-version }}
                  coverage: ${{ matrix.coverage-extension }}
                  extensions: ${{ matrix.php-extensions }}
                  ini-values: display_errors=on, display_startup_errors=on, error_reporting=-1
            - name: List php modules
              run: php -m
            - name: List php modules using "no php ini" mode
              run: php -m -n
            - name: Cache module
              uses: actions/cache@v4
              with:
                  path: ~/.composer/cache/
                  key: composer-cache
            - name: Install dependencies
              run: composer update --no-interaction
            - name: Install test dependencies
              run: cd ./tests && composer update --no-interaction && cd ../
            - name: Run shell-based test suite
              if: runner.os == 'Linux'
              run: ./tests/launch.sh
            - name: Send coverage
              if: matrix.coverage-extension != 'none'
              uses: codecov/codecov-action@v3
              with:
                flags: php-${{ matrix.php-version }}-${{ matrix.os }}
                name: php-${{ matrix.php-version }}-${{ matrix.os }}
            - name: Create output folder
              run: mkdir ./tests/output
            - name: Run PHP-based test suite
              run: php ./tests/launch.php --output-dir ./tests/output
              # The PHP_BINARY environment variable is only available since PHP 5.4
              env:
                PHP_BINARY: ${{ (matrix.os == 'ubuntu-latest') && '/usr/bin/php' || ((matrix.os == 'macos-latest') && '/usr/local/bin/php' || 'C:\tools\php\php.exe') }}
            # For debugging issues
            - name: Archive test results
              if: failure()
              uses: actions/upload-artifact@v4
              with:
                name: test-run-output-${{ matrix.php-version }}-${{ matrix.os }}
                retention-days: 1
                path: ./tests/output

    analyse-php:
        name: Static Analysis
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - name: Use php 8.2
              uses: shivammathur/setup-php@v2
              with:
                  php-version: 8.2
            - name: Cache module
              uses: actions/cache@v4
              with:
                  path: ~/.composer/cache/
                  key: composer-cache
            - name: Install phpstan
              run: composer require --dev phpstan/phpstan
            - name: Analyse files
              run: ./vendor/bin/phpstan --memory-limit=6G
