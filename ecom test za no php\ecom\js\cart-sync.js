/**
 * cart-sync.js - Utility for synchronizing cart state across all pages
 */

class CartSync {
    constructor() {
        this.STORAGE_KEY = 'cart';
        this.EVENT_NAME = 'cartUpdated';
        this.POLL_INTERVAL = 2000; // Check for changes every 2 seconds
        this.lastCartHash = null;
        
        // Initialize
        this.initEventListeners();
        this.startPolling();
    }
    
    /**
     * Get current cart state from localStorage
     */
    getCart() {
        try {
            const cartData = localStorage.getItem(this.STORAGE_KEY);
            return cartData ? JSON.parse(cartData) : [];
        } catch (e) {
            console.error("CartSync: Error reading cart from localStorage", e);
            return [];
        }
    }
    
    /**
     * Save cart to localStorage
     */
    saveCart(cart) {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cart));
            this.lastCartHash = this.generateCartHash(cart);
        } catch (e) {
            console.error("CartSync: Error saving cart to localStorage", e);
        }
    }
    
    /**
     * Generate a simple hash for cart comparison
     */
    generateCartHash(cart) {
        return JSON.stringify(cart.map(item => ({
            product_id: item.product_id,
            variation_id: item.variation_id,
            quantity: item.quantity
        })));
    }
    
    /**
     * Check if cart has changed
     */
    hasCartChanged() {
        const currentCart = this.getCart();
        const currentHash = this.generateCartHash(currentCart);
        return currentHash !== this.lastCartHash;
    }
    
    /**
     * Update UI elements that show cart state
     */
    updateCartUI() {
        const cart = this.getCart();
        
        // Update cart count in header
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(el => {
            el.textContent = cart.length;
        });
        
        // Update "Add to Cart" buttons on product pages
        if (document.querySelector('.add-to-cart-btn')) {
            document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
                const productId = btn.closest('[data-product-id]')?.dataset.productId || 
                                btn.closest('form')?.querySelector('[name="product_id"]')?.value;
                const variationId = btn.closest('[data-variation-id]')?.dataset.variationId || 
                                   btn.closest('form')?.querySelector('[name="variation_id"]')?.value;
                
                if (productId) {
                    const isInCart = cart.some(item => 
                        String(item.product_id) === String(productId) && 
                        (!variationId || String(item.variation_id) === String(variationId)));
                    
                    if (isInCart) {
                        btn.disabled = true;
                        btn.querySelector('.btn-text').textContent = 'Already in Cart';
                        if (btn.querySelector('.btn-icon')) {
                            btn.querySelector('.btn-icon').innerHTML = '<i class="fas fa-check"></i>';
                        }
                    } else {
                        // Check stock and update accordingly
                        const stockElement = document.getElementById('productAvailability');
                        const isInStock = stockElement && 
                                         (stockElement.textContent.includes('In Stock') || 
                                          stockElement.style.color === '#1cc88a');
                        
                        btn.disabled = !isInStock;
                        btn.querySelector('.btn-text').textContent = isInStock ? 'Add to Cart' : 'Out of Stock';
                        if (btn.querySelector('.btn-icon')) {
                            btn.querySelector('.btn-icon').innerHTML = isInStock ? 
                                '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>' : 
                                '<i class="fas fa-times-circle"></i>';
                        }
                    }
                }
            });
        }
    }
    
    /**
     * Initialize event listeners for cart updates
     */
    initEventListeners() {
        // Listen for custom cart update events
        document.addEventListener(this.EVENT_NAME, () => {
            this.updateCartUI();
        });
        
        // Listen for storage events (changes from other tabs)
        window.addEventListener('storage', (e) => {
            if (e.key === this.STORAGE_KEY) {
                this.lastCartHash = this.generateCartHash(this.getCart());
                this.updateCartUI();
            }
        });
    }
    
    /**
     * Start polling for cart changes (for cases where storage events don't work)
     */
    startPolling() {
        this.pollInterval = setInterval(() => {
            if (this.hasCartChanged()) {
                this.lastCartHash = this.generateCartHash(this.getCart());
                this.updateCartUI();
                
                // Dispatch event for other components to listen to
                document.dispatchEvent(new CustomEvent(this.EVENT_NAME));
            }
        }, this.POLL_INTERVAL);
    }
    
    /**
     * Stop polling (cleanup)
     */
    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }
    }
}

// Initialize the CartSync when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cartSync = new CartSync();
});