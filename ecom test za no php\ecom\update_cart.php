<?php
session_start();
header('Content-Type: application/json');

if (isset($_POST['action']) && $_POST['action'] === 'remove' && isset($_POST['key'])) {
    $keyToRemove = $_POST['key'];
    if (isset($_SESSION['cart'][$keyToRemove])) {
        unset($_SESSION['cart'][$keyToRemove]);
        $totalItems = 0;
        if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
            foreach ($_SESSION['cart'] as $item) {
                $totalItems += (int)($item['quantity'] ?? 0);
            }
        }
        echo json_encode(['success' => true, 'total_items' => $totalItems]);
        exit();
    } else {
        echo json_encode(['success' => false, 'message' => 'Item not found in cart.']);
        exit();
    }
}

// Handle other actions (update_qty, update_installation) if needed

echo json_encode(['success' => false, 'message' => 'Invalid action.']);
?>