<?php
// Enable full error reporting for development
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Ensures header.php is included (DB connection, session, etc.)
require_once('header.php');

// --- Initial Setup ---
$error_message = '';
$success_message = '';
$product_data = null;
$product_featured_colors = [];
$product_variations = [];

$color_options = '';
$top_categories = [];
$mid_category_map = [];
$size_options = '';
$color_result = []; // For populating options/checkboxes
$sizes = []; // For populating options

// --- Get Product ID from URL ---
if (!isset($_GET['id']) || !filter_var($_GET['id'], FILTER_VALIDATE_INT)) {
    // Redirect or show error if ID is missing or invalid
    header('Location: product.php?status=invalid_id');
    exit;
}
$p_id = $_GET['id'];

// --- Try to fetch initial form options data (like in add page) ---
try {
    // Colors
    $stmt_colors = $pdo->prepare("SELECT * FROM tbl_color ORDER BY color_name ASC");
    $stmt_colors->execute();
    $color_result = $stmt_colors->fetchAll(PDO::FETCH_ASSOC);
    foreach ($color_result as $color) {
        $color_options .= '<option value="' . $color['color_id'] . '">' . htmlspecialchars($color['color_name']) . '</option>';
    }

    // Top Categories
    $stmt_tcat = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC");
    $stmt_tcat->execute();
    $top_categories = $stmt_tcat->fetchAll(PDO::FETCH_ASSOC);

    // Mid Categories (for mapping)
    $stmt_mcat = $pdo->prepare("SELECT * FROM tbl_mid_category ORDER BY mcat_name ASC");
    $stmt_mcat->execute();
    $all_mid_categories = $stmt_mcat->fetchAll(PDO::FETCH_ASSOC);
    $mid_category_map = [];
    foreach ($all_mid_categories as $mcat) {
        $mid_category_map[$mcat['tcat_id']][] = $mcat;
    }

     // Sizes
     $stmt_sizes_fetch = $pdo->prepare("SELECT * FROM tbl_size ORDER BY size_name ASC");
     $stmt_sizes_fetch->execute();
     $sizes = $stmt_sizes_fetch->fetchAll(PDO::FETCH_ASSOC);
     foreach ($sizes as $size) {
         $size_options .= '<option value="'.$size['size_id'].'">'.htmlspecialchars($size['size_name']).'</option>';
     }

} catch (PDOException $e) {
    $error_message = "Database error fetching form options: " . $e->getMessage();
    // Allow form to load but options might be empty
}

// --- Fetch Existing Product Data ---
try {
    // Fetch main product details
    $stmt_product = $pdo->prepare("SELECT * FROM tbl_product WHERE p_id = ?");
    $stmt_product->execute([$p_id]);
    $product_data = $stmt_product->fetch(PDO::FETCH_ASSOC);

    if (!$product_data) {
        // Product not found, redirect or show error
        header('Location: product.php?status=not_found');
        exit;
    }

    // Fetch selected featured colors for this product
    $stmt_feat_colors = $pdo->prepare("SELECT color_id FROM tbl_product_color WHERE p_id = ?");
    $stmt_feat_colors->execute([$p_id]);
    // Fetch just the color_id column into a simple array
    $product_featured_colors = $stmt_feat_colors->fetchAll(PDO::FETCH_COLUMN, 0);

    // Fetch existing variations for this product
    $stmt_variations = $pdo->prepare("SELECT * FROM tbl_product_variation WHERE p_id = ? ORDER BY variation_id ASC"); // Assuming variation_id exists and is PK
    $stmt_variations->execute([$p_id]);
    $product_variations = $stmt_variations->fetchAll(PDO::FETCH_ASSOC);


} catch (PDOException $e) {
    $error_message = "Database error fetching product data: " . $e->getMessage();
    // Prevent form rendering if essential product data is missing
    // Display error message within the HTML structure later
    $product_data = null; // Ensure form doesn't try to populate if fetch failed
}


// --- Form Submission Logic (Handles UPDATE) ---
if (isset($_POST['form1'])) {
    $valid = 1; // Assume valid initially
    $p_id_posted = $_POST['p_id']; // Get product ID from hidden field

    // --- Security Check: Ensure posted ID matches the ID from URL ---
    if ($p_id_posted != $p_id) {
        $valid = 0;
        $error_message .= "Product ID mismatch. Operation aborted.<br>";
    }

    // --- Basic Field Validations (Similar to Add, adjust as needed) ---
    if (empty($_POST['tcat_id'])) {
        $valid = 0;
        $error_message .= "You must select a top level category.<br>";
    }
    if (empty($_POST['p_name'])) {
        $valid = 0;
        $error_message .= "Product name cannot be empty.<br>";
    }
     // Validate prices
    if (!empty($_POST['p_old_price']) && !is_numeric($_POST['p_old_price'])) {
        $valid = 0;
        $error_message .= "Old Price must be a number if provided.<br>";
    }
     if (empty($_POST['p_current_price']) || !is_numeric($_POST['p_current_price'])) {
        $valid = 0;
        $error_message .= "Current Price cannot be empty and must be a number.<br>";
    }
     // Validate base quantity
    if (!isset($_POST['p_qty']) || !ctype_digit((string)$_POST['p_qty'])) {
        $valid = 0;
        $error_message .= "Base Quantity cannot be empty and must be a whole number (0 or more).<br>";
    }

    // --- Validate Featured Photo Upload (Optional on Edit) ---
    $final_featured_photo = $product_data['p_featured_photo']; // Default to existing photo
    $new_featured_photo_uploaded = false;
    $delete_featured_photo = isset($_POST['delete_featured_photo']) && $_POST['delete_featured_photo'] == '1';
    $allowed_photo_ext = array('jpg', 'jpeg', 'png', 'gif');
    $allowed_video_ext = array('mp4', 'webm', 'ogg', 'mov');
    $allowed_ext = array_merge($allowed_photo_ext, $allowed_video_ext);
    $upload_path = "../assets/uploads/";

    if (isset($_FILES['p_featured_photo']) && $_FILES['p_featured_photo']['error'] == 0 && !empty($_FILES['p_featured_photo']['name'])) {
        $new_featured_photo_uploaded = true;
        $path = $_FILES['p_featured_photo']['name'];
        $path_tmp = $_FILES['p_featured_photo']['tmp_name'];
        $ext = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        // Validate new photo
        if (!in_array($ext, $allowed_ext)) {
            $valid = 0;
            $error_message .= "New featured photo must be in jpg, jpeg, png, or gif format.<br>";
        } else {
             // Generate final name for the *new* photo (using existing p_id with timestamp for uniqueness)
             $final_featured_photo = 'product-featured-' . $p_id . '-' . time() . '.' . $ext;
        }
    } elseif (isset($_FILES['p_featured_photo']) && $_FILES['p_featured_photo']['error'] != 0 && $_FILES['p_featured_photo']['error'] != UPLOAD_ERR_NO_FILE) {
        // An error occurred during upload attempt (other than no file)
        $valid = 0;
        $error_message .= "Error uploading new featured photo. Code: " . $_FILES['p_featured_photo']['error'] . "<br>";
    }

    // --- Validate Featured Colors ---
    if (empty($_POST['p_featured_colors']) || !is_array($_POST['p_featured_colors'])) {
        $valid = 0;
        $error_message .= "You must select at least one available color.<br>";
    } else {
        // Optional: Validate submitted color IDs
        $available_color_ids = array_column($color_result, 'color_id');
        foreach ($_POST['p_featured_colors'] as $submitted_color_id) {
           if (!in_array($submitted_color_id, $available_color_ids)) {
               $valid = 0;
               $error_message .= "Invalid available color selected.<br>";
               break;
           }
        }
    }

    // --- Validation for Variations ---
    $variation_ids_submitted = isset($_POST['variation_id']) ? $_POST['variation_id'] : []; // IDs of existing variations submitted
    $variations_to_delete = isset($_POST['delete_variation']) ? $_POST['delete_variation'] : []; // IDs marked for deletion

    if (isset($_POST['variation_price'])) {
        foreach ($_POST['variation_price'] as $index => $price) {
            $current_variation_id = isset($variation_ids_submitted[$index]) ? $variation_ids_submitted[$index] : null;

            // Skip validation if this variation is marked for deletion
            if ($current_variation_id && in_array($current_variation_id, $variations_to_delete)) {
                continue;
            }

            // Get other values for this row
            $var_qty = isset($_POST['variation_qty'][$index]) ? $_POST['variation_qty'][$index] : null;
            $var_img_name = isset($_FILES['variation_image']['name'][$index]) ? $_FILES['variation_image']['name'][$index] : null;
            $var_img_error = isset($_FILES['variation_image']['error'][$index]) ? $_FILES['variation_image']['error'][$index] : null;
            $remove_image_flag = isset($_POST['remove_variation_image'][$index]) ? $_POST['remove_variation_image'][$index] : 0;

            // --- Validate ONLY if Price AND Quantity are provided (for both new and existing variations) ---
            if (($price !== '' && $price !== null) && ($var_qty !== '' && $var_qty !== null)) {
                // Price & Quantity validation
                if (!is_numeric($price) || $price < 0) {
                    $valid = 0;
                    $error_message .= "Variation #" . ($index+1) . ": Price must be a valid non-negative number.<br>";
                }
                if (!ctype_digit((string)$var_qty)) {
                    $valid = 0;
                    $error_message .= "Variation #" . ($index+1) . ": Quantity must be a whole number (0 or more).<br>";
                }

                // --- Variation Image Validation (only if a *new* image was submitted) ---
                if (!empty($var_img_name) && $var_img_error == 0) {
                    $var_ext = strtolower(pathinfo($var_img_name, PATHINFO_EXTENSION));
                    if (!in_array($var_ext, $allowed_ext)) {
                        $valid = 0;
                        $error_message .= "Variation #" . ($index+1) . ": New image must be jpg, jpeg, png, or gif.<br>";
                    }
                } elseif ($var_img_error != 0 && $var_img_error != UPLOAD_ERR_NO_FILE) {
                    $valid = 0;
                    $error_message .= "Variation #" . ($index+1) . ": Error uploading new image. Code: " . $var_img_error . "<br>";
                }
            }
            // Allow submitting rows with only optional fields (like color/size) if they represent an *existing* variation being modified
            // But if it's a *new* row (no variation_id), price/qty are implicitly required by the check above.
        }
    }


    // --- If All Validations Pass, Proceed with Database UPDATE Operations ---
    if ($valid == 1) {
        $pdo->beginTransaction(); // Start transaction
        $old_featured_photo_to_delete = null;
        $moved_featured_photo_path = null;
        $moved_variation_files = []; // Track new variation files moved
        $variation_files_to_delete = []; // Track old variation files to delete

        try {
            // --- Handle Featured Photo Update ---
            if ($delete_featured_photo) {
                // User wants to delete the featured photo
                $old_featured_photo_to_delete = $product_data['p_featured_photo'];
                $final_featured_photo = ''; // Set to empty string in database (column doesn't allow NULL)
            } elseif ($new_featured_photo_uploaded) {
                // Store old photo name for deletion *after* successful move
                $old_featured_photo_to_delete = $product_data['p_featured_photo'];
                $moved_featured_photo_path = $upload_path . $final_featured_photo; // Full path for the new photo

                if (!move_uploaded_file($_FILES['p_featured_photo']['tmp_name'], $moved_featured_photo_path)) {
                    throw new Exception("Failed to move new featured photo to destination.");
                }
            }
            // $final_featured_photo variable now holds either the new photo name, null (if deleted), or the original one


            // --- Process installation fee - use default if empty or not numeric ---
            $installation_fee = 15000; // Default value
            if (!empty($_POST['installation_fee']) && is_numeric($_POST['installation_fee'])) {
                $installation_fee = floatval($_POST['installation_fee']);
            }

            // --- Update Main Product Details ---
            $stmt_update_product = $pdo->prepare("UPDATE tbl_product SET
                p_name = ?,
                p_old_price = ?,
                p_current_price = ?,
                p_qty = ?,
                p_featured_photo = ?,
                p_featured_color = ?,
                p_description = ?,
                p_short_description = ?,
                p_feature = ?,
                p_condition = ?,
                p_return_policy = ?,
                p_is_featured = ?,
                p_is_active = ?,
                tcat_id = ?,
                mcat_id = ?,
                installation_fee = ?
                WHERE p_id = ?");

            // Get the first selected color ID for p_featured_color (default to 0 if none)
            $p_featured_color = 0; // Default value
            if (!empty($_POST['p_featured_colors']) && is_array($_POST['p_featured_colors']) && count($_POST['p_featured_colors']) > 0) {
                $p_featured_color = (int)$_POST['p_featured_colors'][0]; // Use the first selected color
            }

            $stmt_update_product->execute(array(
                $_POST['p_name'],
                $_POST['p_old_price'],
                $_POST['p_current_price'],
                $_POST['p_qty'],
                $final_featured_photo, // Use the determined photo name (new or old)
                $p_featured_color, // Add p_featured_color value
                $_POST['p_description'],
                $_POST['p_short_description'],
                $_POST['p_feature'],
                $_POST['p_condition'],
                $_POST['p_return_policy'],
                $_POST['p_is_featured'],
                $_POST['p_is_active'],
                $_POST['tcat_id'],
                empty($_POST['mcat_id']) ? null : $_POST['mcat_id'],
                $installation_fee, // Add installation fee
                $p_id // WHERE clause
            ));

            // --- Update Featured Colors (Delete old, insert new) ---
            // Delete existing colors for this product
            $stmt_delete_colors = $pdo->prepare("DELETE FROM tbl_product_color WHERE p_id = ?");
            $stmt_delete_colors->execute([$p_id]);

            // Insert the newly selected colors
            if (!empty($_POST['p_featured_colors']) && is_array($_POST['p_featured_colors'])) {
                $stmt_insert_color = $pdo->prepare("INSERT INTO tbl_product_color (p_id, color_id) VALUES (?, ?)");
                foreach ($_POST['p_featured_colors'] as $featured_color_id) {
                    if (filter_var($featured_color_id, FILTER_VALIDATE_INT)) {
                        $stmt_insert_color->execute([$p_id, $featured_color_id]);
                    }
                }
            }

            // --- Process Variations (Delete, Update, Insert) ---
            $variation_upload_path = "../assets/uploads/product_variations/";

            // 1. Handle Deletions
            if (!empty($variations_to_delete)) {
                // Fetch image names before deleting rows
                $placeholders = implode(',', array_fill(0, count($variations_to_delete), '?'));
                $stmt_get_deleted_images = $pdo->prepare("SELECT variation_image FROM tbl_product_variation WHERE variation_id IN ($placeholders) AND p_id = ?");
                $params = array_merge($variations_to_delete, [$p_id]);
                $stmt_get_deleted_images->execute($params);
                $images_to_delete = $stmt_get_deleted_images->fetchAll(PDO::FETCH_COLUMN, 0);
                foreach ($images_to_delete as $img) {
                    if (!empty($img)) {
                        $variation_files_to_delete[] = $variation_upload_path . $img;
                    }
                }

                // Delete the variation rows
                $stmt_delete_variation = $pdo->prepare("DELETE FROM tbl_product_variation WHERE variation_id IN ($placeholders) AND p_id = ?");
                $stmt_delete_variation->execute($params);
            }

            // 2. Handle Updates and Inserts
            $stmt_update_variation = $pdo->prepare("UPDATE tbl_product_variation SET
                variation_color = ?, variation_size = ?, variation_price = ?, variation_qty = ?,
                variation_name = ?, variation_description = ?, variation_image = ?
                WHERE variation_id = ? AND p_id = ?");

            $stmt_insert_variation = $pdo->prepare("INSERT INTO tbl_product_variation(
                p_id, variation_color, variation_size, variation_price, variation_qty,
                variation_name, variation_description, variation_image
            ) VALUES (?,?,?,?,?,?,?,?)");

            if (isset($_POST['variation_price'])) {
                for ($i = 0; $i < count($_POST['variation_price']); $i++) {
                    $current_variation_id = isset($_POST['variation_id'][$i]) ? $_POST['variation_id'][$i] : null;

                    // Skip if marked for deletion
                    if ($current_variation_id && in_array($current_variation_id, $variations_to_delete)) {
                        continue;
                    }

                    // Get submitted data for this row
                    $var_price = isset($_POST['variation_price'][$i]) ? $_POST['variation_price'][$i] : null;
                    $var_qty = isset($_POST['variation_qty'][$i]) ? $_POST['variation_qty'][$i] : null;

                    // Process only if price and quantity are valid
                    if ($var_price !== null && $var_price !== '' && is_numeric($var_price) && $var_price >= 0 &&
                        $var_qty !== null && $var_qty !== '' && ctype_digit((string)$var_qty))
                    {
                        $var_color_id = isset($_POST['variation_color'][$i]) && $_POST['variation_color'][$i] !== '' ? $_POST['variation_color'][$i] : null;
                        $var_size_id = isset($_POST['variation_size'][$i]) && $_POST['variation_size'][$i] !== '' ? $_POST['variation_size'][$i] : null;
                        $var_name = isset($_POST['variation_name'][$i]) && trim($_POST['variation_name'][$i]) !== '' ? trim($_POST['variation_name'][$i]) : null;
                        $var_desc = isset($_POST['variation_description'][$i]) && trim($_POST['variation_description'][$i]) !== '' ? trim($_POST['variation_description'][$i]) : null;
                        $remove_image_flag = isset($_POST['remove_variation_image'][$index]) ? $_POST['remove_variation_image'][$index] : 0;

                        $final_variation_image_name = null; // Holds the name to be saved in DB
                        $new_variation_image_uploaded_for_row = false;
                        $old_variation_image_name = null;

                        // Determine the image name for DB (handle new upload, removal, or keeping existing)
                        if ($current_variation_id) {
                            // Fetch existing image name for potential deletion or keeping
                            $stmt_get_img = $pdo->prepare("SELECT variation_image FROM tbl_product_variation WHERE variation_id = ?");
                            $stmt_get_img->execute([$current_variation_id]);
                            $old_variation_image_name = $stmt_get_img->fetchColumn();
                            $final_variation_image_name = $old_variation_image_name; // Default to keeping old image
                        }

                        // Check if user wants to remove the existing image
                        if ($current_variation_id && $remove_image_flag == 1 && !empty($old_variation_image_name)) {
                            $variation_files_to_delete[] = $variation_upload_path . $old_variation_image_name;
                            $final_variation_image_name = null; // Set DB value to null
                        }

                        // Check for new image upload for this row
                        if (isset($_FILES['variation_image']['name'][$i]) && $_FILES['variation_image']['error'][$i] == 0 && !empty($_FILES['variation_image']['name'][$i])) {
                            $new_variation_image_uploaded_for_row = true;
                            $var_tmp_path = $_FILES['variation_image']['tmp_name'][$i];
                            $var_ext = strtolower(pathinfo($_FILES['variation_image']['name'][$i], PATHINFO_EXTENSION));

                            // Generate unique name
                            $final_variation_image_name = 'product-variation-' . $p_id . '-' . ($current_variation_id ?? 'new' . $i) . '-' . time() . uniqid() . '.' . $var_ext;
                            $moved_variation_image_path = $variation_upload_path . $final_variation_image_name;

                            if (!move_uploaded_file($var_tmp_path, $moved_variation_image_path)) {
                                // Handle failed move - revert image name, log error
                                $final_variation_image_name = $old_variation_image_name; // Revert to old name if update, or null if new
                                $new_variation_image_uploaded_for_row = false;
                                $error_message .= "Warning: Failed to move new image for variation #" . ($i+1) . ". Image not updated.<br>";
                                error_log("Failed move variation image: " . $var_tmp_path . " to " . $moved_variation_image_path);
                            } else {
                                // Successfully moved new file
                                $moved_variation_files[] = $moved_variation_image_path; // Track for potential rollback
                                // If this replaced an old image, add old one to delete list
                                if ($current_variation_id && !empty($old_variation_image_name) && $old_variation_image_name != $final_variation_image_name) {
                                     $variation_files_to_delete[] = $variation_upload_path . $old_variation_image_name;
                                }
                            }
                        }

                        // --- Perform Insert or Update ---
                        if ($current_variation_id) {
                            // Update Existing Variation
                            $stmt_update_variation->execute([
                                $var_color_id, $var_size_id, $var_price, $var_qty,
                                $var_name, $var_desc, $final_variation_image_name, // Use the final determined image name
                                $current_variation_id, $p_id
                            ]);
                        } else {
                            // Insert New Variation
                            $stmt_insert_variation->execute([
                                $p_id, $var_color_id, $var_size_id, $var_price, $var_qty,
                                $var_name, $var_desc, $final_variation_image_name // Use the final determined image name (null if no upload/failed)
                            ]);
                        }
                    } // End if price/qty valid
                } // End for loop
            } // End if isset variation_price

            // --- Process Additional Photos ---
            // First, handle deletions
            if (isset($_POST['delete_photo']) && is_array($_POST['delete_photo'])) {
                try {
                    // Get photo names before deleting from database
                    $placeholders = implode(',', array_fill(0, count($_POST['delete_photo']), '?'));
                    $stmt_get_photos = $pdo->prepare("SELECT photo_name FROM tbl_product_photo WHERE photo_id IN ($placeholders) AND p_id = ?");
                    $params = array_merge($_POST['delete_photo'], [$p_id]);
                    $stmt_get_photos->execute($params);
                    $photos_to_delete = $stmt_get_photos->fetchAll(PDO::FETCH_COLUMN, 0);

                    // Delete from database
                    $stmt_delete_photos = $pdo->prepare("DELETE FROM tbl_product_photo WHERE photo_id IN ($placeholders) AND p_id = ?");
                    $stmt_delete_photos->execute($params);

                    // Delete files from the uploads directory
                    foreach ($photos_to_delete as $photo_name) {
                        if (!empty($photo_name)) {
                            $file_path = $upload_path . $photo_name;
                            if (file_exists($file_path)) {
                                if (!@unlink($file_path)) {
                                    error_log("Failed to delete file: " . $file_path);
                                    $error_message .= "Warning: Could not delete photo file: " . htmlspecialchars($photo_name) . "<br>";
                                }
                            } else {
                                error_log("File not found for deletion: " . $file_path);
                            }
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error processing photo deletions: " . $e->getMessage());
                    $error_message .= "Error processing photo deletions. Please try again.<br>";
                }
            }

            // Then, handle new photo uploads
            if (isset($_FILES['additional_photos'])) {
                $additional_photos = $_FILES['additional_photos'];
                $photo_order = 0;

                // Get current max photo_order
                $stmt_max_order = $pdo->prepare("SELECT MAX(photo_order) as max_order FROM tbl_product_photo WHERE p_id = ?");
                $stmt_max_order->execute([$p_id]);
                $result = $stmt_max_order->fetch(PDO::FETCH_ASSOC);
                $photo_order = $result['max_order'] ? $result['max_order'] + 1 : 0;

                // Prepare statement for photo/video insertion
                $stmt_photo = $pdo->prepare("INSERT INTO tbl_product_photo (p_id, photo_name, photo_order, media_type) VALUES (?, ?, ?, ?)");

                // Process each additional photo/video
                for ($i = 0; $i < count($additional_photos['name']); $i++) {
                    if ($additional_photos['error'][$i] == 0 && !empty($additional_photos['name'][$i])) {
                        $photo_path = $additional_photos['name'][$i];
                        $photo_tmp = $additional_photos['tmp_name'][$i];
                        $photo_ext = strtolower(pathinfo($photo_path, PATHINFO_EXTENSION));

                        // Get media type from form
                        $media_type = isset($_POST['additional_media_type'][$i]) ? $_POST['additional_media_type'][$i] : 'photo';

                        // Determine allowed extensions based on media type
                        $valid_extensions = ($media_type === 'video') ? $allowed_video_ext : $allowed_photo_ext;

                        // Validate file type
                        if (in_array($photo_ext, $valid_extensions)) {
                            // Generate unique filename with media type prefix
                            $prefix = ($media_type === 'video') ? 'product-video-' : 'product-photo-';
                            $final_photo_name = $prefix . $p_id . '-' . time() . uniqid() . '.' . $photo_ext;
                            $final_photo_path = $upload_path . $final_photo_name;

                            // Move uploaded file
                            if (move_uploaded_file($photo_tmp, $final_photo_path)) {
                                // Insert into database with media type
                                $stmt_photo->execute([$p_id, $final_photo_name, $photo_order, $media_type]);
                                $photo_order++;

                                // Add to cleanup list for potential rollback
                                $moved_variation_files[] = $final_photo_path;
                            } else {
                                $error_message .= "Warning: Failed to move additional " . $media_type . " #" . ($i+1) . ".<br>";
                            }
                        } else {
                            $error_message .= "Warning: Invalid file type for additional " . $media_type . " #" . ($i+1) . ".<br>";
                        }
                    }
                }
            }

            // --- If we reach here, commit the transaction ---
            $pdo->commit();
            $success_message = "Product updated successfully!";

            // Add specific success details
            if ($delete_featured_photo) {
                $success_message .= " Featured photo has been deleted.";
            } elseif ($new_featured_photo_uploaded) {
                $success_message .= " Featured photo has been updated to: " . $final_featured_photo;
            }

            // --- Delete old files AFTER successful commit ---
            // Delete old featured photo if replaced
            if ($old_featured_photo_to_delete && !empty($old_featured_photo_to_delete)) {
                $old_photo_paths = [
                    $upload_path . $old_featured_photo_to_delete,
                    "../assets/uploads/" . $old_featured_photo_to_delete,
                    "assets/uploads/" . $old_featured_photo_to_delete
                ];

                foreach ($old_photo_paths as $old_path) {
                    if (file_exists($old_path)) {
                        if (@unlink($old_path)) {
                            error_log("Successfully deleted old featured photo: " . $old_path);
                            break; // Only delete one instance
                        } else {
                            error_log("Failed to delete old featured photo: " . $old_path);
                        }
                    }
                }
            }
            // Delete old/removed variation photos
            foreach ($variation_files_to_delete as $file_path) {
                if (file_exists($file_path)) {
                    @unlink($file_path);
                }
            }

            // --- Refresh data after update to show changes immediately ---
            // Re-fetch product data
            $stmt_product->execute([$p_id]);
            $product_data = $stmt_product->fetch(PDO::FETCH_ASSOC);
            // Re-fetch featured colors
            $stmt_feat_colors->execute([$p_id]);
            $product_featured_colors = $stmt_feat_colors->fetchAll(PDO::FETCH_COLUMN, 0);
            // Re-fetch variations
            $stmt_variations->execute([$p_id]);
            $product_variations = $stmt_variations->fetchAll(PDO::FETCH_ASSOC);

             // Optional: Clear POST to prevent accidental resubmit if staying on page
             // $_POST = [];


        } catch (Exception $e) {
            // --- Error occurred, rollback ---
            if ($pdo->inTransaction()) {
                 $pdo->rollBack();
            }
            $error_message .= "An error occurred during update: " . $e->getMessage() . "<br>";

             // --- Clean up any NEW files moved before the error ---
             if (!empty($moved_featured_photo_path) && file_exists($moved_featured_photo_path)) {
                  @unlink($moved_featured_photo_path);
             }
             foreach ($moved_variation_files as $file_path) {
                 if (file_exists($file_path)) {
                     @unlink($file_path);
                 }
             }
        } // End Try-Catch
    } // End if $valid == 1
} // End if form submitted

// --- If product data fetch failed earlier, display error and stop ---
if (!$product_data && empty($error_message)) { // Check if fetch failed without a prior error message set
    $error_message = "Product with ID " . htmlspecialchars($p_id) . " not found.";
}
if (!$product_data && !empty($error_message)) {
    echo '<section class="content"><div class="callout callout-danger"><h4>Error</h4><p>' . $error_message . '</p><p><a href="product.php">Back to Product List</a></p></div></section>';
    require_once('footer.php'); // Include footer to close HTML
    exit; // Stop script execution
}

?>

<section class="content-header">
    <div class="content-header-left">
        <h1><i class="fa fa-edit"></i> Edit Product</h1>
    </div>
    <div class="content-header-right">
        <a href="product-add.php" class="btn btn-success btn-sm" style="margin-right: 5px;"><i class="fa fa-plus"></i> Add New Product</a>
        <a href="product.php" class="btn btn-info btn-sm"><i class="fa fa-list"></i> View All Products</a>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="icon fa fa-ban"></i> Errors Found:</h4>
                <p><?php echo $error_message; /* Contains HTML breaks */ ?></p>
            </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="icon fa fa-check"></i> Success!</h4>
                <p><?php echo htmlspecialchars($success_message); ?></p>
            </div>
            <?php endif; ?>

            <form class="form-horizontal" action="product-edit.php?id=<?php echo $p_id; ?>" method="post" enctype="multipart/form-data" id="product-form">
                <input type="hidden" name="p_id" value="<?php echo htmlspecialchars($product_data['p_id']); ?>">
                
                <div class="nav-tabs-custom">
                    <ul class="nav nav-tabs">
                        <li class="active"><a href="#tab_1" data-toggle="tab"><i class="fa fa-info-circle"></i> Basic Info</a></li>
                        <li><a href="#tab_2" data-toggle="tab"><i class="fa fa-image"></i> Images & Description</a></li>
                        <li><a href="#tab_3" data-toggle="tab"><i class="fa fa-list"></i> Variations</a></li>
                        
                    </ul>
                    
                    <div class="tab-content">
                        <div class="tab-pane active" id="tab_1">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="panel panel-info">
                                            <div class="panel-heading">
                                                <h3 class="panel-title"><i class="fa fa-info-circle"></i> Basic Product Information (ID: <?php echo htmlspecialchars($product_data['p_id']); ?>)</h3>
                                            </div>
                                            <div class="panel-body">
                                                <!-- Categories Section -->
                                                <div class="form-group <?php echo (!empty($error_message) && empty($_POST['tcat_id']) && isset($_POST['form1'])) ? 'has-error' : ''; ?>">
                                                    <label for="tcat_id" class="col-sm-3 control-label">
                                                        <i class="fa fa-folder"></i> Main Category <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <select name="tcat_id" id="tcat_id" class="form-control select2" required>
                                                            <option value="">Select Main Category</option>
                                                            <?php
                                                            $selected_tcat = isset($_POST['tcat_id']) ? $_POST['tcat_id'] : $product_data['tcat_id'];
                                                            foreach ($top_categories as $cat): ?>
                                                                <option value="<?php echo $cat['tcat_id']; ?>" <?php echo ($selected_tcat == $cat['tcat_id']) ? 'selected' : ''; ?>>
                                                                    <?php echo htmlspecialchars($cat['tcat_name']); ?>
                                                                </option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <small class="help-block">Select the main category for this product</small>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="mcat_id" class="col-sm-3 control-label">
                                                        <i class="fa fa-folder-open"></i> Sub Category
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <select name="mcat_id" id="mcat_id" class="form-control select2">
                                                            <option value="">-- Select Main Category First --</option>
                                                        </select>
                                                        <small class="help-block">Optional: Choose a subcategory if applicable</small>
                                                    </div>
                                                </div>

                                                <!-- Product Name -->
                                                <div class="form-group <?php echo (!empty($error_message) && empty($_POST['p_name']) && isset($_POST['form1'])) ? 'has-error' : ''; ?>">
                                                    <label for="p_name" class="col-sm-3 control-label">
                                                        <i class="fa fa-tag"></i> Product Name <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <input type="text" name="p_name" id="p_name" class="form-control" 
                                                            value="<?php echo htmlspecialchars(isset($_POST['p_name']) ? $_POST['p_name'] : $product_data['p_name']); ?>" 
                                                            required 
                                                            placeholder="Enter product name">
                                                        <small class="help-block">Enter a clear and descriptive name for the product</small>
                                                    </div>
                                                </div>
                                                <!-- Pricing Section -->
                                                <div class="form-group <?php echo (!empty($error_message) && !empty($_POST['p_old_price']) && !is_numeric($_POST['p_old_price'])) ? 'has-error' : ''; ?>">
                                                    <label for="p_old_price" class="col-sm-3 control-label">
                                                        <i class="fa fa-money"></i> Old Price
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><strong>TSH</strong></span>
                                                            <input type="text" name="p_old_price" id="p_old_price" 
                                                                class="form-control" 
                                                                pattern="^\d+(\.\d{1,2})?$" 
                                                                title="Enter a valid price (e.g., 10.99)" 
                                                                placeholder="e.g., 2000" 
                                                                value="<?php echo htmlspecialchars(isset($_POST['p_old_price']) ? $_POST['p_old_price'] : $product_data['p_old_price']); ?>">
                                                            <span class="input-group-addon"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        <small class="help-block">Optional. Leave empty or set to 0 if no old price.</small>
                                                    </div>
                                                </div>

                                                <div class="form-group <?php echo (!empty($error_message) && (empty($_POST['p_current_price']) || !is_numeric($_POST['p_current_price'])) && isset($_POST['form1'])) ? 'has-error' : ''; ?>">
                                                    <label for="p_current_price" class="col-sm-3 control-label">
                                                        <i class="fa fa-tag"></i> Current Price <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><strong>TSH</strong></span>
                                                            <input type="text" name="p_current_price" id="p_current_price" 
                                                                class="form-control" 
                                                                pattern="^\d+(\.\d{1,2})?$" 
                                                                title="Enter a valid price (e.g., 9.99)" 
                                                                placeholder="e.g., 2000" 
                                                                value="<?php echo htmlspecialchars(isset($_POST['p_current_price']) ? $_POST['p_current_price'] : $product_data['p_current_price']); ?>" 
                                                                required>
                                                            <span class="input-group-addon"><i class="fa fa-asterisk text-danger"></i></span>
                                                        </div>
                                                        <small class="help-block">Enter the current selling price of the product</small>
                                                    </div>
                                                </div>

                                                <div class="form-group <?php echo (!empty($error_message) && (!isset($_POST['p_qty']) || !ctype_digit((string)$_POST['p_qty'])) && isset($_POST['form1'])) ? 'has-error' : ''; ?>">
                                                    <label for="p_qty" class="col-sm-3 control-label">
                                                        <i class="fa fa-cubes"></i> Base Quantity <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <div class="input-group">
                                                            <input type="number" name="p_qty" id="p_qty" 
                                                                class="form-control" 
                                                                min="0" 
                                                                step="1" 
                                                                value="<?php echo htmlspecialchars(isset($_POST['p_qty']) ? $_POST['p_qty'] : $product_data['p_qty']); ?>" 
                                                                required>
                                                            <span class="input-group-addon"><i class="fa fa-cube"></i></span>
                                                        </div>
                                                        <small class="help-block">Overall stock count. Variation quantities are managed separately.</small>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label for="installation_fee" class="col-sm-3 control-label">
                                                        <i class="fa fa-wrench"></i> Installation Fee
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><strong>TSH</strong></span>
                                                            <input type="text" name="installation_fee" id="installation_fee" 
                                                                class="form-control" 
                                                                pattern="^\d+(\.\d{1,2})?$" 
                                                                title="Enter a valid installation fee" 
                                                                placeholder="Default: 15000" 
                                                                value="<?php echo isset($_POST['installation_fee']) ? htmlspecialchars($_POST['installation_fee']) : (isset($product_data['installation_fee']) ? htmlspecialchars($product_data['installation_fee']) : '15000'); ?>">
                                                            <span class="input-group-addon"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        <small class="help-block">Installation fee for this product. Default is 15000 if left empty.</small>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Images Tab -->
                        <div class="tab-pane" id="tab_2">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="panel panel-info">
                                            <div class="panel-heading">
                                                <h3 class="panel-title"><i class="fa fa-image"></i> Product Images</h3>
                                            </div>
                                            <div class="panel-body">
                                                <!-- Featured Photo Section -->
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">
                                                        <i class="fa fa-star"></i> Featured Photo
                                                    </label>
                                                    <div class="col-sm-9">
                                        <?php
                                        $featured_photo_path = "";
                                        $featured_photo_exists = false;

                                        if (!empty($product_data['p_featured_photo']) && trim($product_data['p_featured_photo']) !== '') {
                                            $possible_paths = [
                                                "../assets/uploads/" . $product_data['p_featured_photo'],
                                                "assets/uploads/" . $product_data['p_featured_photo'],
                                                "../assets/uploads/product_photos/" . $product_data['p_featured_photo']
                                            ];

                                            foreach ($possible_paths as $path) {
                                                if (file_exists($path)) {
                                                    $featured_photo_path = $path;
                                                    $featured_photo_exists = true;
                                                    break;
                                                }
                                            }
                                        }

                                        if ($featured_photo_exists): ?>
                                            <div class="featured-photo-container well">
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <div class="image-preview-container" style="text-align: center;">
                                                            <img src="<?php echo htmlspecialchars($featured_photo_path); ?>" 
                                                                alt="Current Featured Photo" 
                                                                class="img-thumbnail" 
                                                                style="max-width: 100%; height: auto; margin-bottom: 10px;">
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <div class="image-info">
                                                            <h4><i class="fa fa-image"></i> Current Featured Image</h4>
                                                            <p class="text-muted">
                                                                <i class="fa fa-file-image-o"></i> 
                                                                <?php echo htmlspecialchars($product_data['p_featured_photo']); ?>
                                                            </p>
                                                            <div class="form-group" style="margin-top: 15px;">
                                                                <label class="text-danger" style="font-weight: normal;">
                                                                    <input type="checkbox" name="delete_featured_photo" value="1" id="delete_featured_photo">
                                                                    <i class="fa fa-trash"></i> Delete this photo
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-warning">
                                                <h4><i class="fa fa-exclamation-triangle"></i> No Featured Photo</h4>
                                                <?php if (!empty($product_data['p_featured_photo']) && trim($product_data['p_featured_photo']) !== ''): ?>
                                                    <p>Featured photo file not found: <code><?php echo htmlspecialchars($product_data['p_featured_photo']); ?></code></p>
                                                <?php else: ?>
                                                    <p>No featured photo has been uploaded for this product.</p>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Upload New Photo Section -->
                                        <div class="upload-section" style="margin-top: 20px;">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="fa fa-upload"></i></span>
                                                <input type="file" name="p_featured_photo" id="p_featured_photo" 
                                                    class="form-control" 
                                                    accept=".jpg,.jpeg,.png,.gif">
                                            </div>
                                            <small class="help-block">
                                                <i class="fa fa-info-circle"></i> 
                                                Accepted formats: JPG, JPEG, PNG, GIF. Max size: 5MB. 
                                                Uploading a new file will replace the current one.
                                            </small>

                                            <div id="featured-photo-preview" style="display: none; margin-top: 15px;">
                                                <div class="panel panel-info">
                                                    <div class="panel-heading">
                                                        <h3 class="panel-title"><i class="fa fa-eye"></i> New Photo Preview</h3>
                                                    </div>
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="col-sm-4">
                                                                <img id="preview-image" src="" alt="Preview" 
                                                                    class="img-thumbnail" 
                                                                    style="max-width: 100%; height: auto;">
                                                            </div>
                                                            <div class="col-sm-8">
                                                                <div id="preview-info"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                                <!-- Additional Photos/Videos Section -->
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">
                                                        <i class="fa fa-images"></i> Additional Media
                                                    </label>
                                                    <div class="col-sm-9">
                                        <?php
                                        // Fetch existing additional photos/videos
                                        $stmt_photos = $pdo->prepare("SELECT * FROM tbl_product_photo WHERE p_id = ? ORDER BY photo_order ASC");
                                        $stmt_photos->execute([$p_id]);
                                        $existing_photos = $stmt_photos->fetchAll(PDO::FETCH_ASSOC);
                                        ?>
                                        <div id="additional-photos-container">
                                            <?php if (!empty($existing_photos)): ?>
                                                <div class="panel panel-default">
                                                    <div class="panel-heading">
                                                        <h4 class="panel-title">
                                                            <i class="fa fa-images"></i> Current Photos/Videos
                                                            <span class="badge"><?php echo count($existing_photos); ?></span>
                                                        </h4>
                                                    </div>
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <?php foreach ($existing_photos as $photo):
                                                                $is_video = isset($photo['media_type']) && $photo['media_type'] === 'video';
                                                                $file_path = "../assets/uploads/" . htmlspecialchars($photo['photo_name']);
                                                            ?>
                                                                <div class="col-md-4 col-sm-6" style="margin-bottom: 20px;">
                                                                    <div class="thumbnail">
                                                                        <div class="media-wrapper" style="position: relative;">
                                                                            <?php if ($is_video && file_exists($file_path)): ?>
                                                                                <video class="img-responsive" controls>
                                                                                    <source src="<?php echo $file_path; ?>" type="video/mp4">
                                                                                    Your browser does not support the video tag.
                                                                                </video>
                                                                                <span class="label label-info" style="position: absolute; top: 10px; right: 10px;">
                                                                                    <i class="fa fa-video-camera"></i> Video
                                                                                </span>
                                                                            <?php else: ?>
                                                                                <img src="<?php echo $file_path; ?>"
                                                                                     alt="Product Photo"
                                                                                     class="img-responsive">
                                                                                <span class="label label-primary" style="position: absolute; top: 10px; right: 10px;">
                                                                                    <i class="fa fa-image"></i> Photo
                                                                                </span>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                        <div class="caption">
                                                                            <div class="btn-group btn-group-justified">
                                                                                <div class="btn-group">
                                                                                    <button type="button" class="btn btn-default btn-sm preview-media">
                                                                                        <i class="fa fa-eye"></i> Preview
                                                                                    </button>
                                                                                </div>
                                                                                <div class="btn-group">
                                                                                    <label class="btn btn-danger btn-sm">
                                                                                        <input type="checkbox" name="delete_photo[]" 
                                                                                            value="<?php echo $photo['photo_id']; ?>" 
                                                                                            style="display: none;">
                                                                                        <i class="fa fa-trash"></i> Delete
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <!-- Add New Media Section -->
                                            <div class="panel panel-info">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title"><i class="fa fa-plus-circle"></i> Add New Media</h4>
                                                </div>
                                                <div class="panel-body">
                                                    <div class="new-photos">
                                                        <div class="additional-photo-item well well-sm">
                                                            <div class="row">
                                                                <div class="col-sm-3">
                                                                    <select name="additional_media_type[]" class="form-control media-type-selector">
                                                                        <option value="photo">Photo</option>
                                                                        <option value="video">Video</option>
                                                                    </select>
                                                                </div>
                                                                <div class="col-sm-7">
                                                                    <div class="input-group">
                                                                        <span class="input-group-addon"><i class="fa fa-file-image"></i></span>
                                                                        <input type="file" name="additional_photos[]" 
                                                                            class="form-control media-file" 
                                                                            accept=".jpg,.jpeg,.png,.gif">
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-2">
                                                                    <button type="button" class="btn btn-danger btn-block remove-photo">
                                                                        <i class="fa fa-times"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div class="help-block file-type-hint">
                                                                <small><i class="fa fa-info-circle"></i> Accepted formats: JPG, JPEG, PNG, GIF</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <button type="button" id="add-photo" class="btn btn-success btn-sm">
                                                        <i class="fa fa-plus"></i> Add Another Media
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                            </div>
                        </div>

                                                <!-- Available Colors Section -->
                                                <div class="form-group <?php echo (!empty($error_message) && (empty($_POST['p_featured_colors']) || !is_array($_POST['p_featured_colors']))) ? 'has-error' : ''; ?>">
                                                    <label class="col-sm-3 control-label">
                                                        <i class="fa fa-palette"></i> Available Colors <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <div class="panel panel-default">
                                                            <div class="panel-body" style="max-height: 200px; overflow-y: auto;">
                                                                <?php
                                                                $checked_colors = isset($_POST['p_featured_colors']) ? $_POST['p_featured_colors'] : $product_featured_colors;
                                                                if (!is_array($checked_colors)) $checked_colors = [];

                                                                if (!empty($color_result)): ?>
                                                                    <div class="row">
                                                                        <?php foreach ($color_result as $color): ?>
                                                                            <div class="col-md-4 col-sm-6">
                                                                                <div class="checkbox" style="margin-top: 5px; margin-bottom: 5px;">
                                                                                    <label class="color-option">
                                                                                        <input type="checkbox"
                                                                                               name="p_featured_colors[]"
                                                                                               value="<?php echo $color['color_id']; ?>"
                                                                                               <?php echo in_array($color['color_id'], $checked_colors) ? ' checked' : ''; ?>>
                                                                                        <span class="color-name">
                                                                                            <i class="fa fa-square"></i>
                                                                                            <?php echo htmlspecialchars($color['color_name']); ?>
                                                                                        </span>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        <?php endforeach; ?>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <div class="alert alert-warning">
                                                                        <i class="fa fa-exclamation-triangle"></i>
                                                                        No colors found in the database. Please add colors first.
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <small class="help-block">
                                                            <i class="fa fa-info-circle"></i>
                                                            Select all colors this product is generally available in.
                                                        </small>
                                                    </div>
                                                </div>

                        <!-- Description Tab -->
                        <div class="tab-pane" id="tab_4">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="panel panel-info">
                                            <div class="panel-heading">
                                                <h3 class="panel-title"><i class="fa fa-file-text"></i> Product Information</h3>
                                            </div>
                                            <div class="panel-body">
                                                <!-- Main Description -->
                                                <div class="form-group">
                                                    <label for="editor1" class="col-sm-3 control-label">
                                                        <i class="fa fa-paragraph"></i> Full Description
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <textarea name="p_description" class="form-control ckeditor-instance" id="editor1" rows="8"><?php echo htmlspecialchars(isset($_POST['p_description']) ? $_POST['p_description'] : $product_data['p_description']); ?></textarea>
                                                        <small class="help-block">Provide a detailed description of the product</small>
                                                    </div>
                                                </div>

                                                <!-- Key Features -->
                                                <div class="form-group">
                                                    <label for="editor2" class="col-sm-3 control-label">
                                                        <i class="fa fa-list-ul"></i> Key Features
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <textarea name="p_short_description" class="form-control ckeditor-instance" id="editor2" rows="4"><?php echo htmlspecialchars(isset($_POST['p_short_description']) ? $_POST['p_short_description'] : $product_data['p_short_description']); ?></textarea>
                                                        <small class="help-block">List the main features and highlights</small>
                                                    </div>
                                                </div>

                                                <!-- Extra Info -->
                                                <div class="form-group">
                                                    <label for="editor3" class="col-sm-3 control-label">
                                                        <i class="fa fa-info-circle"></i> Extra Info
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <textarea name="p_feature" class="form-control ckeditor-instance" id="editor3" rows="5"><?php echo htmlspecialchars(isset($_POST['p_feature']) ? $_POST['p_feature'] : $product_data['p_feature']); ?></textarea>
                                                        <small class="help-block">Additional product information or specifications</small>
                                                    </div>
                                                </div>

                                                <!-- Technical Features -->
                                                <div class="form-group">
                                                    <label for="editor4" class="col-sm-3 control-label">
                                                        <i class="fa fa-cogs"></i> Technical Features
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <textarea name="p_condition" class="form-control ckeditor-instance" id="editor4" rows="5"><?php echo htmlspecialchars(isset($_POST['p_condition']) ? $_POST['p_condition'] : $product_data['p_condition']); ?></textarea>
                                                        <small class="help-block">Technical specifications and requirements</small>
                                                    </div>
                                                </div>

                                                <!-- Return Policy -->
                                                <div class="form-group">
                                                    <label for="editor5" class="col-sm-3 control-label">
                                                        <i class="fa fa-undo"></i> Return Policy
                                                    </label>
                                                    <div class="col-sm-9">
                                                        <textarea name="p_return_policy" class="form-control ckeditor-instance" id="editor5" rows="5"><?php echo htmlspecialchars(isset($_POST['p_return_policy']) ? $_POST['p_return_policy'] : $product_data['p_return_policy']); ?></textarea>
                                                        <small class="help-block">Product return and warranty information</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                                <!-- Product Settings Section -->
                                                <div class="panel panel-default">
                                                    <div class="panel-heading">
                                                        <h3 class="panel-title"><i class="fa fa-cog"></i> Product Settings</h3>
                                                    </div>
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="p_is_featured" class="col-sm-4 control-label">
                                                                        <i class="fa fa-star"></i> Featured Product
                                                                    </label>
                                                                    <div class="col-sm-8">
                                                                        <select name="p_is_featured" class="form-control select2" id="p_is_featured">
                                                                            <?php $selected_featured = isset($_POST['p_is_featured']) ? $_POST['p_is_featured'] : $product_data['p_is_featured']; ?>
                                                                            <option value="0" <?php echo ($selected_featured == 0) ? 'selected' : ''; ?>>No</option>
                                                                            <option value="1" <?php echo ($selected_featured == 1) ? 'selected' : ''; ?>>Yes</option>
                                                                        </select>
                                                                        <small class="help-block">Display this product in featured sections</small>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="p_is_active" class="col-sm-4 control-label">
                                                                        <i class="fa fa-toggle-on"></i> Product Status
                                                                    </label>
                                                                    <div class="col-sm-8">
                                                                        <select name="p_is_active" class="form-control select2" id="p_is_active">
                                                                            <?php $selected_active = isset($_POST['p_is_active']) ? $_POST['p_is_active'] : $product_data['p_is_active']; ?>
                                                                            <option value="1" <?php echo ($selected_active == 1) ? 'selected' : ''; ?>>Active</option>
                                                                            <option value="0" <?php echo ($selected_active == 0) ? 'selected' : ''; ?>>Inactive</option>
                                                                        </select>
                                                                        <small class="help-block">Control product visibility on the site</small>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Form Actions -->
                                                <div class="form-group" style="margin-top: 20px;">
                                                    <div class="col-sm-offset-3 col-sm-9">
                                                        <button type="submit" class="btn btn-success btn-lg" name="form1">
                                                            <i class="fa fa-save"></i> Update Product
                                                        </button>
                                                        <a href="product.php" class="btn btn-default btn-lg">
                                                            <i class="fa fa-times"></i> Cancel
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Variations Tab -->
                        <div class="tab-pane" id="tab_3">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="panel panel-info">
                                            <div class="panel-heading">
                                                <h3 class="panel-title"><i class="fa fa-cubes"></i> Product Variations</h3>
                                            </div>
                                            <div class="panel-body">
                                                <p class="help-block">
                                                    <i class="fa fa-info-circle"></i> 
                                                    Update existing variations or add new ones. Price and Quantity are required for each variation.
                                                </p>
                                                <div id="variation-container">
                                                    <?php
                                                    // --- Populate Existing Variations (or repopulate from POST on error) ---
                                                    $variations_to_display = [];
                                                    if (isset($_POST['form1']) && !empty($error_message)) {
                                                        // Repopulate from POST data if validation failed
                                                        if (isset($_POST['variation_price'])) {
                                        foreach ($_POST['variation_price'] as $index => $price) {
                                            $variations_to_display[] = [
                                                'variation_id' => isset($_POST['variation_id'][$index]) ? $_POST['variation_id'][$index] : null, // Keep track of existing ID
                                                'variation_color' => isset($_POST['variation_color'][$index]) ? $_POST['variation_color'][$index] : '',
                                                'variation_size' => isset($_POST['variation_size'][$index]) ? $_POST['variation_size'][$index] : '',
                                                'variation_price' => $price,
                                                'variation_qty' => isset($_POST['variation_qty'][$index]) ? $_POST['variation_qty'][$index] : '0',
                                                'variation_name' => isset($_POST['variation_name'][$index]) ? $_POST['variation_name'][$index] : '',
                                                'variation_description' => isset($_POST['variation_description'][$index]) ? $_POST['variation_description'][$index] : '',
                                                'variation_image' => isset($_POST['existing_variation_image'][$index]) ? $_POST['existing_variation_image'][$index] : null, // Keep track of existing image name
                                                'delete_variation' => isset($_POST['delete_variation']) && in_array($_POST['variation_id'][$index], $_POST['delete_variation']) ? 1 : 0, // Check if marked for delete
                                                'remove_variation_image' => isset($_POST['remove_variation_image'][$index]) ? 1 : 0 // Check if image marked for removal
                                            ];
                                        }
                                    }
                                } else {
                                    // Populate from fetched database data on initial load
                                    $variations_to_display = $product_variations;
                                }

                                // --- Loop through variations to display ---
                                if (!empty($variations_to_display)):
                                    foreach ($variations_to_display as $index => $var):
                                        // Skip displaying variations marked for deletion in POST data (they are handled server-side)
                                        if (isset($var['delete_variation']) && $var['delete_variation'] == 1) continue;

                                        $variation_id = isset($var['variation_id']) ? htmlspecialchars($var['variation_id']) : '';
                                        $var_color = isset($var['variation_color']) ? $var['variation_color'] : '';
                                        $var_size = isset($var['variation_size']) ? $var['variation_size'] : '';
                                        $var_price = isset($var['variation_price']) ? htmlspecialchars($var['variation_price']) : '';
                                        $var_qty = isset($var['variation_qty']) ? htmlspecialchars($var['variation_qty']) : '0';
                                        $var_name = isset($var['variation_name']) ? htmlspecialchars($var['variation_name']) : '';
                                        $var_desc = isset($var['variation_description']) ? htmlspecialchars($var['variation_description']) : '';
                                        $var_image = isset($var['variation_image']) ? htmlspecialchars($var['variation_image']) : '';
                                        $remove_image_checked = isset($var['remove_variation_image']) && $var['remove_variation_image'] == 1;
                                ?>
                                 <div class="variation-item panel panel-default" style="margin-bottom: 20px;">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-cube"></i> 
                                            Variation <?php echo $index + 1; ?>
                                            <?php if (!empty($var_name)): ?>
                                                - <?php echo htmlspecialchars($var_name); ?>
                                            <?php endif; ?>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                     <?php if ($variation_id): ?>
                                        <input type="hidden" name="variation_id[]" value="<?php echo $variation_id; ?>">
                                        <input type="hidden" name="existing_variation_image[<?php echo $index; ?>]" value="<?php echo $var_image; ?>">
                                     <?php endif; ?>

                                     <div class="row">
                                         <div class="col-md-2">
                                             <div class="form-group">
                                                <label><i class="fa fa-palette"></i> Color</label>
                                                <select name="variation_color[]" class="form-control select2">
                                                    <option value="">-- Optional --</option>
                                                    <?php foreach ($color_result as $color): ?>
                                                        <option value="<?php echo $color['color_id']; ?>" 
                                                            <?php echo ($var_color == $color['color_id'] ? 'selected' : ''); ?>>
                                                            <?php echo htmlspecialchars($color['color_name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group">
                                                <label><i class="fa fa-ruler"></i> Size</label>
                                                <select name="variation_size[]" class="form-control select2">
                                                    <option value="">-- Optional --</option>
                                                    <?php foreach ($sizes as $size): ?>
                                                        <option value="<?php echo $size['size_id']; ?>" 
                                                            <?php echo ($var_size == $size['size_id'] ? 'selected' : ''); ?>>
                                                            <?php echo htmlspecialchars($size['size_name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group">
                                                <label><i class="fa fa-tag"></i> Price <span class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <span class="input-group-addon">TSH</span>
                                                    <input type="text" name="variation_price[]" class="form-control" 
                                                        placeholder="9.99" pattern="^\d+(\.\d{1,2})?$" 
                                                        title="Enter a valid price" value="<?php echo $var_price; ?>">
                                                </div>
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group">
                                                <label><i class="fa fa-cubes"></i> Quantity <span class="text-danger">*</span></label>
                                                <input type="number" name="variation_qty[]" class="form-control" 
                                                    min="0" step="1" value="<?php echo $var_qty; ?>" placeholder="0">
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group">
                                                <label><i class="fa fa-font"></i> Name</label>
                                                <input type="text" name="variation_name[]" class="form-control" 
                                                    placeholder="e.g., Red-Large" value="<?php echo $var_name; ?>">
                                             </div>
                                         </div>
                                         <div class="col-md-2">
                                             <div class="form-group">
                                                <label><i class="fa fa-info-circle"></i> Description</label>
                                                <textarea name="variation_description[]" class="form-control" 
                                                    rows="1" placeholder="Notes..."><?php echo $var_desc; ?></textarea>
                                             </div>
                                         </div>
                                     </div>
                                     <div class="row" style="margin-top: 15px;">
                                         <div class="col-md-6">
                                             <div class="form-group">
                                                <label><i class="fa fa-image"></i> Image</label>
                                                <?php if (!empty($var_image) && file_exists("../assets/uploads/product_variations/" . $var_image)): ?>
                                                    <div class="variation-image-preview well well-sm">
                                                        <div class="row">
                                                            <div class="col-sm-4">
                                                                <img src="../assets/uploads/product_variations/<?php echo $var_image; ?>" 
                                                                    alt="Current Variation Image" 
                                                                    class="img-thumbnail" 
                                                                    style="max-width: 100%; height: auto;">
                                                            </div>
                                                            <div class="col-sm-8">
                                                                <div class="checkbox" style="margin-top: 10px;">
                                                                    <label class="text-danger">
                                                                        <input type="checkbox" name="remove_variation_image[<?php echo $index; ?>]" 
                                                                            value="1" <?php echo $remove_image_checked ? 'checked' : ''; ?>>
                                                                        <i class="fa fa-trash"></i> Remove current image
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="input-group">
                                                    <span class="input-group-addon"><i class="fa fa-upload"></i></span>
                                                    <input type="file" name="variation_image[]" class="form-control" 
                                                        accept=".jpg,.jpeg,.png,.gif">
                                                </div>
                                                <small class="help-block">Optional: Upload a new image to replace the current one</small>
                                             </div>
                                         </div>
                                         <div class="col-md-6">
                                             <div class="form-group text-right" style="margin-top: 25px;">
                                                <?php if ($variation_id): ?>
                                                    <button type="button" class="btn btn-danger remove-variation">
                                                        <i class="fa fa-trash"></i> Delete Variation
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-warning remove-variation">
                                                        <i class="fa fa-times"></i> Remove
                                                    </button>
                                                <?php endif; ?>
                                             </div>
                                         </div>
                                     </div>
                                    </div><!-- /.panel-body -->
                                 </div><!-- /.panel -->
<?php 
                                    endforeach; // End loop through variations
                                endif; // End if !empty variations 
?>

                                <div id="new-variation-template" class="variation-item panel panel-default" style="display: none;">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">New Variation</h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label><i class="fa fa-palette"></i> Color</label>
                                                    <select name="variation_color[]" class="form-control select2">
                                                        <option value="">-- Optional --</option>
                                                        <?php echo $color_options; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label><i class="fa fa-ruler"></i> Size</label>
                                                    <select name="variation_size[]" class="form-control select2">
                                                        <option value="">-- Optional --</option>
                                                        <?php echo $size_options; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label><i class="fa fa-tag"></i> Price <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <span class="input-group-addon">TSH</span>
                                                        <input type="text" name="variation_price[]" class="form-control" 
                                                            placeholder="9.99" pattern="^\d+(\.\d{1,2})?$" 
                                                            title="Enter a valid price">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label><i class="fa fa-cubes"></i> Quantity <span class="text-danger">*</span></label>
                                                    <input type="number" name="variation_qty[]" class="form-control" 
                                                        min="0" step="1" value="0" placeholder="0">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label><i class="fa fa-font"></i> Name</label>
                                                    <input type="text" name="variation_name[]" class="form-control" 
                                                        placeholder="e.g., Red-Large">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label><i class="fa fa-info-circle"></i> Description</label>
                                                    <textarea name="variation_description[]" class="form-control" 
                                                        rows="1" placeholder="Notes..."></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" style="margin-top: 15px;">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><i class="fa fa-image"></i> Image</label>
                                                    <input type="file" name="variation_image[]" class="form-control" 
                                                        accept=".jpg,.jpeg,.png,.gif">
                                                    <small class="help-block">Optional: Upload an image for this variation</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6 text-right">
                                                <button type="button" class="btn btn-danger remove-variation" style="margin-top: 25px;">
                                                    <i class="fa fa-trash"></i> Remove Variation
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                            </div><!-- /#variation-container -->
                                            
                                            <div class="text-center" style="margin: 20px 0;">
                                                <button type="button" id="add-variation" class="btn btn-success btn-lg">
                                                    <i class="fa fa-plus-circle"></i> Add New Variation
                                                </button>
                                            </div>
                                        </div><!-- /.panel-body -->
                                    </div><!-- /.panel -->
                                </div><!-- /.col-md-12 -->
                            </div><!-- /.row -->
                            </div><!-- /.box-body -->
                        </div><!-- /.tab-pane -->
                    </div><!-- /.tab-content -->
                </div><!-- /.nav-tabs-custom -->

                <!-- Form Actions -->
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-8">
                                    <button type="submit" class="btn btn-success btn-lg" name="form1">
                                        <i class="fa fa-save"></i> Update Product
                                    </button>
                                    <a href="product.php" class="btn btn-default btn-lg" style="margin-left: 10px;">
                                        <i class="fa fa-times"></i> Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>


<?php
require_once('footer.php');
?>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // SweetAlert for success and error messages
    <?php if($success_message != ''): ?>
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: '<?php echo addslashes($success_message); ?>',
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    <?php endif; ?>

    <?php if($error_message != ''): ?>
        Swal.fire({
            icon: 'error',
            title: 'Error',
            html: '<?php echo addslashes($error_message); ?>',
            timer: 5000,
            showConfirmButton: true
        });
    <?php endif; ?>
</script>

<script>
$(document).ready(function() {
    // Initialize Select2 for all dropdowns
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({ width: '100%' });
    } else {
        console.warn("Select2 library not loaded.");
    }

    // Initialize CKEditor replacements
    $('.ckeditor-instance').removeClass('ckeditor-instance').addClass('summernote-instance');
    if (typeof CKEDITOR !== 'undefined') {
        for (var i in CKEDITOR.instances) {
            CKEDITOR.instances[i].destroy();
        }
    }

    // Initialize media type selectors
    $('.media-type-selector').first().trigger('change');

    // --- Variation Management ---
    function initializeNewVariation($variation) {
        // Initialize Select2 for the new variation's dropdowns
        $variation.find('.select2').select2({
            width: '100%',
            dropdownParent: $variation // Ensure proper z-index handling
        });

        // Add hover effect
        $variation.hover(
            function() { $(this).addClass('panel-primary').removeClass('panel-default'); },
            function() { $(this).removeClass('panel-primary').addClass('panel-default'); }
        );
    }

    // Add new variation
    $('#add-variation').on('click', function() {
        var $template = $('#new-variation-template');
        if (!$template.length) {
            console.error("Variation template not found");
            return;
        }

        // Clone the template
        var $newVariation = $template.clone()
            .removeAttr('id')
            .addClass('variation-clone')
            .show();

        // Insert before the template
        $template.before($newVariation);

        // Initialize the new variation's components
        initializeNewVariation($newVariation);

        // Scroll to the new variation
        $('html, body').animate({
            scrollTop: $newVariation.offset().top - 100
        }, 500);
    });

    // Remove variation
    $(document).on('click', '.remove-variation', function() {
        var $variation = $(this).closest('.variation-item');
        var $variationId = $variation.find('input[name="variation_id[]"]').val();

        if ($variationId) {
            // Existing variation - confirm deletion
            Swal.fire({
                title: 'Delete Variation?',
                text: "This variation will be permanently deleted when you save the product.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Mark for deletion and visually indicate
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'delete_variation[]',
                        value: $variationId
                    }).appendTo('#product-form');

                    $variation
                        .css('opacity', '0.5')
                        .find('input, select, textarea').prop('disabled', true);
                    
                    $(this).prop('disabled', true).text('Marked for Deletion');
                }
            });
        } else {
            // New variation - remove immediately with animation
            $variation.slideUp(300, function() {
                $(this).remove();
            });
        }
    });

    // Initialize existing variations
    $('.variation-item').each(function() {
        initializeNewVariation($(this));
    });

    // --- Mid Category Loading Logic ---
    var midCategoryMap = <?php echo json_encode($mid_category_map); ?>;
    // Get the initially selected mcat_id (either from POST on error, or from DB data)
    var initiallySelectedMcat = '<?php echo isset($_POST['mcat_id']) ? $_POST['mcat_id'] : $product_data['mcat_id']; ?>';

    function populateMidCategories() {
        var tcatId = $('#tcat_id').val();
        var mcatSelect = $('#mcat_id');

        mcatSelect.html('<option value="">-- Optional --</option>'); // Start with default

        if (tcatId && midCategoryMap[tcatId]) {
             $.each(midCategoryMap[tcatId], function(i, mcat) {
                  var option = $('<option></option>').val(mcat.mcat_id).text(mcat.mcat_name);
                  // Select if it matches the initial DB/POST value
                  if (mcat.mcat_id == initiallySelectedMcat) {
                       option.prop('selected', true);
                  }
                  mcatSelect.append(option);
             });
        } else {
             mcatSelect.html('<option value="">-- Select Main Category First --</option>');
        }

        // Trigger change for Select2
        if (mcatSelect.hasClass('select2')) {
             mcatSelect.trigger('change.select2');
        }
    }

    // Populate on initial page load
    populateMidCategories();

    // Add change event listener
    $('#tcat_id').on('change', function() {
        initiallySelectedMcat = ''; // Clear initial selection on user change
        populateMidCategories();
    });


    // --- Product Variation Management ---

    // Get the HTML template for adding *new* variations
    var newVariationTemplateHtml = $('#new-variation-template').prop('outerHTML');
     // Remove the ID from the template string to avoid duplicate IDs when cloning
    newVariationTemplateHtml = newVariationTemplateHtml.replace('id="new-variation-template"', '');
    // Ensure the template itself is removed from the DOM after grabbing its HTML
    $('#new-variation-template').remove();


    if (!newVariationTemplateHtml) {
         console.error("Could not find the new variation template HTML.");
    }

    // Add new variation block
    $('#add-variation').on('click', function() {
        if (!newVariationTemplateHtml) {
            alert("Error: Could not create new variation item.");
            return;
        }
        var newBlock = $(newVariationTemplateHtml);
        // Ensure it's displayed (template was hidden)
        newBlock.show();
        // Append the new block
        $('#variation-container').append(newBlock);
    });

    // Remove variation block (handles both existing and new)
    $('#variation-container').on('click', '.remove-variation', function() {
        var variationItem = $(this).closest('.variation-item');
        var variationIdInput = variationItem.find('input[name="variation_id[]"]');

        if (variationIdInput.length > 0 && variationIdInput.val() !== '') {
            // This is an EXISTING variation
            if (confirm('Mark this existing variation for deletion? It will be permanently removed when you update the product.')) {
                // Add a hidden input to mark for deletion
                var variationId = variationIdInput.val();
                $('<input>').attr({
                    type: 'hidden',
                    name: 'delete_variation[]',
                    value: variationId
                }).appendTo('#product-form'); // Append to the main form

                // Visually indicate deletion (e.g., cross out, change background)
                variationItem.css({ 'opacity': '0.5', 'text-decoration': 'line-through', 'background-color': '#f2dede' });
                // Disable inputs within this item to prevent further edits
                variationItem.find('input, select, textarea, button').prop('disabled', true);
                // Keep the remove button functional in case they change their mind (requires more JS)
                // For simplicity now, we just disable everything. Re-enable requires tracking state.
                 $(this).prop('disabled', true); // Disable the remove button itself after marking
            }
        } else {
            // This is a NEW variation (not saved yet)
            if (confirm('Remove this newly added variation row?')) {
                variationItem.fadeOut(300, function() { $(this).remove(); });
            }
        }
    });

    // --- Featured Photo Preview ---
    $('#p_featured_photo').on('change', function() {
        var file = this.files[0];
        var previewContainer = $('#featured-photo-preview');
        var previewInfo = $('#preview-info');
        var previewImage = $('#preview-image');

        if (file) {
            // Validate file type
            var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File Type',
                    text: 'Please select a valid image file (JPG, JPEG, PNG, or GIF).',
                    timer: 3000
                });
                $(this).val(''); // Clear the input
                previewContainer.hide();
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: 'Please select an image smaller than 5MB.',
                    timer: 3000
                });
                $(this).val(''); // Clear the input
                previewContainer.hide();
                return;
            }

            // Show file info
            var fileSize = (file.size / 1024 / 1024).toFixed(2);
            previewInfo.html('<strong>' + file.name + '</strong><br>Size: ' + fileSize + ' MB<br>Type: ' + file.type);

            // Show image preview
            var reader = new FileReader();
            reader.onload = function(e) {
                previewImage.attr('src', e.target.result);
                previewContainer.show();
            };
            reader.readAsDataURL(file);
        } else {
            previewContainer.hide();
        }
    });

    // --- Additional Photos/Videos Management ---
    $('#add-photo').on('click', function() {
        var newPhotoField = `
            <div class="additional-photo-item" style="margin-bottom: 10px;">
                <div class="form-group" style="margin-bottom: 5px;">
                    <select name="additional_media_type[]" class="form-control input-sm media-type-selector">
                        <option value="photo">Photo</option>
                        <option value="video">Video</option>
                    </select>
                </div>
                <div class="input-group">
                    <input type="file" name="additional_photos[]" class="form-control media-file" accept=".jpg,.jpeg,.png,.gif">
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-danger remove-photo"><i class="fa fa-times"></i></button>
                    </span>
                </div>
                <span class="text-muted file-type-hint" style="font-size:11px;">(Optional: jpg, jpeg, png, gif)</span>
            </div>
        `;
        $('#additional-photos-container .new-photos').append(newPhotoField);
    });

    // Remove additional photo/video field
    $('#additional-photos-container').on('click', '.remove-photo', function() {
        $(this).closest('.additional-photo-item').fadeOut(300, function() {
            $(this).remove();
        });
    });

    // Handle media type change
    $('#additional-photos-container').on('change', '.media-type-selector', function() {
        var mediaType = $(this).val();
        var fileInput = $(this).closest('.additional-photo-item').find('.media-file');
        var fileTypeHint = $(this).closest('.additional-photo-item').find('.file-type-hint');

        if (mediaType === 'photo') {
            fileInput.attr('accept', '.jpg,.jpeg,.png,.gif');
            fileTypeHint.text('(Optional: jpg, jpeg, png, gif)');
        } else if (mediaType === 'video') {
            fileInput.attr('accept', '.mp4,.webm,.ogg,.mov');
            fileTypeHint.text('(Optional: mp4, webm, ogg, mov)');
        }
    });

});
</script>
