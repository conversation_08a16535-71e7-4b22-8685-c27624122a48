<?php
/**
 * Debug Login Issue
 * Investigates the 500 error during login
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>🔍 Login Debug</h1>";

$test_email = '<EMAIL>';
$test_password = 'changawa';

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🧪 Testing Login Process</h2>";
echo "<p><strong>Email:</strong> {$test_email}</p>";
echo "<p><strong>Password:</strong> {$test_password}</p>";
echo "</div>";

// Step 1: Check if user exists in database
echo "<h2>1. Database User Check</h2>";

try {
    $stmt = $pdo->prepare("SELECT cust_id, cust_fname, cust_lname, cust_email, cust_password, cust_status FROM tbl_customer WHERE cust_email = ?");
    $stmt->execute([$test_email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p style='color: green;'>✅ User found in database</p>";
        echo "<p><strong>User ID:</strong> {$user['cust_id']}</p>";
        echo "<p><strong>Name:</strong> {$user['cust_fname']} {$user['cust_lname']}</p>";
        echo "<p><strong>Email:</strong> {$user['cust_email']}</p>";
        echo "<p><strong>Status:</strong> {$user['cust_status']}</p>";
        echo "<p><strong>Password Hash:</strong> " . substr($user['cust_password'], 0, 30) . "...</p>";
    } else {
        echo "<p style='color: red;'>❌ User not found in database</p>";
        echo "<p>Let's create this user for testing...</p>";
        
        // Create the user
        $hashed_password = password_hash($test_password, PASSWORD_DEFAULT);
        $create_stmt = $pdo->prepare("
            INSERT INTO tbl_customer (
                cust_fname, cust_lname, cust_email, cust_password, 
                cust_phone, cust_address, cust_city, cust_region, 
                cust_country, cust_status, cust_created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $result = $create_stmt->execute([
            'Chanom',
            'Media',
            $test_email,
            $hashed_password,
            '+255123456789',
            '123 Test Street',
            'Dar es Salaam',
            'Dar es Salaam',
            'Tanzania',
            1
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ User created successfully</p>";
            
            // Fetch the created user
            $stmt->execute([$test_email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            echo "<p style='color: red;'>❌ Failed to create user</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Step 2: Test password verification
if ($user) {
    echo "<h2>2. Password Verification Test</h2>";
    
    try {
        $password_valid = password_verify($test_password, $user['cust_password']);
        
        if ($password_valid) {
            echo "<p style='color: green;'>✅ Password verification successful</p>";
        } else {
            echo "<p style='color: red;'>❌ Password verification failed</p>";
            echo "<p>Let's test with Auth::verifyPassword method...</p>";
            
            // Test with Auth class
            $auth_valid = Auth::verifyPassword($test_password, $user['cust_password']);
            if ($auth_valid) {
                echo "<p style='color: green;'>✅ Auth::verifyPassword successful</p>";
            } else {
                echo "<p style='color: red;'>❌ Auth::verifyPassword failed</p>";
                
                // Update password with correct hash
                echo "<p>Updating password hash...</p>";
                $new_hash = Auth::hashPassword($test_password);
                $update_stmt = $pdo->prepare("UPDATE tbl_customer SET cust_password = ? WHERE cust_email = ?");
                $update_stmt->execute([$new_hash, $test_email]);
                echo "<p style='color: green;'>✅ Password hash updated</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Password verification error: " . $e->getMessage() . "</p>";
    }
}

// Step 3: Test JWT token generation
echo "<h2>3. JWT Token Generation Test</h2>";

try {
    $payload = [
        'user_id' => $user['cust_id'] ?? 1,
        'email' => $test_email,
        'first_name' => $user['cust_fname'] ?? 'Test',
        'last_name' => $user['cust_lname'] ?? 'User',
        'iat' => time(),
        'exp' => time() + JWT_EXPIRY
    ];
    
    $token = SimpleJWT::encode($payload, JWT_SECRET);
    echo "<p style='color: green;'>✅ JWT token generated successfully</p>";
    echo "<p><strong>Token:</strong> " . substr($token, 0, 50) . "...</p>";
    
    // Test token decoding
    $decoded = SimpleJWT::decode($token, JWT_SECRET);
    echo "<p style='color: green;'>✅ JWT token decoded successfully</p>";
    echo "<p><strong>Decoded payload:</strong></p>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo json_encode($decoded, JSON_PRETTY_PRINT);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ JWT error: " . $e->getMessage() . "</p>";
}

// Step 4: Test the actual auth endpoint manually
echo "<h2>4. Manual Auth Endpoint Test</h2>";

try {
    // Simulate the auth endpoint logic
    $method = 'POST';
    $segments = ['login'];
    $input = [
        'email' => $test_email,
        'password' => $test_password
    ];
    
    echo "<p>Simulating auth endpoint with:</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo "Method: {$method}\n";
    echo "Segments: " . json_encode($segments) . "\n";
    echo "Input: " . json_encode($input, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    // Include the auth endpoint
    ob_start();
    include __DIR__ . '/endpoints/auth.php';
    $auth_output = ob_get_clean();
    
    echo "<p><strong>Auth endpoint output:</strong></p>";
    echo "<pre style='background: #e3f2fd; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($auth_output);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Auth endpoint error: " . $e->getMessage() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo $e->getTraceAsString();
    echo "</pre>";
}

// Step 5: Test via cURL to see actual API response
echo "<h2>5. Live API Test</h2>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/ecom/api/v1/auth/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'email' => $test_email,
    'password' => $test_password
]));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";

if ($error) {
    echo "<p style='color: red;'>❌ cURL Error: {$error}</p>";
} else {
    echo "<p><strong>API Response:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    $response_data = json_decode($response, true);
    if ($response_data) {
        if ($response_data['status'] === 'success') {
            echo "<p style='color: green;'>✅ API login successful!</p>";
        } else {
            echo "<p style='color: red;'>❌ API login failed: " . ($response_data['message'] ?? 'Unknown error') . "</p>";
        }
    }
}

// Step 6: Check error logs
echo "<h2>6. Error Log Check</h2>";

$error_log_path = ini_get('error_log');
if ($error_log_path && file_exists($error_log_path)) {
    echo "<p><strong>Error log path:</strong> {$error_log_path}</p>";
    $recent_errors = tail($error_log_path, 10);
    echo "<p><strong>Recent errors:</strong></p>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars($recent_errors);
    echo "</pre>";
} else {
    echo "<p>Error log not found or not configured</p>";
}

// Helper function to read last lines of a file
function tail($filename, $lines = 10) {
    $handle = fopen($filename, "r");
    if (!$handle) return '';
    
    $linecounter = $lines;
    $pos = -2;
    $beginning = false;
    $text = array();
    
    while ($linecounter > 0) {
        $t = " ";
        while ($t != "\n") {
            if (fseek($handle, $pos, SEEK_END) == -1) {
                $beginning = true;
                break;
            }
            $t = fgetc($handle);
            $pos--;
        }
        $linecounter--;
        if ($beginning) {
            rewind($handle);
        }
        $text[$lines - $linecounter - 1] = fgets($handle);
        if ($beginning) break;
    }
    fclose($handle);
    return implode("", array_reverse($text));
}

echo "<hr>";
echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
