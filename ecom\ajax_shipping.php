<?php
session_start();
include("../admin/inc/config.php");

$country = $_POST['country'] ?? '';
$subtotal = floatval($_POST['subtotal'] ?? 0);

// Fetch shipping fee from database based on country
$stmt = $pdo->prepare("SELECT shipping_fee FROM shipping_rates WHERE country = ?");
$stmt->execute([$country]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

$shipping_fee = $result ? floatval($result['shipping_fee']) : 0.0;

// Store in session
$_SESSION['shipping_country'] = $country;
$_SESSION['shipping_fee'] = $shipping_fee;

// Return JSON response
echo json_encode([
    'shipping_fee' => $shipping_fee,
    'total' => $subtotal + $shipping_fee
]);