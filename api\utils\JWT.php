<?php
/**
 * Simple JWT Implementation
 * Basic JWT encoding/decoding for API authentication
 */

class SimpleJWT {

    /**
     * Encode JWT token
     */
    public static function encode($payload, $secret, $algorithm = 'HS256') {
        $header = [
            'typ' => 'JWT',
            'alg' => $algorithm
        ];

        $header_encoded = self::base64UrlEncode(json_encode($header));
        $payload_encoded = self::base64UrlEncode(json_encode($payload));

        $signature = self::sign($header_encoded . '.' . $payload_encoded, $secret, $algorithm);
        $signature_encoded = self::base64UrlEncode($signature);

        return $header_encoded . '.' . $payload_encoded . '.' . $signature_encoded;
    }

    /**
     * Decode JWT token
     */
    public static function decode($token, $secret, $algorithm = 'HS256') {
        $parts = explode('.', $token);

        if (count($parts) !== 3) {
            throw new Exception('Invalid token format');
        }

        list($header_encoded, $payload_encoded, $signature_encoded) = $parts;

        $header = json_decode(self::base64UrlDecode($header_encoded), true);
        $payload = json_decode(self::base64UrlDecode($payload_encoded), true);

        if (!$header || !$payload) {
            throw new Exception('Invalid token data');
        }

        // Verify algorithm
        if ($header['alg'] !== $algorithm) {
            throw new Exception('Algorithm mismatch');
        }

        // Verify signature
        $expected_signature = self::sign($header_encoded . '.' . $payload_encoded, $secret, $algorithm);
        $provided_signature = self::base64UrlDecode($signature_encoded);

        if (!hash_equals($expected_signature, $provided_signature)) {
            throw new Exception('Invalid signature');
        }

        // Check expiration
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            throw new Exception('Token expired');
        }

        return $payload;
    }

    /**
     * Create signature
     */
    private static function sign($data, $secret, $algorithm) {
        switch ($algorithm) {
            case 'HS256':
                return hash_hmac('sha256', $data, $secret, true);
            case 'HS384':
                return hash_hmac('sha384', $data, $secret, true);
            case 'HS512':
                return hash_hmac('sha512', $data, $secret, true);
            default:
                throw new Exception('Unsupported algorithm');
        }
    }

    /**
     * Base64 URL encode
     */
    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Base64 URL decode
     */
    private static function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
}
