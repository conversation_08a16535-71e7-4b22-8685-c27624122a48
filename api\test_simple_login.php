<?php
/**
 * Test Simple Login
 * Test the standalone login endpoint
 */

echo "<h1>🔐 Testing Simple Login Endpoint</h1>";

$test_email = '<EMAIL>';
$test_password = 'changawa';

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🧪 Testing Standalone Login</h2>";
echo "<p>This tests a simplified login endpoint that bypasses routing issues.</p>";
echo "<p><strong>Email:</strong> {$test_email}</p>";
echo "<p><strong>Password:</strong> {$test_password}</p>";
echo "</div>";

function testSimpleLogin($email, $password) {
    $url = 'http://localhost/ecom/api/simple_login.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'email' => $email,
        'password' => $password
    ]));
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error,
        'error' => $error,
        'http_code' => $http_code,
        'content_type' => $content_type,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test 1: Simple login
echo "<h2>1. Simple Login Test</h2>";

$result = testSimpleLogin($test_email, $test_password);

echo "<p><strong>URL:</strong> http://localhost/ecom/api/simple_login.php</p>";
echo "<p><strong>Method:</strong> POST</p>";
echo "<p><strong>Content-Type:</strong> {$result['content_type']}</p>";
echo "<p><strong>HTTP Code:</strong> {$result['http_code']}</p>";

if ($result['error']) {
    echo "<p style='color: red; font-weight: bold;'>❌ cURL Error: {$result['error']}</p>";
} else {
    echo "<p><strong>Raw Response:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($result['response']);
    echo "</pre>";
    
    // Check if response is valid JSON
    $json_error = json_last_error();
    if ($json_error === JSON_ERROR_NONE && $result['data']) {
        echo "<p style='color: green; font-weight: bold;'>✅ VALID JSON RESPONSE!</p>";
        
        $status = $result['data']['status'] ?? 'unknown';
        $message = $result['data']['message'] ?? 'No message';
        
        if ($status === 'success') {
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 LOGIN SUCCESSFUL!</p>";
            
            if (isset($result['data']['data']['token'])) {
                $token = $result['data']['data']['token'];
                echo "<p style='color: green;'>✅ JWT Token received</p>";
                echo "<p><strong>Token:</strong> " . substr($token, 0, 50) . "...</p>";
                
                // Test the token
                echo "<h3>🔑 Token Validation Test</h3>";
                echo "<p>Token appears to be valid JWT format</p>";
                
                $token_parts = explode('.', $token);
                if (count($token_parts) === 3) {
                    echo "<p style='color: green;'>✅ Token has correct JWT structure (3 parts)</p>";
                    
                    // Decode payload (for display only)
                    $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $token_parts[1])), true);
                    if ($payload) {
                        echo "<p><strong>Token Payload:</strong></p>";
                        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
                        echo json_encode($payload, JSON_PRETTY_PRINT);
                        echo "</pre>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Token has incorrect structure</p>";
                }
            }
            
            if (isset($result['data']['data']['user'])) {
                $user = $result['data']['data']['user'];
                echo "<p><strong>User Data:</strong></p>";
                echo "<ul>";
                echo "<li><strong>ID:</strong> {$user['id']}</li>";
                echo "<li><strong>Name:</strong> {$user['first_name']} {$user['last_name']}</li>";
                echo "<li><strong>Email:</strong> {$user['email']}</li>";
                echo "<li><strong>Phone:</strong> " . ($user['phone'] ?? 'Not set') . "</li>";
                echo "</ul>";
            }
            
        } else {
            echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ LOGIN FAILED</p>";
            echo "<p><strong>Error:</strong> {$message}</p>";
            
            if (isset($result['data']['debug'])) {
                echo "<p><strong>Debug Info:</strong> {$result['data']['debug']}</p>";
            }
        }
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ INVALID JSON RESPONSE</p>";
        echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>";
        
        // Check if response starts with HTML
        if (strpos(trim($result['response']), '<') === 0) {
            echo "<p style='color: red;'>Response appears to be HTML, not JSON</p>";
        }
    }
}

// Test 2: Wrong password
echo "<h2>2. Wrong Password Test</h2>";

$wrong_result = testSimpleLogin($test_email, 'wrongpassword');
echo "<p><strong>HTTP Code:</strong> {$wrong_result['http_code']}</p>";

if ($wrong_result['data'] && $wrong_result['data']['status'] === 'error') {
    echo "<p style='color: green;'>✅ Wrong password correctly rejected</p>";
    echo "<p><strong>Error Message:</strong> {$wrong_result['data']['message']}</p>";
} else {
    echo "<p style='color: red;'>❌ Wrong password should have been rejected</p>";
}

// Test 3: Missing fields
echo "<h2>3. Missing Fields Test</h2>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/ecom/api/simple_login.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['email' => $test_email])); // Missing password

$missing_response = curl_exec($ch);
$missing_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> {$missing_http_code}</p>";

$missing_data = json_decode($missing_response, true);
if ($missing_data && $missing_data['status'] === 'error' && $missing_http_code === 400) {
    echo "<p style='color: green;'>✅ Missing fields correctly rejected</p>";
    echo "<p><strong>Error Message:</strong> {$missing_data['message']}</p>";
} else {
    echo "<p style='color: red;'>❌ Missing fields should have been rejected with 400 status</p>";
}

// Flutter implementation example
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📱 Flutter Implementation</h2>";
echo "<p>Use this working endpoint in your Flutter app:</p>";

echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// Working login function
Future<Map<String, dynamic>> login(String email, String password) async {
  try {
    final response = await http.post(
      Uri.parse('http://localhost/ecom/api/simple_login.php'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'email': email,
        'password': password,
      }),
    );
    
    print('Response status: \${response.statusCode}');
    print('Response body: \${response.body}');
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      
      if (data['status'] == 'success') {
        // Save token
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('auth_token', data['data']['token']);
        await prefs.setString('user_data', json.encode(data['data']['user']));
        
        return {'success': true, 'data': data};
      } else {
        return {'success': false, 'error': data['message']};
      }
    } else {
      return {'success': false, 'error': 'HTTP \${response.statusCode}'};
    }
  } catch (e) {
    return {'success': false, 'error': 'Network error: \$e'};
  }
}

// Usage example
void _handleLogin() async {
  final result = await login(
    _emailController.text,
    _passwordController.text,
  );
  
  if (result['success']) {
    // Login successful
    Navigator.pushReplacementNamed(context, '/home');
  } else {
    // Show error
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(result['error'])),
    );
  }
}
");
echo "</pre>";
echo "</div>";

// Summary
if ($result['success'] && $result['data'] && $result['data']['status'] === 'success') {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>🎉 SUCCESS!</h2>";
    echo "<p><strong>The simple login endpoint is working perfectly!</strong></p>";
    echo "<p>✅ Returns valid JSON</p>";
    echo "<p>✅ Generates JWT tokens</p>";
    echo "<p>✅ Validates credentials correctly</p>";
    echo "<p>✅ Handles errors properly</p>";
    echo "<p><strong>Use this endpoint in your Flutter app:</strong></p>";
    echo "<p><code>http://localhost/ecom/api/simple_login.php</code></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>⚠️ Issues Found</h2>";
    echo "<p>There are still issues with the login endpoint. Check:</p>";
    echo "<ul>";
    echo "<li>Database connection and credentials</li>";
    echo "<li>Customer table structure</li>";
    echo "<li>PHP error logs</li>";
    echo "<li>Apache configuration</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Simple login test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
