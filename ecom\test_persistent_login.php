<?php
/**
 * Test script for persistent login functionality
 * This script helps verify that the remember me system is working correctly
 */

// Include session configuration
include("session_config.php");
session_start();

// Include database connection
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Persistent Login Test - SMART LIFE</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
        }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Persistent Login System Test</h1>
        
        <?php
        // Test 1: Check if user is logged in
        echo "<h2>Test 1: Authentication Status</h2>";
        if (isUserLoggedIn()) {
            $customer = $_SESSION['customer'];
            echo "<div class='status success'>✅ User is logged in</div>";
            echo "<p><strong>Customer ID:</strong> " . htmlspecialchars($customer['cust_id']) . "</p>";
            echo "<p><strong>Name:</strong> " . htmlspecialchars($customer['cust_fname'] . ' ' . $customer['cust_lname']) . "</p>";
            echo "<p><strong>Email:</strong> " . htmlspecialchars($customer['cust_email']) . "</p>";
        } else {
            echo "<div class='status error'>❌ User is not logged in</div>";
            echo "<p><a href='login.php' class='btn'>Go to Login</a></p>";
        }
        
        // Test 2: Check for remember token cookie
        echo "<h2>Test 2: Remember Token Cookie</h2>";
        $remember_token = getRememberToken();
        if ($remember_token) {
            echo "<div class='status success'>✅ Remember token cookie found</div>";
            echo "<p><strong>Token (first 10 chars):</strong> " . htmlspecialchars(substr($remember_token, 0, 10)) . "...</p>";
        } else {
            echo "<div class='status warning'>⚠️ No remember token cookie found</div>";
            echo "<p>This is normal if user hasn't checked 'Remember Me' or has logged out.</p>";
        }
        
        // Test 3: Database table check
        echo "<h2>Test 3: Database Table Status</h2>";
        try {
            $stmt = $pdo->prepare("SHOW TABLES LIKE 'tbl_remember_tokens'");
            $stmt->execute();
            $table_exists = $stmt->fetch();
            
            if ($table_exists) {
                echo "<div class='status success'>✅ Remember tokens table exists</div>";
                
                // Count active tokens
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_remember_tokens WHERE expires_at > NOW()");
                $stmt->execute();
                $active_count = $stmt->fetch()['count'];
                
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_remember_tokens WHERE expires_at <= NOW()");
                $stmt->execute();
                $expired_count = $stmt->fetch()['count'];
                
                echo "<p><strong>Active tokens:</strong> $active_count</p>";
                echo "<p><strong>Expired tokens:</strong> $expired_count</p>";
                
                if ($expired_count > 0) {
                    echo "<p><a href='?cleanup=1' class='btn btn-danger'>Clean up expired tokens</a></p>";
                }
                
            } else {
                echo "<div class='status error'>❌ Remember tokens table does not exist</div>";
                echo "<p><a href='setup_remember_tokens.php' class='btn'>Run Setup Script</a></p>";
            }
        } catch (PDOException $e) {
            echo "<div class='status error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        
        // Test 4: Session configuration
        echo "<h2>Test 4: Session Configuration</h2>";
        echo "<table>";
        echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
        
        $session_settings = [
            'Session Name' => session_name(),
            'Session ID' => session_id(),
            'Cookie Lifetime' => ini_get('session.cookie_lifetime'),
            'Cookie Secure' => ini_get('session.cookie_secure') ? 'Yes' : 'No',
            'Cookie HttpOnly' => ini_get('session.cookie_httponly') ? 'Yes' : 'No',
            'Cookie SameSite' => ini_get('session.cookie_samesite'),
            'Use Strict Mode' => ini_get('session.use_strict_mode') ? 'Yes' : 'No',
        ];
        
        foreach ($session_settings as $setting => $value) {
            $status = "info";
            if ($setting === 'Cookie HttpOnly' && $value === 'No') $status = "warning";
            if ($setting === 'Use Strict Mode' && $value === 'No') $status = "warning";
            
            echo "<tr>";
            echo "<td>$setting</td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            echo "<td><span class='status $status' style='padding: 5px 10px; font-size: 12px;'>" . 
                 ($status === 'warning' ? '⚠️' : '✅') . "</span></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test 5: Constants check
        echo "<h2>Test 5: Configuration Constants</h2>";
        $constants = [
            'REMEMBER_TOKEN_EXPIRY' => defined('REMEMBER_TOKEN_EXPIRY') ? REMEMBER_TOKEN_EXPIRY . ' seconds (' . (REMEMBER_TOKEN_EXPIRY / 86400) . ' days)' : 'Not defined',
            'REMEMBER_COOKIE_NAME' => defined('REMEMBER_COOKIE_NAME') ? REMEMBER_COOKIE_NAME : 'Not defined',
            'REMEMBER_TOKEN_LENGTH' => defined('REMEMBER_TOKEN_LENGTH') ? REMEMBER_TOKEN_LENGTH . ' characters' : 'Not defined',
        ];
        
        echo "<table>";
        echo "<tr><th>Constant</th><th>Value</th></tr>";
        foreach ($constants as $constant => $value) {
            echo "<tr><td>$constant</td><td>" . htmlspecialchars($value) . "</td></tr>";
        }
        echo "</table>";
        
        // Handle cleanup request
        if (isset($_GET['cleanup']) && $_GET['cleanup'] === '1') {
            try {
                $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE expires_at <= NOW()");
                $stmt->execute();
                $deleted = $stmt->rowCount();
                echo "<div class='status success'>✅ Cleaned up $deleted expired tokens</div>";
                echo "<script>setTimeout(() => window.location.href = 'test_persistent_login.php', 2000);</script>";
            } catch (PDOException $e) {
                echo "<div class='status error'>❌ Cleanup failed: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
        ?>
        
        <h2>🧪 Test Instructions</h2>
        <div class="info status">
            <strong>To test persistent login:</strong>
            <ol>
                <li>Make sure you're logged out</li>
                <li>Go to <a href="login.php">login page</a></li>
                <li>Enter valid credentials</li>
                <li>Check "Remember me for 30 days"</li>
                <li>Login successfully</li>
                <li>Close browser completely</li>
                <li>Reopen browser and visit this page</li>
                <li>You should be automatically logged in</li>
            </ol>
        </div>
        
        <div class="warning status">
            <strong>Note:</strong> For HTTPS-only cookies to work properly, test on a domain with SSL certificate in production.
        </div>
        
        <p>
            <a href="login.php" class="btn">Login Page</a>
            <a href="dashboard.php" class="btn">Dashboard</a>
            <a href="logout.php" class="btn btn-danger">Logout</a>
            <a href="?refresh=1" class="btn">Refresh Test</a>
        </p>
    </div>
</body>
</html>
