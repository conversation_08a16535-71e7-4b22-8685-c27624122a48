# WhatsApp Order 500 Error - Troubleshooting Guide

## Quick Fix Steps

### Step 1: Run Database Migration
The most likely cause is that the database doesn't have the 'whatsapp' enum value.

**Option A: Automatic Migration**
1. Open your browser and go to: `http://your-domain/ecom/run_whatsapp_migration.php`
2. This will automatically check and run the migration if needed

**Option B: Manual Migration**
Run this SQL command in your database:
```sql
ALTER TABLE orders 
MODIFY COLUMN payment_status ENUM('pending', 'success', 'failed', 'whatsapp') DEFAULT 'pending';
```

### Step 2: Run Diagnostic
1. Open your browser and go to your cart page
2. Add some items to cart
3. Click the WhatsApp button
4. Check the browser console (F12 → Console tab)
5. Look for the "Diagnostic result:" message

### Step 3: Check Server Error Logs
Look in your server error logs for detailed PHP errors:
- **XAMPP**: `C:\xampp\apache\logs\error.log`
- **Linux**: `/var/log/apache2/error.log` or `/var/log/nginx/error.log`

## Common Issues and Solutions

### Issue 1: Database Migration Not Run
**Error**: "whatsapp enum missing"
**Solution**: Run the migration SQL above

### Issue 2: File Path Issues
**Error**: "session_config.php not found" or "config.php not found"
**Solution**: 
- Ensure all files are in the correct directory structure
- Check that `../admin/inc/config.php` exists relative to the ecom folder

### Issue 3: User Not Logged In
**Error**: "User not logged in"
**Solution**: 
- Make sure you're logged into the website
- Check if the `isUserLoggedIn()` function is working

### Issue 4: Empty Cart
**Error**: "Cart is empty"
**Solution**: 
- Add items to your cart before testing
- Check if cart data is stored in localStorage

### Issue 5: Database Connection Issues
**Error**: "PDO connection not available"
**Solution**: 
- Check database credentials in `../admin/inc/config.php`
- Ensure MySQL/database server is running

## Diagnostic Information

The diagnostic script will show you exactly what's failing. Here's what to look for:

### Successful Diagnostic Output:
```json
{
  "success": true,
  "message": "Diagnostic completed successfully",
  "debug_info": {
    "step": "all_tests_passed",
    "status": "success",
    "whatsapp_enum_exists": true,
    "user_logged_in": true,
    "cart_count": 2
  }
}
```

### Failed Diagnostic Output:
```json
{
  "success": false,
  "message": "Diagnostic failed: [specific error]",
  "debug_info": {
    "step": "[where it failed]",
    "error": "[detailed error message]"
  }
}
```

## Manual Testing Commands

Open browser console and run these to test each component:

```javascript
// Test 1: Check if files exist
fetch('whatsapp_order_debug.php', {method: 'POST', body: '{}'})
  .then(r => r.json())
  .then(console.log);

// Test 2: Check migration
fetch('run_whatsapp_migration.php')
  .then(r => r.json())
  .then(console.log);

// Test 3: Check cart data
console.log('Cart:', JSON.parse(localStorage.getItem('cart') || '[]'));

// Test 4: Check login status
console.log('Logged in:', <?php echo json_encode(isUserLoggedIn()); ?>);
```

## File Checklist

Ensure these files exist in the correct locations:

### Required Files:
- [ ] `ecom/whatsapp_order.php` (main handler)
- [ ] `ecom/whatsapp_order_debug.php` (diagnostic)
- [ ] `ecom/run_whatsapp_migration.php` (migration)
- [ ] `ecom/session_config.php` (session config)
- [ ] `ecom/auto_cleanup.php` (optional)
- [ ] `admin/inc/config.php` (database config)
- [ ] `admin/inc/functions.php` (helper functions)

### Database Requirements:
- [ ] `orders` table exists
- [ ] `payment_status` column has 'whatsapp' enum value
- [ ] `tbl_customer` table exists
- [ ] Database connection working

## Quick Recovery Steps

If you want to bypass the diagnostic temporarily:

1. **Remove diagnostic code** from cart.php:
   - Comment out lines 1555-1570 in cart.php
   - Keep only the original fetch to 'whatsapp_order.php'

2. **Use simple error handling**:
   ```javascript
   try {
       const response = await fetch('whatsapp_order.php', {
           method: 'POST',
           headers: { 'Content-Type': 'application/json' },
           body: JSON.stringify(orderData)
       });
       
       if (!response.ok) {
           const errorText = await response.text();
           console.error('Server response:', errorText);
           throw new Error(`Server error: ${response.status}`);
       }
       
       const result = await response.json();
       // ... rest of success handling
   } catch (error) {
       console.error('Full error:', error);
       alert('Error details: ' + error.message);
   }
   ```

## Contact Support

If the issue persists after trying these steps:

1. **Share the diagnostic output** from the browser console
2. **Share any server error log entries** 
3. **Confirm which step you completed** (migration, file check, etc.)
4. **Share your server environment** (XAMPP, Linux, etc.)

The diagnostic script will give us the exact information needed to fix the issue quickly!
