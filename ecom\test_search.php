<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "Starting test...<br>";

// Test session start
session_start();
echo "Session started successfully<br>";

// Test file includes
echo "Testing file includes...<br>";

if (!file_exists("../admin/inc/config.php")) {
    die("Error: config.php file not found at ../admin/inc/config.php");
}
echo "config.php found<br>";

if (!file_exists("../admin/inc/functions.php")) {
    die("Error: functions.php file not found at ../admin/inc/functions.php");
}
echo "functions.php found<br>";

if (!file_exists("../admin/inc/CSRF_Protect.php")) {
    die("Error: CSRF_Protect.php file not found at ../admin/inc/CSRF_Protect.php");
}
echo "CSRF_Protect.php found<br>";

try {
    include("../admin/inc/config.php");
    echo "config.php included successfully<br>";
} catch (Exception $e) {
    die("Error including config.php: " . $e->getMessage());
}

try {
    include("../admin/inc/functions.php");
    echo "functions.php included successfully<br>";
} catch (Exception $e) {
    die("Error including functions.php: " . $e->getMessage());
}

try {
    include("../admin/inc/CSRF_Protect.php");
    echo "CSRF_Protect.php included successfully<br>";
} catch (Exception $e) {
    die("Error including CSRF_Protect.php: " . $e->getMessage());
}

// Test database connection
if (!isset($pdo)) {
    die("Error: Database connection not established. Please check your config.php file.");
}
echo "Database connection established<br>";

// Test a simple query
try {
    $statement = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_settings");
    $statement->execute();
    $result = $statement->fetch(PDO::FETCH_ASSOC);
    echo "Settings table query successful. Count: " . $result['count'] . "<br>";
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}

// Test categories query
try {
    $statement = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_top_category");
    $statement->execute();
    $result = $statement->fetch(PDO::FETCH_ASSOC);
    echo "Categories table query successful. Count: " . $result['count'] . "<br>";
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}

// Test products query
try {
    $statement = $pdo->prepare("SELECT COUNT(*) as count FROM tbl_product WHERE p_is_active = 1");
    $statement->execute();
    $result = $statement->fetch(PDO::FETCH_ASSOC);
    echo "Products table query successful. Active products count: " . $result['count'] . "<br>";
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}

echo "<br><strong>All tests passed! The search_results.php should work properly.</strong><br>";
echo "<a href='search_results.php?q=smart'>Test search_results.php with 'smart' query</a><br>";
echo "<a href='search_results.php?q=smart&debug=1'>Test search_results.php with debug mode</a>";
?>
