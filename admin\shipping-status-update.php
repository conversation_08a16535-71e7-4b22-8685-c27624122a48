<?php
require_once('header.php');

if(isset($_GET['id']) && isset($_GET['status'])) {
    $id = $_GET['id'];
    $status = $_GET['status'];
    
    // Validate status
    $valid_statuses = ['delivered', 'processing', 'not_delivered'];
    if(!in_array($status, $valid_statuses)) {
        header('location: shipping-tracking.php');
        exit;
    }
    
    // Update the shipping status
    $statement = $pdo->prepare("UPDATE orders SET shipping_status=? WHERE id=?");
    $statement->execute(array($status, $id));
    
    // Redirect back to shipping tracking page
    header('location: shipping-tracking.php');
    exit;
} else {
    header('location: shipping-tracking.php');
    exit;
}
?> 