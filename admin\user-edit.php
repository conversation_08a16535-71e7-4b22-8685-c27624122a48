<?php
require_once('header.php');

if(!isset($_REQUEST['id'])) {
    header('location: user-view.php');
    exit;
} else {
    // Check the id is valid or not
    $statement = $pdo->prepare("SELECT * FROM tbl_user WHERE id=?");
    $statement->execute(array($_REQUEST['id']));
    $total = $statement->rowCount();
    if( $total == 0 ) {
        header('location: user-view.php');
        exit;
    }
}

if(isset($_POST['form1'])) {
    $valid = 1;

    if(empty($_POST['full_name'])) {
        $valid = 0;
        $error_message .= "Full Name can not be empty<br>";
    }

    if(empty($_POST['email'])) {
        $valid = 0;
        $error_message .= "Email can not be empty<br>";
    } else {
        // Check if email already exists
        $statement = $pdo->prepare("SELECT * FROM tbl_user WHERE email=? AND id!=?");
        $statement->execute(array($_POST['email'], $_REQUEST['id']));
        $total = $statement->rowCount();
        if($total) {
            $valid = 0;
            $error_message .= "Email already exists<br>";
        }
    }

    if(empty($_POST['phone'])) {
        $valid = 0;
        $error_message .= "Phone can not be empty<br>";
    }

    if($valid == 1) {
        // Update user information
        $statement = $pdo->prepare("UPDATE tbl_user SET
            full_name=?,
            email=?,
            phone=?,
            role=?,
            status=?
            WHERE id=?");
        $statement->execute(array(
            $_POST['full_name'],
            $_POST['email'],
            $_POST['phone'],
            $_POST['role'],
            $_POST['status'],
            $_REQUEST['id']
        ));

        // If password is provided, update it
        if(!empty($_POST['new_password'])) {
            $statement = $pdo->prepare("UPDATE tbl_user SET password=? WHERE id=?");
            $statement->execute(array(md5($_POST['new_password']), $_REQUEST['id']));
        }

        // If photo is uploaded
        if(!empty($_FILES['photo']['name'])) {
            $target_dir = "../assets/uploads/";
            $target_file = $target_dir . basename($_FILES["photo"]["name"]);
            $imageFileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));

            // Check if image file is a actual image or fake image
            $check = getimagesize($_FILES["photo"]["tmp_name"]);
            if($check !== false) {
                // Allow certain file formats
                if($imageFileType == "jpg" || $imageFileType == "png" || $imageFileType == "jpeg" || $imageFileType == "gif" ) {
                    // Upload file
                    if(move_uploaded_file($_FILES["photo"]["tmp_name"], $target_file)) {
                        $statement = $pdo->prepare("UPDATE tbl_user SET photo=? WHERE id=?");
                        $statement->execute(array($_FILES["photo"]["name"], $_REQUEST['id']));
                    }
                }
            }
        }

        $success_message = 'User information is updated successfully.';
    }
}

// Get user information
$statement = $pdo->prepare("SELECT * FROM tbl_user WHERE id=?");
$statement->execute(array($_REQUEST['id']));
$result = $statement->fetchAll(PDO::FETCH_ASSOC);
foreach ($result as $row) {
    $full_name = $row['full_name'];
    $email = $row['email'];
    $phone = $row['phone'];
    $photo = $row['photo'];
    $role = $row['role'];
    $status = $row['status'];
    $password = $row['password'];
}
?>

<section class="content-header">
    <div class="content-header-left">
        <h1>Edit User</h1>
    </div>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <?php if($error_message): ?>
            <div class="callout callout-danger">
                <p><?php echo $error_message; ?></p>
            </div>
            <?php endif; ?>

            <?php if($success_message): ?>
            <div class="callout callout-success">
                <p><?php echo $success_message; ?></p>
            </div>
            <?php endif; ?>

            <form class="form-horizontal" action="" method="post" enctype="multipart/form-data">
                <div class="box box-info">
                    <div class="box-body">
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">Full Name <span>*</span></label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="full_name" value="<?php echo $full_name; ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">Email <span>*</span></label>
                            <div class="col-sm-6">
                                <input type="email" class="form-control" name="email" value="<?php echo $email; ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">Phone <span>*</span></label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="phone" value="<?php echo $phone; ?>">
                            </div>
                        </div>
                        <?php
// Function to try to find the original password from common passwords
function tryDecodePassword($md5Hash) {
    // Array of common passwords to check against
    $commonPasswords = array(
        'password', '123456', '12345678', 'qwerty', 'admin',
        'welcome', 'password123', 'abc123', '111111', '123123',
        'admin123', 'letmein', 'monkey', '1234567', 'sunshine',
        'master', '123456789', 'welcome1', 'shadow', 'ashley',
        'football', 'jesus', 'michael', 'ninja', 'mustang',
        'password1', 'superman', '1qaz2wsx', '7777777', 'qwertyuiop',
        'baseball', 'dragon', 'michael', 'football', 'jordan',
        'love', 'password', 'michelle', 'charlie', 'batman',
        'jennifer', 'thomas', 'robert', 'soccer', 'anthony',
        'daniel', 'starwars', 'klaster', 'george', 'asshole',
        'computer', 'michelle', 'jessica', 'pepper', '1111',
        'zxcvbnm', 'freedom', 'whatever', 'qazwsx', 'trustno1',
        'iloveyou', 'princess', 'admin1', 'welcome2', 'adminadmin'
    );

    // Check if the hash matches any of the common passwords
    foreach ($commonPasswords as $pwd) {
        if (md5($pwd) === $md5Hash) {
            return $pwd;
        }
    }

    // If no match found, return false
    return false;
}

// Try to decode the password
$decodedPassword = tryDecodePassword($password);
?>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">Current Password</label>
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <?php if ($decodedPassword): ?>
                                    <input type="password" class="form-control" id="current-password" value="<?php echo $decodedPassword; ?>" disabled>
                                    <?php else: ?>
                                    <input type="password" class="form-control" id="current-password" value="<?php echo $password; ?>" disabled>
                                    <?php endif; ?>
                                    <span class="input-group-btn">
                                        <button class="btn btn-default" type="button" id="toggle-password">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                    </span>
                                </div>
                                <small class="text-muted">
                                    <?php if ($decodedPassword): ?>
                                    Password successfully decoded. Click the eye icon to view.
                                    <?php else: ?>
                                    This is the MD5 hash of the password. The original password could not be determined.
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        <script>
                            document.getElementById('toggle-password').addEventListener('click', function() {
                                var passwordField = document.getElementById('current-password');
                                var eyeIcon = this.querySelector('i');

                                if (passwordField.type === 'password') {
                                    passwordField.type = 'text';
                                    eyeIcon.classList.remove('fa-eye');
                                    eyeIcon.classList.add('fa-eye-slash');
                                } else {
                                    passwordField.type = 'password';
                                    eyeIcon.classList.remove('fa-eye-slash');
                                    eyeIcon.classList.add('fa-eye');
                                }
                            });
                        </script>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">New Password</label>
                            <div class="col-sm-6">
                                <input type="password" class="form-control" name="new_password" placeholder="Enter new password to change">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">Photo</label>
                            <div class="col-sm-6">
                                <input type="file" name="photo">
                                <?php if($photo): ?>
                                <div style="margin-top:10px;">
                                    <img src="../assets/uploads/<?php echo $photo; ?>" alt="" style="width:100px;">
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">Role <span>*</span></label>
                            <div class="col-sm-6">
                                <select name="role" class="form-control">
                                    <option value="admin" <?php if($role == 'admin'){echo 'selected';} ?>>Admin</option>
                                    <option value="super_admin" <?php if($role == 'super_admin'){echo 'selected';} ?>>Super Admin</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label">Status <span>*</span></label>
                            <div class="col-sm-6">
                                <select name="status" class="form-control">
                                    <option value="active" <?php if($status == 'active'){echo 'selected';} ?>>Active</option>
                                    <option value="inactive" <?php if($status == 'inactive'){echo 'selected';} ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="" class="col-sm-2 control-label"></label>
                            <div class="col-sm-6">
                                <button type="submit" class="btn btn-success pull-left" name="form1">Update</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<?php require_once('footer.php'); ?>