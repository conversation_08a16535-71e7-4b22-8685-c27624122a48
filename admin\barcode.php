<?php
require_once('inc/config.php');

// Check if user is logged in
if(!isset($_SESSION['user'])) {
    header('HTTP/1.0 401 Unauthorized');
    exit('Unauthorized');
}

$text = $_GET['text'] ?? '';
$size = $_GET['size'] ?? 40;
$download = $_GET['download'] ?? false;

if(empty($text)) {
    header('HTTP/1.0 400 Bad Request');
    exit('Text parameter required');
}

// Simple barcode generation using Code 128
function generateBarcode($text, $size = 40) {
    // This is a simplified barcode generator
    // For production, consider using a proper barcode library like TCPDF or similar
    
    $width = strlen($text) * 12 + 40;
    $height = $size + 20;
    
    // Create image
    $image = imagecreate($width, $height);
    
    // Colors
    $white = imagecolorallocate($image, 255, 255, 255);
    $black = imagecolorallocate($image, 0, 0, 0);
    
    // Fill background
    imagefill($image, 0, 0, $white);
    
    // Draw bars (simplified pattern)
    $x = 20;
    for($i = 0; $i < strlen($text); $i++) {
        $char = ord($text[$i]);
        $pattern = $char % 2; // Simplified pattern
        
        for($j = 0; $j < 8; $j++) {
            if(($pattern >> $j) & 1) {
                imagefilledrectangle($image, $x, 10, $x + 1, $size, $black);
            }
            $x += 2;
        }
        $x += 2;
    }
    
    // Add text below barcode
    $font_size = 3;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_x = ($width - $text_width) / 2;
    imagestring($image, $font_size, $text_x, $size + 5, $text, $black);
    
    return $image;
}

// Generate barcode
$barcode = generateBarcode($text, $size);

// Set headers
if($download) {
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="barcode_' . $text . '.png"');
} else {
    header('Content-Type: image/png');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
}

// Output image
imagepng($barcode);
imagedestroy($barcode);
?>
