-- API Migration Script
-- Adds necessary tables and columns for the REST API

-- Add wishlist table if it doesn't exist
CREATE TABLE IF NOT EXISTS `tbl_wishlist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cust_id` int(11) NOT NULL,
  `p_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_wishlist` (`cust_id`, `p_id`),
  KEY `idx_customer` (`cust_id`),
  KEY `idx_product` (`p_id`),
  FOREIGN KEY (`cust_id`) REFERENCES `tbl_customer` (`cust_id`) ON DELETE CASCADE,
  FOREIGN KEY (`p_id`) REFERENCES `tbl_product` (`p_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add orders table if it doesn't exist
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tx_ref` varchar(100) NOT NULL UNIQUE,
  `user_id` int(11) NOT NULL,
  `firstname` varchar(100) NOT NULL,
  `lastname` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text NOT NULL,
  `shipping_country_id` int(11) DEFAULT NULL,
  `shipping_country` varchar(100) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `shipping_fee` decimal(10,2) DEFAULT 0.00,
  `installation_fee_total` decimal(10,2) DEFAULT 0.00,
  `currency` varchar(3) DEFAULT 'TZS',
  `payment_status` enum('pending','success','failed') DEFAULT 'pending',
  `verification_status` enum('waiting','verified','rejected') DEFAULT 'waiting',
  `shipping_status` enum('processing','shipped','delivered','cancelled') DEFAULT 'processing',
  `tracking_number` varchar(100) DEFAULT NULL,
  `carrier` varchar(100) DEFAULT NULL,
  `estimated_delivery` date DEFAULT NULL,
  `shipping_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `tbl_customer` (`cust_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add order_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `variation_id` int(11) DEFAULT NULL,
  `variation_name` varchar(255) DEFAULT NULL,
  `variation_price` decimal(10,2) DEFAULT NULL,
  `product_name` varchar(255) NOT NULL,
  `quantity` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `total` decimal(10,2) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `installation_fee` decimal(10,2) DEFAULT 0.00,
  PRIMARY KEY (`id`),
  KEY `idx_order` (`order_id`),
  KEY `idx_product` (`product_id`),
  FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `tbl_product` (`p_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add shipping_history table for order tracking
CREATE TABLE IF NOT EXISTS `shipping_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `location` varchar(255) NOT NULL,
  `status` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order` (`order_id`),
  FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add installation_fee column to tbl_product if it doesn't exist
ALTER TABLE `tbl_product`
ADD COLUMN IF NOT EXISTS `installation_fee` int(11) DEFAULT 15000 AFTER `p_total_view`;

-- Add last_login column to tbl_customer if it doesn't exist
ALTER TABLE `tbl_customer`
ADD COLUMN IF NOT EXISTS `cust_last_login` timestamp NULL DEFAULT NULL AFTER `cust_created_at`;

-- Add created_at column to tbl_customer if it doesn't exist
ALTER TABLE `tbl_customer`
ADD COLUMN IF NOT EXISTS `cust_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Update existing customers to have created_at if null
UPDATE `tbl_customer` SET `cust_created_at` = NOW() WHERE `cust_created_at` IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_product_active` ON `tbl_product` (`p_is_active`);
CREATE INDEX IF NOT EXISTS `idx_product_featured` ON `tbl_product` (`p_is_featured`);
CREATE INDEX IF NOT EXISTS `idx_product_category` ON `tbl_product` (`tcat_id`);
CREATE INDEX IF NOT EXISTS `idx_product_subcategory` ON `tbl_product` (`mcat_id`);
CREATE INDEX IF NOT EXISTS `idx_product_views` ON `tbl_product` (`p_total_view`);
CREATE INDEX IF NOT EXISTS `idx_customer_email` ON `tbl_customer` (`cust_email`);
CREATE INDEX IF NOT EXISTS `idx_customer_status` ON `tbl_customer` (`cust_status`);

-- Insert sample shipping countries if table is empty
INSERT IGNORE INTO `tbl_country` (`country_name`) VALUES
('Tanzania'),
('Kenya'),
('Uganda'),
('Rwanda'),
('Burundi');

-- Insert sample shipping costs
INSERT IGNORE INTO `tbl_shipping_cost` (`country_id`, `amount`, `estimated_days`, `description`)
SELECT c.country_id,
       CASE
         WHEN c.country_name = 'Tanzania' THEN 5000
         WHEN c.country_name = 'Kenya' THEN 15000
         WHEN c.country_name = 'Uganda' THEN 20000
         WHEN c.country_name = 'Rwanda' THEN 25000
         WHEN c.country_name = 'Burundi' THEN 25000
         ELSE 30000
       END as amount,
       CASE
         WHEN c.country_name = 'Tanzania' THEN 3
         WHEN c.country_name = 'Kenya' THEN 7
         WHEN c.country_name = 'Uganda' THEN 10
         WHEN c.country_name = 'Rwanda' THEN 14
         WHEN c.country_name = 'Burundi' THEN 14
         ELSE 21
       END as estimated_days,
       'Standard shipping' as description
FROM `tbl_country` c
WHERE NOT EXISTS (
    SELECT 1 FROM `tbl_shipping_cost` sc WHERE sc.country_id = c.country_id
);

-- Create API logs table for monitoring
CREATE TABLE IF NOT EXISTS `api_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(255) NOT NULL,
  `method` varchar(10) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `request_data` text DEFAULT NULL,
  `response_code` int(11) NOT NULL,
  `response_time` decimal(8,3) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_endpoint` (`endpoint`),
  KEY `idx_ip` (`ip_address`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create API rate limits table
CREATE TABLE IF NOT EXISTS `api_rate_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `requests` int(11) NOT NULL DEFAULT 1,
  `window_start` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_request` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_ip` (`ip_address`),
  KEY `idx_window_start` (`window_start`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add some sample data for testing (optional)
-- You can uncomment these if you want sample data

/*
-- Sample categories
INSERT IGNORE INTO `tbl_top_category` (`tcat_name`, `show_on_menu`) VALUES
('Electronics', 1),
('Clothing', 1),
('Home & Garden', 1);

-- Sample products
INSERT IGNORE INTO `tbl_product` (
    `p_name`, `p_old_price`, `p_current_price`, `p_qty`,
    `p_featured_photo`, `p_description`, `p_short_description`,
    `p_is_featured`, `p_is_active`, `tcat_id`, `installation_fee`
) VALUES
('Sample Laptop', '1200000', '1000000', 10, 'laptop.jpg',
 'High-performance laptop for work and gaming', 'Powerful laptop with great performance',
 1, 1, 1, 20000),
('Sample Phone', '800000', '700000', 25, 'phone.jpg',
 'Latest smartphone with advanced features', 'Modern smartphone with excellent camera',
 1, 1, 1, 10000);

-- Sample colors
INSERT IGNORE INTO `tbl_color` (`color_name`, `color_code`) VALUES
('Black', '#000000'),
('White', '#FFFFFF'),
('Red', '#FF0000'),
('Blue', '#0000FF');

-- Sample sizes
INSERT IGNORE INTO `tbl_size` (`size_name`) VALUES
('Small'),
('Medium'),
('Large'),
('XL');
*/

-- Migration completed successfully
