<?php
/**
 * Session Configuration for Persistent Login
 * This file configures secure session settings and persistent authentication
 */

// Prevent direct access
if (!defined('SESSION_CONFIG_LOADED')) {
    define('SESSION_CONFIG_LOADED', true);
}

// Session configuration - set before session_start()
if (session_status() === PHP_SESSION_NONE) {
    // Set secure session parameters
    ini_set('session.cookie_lifetime', 0); // Session cookies expire when browser closes (for regular sessions)
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0); // Only send over HTTPS if available
    ini_set('session.cookie_httponly', 1); // Prevent JavaScript access to session cookies
    ini_set('session.cookie_samesite', 'Lax'); // CSRF protection
    ini_set('session.use_strict_mode', 1); // Prevent session fixation
    ini_set('session.gc_maxlifetime', 86400); // 24 hours for session garbage collection

    // Set session name
    session_name('SMARTLIFE_SESSION');
}

/**
 * Constants for persistent authentication
 */
define('REMEMBER_TOKEN_EXPIRY', 30 * 24 * 60 * 60); // 30 days in seconds
define('REMEMBER_COOKIE_NAME', 'smartlife_remember');
define('REMEMBER_TOKEN_LENGTH', 64);

/**
 * Generate a secure random token for persistent authentication
 * @return string
 */
function generateRememberToken() {
    return bin2hex(random_bytes(REMEMBER_TOKEN_LENGTH / 2));
}

/**
 * Set a persistent remember me cookie
 * @param string $token The remember token
 * @param int $expiry Expiration time (default: 30 days)
 */
function setRememberCookie($token, $expiry = null) {
    if ($expiry === null) {
        $expiry = time() + REMEMBER_TOKEN_EXPIRY;
    }

    $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off';

    // Check PHP version for SameSite support
    if (PHP_VERSION_ID >= 70300) {
        // PHP 7.3+ supports SameSite in options array
        setcookie(REMEMBER_COOKIE_NAME, $token, [
            'expires' => $expiry,
            'path' => '/',
            'domain' => '',
            'secure' => $secure,
            'httponly' => true,
            'samesite' => 'Lax'
        ]);
    } else {
        // Older PHP versions - use workaround for SameSite
        setcookie(
            REMEMBER_COOKIE_NAME,
            $token,
            $expiry,
            '/; SameSite=Lax', // Path with SameSite
            '', // Domain
            $secure, // Secure flag
            true // HttpOnly flag
        );
    }
}

/**
 * Clear the remember me cookie
 */
function clearRememberCookie() {
    if (isset($_COOKIE[REMEMBER_COOKIE_NAME])) {
        $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off';

        // Check PHP version for SameSite support
        if (PHP_VERSION_ID >= 70300) {
            // PHP 7.3+ supports SameSite in options array
            setcookie(REMEMBER_COOKIE_NAME, '', [
                'expires' => time() - 3600,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => true,
                'samesite' => 'Lax'
            ]);
        } else {
            // Older PHP versions - use workaround for SameSite
            setcookie(
                REMEMBER_COOKIE_NAME,
                '',
                time() - 3600,
                '/; SameSite=Lax', // Path with SameSite
                '', // Domain
                $secure, // Secure flag
                true // HttpOnly flag
            );
        }
        unset($_COOKIE[REMEMBER_COOKIE_NAME]);
    }
}

/**
 * Get the remember token from cookie
 * @return string|null
 */
function getRememberToken() {
    return $_COOKIE[REMEMBER_COOKIE_NAME] ?? null;
}

/**
 * Store remember token in database
 * @param PDO $pdo Database connection
 * @param int $customer_id Customer ID
 * @param string $token Remember token
 * @param int $expiry Expiration timestamp
 * @return bool Success status
 */
function storeRememberToken($pdo, $customer_id, $token, $expiry) {
    try {
        // First, clean up expired tokens for this user
        $cleanup_stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE customer_id = ? AND expires_at < NOW()");
        $cleanup_stmt->execute([$customer_id]);

        // Store the new token
        $stmt = $pdo->prepare("INSERT INTO tbl_remember_tokens (customer_id, token_hash, expires_at, created_at) VALUES (?, ?, FROM_UNIXTIME(?), NOW())");
        $token_hash = hash('sha256', $token);
        return $stmt->execute([$customer_id, $token_hash, $expiry]);
    } catch (PDOException $e) {
        error_log("Error storing remember token: " . $e->getMessage());
        return false;
    }
}

/**
 * Verify remember token and get customer data
 * @param PDO $pdo Database connection
 * @param string $token Remember token
 * @return array|null Customer data if valid, null otherwise
 */
function verifyRememberToken($pdo, $token) {
    try {
        $token_hash = hash('sha256', $token);

        $stmt = $pdo->prepare("
            SELECT c.*, rt.id as token_id
            FROM tbl_customer c
            INNER JOIN tbl_remember_tokens rt ON c.cust_id = rt.customer_id
            WHERE rt.token_hash = ? AND rt.expires_at > NOW() AND c.cust_status = 1
        ");
        $stmt->execute([$token_hash]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error verifying remember token: " . $e->getMessage());
        return null;
    }
}

/**
 * Delete remember token from database
 * @param PDO $pdo Database connection
 * @param string $token Remember token
 * @return bool Success status
 */
function deleteRememberToken($pdo, $token) {
    try {
        $token_hash = hash('sha256', $token);
        $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE token_hash = ?");
        return $stmt->execute([$token_hash]);
    } catch (PDOException $e) {
        error_log("Error deleting remember token: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete all remember tokens for a customer (useful for logout from all devices)
 * @param PDO $pdo Database connection
 * @param int $customer_id Customer ID
 * @return bool Success status
 */
function deleteAllRememberTokens($pdo, $customer_id) {
    try {
        $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE customer_id = ?");
        return $stmt->execute([$customer_id]);
    } catch (PDOException $e) {
        error_log("Error deleting all remember tokens: " . $e->getMessage());
        return false;
    }
}

/**
 * Clean up expired remember tokens (should be called periodically)
 * @param PDO $pdo Database connection
 * @return bool Success status
 */
function cleanupExpiredTokens($pdo) {
    try {
        $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE expires_at < NOW()");
        $result = $stmt->execute();
        $deleted_count = $stmt->rowCount();

        if ($deleted_count > 0) {
            error_log("Automatic cleanup: Removed $deleted_count expired remember tokens");
        }

        return $result;
    } catch (PDOException $e) {
        error_log("Error cleaning up expired tokens: " . $e->getMessage());
        return false;
    }
}

/**
 * Automatic cleanup trigger - runs cleanup based on probability
 * This provides automatic cleanup without requiring cron jobs
 * @param PDO $pdo Database connection
 * @param float $probability Probability of running cleanup (0.0 to 1.0)
 */
function autoCleanupTokens($pdo, $probability = 0.01) {
    // Only run cleanup based on probability (default 1% chance)
    if (mt_rand() / mt_getrandmax() <= $probability) {
        cleanupExpiredTokens($pdo);
    }
}

/**
 * Check if automatic cleanup should run based on last cleanup time
 * @param PDO $pdo Database connection
 * @return bool True if cleanup should run
 */
function shouldRunCleanup($pdo) {
    try {
        // Check if we have a settings table to store last cleanup time
        $stmt = $pdo->prepare("SHOW TABLES LIKE 'tbl_cleanup_log'");
        $stmt->execute();
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            // Create cleanup log table if it doesn't exist
            createCleanupLogTable($pdo);
            return true; // Run cleanup on first time
        }

        // Check last cleanup time
        $stmt = $pdo->prepare("SELECT last_cleanup FROM tbl_cleanup_log WHERE cleanup_type = 'remember_tokens' ORDER BY last_cleanup DESC LIMIT 1");
        $stmt->execute();
        $last_cleanup = $stmt->fetch();

        if (!$last_cleanup) {
            return true; // No previous cleanup recorded
        }

        // Run cleanup if last cleanup was more than 24 hours ago
        $last_cleanup_time = strtotime($last_cleanup['last_cleanup']);
        $twenty_four_hours_ago = time() - (24 * 60 * 60);

        return $last_cleanup_time < $twenty_four_hours_ago;

    } catch (PDOException $e) {
        error_log("Error checking cleanup schedule: " . $e->getMessage());
        return false;
    }
}

/**
 * Create cleanup log table for tracking automatic cleanups
 * @param PDO $pdo Database connection
 */
function createCleanupLogTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS `tbl_cleanup_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `cleanup_type` varchar(50) NOT NULL,
            `last_cleanup` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `tokens_deleted` int(11) DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `cleanup_type` (`cleanup_type`),
            KEY `last_cleanup` (`last_cleanup`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $pdo->exec($sql);
    } catch (PDOException $e) {
        error_log("Error creating cleanup log table: " . $e->getMessage());
    }
}

/**
 * Log cleanup activity
 * @param PDO $pdo Database connection
 * @param int $tokens_deleted Number of tokens deleted
 */
function logCleanupActivity($pdo, $tokens_deleted) {
    try {
        $stmt = $pdo->prepare("INSERT INTO tbl_cleanup_log (cleanup_type, tokens_deleted) VALUES ('remember_tokens', ?)");
        $stmt->execute([$tokens_deleted]);
    } catch (PDOException $e) {
        error_log("Error logging cleanup activity: " . $e->getMessage());
    }
}

/**
 * Enhanced cleanup function with logging
 * @param PDO $pdo Database connection
 * @return array Cleanup results
 */
function performScheduledCleanup($pdo) {
    try {
        $stmt = $pdo->prepare("DELETE FROM tbl_remember_tokens WHERE expires_at < NOW()");
        $result = $stmt->execute();
        $deleted_count = $stmt->rowCount();

        // Log the cleanup activity
        logCleanupActivity($pdo, $deleted_count);

        if ($deleted_count > 0) {
            error_log("Scheduled cleanup: Removed $deleted_count expired remember tokens");
        }

        return [
            'success' => $result,
            'deleted_count' => $deleted_count,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    } catch (PDOException $e) {
        error_log("Error in scheduled cleanup: " . $e->getMessage());
        return [
            'success' => false,
            'deleted_count' => 0,
            'error' => $e->getMessage()
        ];
    }
}
