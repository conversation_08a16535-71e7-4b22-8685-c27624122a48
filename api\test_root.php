<?php
/**
 * Test API Root Endpoint
 * Specifically tests the API root response
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>API Root Endpoint Test</h1>";

// Test 1: Simulate API root call
echo "<h2>1. Simulating API Root Call</h2>";

try {
    // Set up environment to simulate API root request
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/ecom/api/v1/';
    $_SERVER['SCRIPT_NAME'] = '/ecom/api/index.php';
    
    $method = 'GET';
    $input = [];
    $segments = [];
    $endpoint = '';
    
    echo "<h3>Simulated API Root Response:</h3>";
    
    // Manual API root response
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    
    $api_response = [
        'name' => 'Ecommerce API',
        'version' => API_VERSION,
        'timestamp' => date('c'),
        'endpoints' => [
            'auth' => 'Authentication endpoints',
            'products' => 'Product catalog',
            'categories' => 'Product categories',
            'cart' => 'Shopping cart management',
            'orders' => 'Order management',
            'users' => 'User profile management',
            'search' => 'Product search',
            'wishlist' => 'User wishlist',
            'shipping' => 'Shipping information',
            'settings' => 'Application settings',
            'admin' => 'Admin endpoints'
        ],
        'documentation' => $script_dir . '/v1/docs'
    ];
    
    ob_start();
    Response::success($api_response, 'Welcome to the Ecommerce API');
    $output = ob_get_clean();
    
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
    // Validate JSON
    $json_data = json_decode($output, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✓ Valid JSON response</p>";
        echo "<p><strong>Status:</strong> " . $json_data['status'] . "</p>";
        echo "<p><strong>Message:</strong> " . $json_data['message'] . "</p>";
        echo "<p><strong>API Version:</strong> " . $json_data['api_version'] . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Invalid JSON: " . json_last_error_msg() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 2: Test via cURL
echo "<h2>2. Testing via cURL</h2>";

$api_url = 'http://localhost/ecom/api/v1/';

try {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>cURL Error: " . $error . "</p>";
    } else {
        echo "<p><strong>HTTP Code:</strong> " . $http_code . "</p>";
        echo "<h3>cURL Response:</h3>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        
        // Validate JSON
        $json_data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p style='color: green;'>✓ Valid JSON response via cURL</p>";
        } else {
            echo "<p style='color: red;'>✗ Invalid JSON via cURL: " . json_last_error_msg() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>cURL test error: " . $e->getMessage() . "</p>";
}

// Test 3: Direct URL test links
echo "<h2>3. Direct URL Tests</h2>";
echo "<p>Click these links to test the API endpoints directly:</p>";
echo "<ul>";
echo "<li><a href='/ecom/api/v1/' target='_blank'>API Root</a></li>";
echo "<li><a href='/ecom/api/v1/settings/app' target='_blank'>Settings</a></li>";
echo "<li><a href='/ecom/api/v1/products?limit=3' target='_blank'>Products (3 items)</a></li>";
echo "<li><a href='/ecom/api/v1/categories' target='_blank'>Categories</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>If the cURL test shows valid JSON, the API is working correctly!</strong></p>";
?>
