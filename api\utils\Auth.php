<?php
/**
 * Authentication Utility Class
 * Handles JWT token generation and validation
 */

require_once __DIR__ . '/JWT.php';

class Auth {

    /**
     * Generate JWT token for user
     */
    public static function generateToken($user_data) {
        $payload = [
            'iss' => $_SERVER['HTTP_HOST'] ?? 'localhost', // Issuer
            'aud' => $_SERVER['HTTP_HOST'] ?? 'localhost', // Audience
            'iat' => time(), // Issued at
            'exp' => time() + JWT_EXPIRY, // Expiration
            'user_id' => $user_data['cust_id'],
            'email' => $user_data['cust_email'],
            'first_name' => $user_data['cust_fname'],
            'last_name' => $user_data['cust_lname'],
            'role' => 'customer'
        ];

        return SimpleJWT::encode($payload, JWT_SECRET, JWT_ALGORITHM);
    }

    /**
     * Generate admin JWT token
     */
    public static function generateAdminToken($admin_data) {
        $payload = [
            'iss' => $_SERVER['HTTP_HOST'] ?? 'localhost',
            'aud' => $_SERVER['HTTP_HOST'] ?? 'localhost',
            'iat' => time(),
            'exp' => time() + JWT_EXPIRY,
            'user_id' => $admin_data['id'],
            'email' => $admin_data['email'],
            'full_name' => $admin_data['full_name'],
            'role' => strtolower($admin_data['role'])
        ];

        return SimpleJWT::encode($payload, JWT_SECRET, JWT_ALGORITHM);
    }

    /**
     * Validate JWT token
     */
    public static function validateToken($token) {
        try {
            $decoded = SimpleJWT::decode($token, JWT_SECRET, JWT_ALGORITHM);
            return (array) $decoded;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get token from request headers
     */
    public static function getTokenFromHeaders() {
        $headers = getallheaders();

        if (isset($headers['Authorization'])) {
            $auth_header = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * Get current authenticated user
     */
    public static function getCurrentUser() {
        $token = self::getTokenFromHeaders();

        if (!$token) {
            return null;
        }

        $decoded = self::validateToken($token);

        if (!$decoded) {
            return null;
        }

        return $decoded;
    }

    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated() {
        return self::getCurrentUser() !== null;
    }

    /**
     * Check if user has admin role
     */
    public static function isAdmin() {
        $user = self::getCurrentUser();
        return $user && in_array($user['role'], ['admin', 'super admin']);
    }

    /**
     * Require authentication
     */
    public static function requireAuth() {
        if (!self::isAuthenticated()) {
            Response::unauthorized('Authentication required');
        }
    }

    /**
     * Require admin authentication
     */
    public static function requireAdmin() {
        if (!self::isAdmin()) {
            Response::forbidden('Admin access required');
        }
    }

    /**
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * Generate secure random token
     */
    public static function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }


}
