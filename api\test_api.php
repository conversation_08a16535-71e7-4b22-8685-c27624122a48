<?php
// Simple test API to verify Flutter app integration
// Place this file in your web server root (e.g., htdocs/ecom/api/v1/)

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

$request_uri = $_SERVER['REQUEST_URI'];
$method = $_SERVER['REQUEST_METHOD'];

// Parse the request
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove common base paths
$base_patterns = [
    '/ecom/api/v1/',
    '/ecom/api/',
    '/api/v1/',
    '/api/'
];

foreach ($base_patterns as $pattern) {
    if (strpos($path, $pattern) !== false) {
        $path = str_replace($pattern, '', $path);
        break;
    }
}

// Remove the test_api.php filename if present
$path = str_replace('test_api.php', '', $path);
$path = trim($path, '/');

// If path is empty, this is the root endpoint
if (empty($path)) {
    $path = '';
}

$path_parts = explode('/', $path);
if (empty($path_parts[0])) {
    $path_parts[0] = '';
}

// Mock data
$mock_products = [
    [
        'id' => 1,
        'name' => 'Smart WiFi Doorbell',
        'price' => 150000,
        'old_price' => 180000,
        'image' => '/uploads/smart-doorbell.jpg',
        'description' => 'Advanced smart doorbell with HD video, two-way audio, and motion detection.',
        'category_id' => 1,
        'category_name' => 'SMART DOORBELL',
        'in_stock' => true,
        'stock_quantity' => 25,
        'is_featured' => true,
        'installation_fee' => 15000
    ],
    [
        'id' => 2,
        'name' => 'Smart Light Switch',
        'price' => 45000,
        'old_price' => null,
        'image' => '/uploads/smart-switch.jpg',
        'description' => 'WiFi enabled smart light switch with voice control.',
        'category_id' => 5,
        'category_name' => 'SMART SWITCHES',
        'in_stock' => true,
        'stock_quantity' => 50,
        'is_featured' => true,
        'installation_fee' => 10000
    ],
    [
        'id' => 3,
        'name' => 'Smart Security Camera',
        'price' => 250000,
        'old_price' => 300000,
        'image' => '/uploads/security-camera.jpg',
        'description' => 'High-definition security camera with night vision.',
        'category_id' => 11,
        'category_name' => 'CAMERAS',
        'in_stock' => true,
        'stock_quantity' => 15,
        'is_featured' => true,
        'installation_fee' => 25000
    ]
];

$mock_categories = [
    [
        'id' => 1,
        'name' => 'SMART DOORBELL',
        'description' => 'Smart doorbell systems',
        'image' => '/uploads/category-doorbell.jpg',
        'is_active' => true
    ],
    [
        'id' => 5,
        'name' => 'SMART SWITCHES',
        'description' => 'Smart switches and controls',
        'image' => '/uploads/category-switches.jpg',
        'is_active' => true
    ],
    [
        'id' => 11,
        'name' => 'CAMERAS',
        'description' => 'Security cameras',
        'image' => '/uploads/category-cameras.jpg',
        'is_active' => true
    ]
];

// Route handling
switch ($path_parts[0]) {
    case 'products':
        if ($method === 'GET') {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
            $category = isset($_GET['category']) ? (int)$_GET['category'] : null;
            $featured = isset($_GET['featured']) ? $_GET['featured'] === 'true' : null;

            $filtered_products = $mock_products;

            if ($category) {
                $filtered_products = array_filter($filtered_products, function($p) use ($category) {
                    return $p['category_id'] == $category;
                });
            }

            if ($featured !== null) {
                $filtered_products = array_filter($filtered_products, function($p) use ($featured) {
                    return $p['is_featured'] == $featured;
                });
            }

            $filtered_products = array_values($filtered_products);
            $total = count($filtered_products);
            $total_pages = ceil($total / $limit);

            echo json_encode([
                'status' => 'success',
                'data' => [
                    'products' => $filtered_products,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total_items' => $total,
                        'total_pages' => $total_pages,
                        'has_next_page' => $page < $total_pages,
                        'has_previous_page' => $page > 1
                    ]
                ]
            ]);
        }
        break;

    case 'categories':
        if ($method === 'GET') {
            echo json_encode([
                'status' => 'success',
                'data' => $mock_categories
            ]);
        }
        break;

    case 'auth':
        if ($path_parts[1] === 'login' && $method === 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);

            // Simple mock authentication
            if ($input['email'] === '<EMAIL>' && $input['password'] === 'password') {
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'token' => 'mock_jwt_token_12345',
                        'user' => [
                            'id' => 1,
                            'first_name' => 'John',
                            'last_name' => 'Doe',
                            'email' => '<EMAIL>',
                            'phone' => '+255123456789',
                            'address' => '123 Smart Street',
                            'city' => 'Dar es Salaam',
                            'region' => 'Dar es Salaam',
                            'country' => 'Tanzania',
                            'is_active' => true,
                            'created_at' => '2024-01-01T00:00:00Z',
                            'last_login' => date('c')
                        ]
                    ]
                ]);
            } else {
                http_response_code(401);
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Invalid credentials'
                ]);
            }
        }
        break;

    default:
        http_response_code(404);
        echo json_encode([
            'status' => 'error',
            'message' => 'Endpoint not found'
        ]);
        break;
}
