<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shipping Policy - SMART LIFE</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .policy-section {
            @apply bg-white rounded-2xl shadow-lg p-8 mb-8 transition-all duration-300 hover:shadow-xl border border-gray-100;
        }
        .policy-section h2 {
            @apply text-2xl font-bold text-gray-800 mb-6 flex items-center;
        }
        .policy-section h2:before {
            content: "";
            @apply w-3 h-3 bg-blue-500 rounded-full mr-3;
        }
        .policy-section p {
            @apply text-gray-600 leading-relaxed mb-6;
        }
        .policy-section ul {
            @apply list-none pl-0 mb-6;
        }
        .policy-section li {
            @apply text-gray-600 mb-4 flex items-start;
        }
        .policy-section li:before {
            content: "→";
            @apply text-blue-500 mr-3 mt-1;
        }
        .shipping-table {
            @apply w-full border-collapse my-6 rounded-xl overflow-hidden shadow-md;
        }
        .shipping-table th {
            @apply bg-blue-50 text-gray-700 font-semibold p-4 text-left border-b-2 border-blue-100;
        }
        .shipping-table td {
            @apply p-4 border-b border-gray-100;
        }
        .shipping-table tr:hover {
            @apply bg-blue-50 transition-colors duration-200;
        }
        .metric-card {
            @apply bg-white rounded-2xl p-8 shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 border border-gray-100;
        }
        .metric-card .number {
            @apply text-5xl font-bold text-blue-600 mb-3;
        }
        .warning-box {
            @apply bg-yellow-50 border-l-4 border-yellow-400 p-6 rounded-r-xl mt-6;
        }
        .contact-info {
            @apply bg-blue-50 rounded-2xl p-8 border border-blue-100 shadow-lg;
        }
        .contact-info h3 {
            @apply text-xl font-semibold text-gray-800 mb-6 flex items-center;
        }
        .contact-info h3:before {
            content: "📞";
            @apply mr-3;
        }
        .contact-info ul {
            @apply list-none pl-0;
        }
        .contact-info li {
            @apply text-gray-600 mb-4 flex items-center;
        }
        .contact-info li:before {
            content: "•";
            @apply text-blue-500 mr-3;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow-sm z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
                <a href="index.php#products" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>

                <!-- Search -->
                <div class="relative">
                    <input id="searchInput" type="text" placeholder="Search products, categories..."
                           class="w-64 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="searchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-blue-600">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-blue-600 transition">Home</a>
                <a href="index.php#about" class="block text-gray-700 hover:text-blue-600 transition">About</a>
                <a href="index.php#products" class="block text-gray-700 hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-blue-600 transition">Best Deals</a>
                <a href="index.php#contact" class="block text-gray-700 hover:text-blue-600 transition">Contact</a>
                
                <!-- Search in Mobile Menu -->
                <div class="relative mt-4">
                    <input id="mobileSearchInput" type="text" placeholder="Search products, categories..."
                           class="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="mobileSearchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-blue-600 transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-blue-600 text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <main class="pt-24 pb-12">
        <div class="container mx-auto px-4 max-w-4xl">
            <!-- Hero Section -->
            <div class="text-center mb-12">
                <h1 class="text-5xl font-bold text-gray-900 mb-6">Shipping Policy</h1>
                <p class="text-xl text-gray-600">Fast, reliable, and transparent shipping for your smart home needs</p>
            </div>

            <!-- Quick Summary -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div class="metric-card">
                    <div class="number">1-2 Days</div>
                    <div class="text-gray-600">Processing Time</div>
                </div>
                <div class="metric-card">
                    <div class="number">3-5 Days</div>
                    <div class="text-gray-600">Standard Delivery</div>
                </div>
                <div class="metric-card">
                    <div class="number">Free</div>
                    <div class="text-gray-600">Over TSH 1,000,000 Orders</div>
                </div>
            </div>

            <!-- Shipping Methods -->
            <div class="policy-section">
                <h2>Shipping Methods</h2>
                <p>Choose the shipping method that best suits your needs:</p>
                <div class="overflow-x-auto">
                    <table class="shipping-table">
                        <thead>
                            <tr>
                                <th class="w-1/3">Shipping Method</th>
                                <th class="w-1/3">Delivery Time</th>
                                <th class="w-1/3">Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="group">
                                <td class="font-medium">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </span>
                                        Standard Shipping
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                        3-5 business days
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                        Free for orders over TSH 1,000,000
                                    </div>
                                </td>
                            </tr>
                            <tr class="group">
                                <td class="font-medium">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </span>
                                        Express Shipping
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                        1-2 business days
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                        TSH 35,000
                                    </div>
                                </td>
                            </tr>
                            <tr class="group">
                                <td class="font-medium">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </span>
                                        Next Day Delivery
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                        Next business day
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </span>
                                        TSH 60,000
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <p class="text-sm text-gray-600">Note: All shipping times are estimates and may vary depending on your location and product availability. International shipping rates and times may differ.</p>
                </div>
            </div>

            <!-- Processing Time -->
            <div class="policy-section">
                <h2>Processing Time</h2>
                <p>We strive to process all orders as quickly as possible. Here's what you can expect:</p>
                <ul>
                    <li>Orders are typically processed within 1-2 business days</li>
                    <li>Processing time excludes weekends and holidays</li>
                    <li>You'll receive an order confirmation email immediately after purchase</li>
                    <li>A shipping notification will be sent when your order is dispatched</li>
                </ul>
            </div>

            <!-- International Shipping -->
            <div class="policy-section">
                <h2>International Shipping</h2>
                <p>We're proud to ship our smart home products worldwide. International orders include:</p>
                <ul>
                    <li>Shipping to most countries worldwide</li>
                    <li>Delivery time of 7-14 business days</li>
                    <li>Tracking information provided for all international orders</li>
                    <li>Clear customs documentation</li>
                </ul>
                <div class="warning-box">
                    <p class="text-yellow-700">Note: International customers are responsible for any customs fees, taxes, or duties that may be charged by their country's customs office.</p>
                </div>
            </div>

            <!-- Order Tracking -->
            <div class="policy-section">
                <h2>Order Tracking</h2>
                <p>Stay informed about your order's journey:</p>
                <ul>
                    <li>Tracking number provided via email when your order ships</li>
                    <li>Real-time tracking available on our website</li>
                    <li>Carrier-specific tracking links included in shipping notifications</li>
                    <li>24/7 access to order status and delivery updates</li>
                </ul>
            </div>

            <!-- Shipping Restrictions -->
            <div class="policy-section">
                <h2>Shipping Restrictions</h2>
                <p>Some products may have specific shipping requirements:</p>
                <ul>
                    <li>Size and weight limitations for certain items</li>
                    <li>Special handling requirements for fragile items</li>
                    <li>Restrictions on international shipping for certain products</li>
                    <li>Clear indication of any restrictions on product pages</li>
                </ul>
            </div>

            <!-- Undeliverable Packages -->
            <div class="policy-section">
                <h2>Undeliverable Packages</h2>
                <p>If a package cannot be delivered, we'll help resolve the situation:</p>
                <ul>
                    <li>Immediate notification if a package is returned</li>
                    <li>Options for reshipment or refund</li>
                    <li>Assistance with address verification</li>
                    <li>Support for resolving delivery issues</li>
                </ul>
            </div>

            <!-- Contact Information -->
            <div class="contact-info">
                <h3>Need Help with Shipping?</h3>
                <p class="mb-6">Our customer service team is here to assist you with any shipping-related questions or concerns.</p>
                <ul>
                    <li>Email: <EMAIL></li>
                    <li>Phone: +****************</li>
                    <li>Hours: Monday-Friday, 9am-5pm EST</li>
                    <li>Live Chat: Available during business hours</li>
                </ul>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-200 py-8">
        <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
                <h4 class="text-xl font-semibold mb-4">SMART LIFE<span class="text-blue-600">.</span></h4>
                <p>Your Gateway to a Smarter Luxurious Home</p>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="index.php#home" class="hover:text-blue-600 transition">Home</a></li>
                    <li><a href="index.php#about" class="hover:text-blue-600 transition">About Us</a></li>
                    <li><a href="index.php#products" class="hover:text-blue-600 transition">Products</a></li>
                    <li><a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a></li>
                    <li><a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Information</h4>
                <ul class="space-y-2">
                    <li><a href="subscription_details.php" class="hover:text-blue-600 transition">FAQs & Details</a></li>
                    <li><a href="guides.php" class="hover:text-blue-600 transition">Guides</a></li>
                    <li><a href="shipping_policy.php" class="hover:text-blue-600 transition">Shipping Policy</a></li>
                    <li><a href="privacy_policy.php" class="hover:text-blue-600 transition">Privacy Policy</a></li>
                    <li><a href="#" class="hover:text-blue-600 transition">Terms &amp; Conditions</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Newsletter</h4>
                <p class="mb-4">Subscribe for exclusive offers and smart home insights.</p>
                <form action="subscribe.php" method="post" class="flex flex-col">
                    <div class="flex">
                        <input type="email" name="subscriber_email" required
                               class="w-full px-4 py-2 rounded-l-lg bg-gray-700 text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button type="submit"
                                class="px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-r-lg transition">
                            →
                        </button>
                    </div>
                    <div id="newsletter-message" class="mt-2"></div>
                </form>
            </div>
        </div>
        <div class="mt-8 border-t border-gray-700 pt-4 text-center text-sm">
            <?= $footer_copyright ?>
            <div class="mt-2 space-x-4">
                <a href="#" class="hover:text-blue-600 transition">Privacy</a>
                <a href="#" class="hover:text-blue-600 transition">Terms</a>
                <a href="#" class="hover:text-blue-600 transition">Sitemap</a>
            </div>
        </div>
    </footer>

    <!-- External JavaScript -->
    <script src="js/script.js"></script>

    <!-- Inline Page-Specific JavaScript -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Initialize cart if not exists
            if (!localStorage.getItem('cart')) {
                localStorage.setItem('cart', JSON.stringify([]));
            }

            // Update cart count display
            updateCartCount();

            function updateCartCount() {
                const cart = JSON.parse(localStorage.getItem('cart'));
                const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
                document.querySelector('.cart-count').textContent = totalItems;
            }

            // Mobile Menu Functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function toggleMobileMenu() {
                mobileMenu.classList.toggle('translate-x-full');
                mobileMenuBackdrop.classList.toggle('hidden');
                document.body.style.overflow = mobileMenu.classList.contains('translate-x-full') ? 'auto' : 'hidden';
            }

            mobileMenuButton.addEventListener('click', toggleMobileMenu);
            closeMobileMenu.addEventListener('click', toggleMobileMenu);
            mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

            // Newsletter Form Handling
            const newsletterForm = document.querySelector('form[action="subscribe.php"]');
            const newsletterMessage = document.getElementById('newsletter-message');

            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Get form data
                    const formData = new FormData(newsletterForm);
                    
                    // Show loading state
                    const submitButton = newsletterForm.querySelector('button[type="submit"]');
                    const originalButtonText = submitButton.innerHTML;
                    submitButton.innerHTML = '...';
                    submitButton.disabled = true;
                    
                    // Send form data
                    fetch('subscribe.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Create message element
                        const messageDiv = document.createElement('div');
                        messageDiv.className = `px-4 py-2 rounded-lg ${
                            data.status === 'success' 
                                ? 'bg-green-100 text-green-800 border border-green-200' 
                                : 'bg-red-100 text-red-800 border border-red-200'
                        }`;
                        messageDiv.textContent = data.message;
                        
                        // Clear previous messages and show new one
                        newsletterMessage.innerHTML = '';
                        newsletterMessage.appendChild(messageDiv);
                        
                        // Reset form if successful
                        if (data.status === 'success') {
                            newsletterForm.reset();
                        }
                    })
                    .catch(error => {
                        // Show error message
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'px-4 py-2 rounded-lg bg-red-100 text-red-800 border border-red-200';
                        messageDiv.textContent = 'An error occurred. Please try again.';
                        newsletterMessage.innerHTML = '';
                        newsletterMessage.appendChild(messageDiv);
                    })
                    .finally(() => {
                        // Reset button state
                        submitButton.innerHTML = originalButtonText;
                        submitButton.disabled = false;
                    });
                });
            }
        });
    </script>
</body>
</html> 