.on-off-switch{
    position:relative;
    cursor:pointer;
    overflow:hidden;
    user-select:none;
}

.on-off-switch-track{
    position:absolute;
    border : solid #888;
    z-index:1;
    background-color: #fff;
    overflow:hidden;
}

/* semi transparent white overlay */
.on-off-switch-track-white{
    background-color:#FFF;
    position:absolute;
    opacity:0.2;
    z-index:30;
}
/* Track for "on" state */
.on-off-switch-track-on{
    background-color:#009966;
    border-color:#008844;
    position:absolute;
    z-index:10;
    overflow:hidden;
}
/* Track for "off" state */
.on-off-switch-track-off{
    position:absolute;
    border-color:#CCC;
    z-index:1;
}

.on-off-switch-thumb{
    position:absolute;
    z-index:2;
    overflow:hidden;
}

.on-off-switch-thumb-shadow{
    opacity:0.5;
    border:1px solid #000;
    position:absolute;
}

.track-on-gradient, .track-off-gradient{

    
    position:absolute;
    width:100%;
    height:5px;
}


.on-off-switch-thumb-color{
    
    background-color:#F0F0F0;
    position:absolute;
}

.on-off-switch-thumb-off{
    border-color:#AAA;
    position:absolute;
}
.on-off-switch-thumb-on{
    border-color:#008855;
    z-index:10;
}
.on-off-switch-text{
    width:100%;
    position:absolute;
    font-family:arial;
    user-select:none;
    font-size:10px;
}

.on-off-switch-text-on{
    color:#FFF;
    text-align:left;
}
.on-off-switch-text-off{
    color:#000;
    text-align:right;
}
/* Mouse over thumb effect */
.on-off-switch-thumb-over{
    background-color:#F5F5F5;
}