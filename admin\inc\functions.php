<?php
function get_ext($pdo,$fname)
{

	$up_filename=$_FILES[$fname]["name"];
	$file_basename = substr($up_filename, 0, strripos($up_filename, '.')); // strip extention
	$file_ext = substr($up_filename, strripos($up_filename, '.')); // strip name
	return $file_ext;
}

function ext_check($pdo,$allowed_ext,$my_ext)
{

	$arr1 = array();
	$arr1 = explode("|",$allowed_ext);
	$count_arr1 = count(explode("|",$allowed_ext));

	for($i=0;$i<$count_arr1;$i++)
	{
		$arr1[$i] = '.'.$arr1[$i];
	}


	$str = '';
	$stat = 0;
	for($i=0;$i<$count_arr1;$i++)
	{
		if($my_ext == $arr1[$i])
		{
			$stat = 1;
			break;
		}
	}

	if($stat == 1)
		return true; // file extension match
	else
		return false; // file extension not match
}


function get_ai_id($pdo,$tbl_name)
{
	$statement = $pdo->prepare("SHOW TABLE STATUS LIKE '$tbl_name'");
	$statement->execute();
	$result = $statement->fetchAll(PDO::FETCH_ASSOC);
	foreach($result as $row)
	{
		$next_id = $row['Auto_increment'];
	}
	return $next_id;
}


// Add this function to admin/inc/functions.php if it doesn't exist


// admin/inc/functions.php (Add this function if it doesn't exist)

function isUserLoggedIn() {
    global $pdo;

    // First check if user is logged in via session
    if (isset($_SESSION['customer']) && !empty($_SESSION['customer']['cust_id'])) {
        // Run lightweight cleanup on authenticated requests
        if (isset($pdo)) {
            $auto_cleanup_path = dirname(__FILE__) . '/../../ecom/auto_cleanup.php';
            if (file_exists($auto_cleanup_path)) {
                include_once $auto_cleanup_path;
            }
        }
        return true;
    }

    // Check if user just logged out - prevent auto-login
    if (isset($_SESSION['just_logged_out']) && $_SESSION['just_logged_out'] === true) {
        return false;
    }

    // If not logged in via session, check for remember me cookie
    if (!defined('SESSION_CONFIG_LOADED')) {
        // Include session config if not already loaded
        $session_config_path = dirname(__FILE__) . '/../../ecom/session_config.php';
        if (file_exists($session_config_path)) {
            include_once $session_config_path;
        }
    }

    $remember_token = getRememberToken();
    if ($remember_token && isset($pdo)) {
        $customer_data = verifyRememberToken($pdo, $remember_token);
        if ($customer_data) {
            // Auto-login the user
            session_regenerate_id(true);
            $_SESSION['customer'] = [
                'cust_id' => $customer_data['cust_id'],
                'cust_fname' => $customer_data['cust_fname'],
                'cust_lname' => $customer_data['cust_lname'],
                'cust_email' => $customer_data['cust_email']
            ];

            // Clear the logout flag since user is now logged in
            unset($_SESSION['just_logged_out']);

            // Update last login
            $updateStmt = $pdo->prepare("UPDATE tbl_customer SET cust_last_login = NOW() WHERE cust_id = ?");
            $updateStmt->execute([$customer_data['cust_id']]);

            // Run automatic cleanup after successful auto-login
            $auto_cleanup_path = dirname(__FILE__) . '/../../ecom/auto_cleanup.php';
            if (file_exists($auto_cleanup_path)) {
                include_once $auto_cleanup_path;
            }

            return true;
        } else {
            // Invalid token, clear the cookie
            clearRememberCookie();
        }
    }

    return false;
}

// ... other existing functions ...

// You might also want a function to get current customer data
function getCurrentCustomer() {
    return $_SESSION['customer'] ?? null;
}
