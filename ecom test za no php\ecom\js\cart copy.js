// js/cart.js
// Version: 2025-04-23
// Includes fixes for Name, Price, Photo Path, Photo Filename Fallback, verified Installation Logic,
// Dynamic Country-Based Shipping, Shipping Persistence, and Variation Link Check.

document.addEventListener("DOMContentLoaded", function () {
  // Retrieve global variables generated by PHP (ensure these are available in cart.php)
  // IMPORTANT: productData MUST include p_id, p_name, p_current_price, p_qty, p_featured_photo
  var productData = window.productData || [];
  // Debug: Log the installation fees
  console.log(
    "Copy Init - Default installation fee:",
    window.defaultInstallationFee
  );
  console.log(
    "Copy Init - Product installation fees:",
    window.productInstallationFees
  );
  var productInstallationFees = window.productInstallationFees || {}; // Product-specific installation fees
  var productColors = window.productColors || {};
  var productVariationsData = window.productVariationsData || {};

  // ****** MODIFIED: Shipping Variables ******
  var shippingOptions = window.shippingOptions || []; // Array of {id, name, cost} passed from PHP
  var defaultShippingCost = window.defaultShippingCost || 0; // Default cost from PHP (likely corresponds to defaultShippingAreaId)
  var defaultShippingAreaId = window.defaultShippingAreaId || null; // <<< ADDED: Get default area ID from PHP

  // Get initial value: 1. PHP Session, 2. localStorage, 3. PHP Default Area ID, 4. null
  var selectedCountryId =
    window.selectedCountryId ||
    localStorage.getItem("selectedCountryId") ||
    defaultShippingAreaId ||
    null;
  // ****** END MODIFIED ******

  // --- Login Status ---
  var isLoggedIn =
    typeof isUserLoggedIn !== "undefined" ? isUserLoggedIn : false;

  // --- Build Lookup Map (productsMap) ---
  var productsMap = {};
  productData.forEach(function (prod) {
    productsMap[prod.p_id] = prod;
    if (productVariationsData[prod.p_id]) {
      productsMap[prod.p_id].variations = productVariationsData[prod.p_id];
    }
    if (productColors[prod.p_id]) {
      productsMap[prod.p_id].colors = productColors[prod.p_id];
    }
  });

  // --- Retrieve Cart from localStorage ---
  var cart = JSON.parse(localStorage.getItem("cart")) || [];

  // --- DOM Elements ---
  var cartItemsContainer = document.getElementById("cartItems");
  var installationRow = document.querySelector(".installation-fee-row");
  var productsSubtotalDisplay = document.getElementById("productsSubtotal");
  var shippingFeeDisplay = document.getElementById("shippingFee");
  var installationFeeTotalDisplay = document.getElementById(
    "installationFeeTotal"
  );
  var cartSubtotalDisplay = document.getElementById("cartSubtotal"); // Consider renaming label in HTML to "Subtotal (Products + Install)"
  var cartTotalDisplay = document.getElementById("cartTotal");

  // ****** ADDED/MODIFIED: DOM Elements for Shipping and Summary Visibility ******
  var countrySelector = document.getElementById("countrySelector"); // The dropdown
  var countrySelectorRow = document.querySelector(".country-selector-row"); // The div containing the selector
  var cartSummarySection = document.querySelector(".cart-summary"); // The whole summary div container
  // ****** END ADDED/MODIFIED ******

  // ****** Modal DOM Elements ******
  var modal = document.getElementById("summaryModal");
  var summaryContent = document.getElementById("summaryContent");
  var summaryItemsContainer = summaryContent?.querySelector(".summary-items");
  var summaryTotalsContainer = summaryContent?.querySelector(".summary-totals");
  var modalEmptyCartMsg = summaryContent?.querySelector(".modal-empty-cart");
  var confirmBtn = document.getElementById("confirmCheckout");
  var cancelBtn = modal?.querySelector(".cancel-btn");
  var closeBtn = modal?.querySelector(".close-modal");
  var proceedBtn = document.getElementById("proceedToSummaryBtn");
  // ****** END ADDED ******

  // =========================================================================
  // == CALCULATE SHIPPING COST FUNCTION == (Unchanged logic, reads current selector value)
  // =========================================================================
  function calculateShippingCost() {
    if (!countrySelector) {
      console.warn(
        "Country selector element ('#countrySelector') not found in the HTML."
      );
      return 0;
    }
    const selectedValue = countrySelector.value;

    if (!selectedValue || selectedValue === "") {
      // If "-- PLEASE SELECT AREA --" is selected, use the default cost from PHP
      return defaultShippingCost;
    }

    const selectedOptionElement =
      countrySelector.options[countrySelector.selectedIndex];
    if (!selectedOptionElement) {
      console.warn(
        "Could not find the selected option element for value:",
        selectedValue
      );
      return defaultShippingCost; // Fallback
    }

    const costAttribute = selectedOptionElement.dataset.cost;
    if (costAttribute && costAttribute !== "") {
      // Specific cost defined for this area
      return parseFloat(costAttribute);
    } else {
      // No specific cost (or data-cost missing/empty), use the default
      // This might happen if PHP didn't add data-cost or if the option is somehow malformed
      console.warn(
        `No specific cost (data-cost) found for selected option value ${selectedValue}. Using default: ${defaultShippingCost}`
      );
      return defaultShippingCost;
    }
  }
  // =========================================================================
  // == END FUNCTION ==
  // =========================================================================

  // =========================================================================
  // == RENDER CART TABLE FUNCTION ==
  // =========================================================================
  function renderCart() {
    cartItemsContainer.innerHTML = ""; // Clear previous cart items display
    var productsSubtotal = 0;
    var installationFeeTotal = 0;
    var hasInstallation = false;

    // --- Handle Empty Cart ---
    if (cart.length === 0) {
      var emptyRow = `
                <tr>
                    <td colspan="8"> {/* Adjust colspan */}
                        <div class="empty-cart" style="text-align: center; padding: 40px 20px;">
                            <i class="fas fa-shopping-cart" style="font-size: 3rem; color: #ccc; margin-bottom: 20px;"></i>
                            <h3>Your cart is empty</h3>
                            <p style="color: #666; margin-bottom: 20px;">Looks like you haven't added any items yet.</p>
                            <a href="index.php#products" class="shop-btn" style="display: inline-block; background-color: var(--secondary); color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; transition: all 0.3s;">Shop Now</a>
                        </div>
                    </td>
                </tr>
            `;
      cartItemsContainer.innerHTML = emptyRow;

      // Hide summary and country selector if cart is empty
      if (cartSummarySection) cartSummarySection.style.display = "none";
      if (countrySelectorRow) countrySelectorRow.style.display = "none";
      // Reset totals display
      if (productsSubtotalDisplay) productsSubtotalDisplay.textContent = "0";
      if (shippingFeeDisplay) shippingFeeDisplay.textContent = "0";
      if (installationFeeTotalDisplay)
        installationFeeTotalDisplay.textContent = "0";
      if (installationRow) installationRow.style.display = "none";
      if (cartSubtotalDisplay) cartSubtotalDisplay.textContent = "0";
      if (cartTotalDisplay) cartTotalDisplay.textContent = "0";
    } else {
      // Show summary and country selector if cart has items
      if (cartSummarySection) cartSummarySection.style.display = "block"; // Or your default
      if (countrySelectorRow) countrySelectorRow.style.display = "block"; // Or 'flex', etc.

      // --- Loop Through Cart Items and Render Rows ---
      cart.forEach(function (item, index) {
        const baseProduct = productsMap[item.id]; // Get base product data

        // --- Determine Item Details (Name, Price, Photo) with Fallbacks ---
        var itemName = item.name || baseProduct?.p_name || "Unknown Product";
        var itemPrice = parseFloat(
          item.price || baseProduct?.p_current_price || 0
        );

        let photoFilename = null;
        let photoBasePath = "../assets/uploads/";
        if (item.variation_id && item.variation_id != 0) {
          photoBasePath = "../assets/uploads/product_variations/";
          photoFilename = item.photo;
        } else {
          photoBasePath = "../assets/uploads/";
          photoFilename = item.photo || baseProduct?.p_featured_photo; // Fallback added
        }
        let finalPhotoSrc = "../assets/uploads/placeholder.png";
        if (photoFilename) {
          finalPhotoSrc = `${photoBasePath}${photoFilename}`;
        }
        var itemPhoto = finalPhotoSrc;

        // Stock Calculation logic (remains the same)
        // ...

        // --- Subtotal & Fee Calculations ---
        var quantity = Number(item.quantity || 1);
        var itemSubtotal = itemPrice * quantity;
        productsSubtotal += itemSubtotal;

        if (item.installation) {
          // Get product-specific installation fee or use default
          const productId = item.product_id || item.id;
          // Debug: Log the product ID and installation fee
          console.log("Copy Cart - Product ID:", productId);
          console.log(
            "Copy Cart - Product installation fees:",
            productInstallationFees
          );
          console.log(
            "Copy Cart - Product-specific fee:",
            productInstallationFees[productId]
          );
          console.log(
            "Copy Cart - Default fee:",
            window.defaultInstallationFee
          );

          const productFee =
            productId &&
            productInstallationFees &&
            productInstallationFees[productId]
              ? productInstallationFees[productId]
              : window.defaultInstallationFee;

          console.log("Copy Cart - Using fee:", productFee);
          installationFeeTotal += productFee;
          hasInstallation = true;
        }

        // --- Create Table Row Element ---
        var tr = document.createElement("tr");
        tr.dataset.index = index;

        // 1. Image Cell
        var tdImg = document.createElement("td");
        tdImg.setAttribute("data-label", "Product");
        var img = document.createElement("img");
        img.src = itemPhoto;
        img.alt = itemName;
        img.className = "cart-img";
        tdImg.appendChild(img);
        tr.appendChild(tdImg);

        // 2. Details Cell (Name, Color, Size/Variation)
        var tdName = document.createElement("td");
        tdName.setAttribute("data-label", "Details");
        var nameDiv = document.createElement("div");

        // ****** MODIFIED: Variation Link Check ******
        var productIdForLink = item.id; // Get base product ID from cart item
        if (productIdForLink) {
          // Check if ID exists and is not null/undefined/0
          var aName = document.createElement("a");
          aName.href = "product_detail.php?id=" + productIdForLink; // Link to base product page
          aName.textContent = itemName;
          aName.className = "product-name";
          nameDiv.appendChild(aName);
        } else {
          // If no valid ID, just display the name without a link
          var nameSpan = document.createElement("span");
          nameSpan.textContent = itemName;
          nameSpan.className = "product-name"; // Keep styling
          nameDiv.appendChild(nameSpan);
          console.warn(
            "Cart item at index",
            index,
            "is missing a valid product ID for linking.",
            item
          );
        }
        // ****** END MODIFIED ******

        // Display Color if available
        if (item.color_name) {
          var colorDiv = document.createElement("div");
          colorDiv.className = "product-color";
          colorDiv.textContent = "Color: " + item.color_name;
          if (item.color_code) {
            var colorPreview = document.createElement("span");
            colorPreview.className = "color-preview";
            colorPreview.style.backgroundColor = item.color_code;
            colorDiv.appendChild(colorPreview);
          }
          nameDiv.appendChild(colorDiv);
        }

        // Display Variation if available
        if (item.variation_name) {
          var variationDiv = document.createElement("div");
          variationDiv.className = "product-variation";
          var variationText = item.variation_name; // Default to name
          // Optional: Prepend quantity if available and meaningful
          // if (item.variation_qty && item.variation_qty !== "0" && item.variation_qty !== "") {
          //     variationText = item.variation_qty + ": " + item.variation_name;
          // }
          // Optional: Labeling based on content
          // if (item.variation_name.toLowerCase().includes('size')) {
          //      variationDiv.textContent = "Size: " + variationText;
          // } else {
          //      variationDiv.textContent = variationText;
          // }
          variationDiv.textContent = variationText; // Simplified display
          nameDiv.appendChild(variationDiv);
        }
        tdName.appendChild(nameDiv);
        tr.appendChild(tdName);

        // 3. Price Cell (Unchanged)
        var tdPrice = document.createElement("td");
        tdPrice.setAttribute("data-label", "Price");
        tdPrice.innerHTML = `<span class="currency-symbol">Tsh</span> <span class="price-amount">${itemPrice.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</span>`;
        tr.appendChild(tdPrice);

        // 4. Quantity Cell (with +/- buttons) - Assuming your existing complex logic here is mostly correct
        const tdQuantity = document.createElement("td");
        tdQuantity.setAttribute("data-label", "Quantity");
        const quantityDiv = document.createElement("div");
        quantityDiv.className = "quantity-selector";

        // Function to get available quantity (KEEP YOUR ASYNC FETCH LOGIC)
        const getAvailableQuantity = async (product) => {
          try {
            // For variation products
            if (product.variation_id && product.variation_id != 0) {
              // Check variation_id exists and is not 0
              const response = await fetch(
                `get_variation_stock.php?variation_id=${product.variation_id}`
              );
              if (response.ok) {
                const data = await response.json();
                // Ensure data exists and has the property before parsing
                return data && data.variation_qty
                  ? parseInt(data.variation_qty)
                  : 0;
              } else {
                console.warn(
                  `Failed to fetch stock for variation ${product.variation_id}: ${response.status}`
                );
              }
            }
            // For base products (item.id should exist)
            else if (product.id) {
              const response = await fetch(
                `get_product_stock.php?product_id=${product.id}`
              );
              if (response.ok) {
                const data = await response.json();
                // Ensure data exists and has the property before parsing
                return data && data.p_qty ? parseInt(data.p_qty) : 0;
              } else {
                console.warn(
                  `Failed to fetch stock for product ${product.id}: ${response.status}`
                );
              }
            } else {
              console.warn(
                "Cannot fetch stock, missing product ID or variation ID:",
                product
              );
            }
            return 0; // Fallback to 0
          } catch (error) {
            console.error("Error fetching stock data:", error);
            return 0;
          }
        };

        const btnDecrease = document.createElement("button");
        btnDecrease.className = "quantity-btn";
        btnDecrease.innerHTML = "&#8722;";
        btnDecrease.ariaLabel = "Decrease quantity";

        const spanQty = document.createElement("span");
        spanQty.className = "quantity-input";
        spanQty.textContent = quantity; // Use the already determined quantity
        spanQty.ariaLive = "polite";

        const btnIncrease = document.createElement("button");
        btnIncrease.className = "quantity-btn";
        btnIncrease.innerHTML = "&#43;";
        btnIncrease.ariaLabel = "Increase quantity";

        // Function to update button states
        const updateButtonStates = (currentQty, availableQty) => {
          btnDecrease.disabled = currentQty <= 1;
          btnIncrease.disabled = currentQty >= availableQty;
        };

        // Get available quantity and setup buttons
        getAvailableQuantity(item)
          .then((availableQty) => {
            // Initial state based on current quantity and fetched stock
            updateButtonStates(quantity, availableQty);

            // Decrease button handler
            btnDecrease.addEventListener("click", function () {
              // No need for async here if updateCartItemQuantity is sync
              let currentQuantity = parseInt(spanQty.textContent);
              if (currentQuantity > 1) {
                currentQuantity--;
                item.quantity = currentQuantity; // Update the item in the main cart array
                spanQty.textContent = currentQuantity;
                updateCartItemQuantity(item); // Update localStorage/session
                updateButtonStates(currentQuantity, availableQty); // Update buttons
                renderCart(); // Refresh totals
              }
            });

            // Increase button handler
            btnIncrease.addEventListener("click", async function () {
              // Needs async if getAvailableQuantity is async
              let currentQuantity = parseInt(spanQty.textContent);
              // Re-check stock at the moment of clicking increase
              const currentStock = await getAvailableQuantity(item);
              if (currentQuantity < currentStock) {
                currentQuantity++;
                item.quantity = currentQuantity; // Update the item in the main cart array
                spanQty.textContent = currentQuantity;
                updateCartItemQuantity(item); // Update localStorage/session
                updateButtonStates(currentQuantity, currentStock); // Update buttons based on potentially new stock value
                renderCart(); // Refresh totals
              } else {
                alert(`Only ${currentStock} available in stock.`);
                btnIncrease.disabled = true; // Disable button as we are at max stock
              }
            });
          })
          .catch((error) => {
            console.error(
              "Error setting up quantity buttons for item:",
              item,
              error
            );
            // Disable buttons if stock check fails
            btnDecrease.disabled = true;
            btnIncrease.disabled = true;
          });

        // Add elements to the quantity div
        quantityDiv.appendChild(btnDecrease);
        quantityDiv.appendChild(spanQty);
        quantityDiv.appendChild(btnIncrease);
        tdQuantity.appendChild(quantityDiv);
        tr.appendChild(tdQuantity);

        // Helper function to update cart item quantity in localStorage and trigger session update
        // Keep this function as you had it, ensuring saveCart() is called
        function updateCartItemQuantity(itemToUpdate) {
          // Read the latest cart from localStorage inside the function
          let currentCart = JSON.parse(localStorage.getItem("cart")) || [];
          const itemIndex = currentCart.findIndex((cartItem) => {
            // Match based on variation_id if it exists and is not 0, otherwise match on base id
            if (itemToUpdate.variation_id && itemToUpdate.variation_id != 0) {
              return (
                cartItem.id === itemToUpdate.id &&
                cartItem.variation_id === itemToUpdate.variation_id
              );
            }
            // Ensure we are matching only base products if the itemToUpdate is a base product
            // (cartItem.variation_id should be null, undefined or 0)
            return (
              cartItem.id === itemToUpdate.id &&
              (!cartItem.variation_id || cartItem.variation_id == 0)
            );
          });

          if (itemIndex !== -1) {
            currentCart[itemIndex].quantity = itemToUpdate.quantity; // Update the quantity
            localStorage.setItem("cart", JSON.stringify(currentCart)); // Save back to localStorage
            // Update the global 'cart' variable as well to reflect the change immediately for subsequent operations within this render cycle if needed
            cart = currentCart;
            saveCart(); // Trigger session update and header count update
          } else {
            console.error(
              "Could not find item in cart to update quantity:",
              itemToUpdate
            );
          }
        }

        // 5. Color Selector Cell (FIXED: Use item.product_id)
        var tdColor = document.createElement("td");
        tdColor.setAttribute("data-label", "Color");

        // --- Get Base Product Data using product_id ---
        const baseProductForColor = productsMap[item.product_id]; // <<< FIXED: Use product_id

        // --- Check if Base Product Data and Colors Exist ---
        if (!baseProductForColor) {
          // Defensive check: If baseProduct wasn't found
          console.warn(
            `Could not find base product data in productsMap for item ID: ${item.product_id}. Cannot display colors for cart item at index ${index}.`,
            item
          ); // <<< FIXED: Log product_id
          tdColor.textContent = "N/A";
        } else if (
          baseProductForColor.colors &&
          baseProductForColor.colors.length > 0
        ) {
          // ... (rest of the color cell logic remains the same) ...
          var colorWrapper = document.createElement("div");
          colorWrapper.className = "color-select-wrapper";
          var colorSelect = document.createElement("select");
          colorSelect.className = "color-select";
          colorSelect.dataset.itemIndex = index;

          const currentItemColorId = item.color_id
            ? parseInt(item.color_id)
            : null;

          var defaultOption = document.createElement("option");
          defaultOption.value = "";
          defaultOption.textContent = "Select";
          defaultOption.disabled = !!currentItemColorId;
          defaultOption.selected = !currentItemColorId;
          colorSelect.appendChild(defaultOption);

          baseProductForColor.colors.forEach(function (color) {
            var option = document.createElement("option");
            option.value = color.color_id;
            option.textContent = color.color_name;
            option.dataset.colorCode = color.color_code;
            option.selected = currentItemColorId === parseInt(color.color_id);
            colorSelect.appendChild(option);
          });

          colorSelect.onchange = function () {
            updateColorSelection(this);
          };
          colorWrapper.appendChild(colorSelect);

          if (item.color_code) {
            var colorPreview = document.createElement("span");
            colorPreview.className = "color-preview";
            colorPreview.style.backgroundColor = item.color_code;
            colorWrapper.appendChild(colorPreview);
          }
          tdColor.appendChild(colorWrapper);
        } else {
          tdColor.textContent = "N/A";
        }
        tr.appendChild(tdColor);
        // --- End of Color Cell ---

        // 6. Installation Checkbox Cell (Unchanged)
        var tdInstallation = document.createElement("td");
        tdInstallation.setAttribute("data-label", "Installation");
        var installationDiv = document.createElement("div");
        installationDiv.className = "installation-option";
        var installationCheckbox = document.createElement("input");
        installationCheckbox.type = "checkbox";
        installationCheckbox.id = `installation-${index}`;
        installationCheckbox.checked = item.installation || false;
        installationCheckbox.dataset.itemIndex = index;
        installationCheckbox.onchange = function () {
          updateInstallation(this);
        };
        var installationLabel = document.createElement("label");
        installationLabel.htmlFor = installationCheckbox.id;
        installationLabel.textContent = "Add";
        installationDiv.appendChild(installationCheckbox);
        installationDiv.appendChild(installationLabel);
        tdInstallation.appendChild(installationDiv);
        tr.appendChild(tdInstallation);

        // 7. Subtotal Cell (Unchanged)
        var tdSubtotal = document.createElement("td");
        tdSubtotal.setAttribute("data-label", "Subtotal");
        tdSubtotal.innerHTML = `<span class="currency-symbol">Tsh</span> <span class="price-amount">${itemSubtotal.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</span>`;
        tr.appendChild(tdSubtotal);

        // 8. Action Cell (Remove Button) (Unchanged)
        var tdAction = document.createElement("td");
        tdAction.setAttribute("data-label", "Action");
        var btnRemove = document.createElement("button");
        btnRemove.innerHTML = '<i class="fas fa-trash-alt"></i> Remove'; // Icon + Text
        btnRemove.className = "action-btn remove-btn"; // CSS classes
        btnRemove.title = "Remove Item"; // Tooltip
        btnRemove.onclick = function () {
          removeItem(index);
        }; // Call remove function on click
        tdAction.appendChild(btnRemove);
        tr.appendChild(tdAction);

        // --- Append the completed row to the table body ---
        cartItemsContainer.appendChild(tr);
      }); // End cart.forEach loop
    } // End else (cart not empty)

    // --- Update Price Summary Section ---

    // Calculate shipping cost AFTER loop, based on current dropdown selection
    const currentShippingFee = cart.length > 0 ? calculateShippingCost() : 0;

    if (productsSubtotalDisplay)
      productsSubtotalDisplay.textContent = productsSubtotal.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      );
    if (shippingFeeDisplay)
      shippingFeeDisplay.textContent = currentShippingFee.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      );

    // Handle Installation Fee Display
    if (installationRow) {
      if (hasInstallation && cart.length > 0) {
        installationRow.style.display = "flex"; // Or 'table-row'
        if (installationFeeTotalDisplay)
          installationFeeTotalDisplay.textContent =
            installationFeeTotal.toLocaleString("en-TZ", {
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            });
      } else {
        installationRow.style.display = "none";
      }
    }

    // Calculate Grand Total
    const applicableInstallationFee = hasInstallation
      ? installationFeeTotal
      : 0;
    var grandTotal =
      productsSubtotal + currentShippingFee + applicableInstallationFee;

    // Update "Subtotal" (Products + Installation)
    if (cartSubtotalDisplay)
      cartSubtotalDisplay.textContent = (
        productsSubtotal + applicableInstallationFee
      ).toLocaleString("en-TZ", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      });
    // Update Grand Total
    if (cartTotalDisplay)
      cartTotalDisplay.textContent = grandTotal.toLocaleString("en-TZ", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      });

    // Update cart count in the header
    updateCartCountHeader();
  } // End renderCart function

  // =========================================================================
  // == HELPER FUNCTIONS == (Mostly Unchanged - Check updateColorSelection/updateInstallation if needed)
  // =========================================================================

  // --- Update Color Selection ---
  function updateColorSelection(selectElement) {
    var index = parseInt(selectElement.dataset.itemIndex);
    // Use the global 'cart' variable which should be up-to-date
    if (cart[index]) {
      var selectedOption = selectElement.options[selectElement.selectedIndex];
      cart[index].color_id = selectElement.value
        ? parseInt(selectElement.value)
        : null;
      cart[index].color_name =
        selectedOption.textContent === "Select"
          ? null
          : selectedOption.textContent;
      cart[index].color_code = selectedOption.dataset.colorCode || null;
      saveCart(); // Save changes to localStorage and session
      renderCart(); // Redraw the cart
    }
  }

  // --- Update Installation Status ---
  function updateInstallation(checkboxElement) {
    var index = parseInt(checkboxElement.dataset.itemIndex);
    // Use the global 'cart' variable
    if (cart[index]) {
      cart[index].installation = checkboxElement.checked;
      saveCart();
      renderCart();
    }
  }

  // --- Remove Item from Cart ---
  function removeItem(index) {
    if (confirm("Are you sure you want to remove this item from your cart?")) {
      // Use the global 'cart' variable
      cart.splice(index, 1); // Remove item from the JS array
      saveCart(); // Update localStorage and session
      renderCart(); // Redraw the cart
    }
  }

  // --- Save Cart to localStorage & Update Session ---
  function saveCart() {
    // Use the global 'cart' variable which should reflect the latest state
    localStorage.setItem("cart", JSON.stringify(cart));
    updateCartSession(); // Sync with PHP session
    updateCartCountHeader(); // Update header count
  }

  // --- Update PHP Session via Fetch --- (Ensure structure matches PHP expectation)
  function updateCartSession() {
    const cartForPhpSession = {};
    // Use the global 'cart' variable
    cart.forEach((item) => {
      const productId = item.id;
      // Ensure variation_id is treated consistently (e.g., 0 or null for base products)
      const variationId =
        item.variation_id && item.variation_id != 0 ? item.variation_id : 0;
      const key = `${productId}-${variationId}`;
      const baseProduct = productsMap[productId];

      let sessionItemName = item.name || baseProduct?.p_name || "Unknown";
      let sessionItemPrice = item.price;
      if (sessionItemPrice === undefined || sessionItemPrice === null) {
        sessionItemPrice = baseProduct?.p_current_price;
      }
      sessionItemPrice = sessionItemPrice ?? 0; // Default to 0 if still missing

      let photoFilenameForSession = item.photo;
      // Use variation photo if variation_id is valid, otherwise fallback to base photo or featured photo
      if (variationId && variationId != 0) {
        // We already assigned item.photo correctly based on variation earlier if it exists
        photoFilenameForSession = item.photo || null; // Send null if variation has no specific photo? Or handle in PHP?
      } else {
        // Base product: use item.photo if set (e.g., from initial add), fallback to global product data's featured photo
        photoFilenameForSession =
          item.photo || baseProduct?.p_featured_photo || null;
      }

      cartForPhpSession[key] = {
        product_id: productId,
        variation_id: variationId, // Send 0 for base products
        quantity: Number(item.quantity || 1),
        price: parseFloat(sessionItemPrice),
        name: sessionItemName,
        photo: photoFilenameForSession || "", // Send determined filename or empty string
        color_id: item.color_id || null,
        color_name: item.color_name || null,
        color_code: item.color_code || null,
        // Size might be part of variation, handle as needed - Assuming variation_name covers size if applicable
        // size_id: item.size_id || null,
        // size_name: item.size_name || null,
        variation_name: item.variation_name || null, // <<< ADDED: Send variation name if exists
        installation: item.installation ? 1 : 0,
      };
    });

    fetch("update_cart_session.php", {
      // Ensure path is correct
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ cart: cartForPhpSession }),
    })
      .then((response) => {
        if (!response.ok) {
          return response.text().then((text) => {
            throw new Error(
              `HTTP error! status: ${response.status}, message: ${
                text || "Server error"
              }`
            );
          });
        }
        return response.json();
      })
      .then((data) => {
        if (data.status !== "success") {
          console.error("Failed to update PHP session:", data.message);
        } else {
          // Optional: console.log('PHP session updated successfully.');
        }
      })
      .catch((error) => {
        console.error("Error in updateCartSession fetch:", error);
      });
  }

  // --- Update Cart Count in Header ---
  function updateCartCountHeader() {
    // Use the global 'cart' variable
    var totalCount = cart.reduce(
      (sum, item) => sum + Number(item.quantity || 0),
      0
    );
    document.querySelectorAll(".cart-count").forEach((elem) => {
      elem.textContent = totalCount;
      elem.style.display = totalCount > 0 ? "inline-block" : "none";
    });
  }

  // =========================================================================
  // == ORDER SUMMARY MODAL LOGIC == (MODIFIED FOR SHIPPING)
  // =========================================================================
  function showSummaryModal() {
    var currentCart = JSON.parse(localStorage.getItem("cart")) || []; // Read fresh cart

    if (
      !modal ||
      !summaryItemsContainer ||
      !summaryTotalsContainer ||
      !modalEmptyCartMsg ||
      !confirmBtn
    ) {
      console.error("Modal summary elements not found!");
      return;
    }

    if (currentCart.length === 0) {
      summaryItemsContainer.innerHTML = "";
      summaryTotalsContainer.style.display = "none";
      modalEmptyCartMsg.style.display = "block";
      confirmBtn.style.display = "none";
    } else {
      modalEmptyCartMsg.style.display = "none";
      var htmlItems = "";
      var modalProductsSubtotal = 0;
      var modalInstallationFeeTotal = 0;
      var modalHasInstallation = false;
      // Debug: Log the installation fees
      console.log(
        "Copy - Default installation fee:",
        window.defaultInstallationFee
      );
      console.log(
        "Copy - Product installation fees:",
        window.productInstallationFees
      );

      // Get shipping cost for modal based on the *current selection in the main page dropdown*
      const modalShippingFee = calculateShippingCost(); // Uses the function that reads the dropdown

      currentCart.forEach(function (item, index) {
        const baseProduct = productsMap[item.id];
        const displayName =
          item.name || baseProduct?.p_name || "Unknown Product";
        const itemPrice = parseFloat(
          item.price || baseProduct?.p_current_price || 0
        );

        // Determine Photo logic (same as renderCart)
        let photoFilenameModal = null;
        let photoBasePathModal = "../assets/uploads/";
        if (item.variation_id && item.variation_id != 0) {
          photoBasePathModal = "../assets/uploads/product_variations/";
          photoFilenameModal = item.photo;
        } else {
          photoBasePathModal = "../assets/uploads/";
          photoFilenameModal = item.photo || baseProduct?.p_featured_photo;
        }
        let finalPhotoSrcModal = "../assets/uploads/placeholder.png";
        if (photoFilenameModal) {
          finalPhotoSrcModal = `${photoBasePathModal}${photoFilenameModal}`;
        }
        const photoPath = finalPhotoSrcModal;

        const quantity = Number(item.quantity || 1);
        const itemSubtotal = itemPrice * quantity;
        modalProductsSubtotal += itemSubtotal;

        if (item.installation) {
          // Get product-specific installation fee or use default
          const productId = item.product_id || item.id;

          // Debug: Log the product ID and installation fee
          console.log("Copy Modal - Product ID:", productId);
          console.log(
            "Copy Modal - Product installation fees:",
            window.productInstallationFees
          );
          console.log(
            "Copy Modal - Product-specific fee:",
            window.productInstallationFees[productId]
          );
          console.log(
            "Copy Modal - Default fee:",
            window.defaultInstallationFee
          );

          const productFee =
            productId &&
            window.productInstallationFees &&
            window.productInstallationFees[productId]
              ? window.productInstallationFees[productId]
              : window.defaultInstallationFee;

          console.log("Copy Modal - Using fee:", productFee);

          modalInstallationFeeTotal += productFee;
          modalHasInstallation = true;
        }

        // Build HTML for item (Keep your structure)
        htmlItems += `<div class="summary-item">`;
        htmlItems += `<div class="summary-item-details">`;
        htmlItems += `<img src="${photoPath}" class="summary-item-image" alt="${displayName}">`;
        htmlItems += `<div class="summary-item-text">`;
        htmlItems += `<div class="summary-item-title">${displayName}</div>`;
        htmlItems += `<div class="summary-item-meta">`;
        htmlItems += `<div>Qty: ${item.quantity}</div>`;
        if (item.color_name) {
          htmlItems += `<div>Color: ${item.color_name}${
            item.color_code
              ? `<span class="summary-color" style="background-color:${item.color_code}"></span>`
              : ""
          }</div>`;
        }
        // If variation name exists, display it
        if (item.variation_name) {
          htmlItems += `<div>Variation: ${item.variation_name}</div>`;
        } // Display variation name
        // if (item.size_name) { htmlItems += `<div>Size: ${item.size_name}</div>`; } // Or size if separate
        if (item.installation) {
          htmlItems += `<div style="color:var(--secondary); font-weight:500;"><i class="fas fa-tools"></i> Installation Added</div>`;
        }
        htmlItems += "</div>"; // close meta
        htmlItems += "</div>"; // close text
        htmlItems += "</div>"; // close details
        htmlItems += `<div class="summary-item-price">Tsh ${itemSubtotal.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</div>`;
        htmlItems += `</div>`; // close item
      });

      summaryItemsContainer.innerHTML = htmlItems;

      // --- Build Totals HTML ---
      var htmlTotals = "";
      htmlTotals += `<div class="summary-total-row"><span>Products Subtotal:</span><span>Tsh ${modalProductsSubtotal.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      )}</span></div>`;

      if (modalHasInstallation) {
        htmlTotals += `<div class="summary-total-row"><span>Installation Fee:</span><span>Tsh ${modalInstallationFeeTotal.toLocaleString(
          "en-TZ",
          { minimumFractionDigits: 0, maximumFractionDigits: 0 }
        )}</span></div>`;
      }
      // Show shipping row using the cost calculated from the main page selector
      htmlTotals += `<div class="summary-total-row"><span>Shipping Fee:</span><span>Tsh ${modalShippingFee.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      )}</span></div>`;

      const applicableModalInstallationFee = modalHasInstallation
        ? modalInstallationFeeTotal
        : 0;
      var modalGrandTotal =
        modalProductsSubtotal +
        modalShippingFee +
        applicableModalInstallationFee;
      htmlTotals += `<div class="summary-total-row summary-grand-total"><span>Total:</span><span>Tsh ${modalGrandTotal.toLocaleString(
        "en-TZ",
        { minimumFractionDigits: 0, maximumFractionDigits: 0 }
      )}</span></div>`;

      summaryTotalsContainer.innerHTML = htmlTotals;
      summaryTotalsContainer.style.display = "block";
      confirmBtn.style.display = "inline-block";
    }

    modal.style.display = "block"; // Display the modal
  } // End showSummaryModal

  // ****** ADDED: Set initial dropdown value based on retrieved selectedCountryId ******
  if (countrySelector && selectedCountryId) {
    countrySelector.value = selectedCountryId; // Set the dropdown to the stored/default value
    // Optional: Trigger change event manually if needed, but renderCart will calculate based on this value anyway
    countrySelector.dispatchEvent(new Event("change"));
  }
  // ****** END ADDED ******

  renderCart(); // Draw the initial cart table. This will now use the
  // selectedCountryId set above (from session/localStorage/default)
  // to calculate the initial shipping cost.

  // =========================================================================
  // == EVENT LISTENERS == (MODIFIED FOR SHIPPING PERSISTENCE & CHECKS)
  // =========================================================================

  // --- Listener for Country Selector Change ---
  if (countrySelector) {
    countrySelector.addEventListener("change", function () {
      // Get the selected option
      const selectedOption = this.options[this.selectedIndex];

      // Extract both country ID and shipping fee
      const countryId = this.value;
      const shippingFee = selectedOption.dataset.fee || "0"; // Get fee from data attribute or default to 0

      // Create an object to store both values
      const shippingInfo = {
        countryId: countryId,
        shippingFee: shippingFee,
      };

      // Save the object to localStorage (stringified)
      localStorage.setItem(
        "selectedShippingInfo",
        JSON.stringify(shippingInfo)
      );

      // Update any global variables if needed
      selectedCountryId = countryId;
      currentShippingFee = parseFloat(shippingFee);

      // Re-render the cart to update shipping cost and totals
      renderCart();
    });
  } else {
    console.warn(
      "Country selector element not found, cannot attach change listener."
    );
  }

  // --- Listener for "Proceed to Checkout/Summary" Button ---
  if (proceedBtn) {
    proceedBtn.addEventListener("click", function (e) {
      e.preventDefault();
      // Use the global 'cart' variable
      if (cart.length === 0) {
        alert("Your cart is empty.");
        return;
      }

      // Check if a country/area is selected (value is not empty string)
      if (
        countrySelector &&
        (!countrySelector.value || countrySelector.value === "")
      ) {
        alert("PLEASE SELECT THE AREA before proceeding."); // Use the new text
        if (countrySelector.focus) countrySelector.focus();
        return;
      }

      // Ensure session is up-to-date BEFORE showing summary
      // updateCartSession(); // saveCart called within renderCart usually handles this
      // Show the modal after ensuring calculations/updates are likely done
      showSummaryModal();
      // setTimeout(showSummaryModal, 50); // Short delay might not be needed now
    });
  }

  // --- Listener for "Confirm & Checkout" Button inside Modal ---
  if (confirmBtn) {
    confirmBtn.addEventListener("click", function () {
      // Use the global 'cart' variable
      // Final check for country selection (redundant if proceedBtn check works, but safe)
      if (
        cart.length > 0 &&
        countrySelector &&
        (!countrySelector.value || countrySelector.value === "")
      ) {
        alert(
          "PLEASE SELECT THE AREA on the main cart page before confirming checkout."
        );
        if (modal) modal.style.display = "none"; // Close modal
        if (countrySelector && countrySelector.focus) countrySelector.focus();
        return;
      }

      // Login Check and Redirect Logic (Unchanged)
      if (!isLoggedIn) {
        console.log("User not logged in. Redirecting to login...");
        sessionStorage.setItem("redirectAfterLogin", "checkout.php");
        window.location.href = "login.php";

        fetch("cart_totals.php", {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: new URLSearchParams({
            shipping_fee: document.getElementById("shippingFee").textContent,
            installation_fee: document.getElementById("installationFeeTotal")
              .textContent,
            final_total: document.getElementById("cartTotal").textContent,
          }),
        }).then(() => {
          document.getElementById("checkoutForm").submit();
        });
      } else {
        console.log("User logged in. Proceeding to checkout...");
        // Pass selected country ID to checkout? PHP session should have cart.
        // The selected country ID is now saved in localStorage, PHP could read it,
        // or better, store it in the PHP session when it's selected.
        // For now, redirect assuming checkout.php handles getting cart from session.
        window.location.href = "start_checkout.php"; // Or checkout.php
      }
    });
  }

  // --- Listeners for Closing the Modal --- (Unchanged)
  if (closeBtn) {
    closeBtn.onclick = function () {
      if (modal) modal.style.display = "none";
    };
  }
  if (cancelBtn) {
    cancelBtn.onclick = function () {
      if (modal) modal.style.display = "none";
    };
  }
  window.onclick = function (event) {
    if (event.target == modal) {
      if (modal) modal.style.display = "none";
    }
  };

  // =========================================================================
  // == INITIAL ACTIONS ON PAGE LOAD == (MODIFIED FOR SHIPPING PERSISTENCE)
  // =========================================================================
}); // End DOMContentLoaded Listener
