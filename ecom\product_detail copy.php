<?php
// product_detail.php
ob_start(); // Start output buffering
session_start(); // Start session

// --- Configuration & Includes ---
require_once("../admin/inc/config.php");
require_once("../admin/inc/functions.php");
require_once("../admin/inc/CSRF_Protect.php");

$csrf = new CSRF_Protect();

// --- File Paths & Settings ---
$upload_path_featured = "../assets/uploads/";
$upload_path_variations = "../assets/uploads/product_variations/";
$upload_path_gallery = "../assets/uploads/";
$installation_fee = 15000; // Default installation fee, will be overridden by product-specific fee if available

// --- Input Validation ---
if (!isset($_GET['id']) || !filter_var($_GET['id'], FILTER_VALIDATE_INT) || (int)$_GET['id'] <= 0) {
    error_log("Invalid or missing product ID accessed: " . ($_GET['id'] ?? 'Not Set'));
    header("Location: index.php");
    exit();
}
$product_id = (int)$_GET['id'];

// --- Initialize variables ---
$product = null;
$variations_data = [];
$product_photos = [];
$settings = [];
$footer_copyright = ('&copy; ' . date('Y') . ' SMART LIFE. All rights reserved.');
// REMOVED: Shipping variables
$variations_json = [];
$available_variation_names = [];
$variation_images_map = [];
$initial_variation = null;
$initial_price = 0;
$initial_quantity = 0; // Base quantity, variation quantity overrides if selected
$initial_sku = 'N/A';
$initial_image = 'images/default_product.png';
$initial_description = '';
$initial_variation_id = null;
$initial_variation_name = '';
$initial_size_name = null; // Keep size for variations
$in_cart = false;
$cart_button_text = 'Out of Stock';
$cart_button_disabled = true;
$initial_total = 0;
$base_product_description = '';
$base_product_short_description = '';
$product_category_name = '';
$has_variations = false;

// --- NEW: Color Selection Variables ---
$product_colors = []; // Array to hold all available colors for this product
$product_colors_json = []; // JSON representation for JS
$initial_selected_color_id = null;
$initial_selected_color_name = null;
$initial_selected_color_code = null;
$has_colors = false;

// --- Database Operations ---
try {
    // Fetch Base Product Details
    $stmt_product = $pdo->prepare("
        SELECT p.*, tcat.tcat_name
        FROM tbl_product p
        LEFT JOIN tbl_top_category tcat ON p.tcat_id = tcat.tcat_id
        WHERE p.p_id = ? AND p.p_is_active = 1
    ");
    $stmt_product->execute([$product_id]);
    $product = $stmt_product->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        error_log("Product ID {$product_id} not found or inactive.");
        header("Location: index.php");
        exit();
    }

    // Debug: Log the product data to see if installation_fee is included
    error_log("Product data: " . json_encode($product));

    // Set base product details
    $initial_price = is_numeric($product['p_current_price']) ? (float)$product['p_current_price'] : 0;
    $initial_quantity = (int)$product['p_qty']; // Base quantity
    $initial_sku = htmlspecialchars($product['p_id']);
    if (!empty($product['p_featured_photo'])) {
        $initial_image = $upload_path_featured . htmlspecialchars($product['p_featured_photo']);
    }
    $base_product_description = $product['p_description'] ?? '';
    $base_product_short_description = $product['p_short_description'] ?? '';
    $initial_description = $base_product_description;
    $product_category_name = htmlspecialchars($product['tcat_name'] ?? '');

    // Get product-specific installation fee if available
    if (isset($product['installation_fee']) && is_numeric($product['installation_fee'])) {
        $installation_fee = (int)$product['installation_fee'];
        error_log("Product-specific installation fee found: " . $installation_fee);
    } else {
        error_log("Using default installation fee: " . $installation_fee);
    }

    // --- NEW: Fetch ALL Associated Product Colors ---
    $stmt_product_colors = $pdo->prepare("
        SELECT tc.color_id, tc.color_name, tc.color_code
        FROM tbl_product_color tpc
        JOIN tbl_color tc ON tpc.color_id = tc.color_id
        WHERE tpc.p_id = ?
        ORDER BY tc.color_name ASC
    ");
    $stmt_product_colors->execute([$product_id]);
    $product_colors_raw = $stmt_product_colors->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($product_colors_raw)) {
        $has_colors = true;
        foreach ($product_colors_raw as $color) {
            $color_data = [
                'id' => (int)$color['color_id'],
                'name' => htmlspecialchars($color['color_name']),
                'code' => htmlspecialchars($color['color_code'])
            ];
            $product_colors[$color['color_id']] = $color_data; // Store by ID for easy lookup
        }
        // Set initial selected color (first one in the list)
        $first_color = reset($product_colors); // Get the first element
        if ($first_color) {
            $initial_selected_color_id = $first_color['id'];
            $initial_selected_color_name = $first_color['name'];
            $initial_selected_color_code = $first_color['code'];
        }
        $product_colors_json = $product_colors; // Assign for JS
    }
    // --- END NEW COLOR FETCH ---


    // Fetch Product Variations (No color info here)
    $stmt_variations = $pdo->prepare("
        SELECT
            pv.variation_id, pv.p_id,
            pv.variation_name, pv.variation_description,
            pv.variation_size as size_id, s.size_name,
            pv.variation_price, pv.variation_qty, pv.variation_sku, pv.variation_image
        FROM tbl_product_variation pv
        LEFT JOIN tbl_size s ON pv.variation_size = s.size_id
        WHERE pv.p_id = ?
        ORDER BY pv.variation_name ASC, s.size_name ASC
    ");
    $stmt_variations->execute([$product_id]);
    $variations_data = $stmt_variations->fetchAll(PDO::FETCH_ASSOC);

    // Fetch Additional Gallery Photos
    $stmt_photos = $pdo->prepare("SELECT photo_name FROM tbl_product_photo WHERE p_id = ?");
    $stmt_photos->execute([$product_id]);
    $product_photos = $stmt_photos->fetchAll(PDO::FETCH_ASSOC);

    // Fetch Settings
    $stmt_settings = $pdo->prepare("SELECT footer_copyright FROM tbl_settings WHERE id=1");
    $stmt_settings->execute();
    $settings = $stmt_settings->fetch(PDO::FETCH_ASSOC);
    if ($settings && !empty($settings['footer_copyright'])) {
         $footer_copyright = htmlspecialchars($settings['footer_copyright']);
    }

    // REMOVED: Fetch Shipping Data

} catch (PDOException $e) {
    error_log("Database Error in product_detail.php for product ID {$product_id}: " . $e->getMessage());
    exit('Database Error: ' . $e->getMessage() . ' (Check PHP error log for more details)');
}

// --- Prepare Variation Data for JavaScript & Determine Initial Variation ---
if (!empty($variations_data)) {
    $has_variations = true;
    foreach ($variations_data as $var) {
        // Process variations as before (excluding color)
        $variation_actual_price = is_numeric($var['variation_price']) ? (float)$var['variation_price'] : 0;
        $variation_image_path = $var['variation_image'] ? $upload_path_variations . htmlspecialchars($var['variation_image']) : null;

        $js_var_data = [
            'id' => (int)$var['variation_id'],
            'name' => htmlspecialchars($var['variation_name'] ?? 'Unnamed Variation'),
            'description' => $var['variation_description'] ?? '',
            'size_id' => $var['size_id'] ? (int)$var['size_id'] : null,
            'size_name' => $var['size_name'] ? htmlspecialchars($var['size_name']) : null,
            'price' => $variation_actual_price,
            'quantity' => (int)$var['variation_qty'],
            'sku' => $var['variation_sku'] ? htmlspecialchars($var['variation_sku']) : 'N/A',
            'image' => $variation_image_path
        ];
        $variations_json[$var['variation_id']] = $js_var_data;

        if (!isset($available_variation_names[$var['variation_id']])) {
             $available_variation_names[$var['variation_id']] = $js_var_data['name'];
        }
         if ($variation_image_path) {
              $variation_images_map[$variation_image_path] = $var['variation_id'];
         }
    }

    // Select the first variation as the default
    $first_variation_key = array_key_first($variations_json);
    if ($first_variation_key !== null) {
        $initial_variation = $variations_json[$first_variation_key];
        $initial_variation_id = $initial_variation['id'];
        $initial_variation_name = $initial_variation['name'];
        $initial_price = $initial_variation['price'];
        $initial_quantity = $initial_variation['quantity']; // Variation quantity overrides base
        $initial_sku = $initial_variation['sku'] !== 'N/A' && !empty($initial_variation['sku']) ? $initial_variation['sku'] : $initial_sku;
        $initial_description = !empty($initial_variation['description']) ? $initial_variation['description'] : $base_product_description;
        $initial_size_name = $initial_variation['size_name'];
        if ($initial_variation['image']) {
             $initial_image = $initial_variation['image'];
        }
    }
} else {
    // No variations, use base product details already set
    $has_variations = false;
    // Base price, quantity, description already assigned
    // $initial_quantity remains the base product quantity
}

// --- Check Cart Status for Initial Item (Product + Variation + Color) ---
$item_identifier_for_cart_check = $product_id . '-' . ($initial_variation_id ?? '0') . '-' . ($initial_selected_color_id ?? '0');
if (isset($_SESSION['cart_identifiers']) && is_array($_SESSION['cart_identifiers'])) {
    if (in_array($item_identifier_for_cart_check, $_SESSION['cart_identifiers'])) {
        $in_cart = true;
    }
}
// Note: We'll manage the actual cart check primarily in JS using local storage now.
// The session check here is a basic initial check.


// Determine initial button state
if ($in_cart) { // Basic session check might be insufficient, JS will refine this
    $cart_button_text = 'Already in Cart';
    $cart_button_disabled = true;
} elseif ($has_colors && !$initial_selected_color_id) {
     $cart_button_text = 'Select Color'; // Need color selection first
     $cart_button_disabled = true;
} elseif ($has_variations && !$initial_variation_id && !empty($variations_data)) {
    $cart_button_text = 'Select Option'; // Need variation selection first
    $cart_button_disabled = true;
} elseif ($initial_quantity > 0) {
    $cart_button_text = 'Add to Cart';
    $cart_button_disabled = false; // Will be re-evaluated by JS based on cart & stock
} else {
    $cart_button_text = 'Out of Stock';
    $cart_button_disabled = true;
}

// Calculate initial total price (No shipping, installation added by JS)
$initial_total = $initial_price;

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <title><?php echo htmlspecialchars($product['p_name'] ?? 'Product Details'); ?> <?php echo $initial_variation_name ? '| ' . $initial_variation_name : ''; ?> | SMART Security</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">

  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <link rel="stylesheet" href="css/product.css" />
  <style>
      /* Modern Mobile Cart Icon Styles */
      .mobile-cart-icon {
          display: none;
          position: relative;
      }

      .mobile-cart-icon a {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 12px;
          background: linear-gradient(145deg, #f8f9fa, #e6e7e8);
          box-shadow: 5px 5px 10px rgba(0,0,0,0.05),
                     -5px -5px 10px rgba(255,255,255,0.8);
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .mobile-cart-icon a:hover {
          transform: translateY(-2px);
          box-shadow: 6px 6px 12px rgba(0,0,0,0.06),
                     -6px -6px 12px rgba(255,255,255,0.9);
      }

      .mobile-cart-icon a:active {
          transform: translateY(0);
          box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1),
                     inset -2px -2px 5px rgba(255,255,255,0.5);
      }

      .mobile-cart-icon i {
          color: #00c2ff;
          font-size: 1.25rem;
          transition: transform 0.3s ease;
      }

      .mobile-cart-icon a:hover i {
          transform: scale(1.1);
      }

      .mobile-cart-icon .cart-count {
          position: absolute;
          top: -5px;
          right: -5px;
          min-width: 20px;
          height: 20px;
          border-radius: 10px;
          background: #ff3366;
          color: white;
          font-size: 0.7rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 6px;
          box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          transition: all 0.3s ease;
      }

      /* Empty cart style */
      .mobile-cart-icon .cart-count:empty,
      .mobile-cart-icon .cart-count[innerHTML="0"] {
          transform: scale(0.8);
          opacity: 0.7;
      }

      /* Pulse animation */
      @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
      }

      .pulse-animation {
          animation: pulse 0.5s ease-in-out;
      }

      /* Header right controls container */
      .header-right-controls {
          display: flex;
          align-items: center;
      }

      @media (max-width: 768px) {
          .mobile-cart-icon {
              display: block;
              margin-right: 15px;
          }

          nav {
              justify-content: space-between;
          }

          .header-right-controls {
              display: flex;
              align-items: center;
          }
      }

      /* Basic styling for color swatches */
      .color-selector-group { margin-bottom: 20px; }
      .color-selector { display: flex; flex-wrap: wrap; gap: 10px; margin-top: 5px; }
      .color-option { display: inline-block; position: relative; }
      .color-radio {
          opacity: 0; /* Hide radio */
          position: absolute;
          width: 100%; height: 100%;
          top: 0; left: 0;
          cursor: pointer;
          margin: 0;
      }
      .color-swatch-label {
          display: block;
          width: 30px; /* Adjust size */
          height: 30px;
          border-radius: 50%;
          border: 2px solid #eee;
          cursor: pointer;
          transition: border-color 0.2s;
          position: relative; /* For pseudo-element */
      }
       .color-swatch-label::after { /* Optional: Checkmark for selected */
            content: '\f00c'; /* Font Awesome check icon */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            color: white; /* Or black depending on swatch color */
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.2s;
        }

      .color-radio:checked + .color-swatch-label {
          border-color: #333; /* Highlight selected */
          box-shadow: 0 0 5px rgba(0,0,0,0.3);
      }
       .color-radio:checked + .color-swatch-label::after {
           opacity: 1;
       }
       .color-radio:focus + .color-swatch-label { /* Accessibility */
           outline: 2px solid blue;
           outline-offset: 1px;
       }

      .color-name-display { /* To show selected color name */
          margin-top: 10px;
          font-size: 0.9em;
          color: #555;
      }
      .color-name-display strong { color: #000; }

      /* Quantity Control Styling */
      .quantity-control {
          display: flex;
          align-items: center;
          border: 1px solid #ddd;
          border-radius: 4px;
          overflow: hidden;
          width: fit-content;
      }

      .quantity-btn {
          background-color: #f5f5f5;
          border: none;
          color: #333;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: background-color 0.2s;
      }

      .quantity-btn:hover {
          background-color: #e0e0e0;
      }

      .quantity-btn:disabled {
          color: #ccc;
          cursor: not-allowed;
      }

      .quantity-display {
          padding: 0 15px;
          min-width: 40px;
          text-align: center;
          font-weight: bold;
      }

      .group-label {
             display: block;
             margin-bottom: 8px;
             font-weight: bold;
             color: #333;
             font-size: 0.95em;
         }

      /* Add these new styles */
      .main-image-container {
          position: relative;
          cursor: pointer;
      }

      .main-image-container::after {
          content: 'Click to view more photos';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 8px;
          text-align: center;
          font-size: 14px;
          opacity: 0;
          transition: opacity 0.3s ease;
      }

      .main-image-container:hover::after {
          opacity: 1;
      }

      .main-image-container::before {
          content: '\f002';  /* Changed to correct Font Awesome search icon */
          font-family: 'Font Awesome 6 Free';
          font-weight: 900;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          font-size: 24px;
          background: rgba(0, 0, 0, 0.7);
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 2;
      }

      .main-image-container:hover::before {
          opacity: 1;
      }

      .main-image-container:hover img {
          filter: brightness(0.9);
          transition: filter 0.3s ease;
      }

      /* Read More Feature Styles */
      .short-description-container {
          position: relative;
          max-height: 200px; /* Adjust this value to show approximately 10 lines */
          overflow: hidden;
          transition: max-height 0.5s ease;
      }

      .short-description-container.expanded {
          max-height: 2000px; /* Large enough to show all content */
      }

      .read-more-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 70px;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
          pointer-events: none;
          transition: opacity 0.3s ease;
      }

      .short-description-container.expanded .read-more-overlay {
          opacity: 0;
      }

      .read-more-btn {
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          background: #007bff;
          color: white;
          border: none;
          border-radius: 20px;
          padding: 5px 15px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
          z-index: 2;
      }

      .read-more-btn:hover {
          background: #0056b3;
          transform: translateX(-50%) translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      /* Button text is changed via JavaScript */
  </style>
</head>
<body>

<header>
    <div class="container">
        <nav>
            <a href="index.php" class="logo">SMART LIFE<span>.</span></a>

            <!-- Desktop Navigation Links -->
            <ul class="nav-links" id="navLinks">
                <li><a href="index.php">Home</a></li>
                <li><a href="index.php#about">About</a></li>
                <li><a href="index.php#products">Products</a></li>
                <li><a href="all_products.php">All Products</a></li>
                <li><a href="index.php#gallery">Best Deals</a></li>
                <li><a href="index.php#contact">Contact</a></li>
                <li class="cart-icon">
                    <a href="cart.php" aria-label="View Shopping Cart">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count" id="cartCount">0</span>
                    </a>
                </li>
            </ul>

            <div class="header-right-controls">
                <!-- Mobile Cart Icon -->
                <div class="mobile-cart-icon">
                    <a href="cart.php" aria-label="View Shopping Cart">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count" id="mobileCartCount">0</span>
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <div class="mobile-menu" id="mobileMenuBtn">
                    <div class="menu-btn"><span></span><span></span><span></span></div>
                </div>
            </div>
        </nav>
    </div>
</header>

<main>
    <div class="container">
        <?php if ($product): ?>
        <div class="product-detail-container">
            <div class="product-gallery">
                <div class="main-image-container"> <img id="mainImage" src="<?php echo $initial_image; ?>" alt="<?php echo htmlspecialchars($product['p_name']); ?>" class="main-image" onerror="this.onerror=null; this.src='images/default_product.png';"> </div>
                 <div class="thumbnail-container"> <?php $displayed_thumb_images = []; $base_featured_img_path = !empty($product['p_featured_photo']) ? $upload_path_featured . htmlspecialchars($product['p_featured_photo']) : null; if ($base_featured_img_path && !in_array($base_featured_img_path, $displayed_thumb_images) && !array_key_exists($base_featured_img_path, $variation_images_map) ) { $is_active = ($initial_image == $base_featured_img_path); $data_variation_id_attr = ''; if ($has_variations && !$initial_variation && $is_active) { $data_variation_id_attr = ''; } elseif ($initial_variation && !$initial_variation['image'] && $is_active) { $data_variation_id_attr = ' data-variation-id="' . $initial_variation['id'] . '"'; } echo '<img src="' . $base_featured_img_path . '" alt="Featured Thumbnail" class="thumbnail' . ($is_active ? ' active' : '') . '" data-image="' . $base_featured_img_path . '"' . $data_variation_id_attr . ' onerror="this.style.display=\'none\'">'; $displayed_thumb_images[] = $base_featured_img_path; } foreach ($variations_json as $var_id => $var_data): if ($var_data['image'] && !in_array($var_data['image'], $displayed_thumb_images)): $is_active = ($initial_image == $var_data['image']); echo '<img src="' . $var_data['image'] . '" alt="Variation ' . htmlspecialchars($var_data['name']) . '" class="thumbnail' . ($is_active ? ' active' : '') . '" data-image="' . $var_data['image'] . '" data-variation-id="' . $var_id . '" onerror="this.style.display=\'none\'">'; $displayed_thumb_images[] = $var_data['image']; endif; endforeach; foreach ($product_photos as $index => $photo): if (!empty($photo['photo_name'])) { $gallery_img_path = $upload_path_gallery . htmlspecialchars($photo['photo_name']); if (!in_array($gallery_img_path, $displayed_thumb_images)): echo '<img src="' . $gallery_img_path . '" alt="Gallery Thumbnail ' . ($index + 1) . '" class="thumbnail" data-image="' . $gallery_img_path . '" onerror="this.style.display=\'none\'">'; $displayed_thumb_images[] = $gallery_img_path; endif; } endforeach; ?> </div>
            </div>

            <div class="product-info">
                <h1 class="product-title"><?php echo htmlspecialchars($product['p_name']); ?></h1>
                <div class="product-price" id="productPrice">Tsh <?php echo number_format($initial_price, 0); ?></div>

                <div class="product-meta">
                    <span>Availability: <strong id="productAvailability" style="color: <?php echo ($initial_quantity > 0) ? '#1cc88a' : '#e74a3b'; ?>"><?php echo ($initial_quantity > 0) ? 'In Stock' : 'Out of Stock'; ?></strong></span>
                    <span id="productSku">SKU: <?php echo $initial_sku; ?></span>
                    <?php if (!empty($product_category_name)): ?>
                        <span>Category: <?php echo $product_category_name; ?></span>
                    <?php endif; ?>
                </div>

                 <div class="variation-attributes">
                     <span id="selectedColorDisplay" class="color-name-display" style="<?php echo $has_colors ? 'display: block;' : 'display: none;'; ?>">
                        Color: <strong id="selectedColorName"><?php echo $initial_selected_color_name ?? 'N/A'; ?></strong>
                    </span>
                    <span id="variationSizeDisplay" style="<?php echo $initial_size_name ? 'display: inline-flex;' : 'display: none;'; ?>">
                        Size: <strong id="variationSizeName"><?php echo $initial_size_name ?? ''; ?></strong>
                    </span>
                </div>


                <div class="product-description-container">
                    <?php if (!empty($base_product_description)): ?> <h2>Product Description</h2> <div class="description-content base-description" id="baseDescription"><?php echo $base_product_description; ?></div> <?php endif; ?>
                    <div id="variationDescriptionContainer" style="display: <?php echo ($initial_variation && !empty($initial_variation['description'])) ? 'block' : 'none'; ?>;"> <div class="description-content variation-description" id="variationDescription"><?php echo $initial_variation ? ($initial_variation['description'] ?? '') : ''; ?></div> </div>
                    <?php if (!empty($base_product_short_description)): ?>
                        <h2 style="margin-top: 20px;">Key Features</h2>
                        <div class="description-content short-description-container">
                            <div class="short-description-content"><?php echo $base_product_short_description; ?></div>
                            <div class="read-more-overlay"></div>
                            <button class="read-more-btn">Read More</button>
                        </div>
                    <?php endif; ?>
                </div>

                <button id="openDetailsModalBtn" class="details-modal-trigger">View Specifications & Details</button>
                <input type="hidden" id="selectedVariationId" name="variation_id" value="<?php echo $initial_variation_id ?? ''; ?>">
                <input type="hidden" id="selectedColorId" name="color_id" value="<?php echo $initial_selected_color_id ?? ''; ?>">


                <div class="options-section">
                     <?php if ($has_colors): ?>
                    <div class="color-selector-group">
                        <label class="group-label">Select Color:</label>
                        <div class="color-selector" id="colorSelector">
                            <?php foreach ($product_colors as $color_id => $color_data): ?>
                                <div class="color-option" title="<?php echo $color_data['name']; ?>">
                                    <input type="radio" id="color-<?php echo $color_id; ?>"
                                           name="selected_color_id_selector"
                                           value="<?php echo $color_id; ?>"
                                           class="color-radio"
                                           <?php echo ($initial_selected_color_id == $color_id) ? 'checked' : ''; ?>>
                                    <label for="color-<?php echo $color_id; ?>"
                                           class="color-swatch-label"
                                           style="background-color: <?php echo $color_data['code'] ?: '#ffffff'; ?>; border-color: <?php echo ($color_data['code'] && strtolower($color_data['code']) == '#ffffff') ? '#ccc' : 'transparent'; ?>;"
                                           aria-label="<?php echo $color_data['name']; ?>">
                                           </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="variation-selector-group variation-name-container" style="<?php echo $has_variations ? 'display:block;' : 'display:none;'; ?>">
                        <?php if ($has_variations): ?>
                            <label class="group-label">Select Option:</label>
                            <div class="variation-name-selector" id="variationNameSelector">
                                <?php foreach ($available_variation_names as $var_id => $var_name): ?>
                                    <div class="variation-name-option">
                                        <input type="radio" id="variation-<?php echo $var_id; ?>"
                                               name="variation_id_selector" value="<?php echo $var_id; ?>"
                                               class="variation-name-radio"
                                               <?php echo ($initial_variation_id == $var_id) ? 'checked' : ''; ?>>
                                        <label for="variation-<?php echo $var_id; ?>" class="variation-name-label">
                                            <?php echo $var_name; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>


                    <div class="quantity-selector">
                        <label for="quantity">Quantity:</label>
                        <div class="quantity-control">
                            <button type="button" class="quantity-btn minus-btn" aria-label="Decrease quantity">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span id="quantity-display" class="quantity-display">1</span>
                            <input type="hidden" id="quantity" name="quantity" value="1" min="1"
                                   max="<?php echo max(1, $initial_quantity); ?>"
                                   <?php echo ($initial_quantity <= 0) ? 'disabled' : ''; ?>>
                            <button type="button" class="quantity-btn plus-btn" aria-label="Increase quantity">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                     <div class="additional-options">
                        <?php if($installation_fee > 0): ?>
                            <div class="fee-option">
                                <input type="checkbox" id="installation" name="installation">
                                <label for="installation">Professional Installation</label>
                                <span class="fee-amount" data-fee="<?php echo $installation_fee; ?>">+ Tsh <?php echo number_format($installation_fee, 0); ?></span>
                                <!-- Debug: Show the installation fee source -->
                                <span style="display: none;">Fee source: <?php echo isset($product['installation_fee']) ? 'Product-specific' : 'Default'; ?></span>
                            </div>
                         <?php endif; ?>
                         </div>
                 </div>


                <div class="price-action-section">
                     <div class="total-price">
                         <span class="label">Total:</span>
                         <span class="amount" id="totalPrice">
                             Tsh <?php echo number_format($initial_total, 0); ?>
                         </span>
                     </div>

                    <button class="add-to-cart-btn" id="addToCartBtn" <?php echo $cart_button_disabled ? 'disabled' : ''; ?>>
                         <span class="btn-icon"> <?php /* Icon logic */ if ($in_cart) { echo '<i class="fas fa-check"></i>'; } elseif ($cart_button_disabled && !in_array($cart_button_text, ['Select Option', 'Select Color'])) { echo '<i class="fas fa-times-circle"></i>'; } elseif (in_array($cart_button_text, ['Select Option', 'Select Color'])) { echo '<i class="fas fa-info-circle"></i>'; } else { echo '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>'; } ?> </span>
                         <span class="btn-text"><?php echo $cart_button_text; ?></span>
                     </button>

                     <a href="cart.php" class="return-to-cart">View Cart & Checkout →</a>
                 </div>
            </div>
        </div> <?php else: ?>
             <div style="text-align: center; padding: 50px; background: #fff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);"> <h2>Product Not Found</h2> <p>Sorry, the product you are looking for is not available or could not be loaded.</p> <a href="index.php" style="color: var(--secondary, #6c757d); text-decoration: none; font-weight: 600;">← Return to Home</a> </div>
        <?php endif; ?>
    </div> </main>

<footer>
    <div class="container"> <div class="footer-bottom"> <div class="copyright"><?php echo $footer_copyright; ?></div> </div> </div>
</footer>

<div id="detailsModal" class="modal">
 <div class="modal-content">
 <button class="modal-close-btn" id="closeDetailsModalBtn" aria-label="Close Details"> <i class="fas fa-times"></i> </button>
   <h2>Product Specifications & Details</h2>
   <?php if ($product): ?>
       <div class="modal-section"> <h4>Product Name</h4> <div class="modal-text-content"><?php echo htmlspecialchars($product['p_name']); ?></div> </div>
       <?php if (!empty($product['p_feature'])): ?> <div class="modal-section"> <h4>Features</h4> <div class="modal-text-content"><?php echo $product['p_feature']; ?></div> </div> <?php endif; ?>
       <?php if (!empty($product['p_condition'])): ?> <div class="modal-section"> <h4>Technical Specifications</h4> <div class="modal-text-content"><?php echo $product['p_condition']; ?></div> </div> <?php endif; ?>
       <?php if (!empty($product['p_return_policy'])): ?> <div class="modal-section"> <h4>Return Policy</h4> <div class="modal-text-content"><?php echo $product['p_return_policy']; ?></div> </div> <?php endif; ?>
       <div class="modal-section"> <h4>Availability & Base Info</h4> <p><strong>Base Stock Quantity:</strong> <?php echo (int)$product['p_qty']; ?></p> <?php if($has_variations): ?> <p><em>Note: Price and stock may vary based on selected options. See main page for specific variation details.</em></p> <?php endif; ?> <?php if($has_colors): ?> <p><em>Available colors can be selected on the main page.</em></p> <?php endif; ?> </div>
   <?php else: ?>
       <p>Details could not be loaded.</p>
   <?php endif; ?>
 </div>
</div>

<!-- Photo Gallery Modal -->
<div id="galleryModal" class="gallery-modal">
    <div class="gallery-modal-content">
        <div class="gallery-header">
            <h3>Product Gallery</h3>
            <button class="gallery-close" aria-label="Close Gallery">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="gallery-body">
            <div class="gallery-main">
                <button class="gallery-nav gallery-prev" aria-label="Previous Photo">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="gallery-image-container">
                    <img id="galleryMainImage" src="" alt="" class="gallery-main-image">
                </div>
                <button class="gallery-nav gallery-next" aria-label="Next Photo">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="gallery-thumbnails">
                <?php
                $all_images = [];

                // Add featured image if exists
                if (!empty($product['p_featured_photo'])) {
                    $all_images[] = [
                        'src' => $upload_path_featured . htmlspecialchars($product['p_featured_photo']),
                        'alt' => 'Featured Image'
                    ];
                }

                // Add variation images
                foreach ($variations_json as $var_data) {
                    if (!empty($var_data['image'])) {
                        $all_images[] = [
                            'src' => $var_data['image'],
                            'alt' => 'Variation: ' . htmlspecialchars($var_data['name'])
                        ];
                    }
                }

                // Add gallery photos
                foreach ($product_photos as $photo) {
                    if (!empty($photo['photo_name'])) {
                        $all_images[] = [
                            'src' => $upload_path_gallery . htmlspecialchars($photo['photo_name']),
                            'alt' => 'Gallery Image'
                        ];
                    }
                }

                // Output thumbnails
                foreach ($all_images as $index => $image) {
                    echo '<div class="gallery-thumbnail' . ($index === 0 ? ' active' : '') . '" data-index="' . $index . '">';
                    echo '<img src="' . $image['src'] . '" alt="' . $image['alt'] . '" loading="lazy">';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<style>
/* Gallery Modal Styles */
.gallery-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
}

.gallery-modal.show {
    display: flex;
}

.gallery-modal-content {
    width: 90%;
    max-width: 900px;
    height: 95vh;
    margin: auto;
    background: #fff;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border-bottom: 1px solid #eee;
}

.gallery-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: #333;
}

.gallery-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.gallery-close:hover {
    background: #f5f5f5;
}

.gallery-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.gallery-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: #fff;
    padding: 1rem;
}

.gallery-image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-main-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: #333;
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.gallery-nav:hover {
    background: #fff;
}

.gallery-prev {
    left: 1rem;
}

.gallery-next {
    right: 1rem;
}

.gallery-thumbnails {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    background: #fff;
    border-top: 1px solid #eee;
}

.gallery-thumbnail {
    flex: 0 0 80px;
    height: 80px;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
}

.gallery-thumbnail.active {
    border-color: #007bff;
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

@media (max-width: 768px) {
    .gallery-modal-content {
        width: 100%;
        height: 100vh;
        border-radius: 0;
        margin: 0;
    }

    .gallery-nav {
        width: 35px;
        height: 35px;
        font-size: 1.25rem;
    }

    .gallery-thumbnail {
        flex: 0 0 60px;
        height: 60px;
    }

    .gallery-header {
        padding: 0.75rem;
    }

    .gallery-header h3 {
        font-size: 1.1rem;
    }

    .gallery-body {
        padding: 0.5rem;
    }

    .gallery-thumbnails {
        padding: 0.5rem;
        gap: 0.25rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update cart count immediately when DOM is loaded
    setTimeout(() => {
        updateCartCountDisplay();
    }, 100);

    // --- DOM Element References ---
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');
    const thumbnailContainer = document.querySelector('.thumbnail-container');
    const productPriceElement = document.getElementById('productPrice');
    const availabilityElement = document.getElementById('productAvailability');
    const skuElement = document.getElementById('productSku');
    const quantityInput = document.getElementById('quantity');
    const quantityDisplay = document.getElementById('quantity-display');
    const minusBtn = document.querySelector('.minus-btn');
    const plusBtn = document.querySelector('.plus-btn');
    const addToCartBtn = document.getElementById('addToCartBtn');
    const addToCartBtnIcon = addToCartBtn?.querySelector('.btn-icon');
    const addToCartBtnText = addToCartBtn?.querySelector('.btn-text');
    const totalPriceElement = document.getElementById('totalPrice');
    const variationNameSelectorContainer = document.getElementById('variationNameSelector'); // For Sizes/Options
    const colorSelectorContainer = document.getElementById('colorSelector'); // NEW: For Colors
    const baseDescriptionElement = document.getElementById('baseDescription');
    const variationDescriptionElement = document.getElementById('variationDescription');
    const variationSizeDisplay = document.getElementById('variationSizeDisplay');
    const variationSizeName = document.getElementById('variationSizeName');
    const selectedColorDisplay = document.getElementById('selectedColorDisplay'); // NEW: Display selected color name
    const selectedColorNameElement = document.getElementById('selectedColorName'); // NEW
    const selectedVariationIdInput = document.getElementById('selectedVariationId');
    const selectedColorIdInput = document.getElementById('selectedColorId'); // NEW
    const installationCheckbox = document.getElementById('installation');
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const navLinks = document.getElementById('navLinks');
    const cartCountElement = document.getElementById('cartCount');

    // --- Basic Check ---
    if (!document.querySelector('.product-detail-container')) { console.error("Product detail container not found."); return; }

    // --- Data from PHP ---
    let variationsData = {}; // Variations (e.g., Size) - NO color info here
    try {
        const variationsJsonString = '<?php echo json_encode($variations_json ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE); ?>';
        if (variationsJsonString) variationsData = JSON.parse(variationsJsonString) || {};
    } catch (e) { console.error("Error parsing variations JSON:", e); displayErrorMessage("Error loading product options."); }

    let productColorsData = {}; // NEW: All available colors for the product
    try {
        // Use JSON_FORCE_OBJECT to handle potential empty arrays correctly
        const colorsJsonString = '<?php echo json_encode($product_colors_json ?? [], JSON_FORCE_OBJECT | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE); ?>';
        if (colorsJsonString) productColorsData = JSON.parse(colorsJsonString) || {};
    } catch (e) { console.error("Error parsing colors JSON:", e); displayErrorMessage("Error loading color options."); }


    const baseProductId = <?php echo $product_id; ?>;
    const baseSku = <?php echo json_encode($product['p_id'] ?? 'N/A'); ?>;
    const baseProductFeaturedImage = <?php echo json_encode(!empty($product['p_featured_photo']) ? $upload_path_featured . htmlspecialchars($product['p_featured_photo']) : 'images/default_product.png'); ?>;
    const baseProductPrice = <?php echo isset($product['p_current_price']) && is_numeric($product['p_current_price']) ? (float)$product['p_current_price'] : 0; ?>;
    const baseProductQuantity = <?php echo isset($product['p_qty']) ? (int)$product['p_qty'] : 0; ?>;
    const baseProductDescHTML = <?php echo json_encode($base_product_description ?? ''); ?>;
    const installationFee = <?php echo is_numeric($installation_fee) ? (float)$installation_fee : 0; ?>;
    const hasVariations = Object.keys(variationsData).length > 0;
    const hasColors = Object.keys(productColorsData).length > 0;

    // Debug: Log the installation fee
    console.log("Installation fee:", installationFee);

    // --- State Variables ---
    let currentSelectedVariation = variationsData[selectedVariationIdInput?.value] || null;
    let currentSelectedColorId = selectedColorIdInput?.value || null; // NEW

    // --- Helper Functions ---
    function displayErrorMessage(message) { /* ... same ... */ const infoDiv=document.querySelector('.product-info'); if(infoDiv){ const errorP=document.createElement('p'); errorP.style.color='red'; errorP.style.fontWeight='bold'; errorP.textContent=message; infoDiv.insertBefore(errorP, infoDiv.firstChild); }}
    function formatPrice(amount) { /* ... same ... */ const numericAmount=Number(amount); if(isNaN(numericAmount)){ console.warn("formatPrice non-numeric:", amount); return 'Tsh --'; } return 'Tsh ' + numericAmount.toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0}); }


    function updateDisplay() {
        let priceToShow = baseProductPrice;
        let quantityToShow = baseProductQuantity;
        let skuToShow = baseSku;
        let imageToShow = baseProductFeaturedImage;
        let descriptionToShow = baseProductDescHTML;
        let showVariationDescription = false;
        let variationDescriptionContent = '';
        let sizeNameToShow = null;

        // 1. Apply Variation Changes (if a variation is selected)
        if (currentSelectedVariation) {
            priceToShow = currentSelectedVariation.price;
            quantityToShow = currentSelectedVariation.quantity; // Variation quantity overrides base
            skuToShow = (currentSelectedVariation.sku !== 'N/A' && currentSelectedVariation.sku) ? currentSelectedVariation.sku : baseSku;
            if(currentSelectedVariation.image) imageToShow = currentSelectedVariation.image;
            if(currentSelectedVariation.description) {
                descriptionToShow = currentSelectedVariation.description;
                showVariationDescription = true;
                variationDescriptionContent = currentSelectedVariation.description;
            }
            sizeNameToShow = currentSelectedVariation.size_name; // Get size from variation
        }

        // 2. Update DOM Elements
        if (mainImage && mainImage.src !== imageToShow) {
            mainImage.src = imageToShow;
            thumbnails?.forEach(thumb => thumb.classList.toggle('active', thumb.dataset.image === imageToShow));
        }
        if (productPriceElement) productPriceElement.textContent = formatPrice(priceToShow);
        if (skuElement) skuElement.textContent = 'SKU: ' + skuToShow;

        const isInStock = quantityToShow > 0;
        if (availabilityElement) {
            availabilityElement.textContent = isInStock ? 'In Stock' : 'Out of Stock';
            availabilityElement.style.color = isInStock ? '#1cc88a' : '#e74a3b';
        }
        if (quantityInput) {
            quantityInput.max = quantityToShow > 0 ? quantityToShow : 1; // Max is current stock
            quantityInput.disabled = !isInStock;
            quantityInput.min = isInStock ? 1 : 0;
            let currentQty = parseInt(quantityInput.value);
            if (!isInStock || isNaN(currentQty) || currentQty < 1 || currentQty > quantityToShow) {
                 quantityInput.value = isInStock ? 1 : 0; // Reset to 1 if possible, else 0
            }
             if (!isInStock) quantityInput.value = 0;
             else if (parseInt(quantityInput.value) < 1) quantityInput.value = 1;
        }

        // Update descriptions
        if (variationDescriptionElement) {
             variationDescriptionElement.innerHTML = variationDescriptionContent;
             // Show container based on content presence
             document.getElementById('variationDescriptionContainer').style.display = showVariationDescription && variationDescriptionContent.trim() !== '' ? 'block' : 'none';
        }
        if (baseDescriptionElement) {
             // Optionally hide base description if variation description is shown and has content
             // baseDescriptionElement.style.display = (showVariationDescription && variationDescriptionContent.trim() !== '') ? 'none' : 'block';
             if(!(showVariationDescription && variationDescriptionContent.trim() !== '')) {
                 baseDescriptionElement.innerHTML = baseProductDescHTML; // Ensure base is shown if no variation desc
             }
        }


        // Update Size Display
        if (variationSizeDisplay && variationSizeName) {
            if (sizeNameToShow) {
                variationSizeName.textContent = sizeNameToShow;
                variationSizeDisplay.style.display = 'inline-flex';
            } else {
                variationSizeDisplay.style.display = 'none';
            }
        }

        // Update Selected Color Display
        if (selectedColorDisplay && selectedColorNameElement && currentSelectedColorId && productColorsData[currentSelectedColorId]) {
            selectedColorNameElement.textContent = productColorsData[currentSelectedColorId].name;
            selectedColorDisplay.style.display = 'block';
        } else if (selectedColorDisplay) {
            selectedColorDisplay.style.display = 'none';
        }

        // Always update button state and total price
        const alreadyInCart = checkItemInCart(baseProductId, currentSelectedVariation?.id, currentSelectedColorId);
        updateAddToCartButton(isInStock, alreadyInCart);
        calculateAndUpdateTotal();
    }


    function updateAddToCartButton(isInStock, isAlreadyInCart) {
        if (!addToCartBtn || !addToCartBtnText || !addToCartBtnIcon) return;
        let text = '', iconHtml = '', disabled = true;

        if (isAlreadyInCart) {
            text = 'Already in Cart'; iconHtml = '<i class="fas fa-check"></i>'; disabled = true;
        } else if (!isInStock) {
            text = 'Out of Stock'; iconHtml = '<i class="fas fa-times-circle"></i>'; disabled = true;
        } else if (hasColors && !currentSelectedColorId) {
             text = 'Select Color'; iconHtml = '<i class="fas fa-info-circle"></i>'; disabled = true;
        } else if (hasVariations && !currentSelectedVariation) {
             text = 'Select Option'; iconHtml = '<i class="fas fa-info-circle"></i>'; disabled = true;
        } else {
            text = 'Add to Cart'; iconHtml = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>'; disabled = false;
        }

        addToCartBtnText.textContent = text;
        addToCartBtnIcon.innerHTML = iconHtml;
        addToCartBtn.disabled = disabled;
    }

    function calculateAndUpdateTotal() {
        if (!totalPriceElement || !quantityInput) return;
        let quantity = parseInt(quantityInput.value);
        if (quantityInput.disabled || isNaN(quantity) || quantity < 0) quantity = 0;
        else if (quantity < 1 && !quantityInput.disabled) quantity = 1;

        let unitPrice = currentSelectedVariation ? currentSelectedVariation.price : baseProductPrice;
        let currentTotal = unitPrice * quantity;

        if (installationCheckbox && installationCheckbox.checked) {
            currentTotal += installationFee;
        }
        totalPriceElement.textContent = formatPrice(currentTotal);
    }

    // --- Local Storage Cart Functions (Updated Check) ---
    function getCartFromLocalStorage() { /* ... same ... */ try { const d = localStorage.getItem('cart'); return d ? JSON.parse(d) : []; } catch (e) { console.error("LS Cart Read Error:", e); return []; } }
    function saveCartToLocalStorage(cart) { /* ... same ... */ try { localStorage.setItem('cart', JSON.stringify(cart)); } catch (e) { console.error("LS Cart Save Error:", e); } }
    function updateCartCountDisplay() {
        const cart = getCartFromLocalStorage();
        const totalItems = cart.reduce((total, item) => total + (parseInt(item.quantity) || 0), 0);

        // Update desktop cart counter
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            cartCountElement.textContent = totalItems;
        }

        // Update mobile cart counter
        const mobileCartCount = document.getElementById('mobileCartCount');
        if (mobileCartCount) {
            mobileCartCount.textContent = totalItems;

            // Add animation effect when count changes
            mobileCartCount.classList.add('pulse-animation');
            setTimeout(() => {
                mobileCartCount.classList.remove('pulse-animation');
            }, 1000);
        }

        console.log("Cart count updated to:", totalItems);
    }

    // --- MODIFIED: Check includes color_id ---
    function checkItemInCart(productId, variationId = null, colorId = null) {
        let cart = getCartFromLocalStorage();
        const productIdStr = String(productId);
        const varIdToCheck = variationId ? parseInt(variationId) : null;
        const colorIdToCheck = colorId ? parseInt(colorId) : null;

        return cart.some(item => {
             const itemProductIdStr = String(item.product_id);
             const itemVariationId = item.variation_id ? parseInt(item.variation_id) : null;
             const itemColorId = item.color_id ? parseInt(item.color_id) : null; // Check color_id from stored item

             return itemProductIdStr === productIdStr &&
                    itemVariationId === varIdToCheck &&
                    itemColorId === colorIdToCheck; // Match all three
        });
    }


    // --- Quantity Control Functions ---
    function updateQuantityControls() {
        const max = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
        const currentQty = parseInt(quantityInput.value);

        // Update min/max buttons state
        minusBtn.disabled = currentQty <= 1;
        plusBtn.disabled = currentQty >= max;

        // Update display
        quantityDisplay.textContent = currentQty;
    }

    // Quantity button event listeners
    if (minusBtn) {
        minusBtn.addEventListener('click', function() {
            const currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
                // Trigger change event to update calculations
                quantityInput.dispatchEvent(new Event('change'));
                updateQuantityControls();
            }
        });
    }

    if (plusBtn) {
        plusBtn.addEventListener('click', function() {
            const currentValue = parseInt(quantityInput.value);
            const max = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
            if (currentValue < max) {
                quantityInput.value = currentValue + 1;
                // Trigger change event to update calculations
                quantityInput.dispatchEvent(new Event('change'));
                updateQuantityControls();
            }
        });
    }

    // --- Read More Feature ---
    const readMoreContainer = document.querySelector('.short-description-container');
    const readMoreBtn = document.querySelector('.read-more-btn');

    if (readMoreBtn && readMoreContainer) {
        readMoreBtn.addEventListener('click', function() {
            const isExpanded = readMoreContainer.classList.contains('expanded');

            if (isExpanded) {
                // Collapse
                readMoreContainer.classList.remove('expanded');
                readMoreBtn.textContent = 'Read More';

                // Scroll back to the top of the container
                readMoreContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            } else {
                // Expand
                readMoreContainer.classList.add('expanded');
                readMoreBtn.textContent = 'Show Less';
            }
        });
    }

    // --- Event Listener Setup ---

    // Variation Selection (e.g., Size)
    if (variationNameSelectorContainer) {
        variationNameSelectorContainer.addEventListener('change', function(event) {
            if (event.target.type === 'radio' && event.target.classList.contains('variation-name-radio')) {
                const selectedVarId = event.target.value;

                // Force reset quantity to 1 immediately when variation changes
                if (quantityInput) {
                    quantityInput.value = 1;
                    // Update the display to show 1
                    if (quantityDisplay) {
                        quantityDisplay.textContent = '1';
                    }

                    // Reset button states explicitly
                    if (minusBtn) {
                        minusBtn.disabled = true; // Disable minus button when quantity is 1
                    }
                    if (plusBtn) {
                        plusBtn.disabled = false; // Always enable plus button for new variation
                    }
                }

                currentSelectedVariation = variationsData[selectedVarId] || null;
                if(selectedVariationIdInput) selectedVariationIdInput.value = currentSelectedVariation ? currentSelectedVariation.id : '';

                // Update display first
                updateDisplay();

                // Force update quantity controls after display update to ensure correct state
                updateQuantityControls();

                // Force recalculate total price
                calculateAndUpdateTotal();
            }
        });
    }

     // NEW: Color Selection
     if (colorSelectorContainer) {
         colorSelectorContainer.addEventListener('change', function(event) {
             if (event.target.type === 'radio' && event.target.classList.contains('color-radio')) {

                 // Force reset quantity to 1 immediately when color changes
                 if (quantityInput) {
                     quantityInput.value = 1;
                     // Update the display to show 1
                     if (quantityDisplay) {
                         quantityDisplay.textContent = '1';
                     }

                     // Reset button states explicitly
                     if (minusBtn) {
                         minusBtn.disabled = true; // Disable minus button when quantity is 1
                     }
                     if (plusBtn) {
                         plusBtn.disabled = false; // Always enable plus button for new color
                     }
                 }

                 currentSelectedColorId = event.target.value;
                 if(selectedColorIdInput) selectedColorIdInput.value = currentSelectedColorId;

                 // Update display first
                 updateDisplay();

                 // Force update quantity controls after display update to ensure correct state
                 updateQuantityControls();

                 // Force recalculate total price
                 calculateAndUpdateTotal();
             }
         });
     }
    // Thumbnail Clicks
    if (thumbnailContainer) {
        thumbnailContainer.addEventListener('click', function(event) {
            const targetThumbnail = event.target.closest('.thumbnail');
            if (!targetThumbnail) return;

            const newImageSrc = targetThumbnail.dataset.image;
            const variationId = targetThumbnail.dataset.variationId; // Thumbnails linked to variations

            // Update main image
            if (mainImage && newImageSrc && mainImage.src !== newImageSrc) {
                 mainImage.src = newImageSrc;
                 thumbnails?.forEach(t => t.classList.toggle('active', t.dataset.image === newImageSrc));
            }

            // If thumbnail is linked to a variation, select that variation's radio button
            if (variationId && variationsData[variationId]) {
                const clickedVariation = variationsData[variationId];
                const correspondingRadio = variationNameSelectorContainer?.querySelector(`input[value="${variationId}"]`);
                if (correspondingRadio && !correspondingRadio.checked) {
                    correspondingRadio.checked = true;
                    // Update state and trigger full display update
                    currentSelectedVariation = clickedVariation;
                     if(selectedVariationIdInput) selectedVariationIdInput.value = currentSelectedVariation ? currentSelectedVariation.id : '';
                     updateDisplay();
                } else if (correspondingRadio?.checked && currentSelectedVariation?.id != variationId) {
                    // It's checked but state is somehow out of sync
                     currentSelectedVariation = clickedVariation;
                     if(selectedVariationIdInput) selectedVariationIdInput.value = currentSelectedVariation ? currentSelectedVariation.id : '';
                     updateDisplay();
                }
            }
            // Note: If the clicked thumbnail is NOT linked to a specific variation
            // (e.g., it's a gallery image or the base featured image),
            // we DON'T change the selected variation radio button.
            // The `updateDisplay()` function handles showing the correct image regardless.
        });
    }

    // Quantity & Installation Changes
    quantityInput?.addEventListener('input', calculateAndUpdateTotal); // Price calculation only
    quantityInput?.addEventListener('change', calculateAndUpdateTotal);
    installationCheckbox?.addEventListener('change', calculateAndUpdateTotal);


    // Add to Cart Button Click (Updated to send color_id and use selected color in local storage)
    addToCartBtn?.addEventListener('click', function() {
        const variationId = currentSelectedVariation?.id || null;
        // <<<< MODIFIED: Get color_id from the hidden input >>>>
        const colorId = document.getElementById('selectedColorId')?.value || null;
        const quantity = parseInt(quantityInput?.value);

        // --- Validation ---
        if (hasColors && !colorId) { // Check if color selection is required and missing
            Swal.fire('Color Required', 'Please select a color.', 'warning');
            return;
        }
        if (hasVariations && !variationId) { // Check if variation selection is required and missing
            Swal.fire('Option Required', 'Please select a product option (e.g., size).', 'warning');
            return;
        }

        // Use correct quantity based on whether a variation is selected
        const stockToCheck = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;

        if (isNaN(quantity) || quantity < 1) { Swal.fire('Invalid Quantity', 'Please enter a quantity of at least 1.', 'warning'); return; }
        if (quantity > stockToCheck) { Swal.fire('Insufficient Stock', `Only ${stockToCheck} available. Please enter a lower quantity.`, 'warning'); return; }
        if (stockToCheck <= 0) { Swal.fire('Out of Stock', 'This item/option is currently out of stock.', 'error'); return; }
        // --- End Validation ---


        const formData = new FormData();
        formData.append('product_id', baseProductId);
        if (variationId) formData.append('variation_id', variationId);
        // <<<< MODIFIED: Send color_id >>>>
        if (colorId) formData.append('color_id', colorId);
        formData.append('quantity', quantity);
        formData.append('installation', (installationCheckbox && installationCheckbox.checked) ? '1' : '0');
        // formData.append('csrf_token', '...');

        // --- Button loading state ---
        addToCartBtn.disabled = true;
        if(addToCartBtnIcon && addToCartBtnText){ addToCartBtnIcon.innerHTML = '<span class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></span>'; addToCartBtnText.textContent = 'Adding...'; }

        const addToCartURL = 'add_to_cart.php'; // !!! CHECK PATH !!!
        console.log("Attempting add to cart:", addToCartURL, Object.fromEntries(formData));

        fetch(addToCartURL, { method: 'POST', body: formData })
        .then(response => { /* Error handling */ if(!response.ok){ return response.text().then(text => { throw new Error(`Server error (${response.status}): ${text.substring(0,200)}`) }); } return response.json(); })
        .then(data => {
            console.log("Parsed JSON response:", data);
            if (data && data.status === 'success' && data.added_item) {
                 const addedItem = data.added_item;
                 try {
                     let cart = getCartFromLocalStorage();
                     const addedProductIdStr = String(addedItem.product_id);
                     const addedVariationId = addedItem.variation_id ? parseInt(addedItem.variation_id) : null;
                     // <<<< MODIFIED: Get color_id from response >>>>
                     const addedColorId = addedItem.color_id ? parseInt(addedItem.color_id) : null;

                      // Find item by product, variation, AND color
                     let foundItemIndex = cart.findIndex(item =>
                         String(item.product_id) === addedProductIdStr &&
                         (item.variation_id ? parseInt(item.variation_id) : null) === addedVariationId &&
                         // <<<< MODIFIED: Match color too >>>>
                         (item.color_id ? parseInt(item.color_id) : null) === addedColorId
                     );

                     if (foundItemIndex > -1) {
                         // Update existing item
                         cart[foundItemIndex].quantity = addedItem.quantity; // Server returns NEW total quantity
                         cart[foundItemIndex].installation = addedItem.installation;
                         // Ensure color_id is also present if updating
                         if (addedColorId) cart[foundItemIndex].color_id = addedColorId;
                         console.log("LocalStorage item updated:", cart[foundItemIndex]);
                     } else {
                          // Add new item - use RECEIVED color info for storage
                         const newItemForLocalStorage = {
                             product_id: addedItem.product_id,
                             variation_id: addedVariationId,
                             color_id: addedColorId, // <<< STORE the color ID from response
                             quantity: addedItem.quantity,
                             price: addedItem.price ?? (currentSelectedVariation ? currentSelectedVariation.price : baseProductPrice),
                             name: addedItem.name ?? document.querySelector('.product-title')?.textContent ?? 'Product',
                             variation_name: addedItem.variation_name || (currentSelectedVariation ? currentSelectedVariation.name : null),
                             photo: addedItem.photo ?? mainImage?.src ?? 'images/default_product.png',
                             // <<<< MODIFIED: Use color_name from response/item data >>>>
                             color_name: addedItem.color_name,
                             size_name: addedItem.size_name || (currentSelectedVariation ? currentSelectedVariation.size_name : null),
                             installation: addedItem.installation ?? false
                         };
                         cart.push(newItemForLocalStorage);
                         console.log("LocalStorage item added:", newItemForLocalStorage);
                     }
                     saveCartToLocalStorage(cart);
                     updateCartCountDisplay();
                 } catch (e) {
                     console.error("Error updating localStorage:", e);
                      Swal.fire('Cart Sync Issue', 'Item added, but local cart display might be out of sync.', 'warning');
                 }

                 Swal.fire({ title: 'Added to Cart!', text: data.message || 'Item added successfully.', icon: 'success', timer: 2000, showConfirmButton: false });
                 updateAddToCartButton(true, true); // Update button to "Already in Cart"
                 // Reset quantity after add to cart success
                 quantityInput.value = 1;
                 if (quantityDisplay) quantityDisplay.textContent = '1';
                 updateQuantityControls();
                 calculateAndUpdateTotal();

            } else { /* Handle JSON error response */
                  Swal.fire('Error', data.message || 'Could not add item (unexpected response).', 'error');
                  const stockNow = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
                  updateAddToCartButton(stockNow > 0, false); // Reset button state
            }
        })
        .catch(error => { /* Handle fetch error */
            console.error('Add to Cart Fetch Error:', error);
            Swal.fire('Request Failed', `Could not add item to cart. ${error.message}`, 'error');
            // Reset button based on current selections and stock
             const stockNow = currentSelectedVariation ? currentSelectedVariation.quantity : baseProductQuantity;
             const currentlyInCart = checkItemInCart(baseProductId, currentSelectedVariation?.id, currentSelectedColorId);
             updateAddToCartButton(stockNow > 0, currentlyInCart);
        });
    });


    // Mobile Menu Toggle
    mobileMenuBtn?.addEventListener('click', () => { navLinks?.classList.toggle('active'); mobileMenuBtn.querySelector('.menu-btn')?.classList.toggle('active'); });

    // --- Modal Logic ---
    const detailsModal = document.getElementById('detailsModal');
    const openModalBtn = document.getElementById('openDetailsModalBtn');
    const closeModalBtn = document.getElementById('closeDetailsModalBtn');
    function openModal() { if(detailsModal){ detailsModal.classList.add('show'); document.body.classList.add('modal-open'); } }
    function closeModal() { if(detailsModal){ detailsModal.classList.remove('show'); document.body.classList.remove('modal-open'); } }
    if(openModalBtn && detailsModal) openModalBtn.addEventListener('click', openModal);
    if(closeModalBtn && detailsModal) closeModalBtn.addEventListener('click', closeModal);
    window.addEventListener('click', (e) => { if(detailsModal && e.target == detailsModal) closeModal(); });
    window.addEventListener('keydown', (e) => { if(detailsModal && detailsModal.classList.contains('show') && e.key === 'Escape') closeModal(); });


    // --- Initial Page Setup ---
    function initializePage() {
        console.log("Initializing page (with color selection)...");

        // Initialize cart if not exists in localStorage
        if (!localStorage.getItem('cart')) {
            localStorage.setItem('cart', JSON.stringify([]));
        }

        updateDisplay(); // Update display based on initial variation & color
        updateCartCountDisplay(); // Update header count
        updateQuantityControls(); // Initialize quantity controls

        // Force update cart count after a short delay to ensure DOM is fully loaded
        setTimeout(() => {
            updateCartCountDisplay();
        }, 500);

        console.log("Page initialization complete.");
    }

    // Call initialization when DOM is loaded
    initializePage();

    // Gallery Modal Functionality
    const galleryModal = document.getElementById('galleryModal');
    const galleryMainImage = document.getElementById('galleryMainImage');
    const galleryThumbnails = document.querySelectorAll('.gallery-thumbnail');
    const galleryClose = document.querySelector('.gallery-close');
    const galleryPrev = document.querySelector('.gallery-prev');
    const galleryNext = document.querySelector('.gallery-next');

    let currentImageIndex = 0;
    let autoslideInterval = null;
    const AUTOSLIDE_DELAY = 3000; // 3 seconds

    // Function to start autoslide
    function startAutoslide() {
        if (autoslideInterval) clearInterval(autoslideInterval);
        autoslideInterval = setInterval(showNextImage, AUTOSLIDE_DELAY);
    }

    // Function to stop autoslide
    function stopAutoslide() {
        if (autoslideInterval) {
            clearInterval(autoslideInterval);
            autoslideInterval = null;
        }
    }

    // Open gallery modal
    document.querySelector('.main-image-container').addEventListener('click', function() {
        galleryModal.classList.add('show');
        document.body.classList.add('modal-open');
        showImage(currentImageIndex);
        startAutoslide(); // Start autoslide when modal opens
    });

    // Close gallery modal
    galleryClose.addEventListener('click', function() {
        closeGallery();
        stopAutoslide(); // Stop autoslide when modal closes
    });

    galleryModal.addEventListener('click', function(e) {
        if (e.target === galleryModal) {
            closeGallery();
            stopAutoslide(); // Stop autoslide when modal closes
        }
    });

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && galleryModal.classList.contains('show')) {
            closeGallery();
            stopAutoslide(); // Stop autoslide when modal closes
        }
    });

    // Navigation
    galleryPrev.addEventListener('click', function() {
        showPreviousImage();
        resetAutoslide(); // Reset autoslide timer when manually navigating
    });

    galleryNext.addEventListener('click', function() {
        showNextImage();
        resetAutoslide(); // Reset autoslide timer when manually navigating
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (!galleryModal.classList.contains('show')) return;

        if (e.key === 'ArrowLeft') {
            showPreviousImage();
            resetAutoslide(); // Reset autoslide timer when manually navigating
        } else if (e.key === 'ArrowRight') {
            showNextImage();
            resetAutoslide(); // Reset autoslide timer when manually navigating
        }
    });

    // Thumbnail click
    galleryThumbnails.forEach((thumb, index) => {
        thumb.addEventListener('click', () => {
            showImage(index);
            resetAutoslide(); // Reset autoslide timer when manually selecting image
        });
    });

    function showImage(index) {
        if (index < 0) index = galleryThumbnails.length - 1;
        if (index >= galleryThumbnails.length) index = 0;

        currentImageIndex = index;
        const thumb = galleryThumbnails[index];
        const img = thumb.querySelector('img');

        galleryMainImage.src = img.src;
        galleryMainImage.alt = img.alt;

        galleryThumbnails.forEach(t => t.classList.remove('active'));
        thumb.classList.add('active');
    }

    function showPreviousImage() {
        showImage(currentImageIndex - 1);
    }

    function showNextImage() {
        showImage(currentImageIndex + 1);
    }

    function closeGallery() {
        galleryModal.classList.remove('show');
        document.body.classList.remove('modal-open');
        stopAutoslide(); // Stop autoslide when modal closes
    }

    // Function to reset autoslide timer
    function resetAutoslide() {
        stopAutoslide();
        startAutoslide();
    }
}); // End DOMContentLoaded
</script>

</body>
</html>
<?php
ob_end_flush();
?>