<?php require_once('header.php'); ?>

<section class="content-header">
	<div class="content-header-left">
		<h1>Subscribers</h1>
	</div>
	<div class="content-header-right">
		<a href="subscriber-remove.php" class="btn btn-primary btn-sm">Remove Inactive Subscribers</a>
		<a href="subscriber-csv.php" class="btn btn-success btn-sm">Export as CSV</a>
	</div>
</section>


<section class="content">
  <div class="row">
    <div class="col-md-12">
      <div class="box box-info">        
        <div class="box-body table-responsive">
          <table id="subscriberTable" class="table table-bordered table-hover table-striped">
			<thead>
			    <tr>
			        <th>#</th>
			        <th>Subscriber Email</th>
			        <th>Subscription Date</th>
			        <th>Status</th>
			        <th>Action</th>
			    </tr>
			</thead>
            <tbody>
            	<?php
            	$i=0;
            	$statement = $pdo->prepare("SELECT * FROM tbl_subscriber ORDER BY created_at DESC");
            	$statement->execute();
            	$result = $statement->fetchAll(PDO::FETCH_ASSOC);
            	foreach ($result as $row) {
            		$i++;
            		?>
					<tr>
	                    <td><?php echo $i; ?></td>
	                    <td><?php echo $row['email']; ?></td>
	                    <td><?php echo date('Y-m-d H:i:s', strtotime($row['created_at'])); ?></td>
	                    <td>
	                        <a href="javascript:void(0);" class="status-toggle" data-id="<?php echo $row['id']; ?>" data-status="<?php echo $row['status']; ?>">
	                            <?php if($row['status'] == 1): ?>
	                                <span class="badge badge-success">Active</span>
	                            <?php else: ?>
	                                <span class="badge badge-warning">Inactive</span>
	                            <?php endif; ?>
	                    </a>
	                </td>
	                <td><a href="#" class="btn btn-danger btn-xs" data-href="subscriber-delete.php?id=<?php echo $row['id']; ?>" data-toggle="modal" data-target="#confirm-delete">Delete</a></td>
	                </tr>
            		<?php
            	}
            	?>
            </tbody>
          </table>
        </div>
      </div>
  

</section>


<div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">Delete Confirmation</h4>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this subscriber?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <a class="btn btn-danger btn-ok">Delete</a>
            </div>
        </div>
    </div>
</div>


<?php require_once('footer.php'); ?>

<script>
$(document).ready(function() {
    // Destroy any existing DataTable instance
    if ($.fn.DataTable.isDataTable('#subscriberTable')) {
        $('#subscriberTable').DataTable().destroy();
    }
    
    // Initialize DataTable
    var table = $('#subscriberTable').DataTable({
        "order": [[2, "desc"]],
        "pageLength": 25,
        "language": {
            "search": "Search Subscribers:"
        }
    });
    
    // Delete confirmation modal
    $('#confirm-delete').on('show.bs.modal', function(e) {
        $(this).find('.btn-ok').attr('href', $(e.relatedTarget).data('href'));
    });

    // Status toggle functionality
    $('.status-toggle').click(function() {
        var id = $(this).data('id');
        var currentStatus = $(this).data('status');
        var newStatus = currentStatus == 1 ? 0 : 1;
        var $badge = $(this).find('.badge');
        var $this = $(this);

        $.ajax({
            url: 'subscriber-status.php',
            type: 'POST',
            data: {
                id: id,
                status: newStatus
            },
            success: function(response) {
                if(response.success) {
                    // Update the badge
                    if(newStatus == 1) {
                        $badge.removeClass('badge-warning').addClass('badge-success').text('Active');
                    } else {
                        $badge.removeClass('badge-success').addClass('badge-warning').text('Inactive');
                    }
                    // Update the data-status attribute
                    $this.data('status', newStatus);
                    
                    // Show success message
                    toastr.success('Status updated successfully');
                } else {
                    toastr.error('Error updating status');
                }
            },
            error: function() {
                toastr.error('Error updating status');
            }
        });
    });
});
</script>