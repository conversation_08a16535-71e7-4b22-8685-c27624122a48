<?php
/**
 * Search Endpoints
 * Handles product search and filtering operations
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path
$sub_path = $segments[1] ?? '';

switch ($method) {
    case 'GET':
        switch ($sub_path) {
            case 'products':
                handleSearchProducts($db);
                break;
                
            case 'suggestions':
                handleSearchSuggestions($db);
                break;
                
            case 'filters':
                handleGetFilters($db);
                break;
                
            default:
                handleSearchProducts($db);
        }
        break;
        
    default:
        Response::methodNotAllowed(['GET']);
}

/**
 * Search products with advanced filtering
 */
function handleSearchProducts($db) {
    $query = $_GET['q'] ?? '';
    $category_id = $_GET['category_id'] ?? null;
    $subcategory_id = $_GET['subcategory_id'] ?? null;
    $min_price = $_GET['min_price'] ?? null;
    $max_price = $_GET['max_price'] ?? null;
    $color_ids = $_GET['color_ids'] ?? null;
    $size_ids = $_GET['size_ids'] ?? null;
    $in_stock = $_GET['in_stock'] ?? null;
    $featured = $_GET['featured'] ?? null;
    $sort = $_GET['sort'] ?? 'relevance';
    $order = $_GET['order'] ?? 'desc';
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? DEFAULT_PAGE_SIZE), MAX_PAGE_SIZE);
    
    // Build WHERE conditions
    $where_conditions = ['p.p_is_active = 1'];
    $params = [];
    
    // Text search
    if (!empty($query)) {
        $search_term = "%{$query}%";
        $where_conditions[] = '(p.p_name LIKE ? OR p.p_description LIKE ? OR p.p_short_description LIKE ? OR p.p_feature LIKE ?)';
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    // Category filter
    if ($category_id) {
        $where_conditions[] = 'p.tcat_id = ?';
        $params[] = $category_id;
    }
    
    // Subcategory filter
    if ($subcategory_id) {
        $where_conditions[] = 'p.mcat_id = ?';
        $params[] = $subcategory_id;
    }
    
    // Price range filter
    if ($min_price !== null) {
        $where_conditions[] = 'CAST(p.p_current_price AS DECIMAL(10,2)) >= ?';
        $params[] = $min_price;
    }
    
    if ($max_price !== null) {
        $where_conditions[] = 'CAST(p.p_current_price AS DECIMAL(10,2)) <= ?';
        $params[] = $max_price;
    }
    
    // Stock filter
    if ($in_stock === '1') {
        $where_conditions[] = 'p.p_qty > 0';
    } elseif ($in_stock === '0') {
        $where_conditions[] = 'p.p_qty = 0';
    }
    
    // Featured filter
    if ($featured === '1') {
        $where_conditions[] = 'p.p_is_featured = 1';
    }
    
    // Color filter
    if ($color_ids) {
        $color_array = explode(',', $color_ids);
        $color_placeholders = str_repeat('?,', count($color_array) - 1) . '?';
        $where_conditions[] = "p.p_id IN (SELECT pc.p_id FROM tbl_product_color pc WHERE pc.color_id IN ({$color_placeholders}))";
        $params = array_merge($params, $color_array);
    }
    
    // Size filter
    if ($size_ids) {
        $size_array = explode(',', $size_ids);
        $size_placeholders = str_repeat('?,', count($size_array) - 1) . '?';
        $where_conditions[] = "p.p_id IN (SELECT ps.p_id FROM tbl_product_size ps WHERE ps.size_id IN ({$size_placeholders}))";
        $params = array_merge($params, $size_array);
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Build ORDER BY clause
    $allowed_sorts = ['relevance', 'name', 'price', 'created', 'popularity'];
    $sort = in_array($sort, $allowed_sorts) ? $sort : 'relevance';
    $order = strtoupper($order) === 'ASC' ? 'ASC' : 'DESC';
    
    $order_by = match($sort) {
        'name' => "p.p_name {$order}",
        'price' => "CAST(p.p_current_price AS DECIMAL(10,2)) {$order}",
        'created' => "p.p_id {$order}",
        'popularity' => "p.p_total_view {$order}",
        'relevance' => !empty($query) ? 
            "CASE 
                WHEN p.p_name LIKE ? THEN 1
                WHEN p.p_short_description LIKE ? THEN 2
                WHEN p.p_description LIKE ? THEN 3
                ELSE 4
            END ASC, p.p_total_view DESC" : "p.p_total_view DESC",
        default => "p.p_total_view DESC"
    };
    
    // Add relevance parameters if needed
    if ($sort === 'relevance' && !empty($query)) {
        $relevance_params = [$search_term, $search_term, $search_term];
        $params = array_merge($relevance_params, $params);
    }
    
    // Main search query
    $sql = "
        SELECT 
            p.p_id,
            p.p_name,
            p.p_old_price,
            p.p_current_price,
            p.p_qty,
            p.p_featured_photo,
            p.p_short_description,
            p.p_is_featured,
            p.p_total_view,
            p.installation_fee,
            tc.tcat_name as category_name,
            mc.mcat_name as subcategory_name,
            (SELECT COUNT(*) FROM tbl_product_variation WHERE p_id = p.p_id) as variation_count
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        LEFT JOIN tbl_mid_category mc ON p.mcat_id = mc.mcat_id
        WHERE {$where_clause}
        ORDER BY {$order_by}
    ";
    
    $result = $db->paginate($sql, $params, $page, $limit);
    
    // Format products
    $products = array_map('formatSearchProduct', $result['data']);
    
    // Get search metadata
    $metadata = [
        'query' => $query,
        'filters_applied' => [
            'category_id' => $category_id,
            'subcategory_id' => $subcategory_id,
            'min_price' => $min_price,
            'max_price' => $max_price,
            'color_ids' => $color_ids,
            'size_ids' => $size_ids,
            'in_stock' => $in_stock,
            'featured' => $featured
        ],
        'sort' => $sort,
        'order' => $order
    ];
    
    Response::paginated($products, $result['total'], $page, $limit, 'Search results retrieved successfully', $metadata);
}

/**
 * Get search suggestions
 */
function handleSearchSuggestions($db) {
    $query = $_GET['q'] ?? '';
    $limit = min((int)($_GET['limit'] ?? 10), 20);
    
    if (strlen($query) < 2) {
        Response::success([], 'Query too short for suggestions');
    }
    
    $search_term = "%{$query}%";
    
    // Get product name suggestions
    $product_suggestions = $db->fetchAll(
        "SELECT DISTINCT p.p_name as suggestion, 'product' as type
         FROM tbl_product p
         WHERE p.p_name LIKE ? AND p.p_is_active = 1
         ORDER BY p.p_total_view DESC
         LIMIT ?",
        [$search_term, $limit]
    );
    
    // Get category suggestions
    $category_suggestions = $db->fetchAll(
        "SELECT DISTINCT tc.tcat_name as suggestion, 'category' as type
         FROM tbl_top_category tc
         WHERE tc.tcat_name LIKE ?
         LIMIT ?",
        [$search_term, $limit]
    );
    
    $suggestions = array_merge($product_suggestions, $category_suggestions);
    
    // Limit total suggestions
    $suggestions = array_slice($suggestions, 0, $limit);
    
    Response::success($suggestions, 'Search suggestions retrieved successfully');
}

/**
 * Get available filters for search
 */
function handleGetFilters($db) {
    $category_id = $_GET['category_id'] ?? null;
    $subcategory_id = $_GET['subcategory_id'] ?? null;
    
    // Build base WHERE clause for filtering
    $where_conditions = ['p.p_is_active = 1'];
    $params = [];
    
    if ($category_id) {
        $where_conditions[] = 'p.tcat_id = ?';
        $params[] = $category_id;
    }
    
    if ($subcategory_id) {
        $where_conditions[] = 'p.mcat_id = ?';
        $params[] = $subcategory_id;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get price range
    $price_range = $db->fetchOne(
        "SELECT 
            MIN(CAST(p_current_price AS DECIMAL(10,2))) as min_price,
            MAX(CAST(p_current_price AS DECIMAL(10,2))) as max_price
         FROM tbl_product p
         WHERE {$where_clause}",
        $params
    );
    
    // Get available colors
    $colors = $db->fetchAll(
        "SELECT DISTINCT c.color_id, c.color_name, c.color_code
         FROM tbl_color c
         JOIN tbl_product_color pc ON c.color_id = pc.color_id
         JOIN tbl_product p ON pc.p_id = p.p_id
         WHERE {$where_clause}
         ORDER BY c.color_name",
        $params
    );
    
    // Get available sizes
    $sizes = $db->fetchAll(
        "SELECT DISTINCT s.size_id, s.size_name
         FROM tbl_size s
         JOIN tbl_product_size ps ON s.size_id = ps.size_id
         JOIN tbl_product p ON ps.p_id = p.p_id
         WHERE {$where_clause}
         ORDER BY s.size_name",
        $params
    );
    
    // Get categories (if not filtered by category)
    $categories = [];
    if (!$category_id) {
        $categories = $db->fetchAll(
            "SELECT DISTINCT tc.tcat_id, tc.tcat_name
             FROM tbl_top_category tc
             JOIN tbl_product p ON tc.tcat_id = p.tcat_id
             WHERE p.p_is_active = 1
             ORDER BY tc.tcat_name"
        );
    }
    
    // Get subcategories (if category is selected but not subcategory)
    $subcategories = [];
    if ($category_id && !$subcategory_id) {
        $subcategories = $db->fetchAll(
            "SELECT DISTINCT mc.mcat_id, mc.mcat_name
             FROM tbl_mid_category mc
             JOIN tbl_product p ON mc.mcat_id = p.mcat_id
             WHERE mc.tcat_id = ? AND p.p_is_active = 1
             ORDER BY mc.mcat_name",
            [$category_id]
        );
    }
    
    $filters = [
        'price_range' => [
            'min' => (float)($price_range['min_price'] ?? 0),
            'max' => (float)($price_range['max_price'] ?? 0)
        ],
        'colors' => array_map(function($color) {
            return [
                'id' => (int)$color['color_id'],
                'name' => $color['color_name'],
                'code' => $color['color_code']
            ];
        }, $colors),
        'sizes' => array_map(function($size) {
            return [
                'id' => (int)$size['size_id'],
                'name' => $size['size_name']
            ];
        }, $sizes),
        'categories' => array_map(function($category) {
            return [
                'id' => (int)$category['tcat_id'],
                'name' => $category['tcat_name']
            ];
        }, $categories),
        'subcategories' => array_map(function($subcategory) {
            return [
                'id' => (int)$subcategory['mcat_id'],
                'name' => $subcategory['mcat_name']
            ];
        }, $subcategories)
    ];
    
    Response::success($filters, 'Search filters retrieved successfully');
}

/**
 * Format product data for search results
 */
function formatSearchProduct($product) {
    return [
        'id' => (int)$product['p_id'],
        'name' => $product['p_name'],
        'old_price' => (float)$product['p_old_price'],
        'current_price' => (float)$product['p_current_price'],
        'quantity' => (int)$product['p_qty'],
        'installation_fee' => (int)($product['installation_fee'] ?? DEFAULT_INSTALLATION_FEE),
        'featured_photo' => $product['p_featured_photo'] ? 
            '/assets/uploads/' . $product['p_featured_photo'] : null,
        'short_description' => $product['p_short_description'] ?? '',
        'is_featured' => (bool)($product['p_is_featured'] ?? false),
        'category' => $product['category_name'] ?? null,
        'subcategory' => $product['subcategory_name'] ?? null,
        'in_stock' => (int)$product['p_qty'] > 0,
        'variation_count' => (int)($product['variation_count'] ?? 0),
        'view_count' => (int)($product['p_total_view'] ?? 0)
    ];
}
?>
