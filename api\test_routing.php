<?php
/**
 * Test API Routing
 * Check if URL rewriting and routing is working correctly
 */

echo "<h1>🔍 API Routing Test</h1>";

// Test different API URLs
$test_urls = [
    'http://localhost/ecom/api/v1/',
    'http://localhost/ecom/api/v1/auth/login',
    'http://localhost/ecom/api/index.php',
    'http://localhost/ecom/api/v1/products'
];

function testUrl($url, $method = 'GET', $data = null) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>🧪 Testing: {$url}</h3>";
    echo "<p><strong>Method:</strong> {$method}</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, false);
    
    if ($data && $method === 'POST') {
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $headers = substr($response, 0, $header_size);
    $body = substr($response, $header_size);
    
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    echo "<p><strong>Content Type:</strong> {$content_type}</p>";
    
    if ($error) {
        echo "<p style='color: red;'>❌ Error: {$error}</p>";
    } else {
        echo "<h4>📋 Response Headers:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; max-height: 150px; overflow-y: auto;'>";
        echo htmlspecialchars($headers);
        echo "</pre>";
        
        echo "<h4>📋 Response Body (first 500 chars):</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($body, 0, 500));
        if (strlen($body) > 500) {
            echo "\n... (truncated)";
        }
        echo "</pre>";
        
        // Check if it's valid JSON
        $json_data = json_decode($body, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p style='color: green;'>✅ Valid JSON response</p>";
        } else {
            echo "<p style='color: red;'>❌ Invalid JSON: " . json_last_error_msg() . "</p>";
        }
        
        // Check if it starts with HTML
        if (strpos(trim($body), '<') === 0) {
            echo "<p style='color: red;'>❌ Response is HTML, not JSON</p>";
        }
    }
    
    echo "</div>";
    return $body;
}

// Test API root
echo "<h2>1. Testing API Root</h2>";
testUrl('http://localhost/ecom/api/v1/');

// Test direct index.php access
echo "<h2>2. Testing Direct Index Access</h2>";
testUrl('http://localhost/ecom/api/index.php');

// Test auth endpoint with GET (should return method not allowed)
echo "<h2>3. Testing Auth Endpoint (GET)</h2>";
testUrl('http://localhost/ecom/api/v1/auth/login');

// Test auth endpoint with POST
echo "<h2>4. Testing Auth Endpoint (POST)</h2>";
$login_data = [
    'email' => '<EMAIL>',
    'password' => 'changawa'
];
$auth_response = testUrl('http://localhost/ecom/api/v1/auth/login', 'POST', $login_data);

// Test products endpoint
echo "<h2>5. Testing Products Endpoint</h2>";
testUrl('http://localhost/ecom/api/v1/products');

// Check if mod_rewrite is enabled
echo "<h2>6. Server Configuration Check</h2>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🔧 Server Configuration</h3>";

// Check if mod_rewrite is loaded
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✅ mod_rewrite is enabled</p>";
    } else {
        echo "<p style='color: red;'>❌ mod_rewrite is not enabled</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Cannot check mod_rewrite status</p>";
}

// Check .htaccess file
if (file_exists(__DIR__ . '/.htaccess')) {
    echo "<p style='color: green;'>✅ .htaccess file exists</p>";
} else {
    echo "<p style='color: red;'>❌ .htaccess file missing</p>";
}

// Check if AllowOverride is enabled (indirect test)
$test_htaccess_url = 'http://localhost/ecom/api/v1/nonexistent';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_htaccess_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_NOBODY, true);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code === 200) {
    echo "<p style='color: green;'>✅ URL rewriting appears to be working</p>";
} else {
    echo "<p style='color: red;'>❌ URL rewriting may not be working (HTTP {$http_code})</p>";
}

echo "</div>";

// Manual routing test
echo "<h2>7. Manual Routing Test</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🧪 Manual API Simulation</h3>";

try {
    // Simulate the API environment
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/ecom/api/v1/auth/login';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Capture output
    ob_start();
    
    // Include the main API file
    include __DIR__ . '/index.php';
    
    $manual_output = ob_get_clean();
    
    echo "<p><strong>Manual API Output:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($manual_output);
    echo "</pre>";
    
    $manual_json = json_decode($manual_output, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✅ Manual test produces valid JSON</p>";
    } else {
        echo "<p style='color: red;'>❌ Manual test produces invalid JSON</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Manual test error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Recommendations
echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🔧 Troubleshooting Steps</h2>";

if (strpos($auth_response, '<') === 0) {
    echo "<p><strong>Issue:</strong> API returning HTML instead of JSON</p>";
    echo "<p><strong>Possible causes:</strong></p>";
    echo "<ul>";
    echo "<li>PHP errors being output before JSON</li>";
    echo "<li>URL rewriting not working correctly</li>";
    echo "<li>Wrong endpoint being accessed</li>";
    echo "<li>Missing or incorrect .htaccess configuration</li>";
    echo "</ul>";
    
    echo "<p><strong>Solutions to try:</strong></p>";
    echo "<ol>";
    echo "<li>Check Apache error logs for PHP errors</li>";
    echo "<li>Test direct access: <code>http://localhost/ecom/api/index.php</code></li>";
    echo "<li>Verify mod_rewrite is enabled in Apache</li>";
    echo "<li>Check AllowOverride settings in Apache config</li>";
    echo "<li>Add error suppression to prevent output before JSON</li>";
    echo "</ol>";
} else {
    echo "<p style='color: green;'><strong>Routing appears to be working!</strong></p>";
    echo "<p>If you're still getting JSON parse errors in Flutter, the issue might be:</p>";
    echo "<ul>";
    echo "<li>Network connectivity issues</li>";
    echo "<li>Flutter HTTP client configuration</li>";
    echo "<li>Response encoding issues</li>";
    echo "</ul>";
}

echo "</div>";

echo "<hr>";
echo "<p><strong>Routing test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
