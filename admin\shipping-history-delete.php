<?php
require_once('header.php');

// Debug session
error_log("Session data: " . print_r($_SESSION, true));

// Check if user is logged in
if(!isset($_SESSION['user'])) {
    error_log("User not logged in. Redirecting to login.");
    header('location: login.php');
    exit;
}

// Check if ID parameter exists
if (!isset($_GET['id']) || !isset($_GET['order_id'])) {
    $_SESSION['error_message'] = 'Invalid request. Missing required parameters.';
    header('location: shipping-tracking.php');
    exit;
}

$history_id = (int)$_GET['id'];
$order_id = (int)$_GET['order_id'];

try {
    // Delete the shipping history entry
    $stmt = $pdo->prepare("DELETE FROM shipping_history WHERE id = ? AND order_id = ?");
    $stmt->execute([$history_id, $order_id]);

    if ($stmt->rowCount() > 0) {
        $_SESSION['success_message'] = 'Shipping history entry deleted successfully.';
    } else {
        $_SESSION['error_message'] = 'No shipping history entry found with the given ID.';
    }
} catch (PDOException $e) {
    $_SESSION['error_message'] = 'An error occurred while deleting the shipping history entry.';
    // Log the error for debugging
    error_log("Error deleting shipping history: " . $e->getMessage());
}

// Redirect back to shipping tracking page
header('location: shipping-tracking.php');
exit; 