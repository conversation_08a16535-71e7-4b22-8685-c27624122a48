<?php
ob_start();

// Include session configuration before starting session
include("session_config.php");
session_start();

include("../admin/inc/config.php");
include("../admin/inc/functions.php");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];
    $remember_me = isset($_POST['remember_me']) && $_POST['remember_me'] === '1';

    if (empty($email) || empty($password)) {
        $_SESSION['error_message'] = 'Email and Password are required.';
        header('Location: login.php');
        exit;
    }

    try {
        $stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_email = ? AND cust_status = 1");
        $stmt->execute([$email]);
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($customer && password_verify($password, $customer['cust_password'])) {
            session_regenerate_id(true);
            $_SESSION['customer'] = [
                 'cust_id' => $customer['cust_id'],
                 'cust_fname' => $customer['cust_fname'],
                 'cust_lname' => $customer['cust_lname'],
                 'cust_email' => $customer['cust_email']
             ];

             // Clear logout flag if it exists
             unset($_SESSION['just_logged_out']);

             // Update last login
             $updateStmt = $pdo->prepare("UPDATE tbl_customer SET cust_last_login = NOW() WHERE cust_id = ?");
             $updateStmt->execute([$customer['cust_id']]);

             // Handle "Remember Me" functionality
             if ($remember_me) {
                 $remember_token = generateRememberToken();
                 $expiry = time() + REMEMBER_TOKEN_EXPIRY;

                 if (storeRememberToken($pdo, $customer['cust_id'], $remember_token, $expiry)) {
                     setRememberCookie($remember_token, $expiry);
                 } else {
                     error_log("Failed to store remember token for customer ID: " . $customer['cust_id']);
                 }
             }

            // --- MODIFIED REDIRECTION ---
            // Check if a specific redirect URL (like the third-party checkout) is stored
            if (isset($_SESSION['redirect_url'])) {
                $redirect_url = $_SESSION['redirect_url'];
                unset($_SESSION['redirect_url']); // Clear the stored URL after using it
            } else {
                $redirect_url = 'cart.php'; // Default redirect if none was stored
            }

            header('Location: ' . $redirect_url);
            exit;
        } else {
            $_SESSION['error_message'] = 'Invalid email or password, or account inactive.';
            header('Location: login.php');
            exit;
        }

    } catch (PDOException $e) {
        error_log("Login Error: " . $e->getMessage());
        $_SESSION['error_message'] = 'An error occurred during login.';
        header('Location: login.php');
        exit;
    }
} else {
    header('Location: login.php');
    exit;
}
ob_end_flush();
?>