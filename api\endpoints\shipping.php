<?php
/**
 * Shipping Endpoints
 * Handles shipping information and costs
 */

global $pdo;
$db = new Database($pdo);

// Get the sub-path
$sub_path = $segments[1] ?? '';

switch ($method) {
    case 'GET':
        switch ($sub_path) {
            case 'countries':
                handleGetShippingCountries($db);
                break;
                
            case 'cost':
                handleGetShippingCost($db);
                break;
                
            case 'zones':
                handleGetShippingZones($db);
                break;
                
            default:
                Response::notFound('Shipping endpoint not found');
        }
        break;
        
    default:
        Response::methodNotAllowed(['GET']);
}

/**
 * Get available shipping countries
 */
function handleGetShippingCountries($db) {
    $sql = "
        SELECT 
            c.country_id,
            c.country_name,
            sc.amount as shipping_cost
        FROM tbl_country c
        LEFT JOIN tbl_shipping_cost sc ON c.country_id = sc.country_id
        ORDER BY c.country_name
    ";
    
    $countries = $db->fetchAll($sql);
    
    $formatted_countries = array_map(function($country) {
        return [
            'id' => (int)$country['country_id'],
            'name' => $country['country_name'],
            'shipping_cost' => $country['shipping_cost'] ? (float)$country['shipping_cost'] : null,
            'shipping_available' => $country['shipping_cost'] !== null
        ];
    }, $countries);
    
    Response::success($formatted_countries, 'Shipping countries retrieved successfully');
}

/**
 * Get shipping cost for specific country
 */
function handleGetShippingCost($db) {
    $country_id = $_GET['country_id'] ?? null;
    $country_name = $_GET['country_name'] ?? null;
    
    if (!$country_id && !$country_name) {
        Response::error('Country ID or country name is required', 400);
    }
    
    $sql = "
        SELECT 
            c.country_id,
            c.country_name,
            sc.amount as shipping_cost,
            sc.estimated_days,
            sc.description
        FROM tbl_country c
        LEFT JOIN tbl_shipping_cost sc ON c.country_id = sc.country_id
        WHERE " . ($country_id ? "c.country_id = ?" : "c.country_name = ?");
    
    $param = $country_id ?: $country_name;
    $shipping_info = $db->fetchOne($sql, [$param]);
    
    if (!$shipping_info) {
        Response::notFound('Country not found');
    }
    
    if (!$shipping_info['shipping_cost']) {
        Response::error('Shipping not available to this country', 400, 'SHIPPING_NOT_AVAILABLE');
    }
    
    $response_data = [
        'country_id' => (int)$shipping_info['country_id'],
        'country_name' => $shipping_info['country_name'],
        'shipping_cost' => (float)$shipping_info['shipping_cost'],
        'estimated_days' => $shipping_info['estimated_days'] ? (int)$shipping_info['estimated_days'] : null,
        'description' => $shipping_info['description'],
        'currency' => DEFAULT_CURRENCY
    ];
    
    Response::success($response_data, 'Shipping cost retrieved successfully');
}

/**
 * Get shipping zones with costs
 */
function handleGetShippingZones($db) {
    // Group countries by shipping cost to create zones
    $sql = "
        SELECT 
            sc.amount as shipping_cost,
            sc.estimated_days,
            sc.description,
            GROUP_CONCAT(c.country_name ORDER BY c.country_name SEPARATOR ', ') as countries,
            COUNT(c.country_id) as country_count
        FROM tbl_shipping_cost sc
        JOIN tbl_country c ON sc.country_id = c.country_id
        GROUP BY sc.amount, sc.estimated_days, sc.description
        ORDER BY sc.amount
    ";
    
    $zones = $db->fetchAll($sql);
    
    $formatted_zones = array_map(function($zone) {
        return [
            'shipping_cost' => (float)$zone['shipping_cost'],
            'estimated_days' => $zone['estimated_days'] ? (int)$zone['estimated_days'] : null,
            'description' => $zone['description'],
            'countries' => explode(', ', $zone['countries']),
            'country_count' => (int)$zone['country_count'],
            'currency' => DEFAULT_CURRENCY
        ];
    }, $zones);
    
    Response::success($formatted_zones, 'Shipping zones retrieved successfully');
}
?>
