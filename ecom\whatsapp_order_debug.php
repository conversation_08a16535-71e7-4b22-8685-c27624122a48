<?php
// Debug version of WhatsApp order handler
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set content type for JSON response
header('Content-Type: application/json');

try {
    // Test 1: Basic PHP execution
    $debug_info = [
        'step' => 'basic_php',
        'php_version' => phpversion(),
        'timestamp' => date('Y-m-d H:i:s'),
        'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
    ];

    // Test 2: Include files
    $debug_info['step'] = 'including_files';
    
    if (!file_exists("session_config.php")) {
        throw new Exception("session_config.php not found");
    }
    include("session_config.php");
    $debug_info['session_config'] = 'loaded';

    if (!session_id()) {
        session_start();
    }
    $debug_info['session_started'] = true;
    $debug_info['session_id'] = session_id();

    if (!file_exists("../admin/inc/config.php")) {
        throw new Exception("../admin/inc/config.php not found");
    }
    include("../admin/inc/config.php");
    $debug_info['config'] = 'loaded';

    if (!file_exists("../admin/inc/functions.php")) {
        throw new Exception("../admin/inc/functions.php not found");
    }
    include("../admin/inc/functions.php");
    $debug_info['functions'] = 'loaded';

    if (file_exists("auto_cleanup.php")) {
        include("auto_cleanup.php");
        $debug_info['auto_cleanup'] = 'loaded';
    } else {
        $debug_info['auto_cleanup'] = 'not_found_but_optional';
    }

    // Test 3: Database connection
    $debug_info['step'] = 'testing_database';
    if (!isset($pdo)) {
        throw new Exception("PDO connection not available");
    }
    
    // Test database connection
    $test_query = $pdo->query("SELECT 1");
    if (!$test_query) {
        throw new Exception("Database connection test failed");
    }
    $debug_info['database'] = 'connected';

    // Test 4: Check if orders table exists and has whatsapp enum
    $debug_info['step'] = 'checking_orders_table';
    $table_check = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_status'");
    $payment_status_column = $table_check->fetch(PDO::FETCH_ASSOC);
    
    if (!$payment_status_column) {
        throw new Exception("payment_status column not found in orders table");
    }
    
    $debug_info['payment_status_column'] = $payment_status_column['Type'];
    
    // Check if 'whatsapp' is in the enum
    if (strpos($payment_status_column['Type'], 'whatsapp') === false) {
        $debug_info['whatsapp_enum_missing'] = true;
        $debug_info['migration_needed'] = "ALTER TABLE orders MODIFY COLUMN payment_status ENUM('pending', 'success', 'failed', 'whatsapp') DEFAULT 'pending';";
    } else {
        $debug_info['whatsapp_enum_exists'] = true;
    }

    // Test 5: Check user authentication
    $debug_info['step'] = 'checking_authentication';
    if (!function_exists('isUserLoggedIn')) {
        throw new Exception("isUserLoggedIn function not found");
    }
    
    $debug_info['user_logged_in'] = isUserLoggedIn();
    
    if (!isUserLoggedIn()) {
        $debug_info['session_data'] = $_SESSION;
        throw new Exception("User not logged in");
    }

    // Test 6: Check cart
    $debug_info['step'] = 'checking_cart';
    $debug_info['cart_exists'] = isset($_SESSION['cart']);
    $debug_info['cart_empty'] = empty($_SESSION['cart']);
    $debug_info['cart_count'] = is_array($_SESSION['cart']) ? count($_SESSION['cart']) : 0;

    if (empty($_SESSION['cart'])) {
        throw new Exception("Cart is empty");
    }

    // Test 7: Check POST data
    $debug_info['step'] = 'checking_post_data';
    $debug_info['request_method'] = $_SERVER['REQUEST_METHOD'];
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Method not allowed: " . $_SERVER['REQUEST_METHOD']);
    }

    $input = file_get_contents('php://input');
    $debug_info['raw_input'] = $input;
    
    $post_data = json_decode($input, true);
    $debug_info['parsed_post_data'] = $post_data;
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON decode error: " . json_last_error_msg());
    }

    // Test 8: Customer data
    $debug_info['step'] = 'checking_customer_data';
    $debug_info['customer_session'] = $_SESSION['customer'] ?? null;
    
    if (!isset($_SESSION['customer'])) {
        throw new Exception("Customer data not found in session");
    }

    $customer = $_SESSION['customer'];
    $user_id = $customer['cust_id'];
    $debug_info['user_id'] = $user_id;

    // Test 9: Database customer lookup
    $debug_info['step'] = 'fetching_customer_from_db';
    $stmt = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id = ?");
    $stmt->execute([$user_id]);
    $customerData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$customerData) {
        throw new Exception("Customer not found in database with ID: " . $user_id);
    }
    
    $debug_info['customer_found'] = true;
    $debug_info['customer_name'] = $customerData['cust_fname'] . ' ' . $customerData['cust_lname'];

    // If we get here, everything is working
    $debug_info['step'] = 'all_tests_passed';
    $debug_info['status'] = 'success';
    $debug_info['message'] = 'All diagnostic tests passed successfully';

    echo json_encode([
        'success' => true,
        'message' => 'Diagnostic completed successfully',
        'debug_info' => $debug_info
    ]);

} catch (Exception $e) {
    $debug_info['error'] = $e->getMessage();
    $debug_info['error_line'] = $e->getLine();
    $debug_info['error_file'] = $e->getFile();
    $debug_info['status'] = 'error';

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Diagnostic failed: ' . $e->getMessage(),
        'debug_info' => $debug_info
    ]);
}
?>
