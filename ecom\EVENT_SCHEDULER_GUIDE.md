# MySQL Event Scheduler Setup Guide

## Overview
This guide provides complete instructions for setting up MySQL Event Scheduler to automatically clean up expired remember tokens in the persistent login system.

## 🚀 Quick Setup Options

### Option 1: Web-Based Setup (Recommended)
Visit the Event Scheduler Manager:
```
http://yourdomain.com/ecom/event_scheduler_manager.php
```

### Option 2: Command Line Setup
Run the automated setup script:
```bash
php ecom/setup_event_scheduler.php
```

### Option 3: Manual SQL Setup
Execute the SQL commands from:
```
ecom/event_scheduler_setup.sql
```

## 📋 Manual Setup Steps

### Step 1: Check Event Scheduler Status
```sql
SELECT @@event_scheduler;
```
Expected results:
- `ON` = Event scheduler is enabled
- `OFF` = Event scheduler is disabled
- `DISABLED` = Event scheduler is disabled at startup

### Step 2: Enable Event Scheduler
```sql
SET GLOBAL event_scheduler = ON;
```

### Step 3: Verify Status
```sql
SHOW VARIABLES LIKE 'event_scheduler';
```

### Step 4: Create Daily Cleanup Event
```sql
DELIMITER $$

CREATE EVENT IF NOT EXISTS `cleanup_expired_remember_tokens`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
COMMENT 'Daily cleanup of expired remember me tokens'
DO
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    DECLARE cleanup_time DATETIME DEFAULT NOW();
    
    -- Delete expired tokens
    DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
    SET deleted_count = ROW_COUNT();
    
    -- Log the cleanup activity
    INSERT IGNORE INTO `tbl_cleanup_log` 
    (`cleanup_type`, `tokens_deleted`, `last_cleanup`) 
    VALUES ('remember_tokens', deleted_count, cleanup_time);
    
    -- Log to MySQL if tokens were deleted
    IF deleted_count > 0 THEN
        SELECT CONCAT('Daily cleanup: Removed ', deleted_count, ' expired tokens at ', cleanup_time) AS cleanup_result;
    END IF;
END$$

DELIMITER ;
```

### Step 5: Create Weekly Emergency Cleanup Event
```sql
DELIMITER $$

CREATE EVENT IF NOT EXISTS `emergency_cleanup_old_tokens`
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_TIMESTAMP
COMMENT 'Weekly emergency cleanup of very old tokens (60+ days)'
DO
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- Delete tokens older than 60 days
    DELETE FROM `tbl_remember_tokens` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 60 DAY);
    
    SET deleted_count = ROW_COUNT();
    
    -- Log the emergency cleanup
    INSERT IGNORE INTO `tbl_cleanup_log` 
    (`cleanup_type`, `tokens_deleted`, `last_cleanup`) 
    VALUES ('emergency_cleanup', deleted_count, NOW());
END$$

DELIMITER ;
```

### Step 6: Verify Events Were Created
```sql
SHOW EVENTS;
```

## 🔧 Event Management Commands

### View All Events
```sql
SELECT 
    EVENT_NAME,
    EVENT_DEFINITION,
    INTERVAL_VALUE,
    INTERVAL_FIELD,
    STATUS,
    STARTS,
    ENDS,
    LAST_EXECUTED,
    EVENT_COMMENT
FROM information_schema.EVENTS 
WHERE EVENT_SCHEMA = DATABASE();
```

### Enable/Disable Events
```sql
-- Enable an event
ALTER EVENT `cleanup_expired_remember_tokens` ENABLE;

-- Disable an event
ALTER EVENT `cleanup_expired_remember_tokens` DISABLE;
```

### Modify Event Schedule
```sql
-- Change to run every 12 hours
ALTER EVENT `cleanup_expired_remember_tokens` 
ON SCHEDULE EVERY 12 HOUR;
```

### Drop Events
```sql
DROP EVENT IF EXISTS `cleanup_expired_remember_tokens`;
DROP EVENT IF EXISTS `emergency_cleanup_old_tokens`;
```

## 📊 Monitoring & Troubleshooting

### Check Event Execution History
```sql
SELECT * FROM `tbl_cleanup_log` 
WHERE `cleanup_type` LIKE '%remember_tokens%' 
ORDER BY `last_cleanup` DESC 
LIMIT 10;
```

### Manual Cleanup Test
```sql
DELETE FROM `tbl_remember_tokens` WHERE `expires_at` < NOW();
SELECT ROW_COUNT() as 'Manually deleted tokens';
```

### Check for Errors
```sql
-- View MySQL error log location
SHOW VARIABLES LIKE 'log_error';
```

## ⚙️ Persistent Configuration

### Make Event Scheduler Persistent
Add this to your MySQL configuration file (`my.cnf` or `my.ini`):
```ini
[mysqld]
event_scheduler = ON
```

### Restart MySQL Service
After adding to config file:
```bash
# Linux/Mac
sudo systemctl restart mysql

# Windows
net stop mysql
net start mysql

# XAMPP
Restart MySQL from XAMPP Control Panel
```

## 🔐 Security & Privileges

### Grant EVENT Privileges
```sql
-- Grant EVENT privileges to your database user
GRANT EVENT ON your_database_name.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

### Check Current Privileges
```sql
SHOW GRANTS FOR CURRENT_USER();
```

## 📦 Backup & Restore

### Backup Events
```bash
mysqldump --events --no-data your_database_name > events_backup.sql
```

### Restore Events
```bash
mysql your_database_name < events_backup.sql
```

## ✅ Verification Checklist

Run this verification script to ensure everything is working:
```sql
SELECT 
    'Event Scheduler Status' as Check_Type,
    @@event_scheduler as Status
UNION ALL
SELECT 
    'Total Events Created' as Check_Type,
    COUNT(*) as Status
FROM information_schema.EVENTS 
WHERE EVENT_SCHEMA = DATABASE()
UNION ALL
SELECT 
    'Cleanup Events Active' as Check_Type,
    COUNT(*) as Status
FROM information_schema.EVENTS 
WHERE EVENT_SCHEMA = DATABASE() 
AND EVENT_NAME LIKE '%cleanup%' 
AND STATUS = 'ENABLED';
```

## 🚨 Common Issues & Solutions

### Issue 1: "Access denied for user"
**Problem**: User doesn't have EVENT privileges
**Solution**: 
```sql
GRANT EVENT ON database_name.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### Issue 2: "Event scheduler is disabled"
**Problem**: Event scheduler is not enabled
**Solution**: 
```sql
SET GLOBAL event_scheduler = ON;
```

### Issue 3: Events not executing
**Problem**: Various causes
**Solutions**:
1. Check MySQL error logs
2. Verify event scheduler is ON
3. Check event status: `SHOW EVENTS;`
4. Ensure database connection is stable

### Issue 4: "Table 'tbl_cleanup_log' doesn't exist"
**Problem**: Cleanup log table not created
**Solution**: Run `setup_remember_tokens.php` first

## 📈 Performance Considerations

### Event Frequency Options
- **Every 1 DAY**: Recommended for most sites
- **Every 12 HOUR**: For high-traffic sites
- **Every 6 HOUR**: For very high-traffic sites
- **Every 1 WEEK**: For low-traffic sites

### Monitoring Performance
```sql
-- Check table size
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'tbl_remember_tokens';
```

## 🎯 Best Practices

1. **Enable Persistent Configuration**: Add to MySQL config file
2. **Monitor Regularly**: Check cleanup logs weekly
3. **Test Events**: Run manual cleanup tests monthly
4. **Backup Events**: Include in database backup strategy
5. **Log Monitoring**: Check MySQL error logs for event issues
6. **Performance Monitoring**: Monitor table size and cleanup frequency

## 📞 Support

For issues with Event Scheduler setup:
1. Check MySQL error logs
2. Verify user privileges
3. Test manual cleanup first
4. Use the web-based Event Scheduler Manager
5. Consult MySQL documentation for Event Scheduler

## 🔗 Related Files

- `event_scheduler_setup.sql` - Complete SQL setup script
- `event_scheduler_manager.php` - Web-based management interface
- `setup_event_scheduler.php` - Command-line setup script
- `cleanup_dashboard.php` - Monitoring dashboard
- `test_persistent_login.php` - System testing interface
