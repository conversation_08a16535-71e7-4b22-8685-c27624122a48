-- Database Migration: Add WhatsApp Payment Status
-- This script adds 'whatsapp' to the payment_status enum in the orders table

-- Check current table structure
-- SHOW COLUMNS FROM orders LIKE 'payment_status';

-- Add 'whatsapp' to the payment_status enum
ALTER TABLE orders 
MODIFY COLUMN payment_status ENUM('pending', 'success', 'failed', 'whatsapp') DEFAULT 'pending';

-- Verify the change
-- SHOW COLUMNS FROM orders LIKE 'payment_status';

-- Optional: Add index for WhatsApp orders for better query performance
-- CREATE INDEX idx_whatsapp_orders ON orders (payment_status, created_at) WHERE payment_status = 'whatsapp';

-- Note: Run this migration on your database before testing the WhatsApp order system
-- Example usage:
-- mysql -u your_username -p your_database_name < database_migration_whatsapp.sql
