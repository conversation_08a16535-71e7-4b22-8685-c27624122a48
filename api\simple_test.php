<?php
/**
 * Simple API Test
 * Tests API functionality without URL rewriting
 */

echo "<h1>Simple API Test</h1>";

// Test 1: Direct API call simulation
echo "<h2>1. Direct API Call Test</h2>";

try {
    // Simulate API request
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/ecom/api/v1/settings/app';
    
    // Capture output
    ob_start();
    
    // Include the API
    include __DIR__ . '/index.php';
    
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✓ API executed without errors</p>";
    echo "<h3>API Response:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ API error: " . $e->getMessage() . "</p>";
}

// Test 2: Test individual endpoints
echo "<h2>2. Test Individual Endpoints</h2>";

// Include configuration
require_once __DIR__ . '/config/config.php';

// Test settings endpoint directly
echo "<h3>Testing Settings Endpoint:</h3>";
try {
    // Simulate the endpoint call
    $segments = ['settings', 'app'];
    $method = 'GET';
    $input = [];
    
    ob_start();
    include __DIR__ . '/endpoints/settings.php';
    $settings_output = ob_get_clean();
    
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($settings_output);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Settings endpoint error: " . $e->getMessage() . "</p>";
}

// Test 3: Manual API calls
echo "<h2>3. Manual API Response Test</h2>";

try {
    // Test Response class directly
    header('Content-Type: application/json');
    
    $test_data = [
        'message' => 'API is working!',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => API_VERSION
    ];
    
    echo "<h3>Sample API Response:</h3>";
    echo "<pre style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
    echo json_encode([
        'status' => 'success',
        'message' => 'Test response',
        'timestamp' => date('c'),
        'api_version' => API_VERSION,
        'data' => $test_data
    ], JSON_PRETTY_PRINT);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Response test error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p>If you see JSON responses above, the API core is working!</p>";
echo "<p>The issue might be with URL rewriting or server configuration.</p>";
?>
