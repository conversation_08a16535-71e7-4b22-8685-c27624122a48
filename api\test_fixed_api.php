<?php
/**
 * Test Fixed API
 * Test the main API after fixes
 */

echo "<h1>🔧 Testing Fixed API</h1>";

$test_email = '<EMAIL>';
$test_password = 'changawa';

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>🧪 Testing Main API After Fixes</h2>";
echo "<p>Testing both the main API routing and the simple login endpoint.</p>";
echo "</div>";

function testApiEndpoint($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error,
        'error' => $error,
        'http_code' => $http_code,
        'content_type' => $content_type,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test 1: API Root
echo "<h2>1. API Root Test</h2>";

$root_result = testApiEndpoint('http://localhost/ecom/api/v1/');

echo "<p><strong>URL:</strong> http://localhost/ecom/api/v1/</p>";
echo "<p><strong>HTTP Code:</strong> {$root_result['http_code']}</p>";
echo "<p><strong>Content Type:</strong> {$root_result['content_type']}</p>";

if ($root_result['data'] && $root_result['data']['status'] === 'success') {
    echo "<p style='color: green; font-weight: bold;'>✅ API Root Working!</p>";
    echo "<p><strong>API Version:</strong> " . ($root_result['data']['data']['version'] ?? 'Unknown') . "</p>";
} else {
    echo "<p style='color: red;'>❌ API Root Failed</p>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
    echo htmlspecialchars(substr($root_result['response'], 0, 500));
    echo "</pre>";
}

// Test 2: Main API Auth Login
echo "<h2>2. Main API Auth Login Test</h2>";

$main_auth_result = testApiEndpoint('http://localhost/ecom/api/v1/auth/login', 'POST', [
    'email' => $test_email,
    'password' => $test_password
]);

echo "<p><strong>URL:</strong> http://localhost/ecom/api/v1/auth/login</p>";
echo "<p><strong>HTTP Code:</strong> {$main_auth_result['http_code']}</p>";
echo "<p><strong>Content Type:</strong> {$main_auth_result['content_type']}</p>";

if ($main_auth_result['error']) {
    echo "<p style='color: red;'>❌ cURL Error: {$main_auth_result['error']}</p>";
} else {
    echo "<p><strong>Response:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars($main_auth_result['response']);
    echo "</pre>";
    
    if ($main_auth_result['data'] && $main_auth_result['data']['status'] === 'success') {
        echo "<p style='color: green; font-weight: bold;'>✅ Main API Auth Working!</p>";
        $main_token = $main_auth_result['data']['data']['token'] ?? null;
        if ($main_token) {
            echo "<p style='color: green;'>✅ JWT Token received from main API</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Main API Auth Failed</p>";
        if ($main_auth_result['data']) {
            echo "<p><strong>Error:</strong> " . ($main_auth_result['data']['message'] ?? 'Unknown error') . "</p>";
        }
    }
}

// Test 3: Simple Login (for comparison)
echo "<h2>3. Simple Login Test (Comparison)</h2>";

$simple_result = testApiEndpoint('http://localhost/ecom/api/simple_login.php', 'POST', [
    'email' => $test_email,
    'password' => $test_password
]);

echo "<p><strong>URL:</strong> http://localhost/ecom/api/simple_login.php</p>";
echo "<p><strong>HTTP Code:</strong> {$simple_result['http_code']}</p>";

if ($simple_result['data'] && $simple_result['data']['status'] === 'success') {
    echo "<p style='color: green; font-weight: bold;'>✅ Simple Login Working!</p>";
    $simple_token = $simple_result['data']['data']['token'] ?? null;
    if ($simple_token) {
        echo "<p style='color: green;'>✅ JWT Token received from simple login</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Simple Login Failed</p>";
}

// Test 4: Compare responses
echo "<h2>4. Response Comparison</h2>";

echo "<div style='display: flex; gap: 20px;'>";

echo "<div style='flex: 1; border: 1px solid #ddd; padding: 15px; border-radius: 5px;'>";
echo "<h3>Main API Response</h3>";
if ($main_auth_result['data']) {
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px;'>";
    echo json_encode($main_auth_result['data'], JSON_PRETTY_PRINT);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>No valid JSON response</p>";
}
echo "</div>";

echo "<div style='flex: 1; border: 1px solid #ddd; padding: 15px; border-radius: 5px;'>";
echo "<h3>Simple Login Response</h3>";
if ($simple_result['data']) {
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px;'>";
    echo json_encode($simple_result['data'], JSON_PRETTY_PRINT);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>No valid JSON response</p>";
}
echo "</div>";

echo "</div>";

// Test 5: Products endpoint
echo "<h2>5. Products Endpoint Test</h2>";

$products_result = testApiEndpoint('http://localhost/ecom/api/v1/products?limit=3');

echo "<p><strong>URL:</strong> http://localhost/ecom/api/v1/products?limit=3</p>";
echo "<p><strong>HTTP Code:</strong> {$products_result['http_code']}</p>";

if ($products_result['data'] && $products_result['data']['status'] === 'success') {
    echo "<p style='color: green; font-weight: bold;'>✅ Products Endpoint Working!</p>";
    $products = $products_result['data']['data']['products'] ?? [];
    echo "<p><strong>Products Found:</strong> " . count($products) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Products Endpoint Failed</p>";
    if ($products_result['response']) {
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px; max-height: 150px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($products_result['response'], 0, 300));
        echo "</pre>";
    }
}

// Summary and recommendations
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>📊 Test Summary</h2>";

$main_api_working = $main_auth_result['data'] && $main_auth_result['data']['status'] === 'success';
$simple_login_working = $simple_result['data'] && $simple_result['data']['status'] === 'success';

if ($main_api_working) {
    echo "<p style='color: green; font-weight: bold;'>🎉 MAIN API IS NOW WORKING!</p>";
    echo "<p>✅ API routing fixed</p>";
    echo "<p>✅ Authentication working</p>";
    echo "<p>✅ JSON responses clean</p>";
    echo "<p><strong>Recommended for Flutter:</strong> Use the main API endpoints</p>";
    echo "<ul>";
    echo "<li><strong>Login:</strong> <code>http://localhost/ecom/api/v1/auth/login</code></li>";
    echo "<li><strong>Products:</strong> <code>http://localhost/ecom/api/v1/products</code></li>";
    echo "<li><strong>API Root:</strong> <code>http://localhost/ecom/api/v1/</code></li>";
    echo "</ul>";
} elseif ($simple_login_working) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ MAIN API STILL HAS ISSUES</p>";
    echo "<p>✅ Simple login endpoint working</p>";
    echo "<p>❌ Main API routing needs more work</p>";
    echo "<p><strong>Temporary solution for Flutter:</strong> Use simple login endpoint</p>";
    echo "<ul>";
    echo "<li><strong>Login:</strong> <code>http://localhost/ecom/api/simple_login.php</code></li>";
    echo "<li>Other endpoints may need similar fixes</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ BOTH ENDPOINTS HAVE ISSUES</p>";
    echo "<p>Need to investigate further:</p>";
    echo "<ul>";
    echo "<li>Database connection</li>";
    echo "<li>PHP configuration</li>";
    echo "<li>Apache settings</li>";
    echo "<li>File permissions</li>";
    echo "</ul>";
}

echo "</div>";

// Flutter implementation
if ($main_api_working || $simple_login_working) {
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>📱 Flutter Implementation</h2>";
    
    $working_url = $main_api_working ? 'http://localhost/ecom/api/v1/auth/login' : 'http://localhost/ecom/api/simple_login.php';
    
    echo "<p>Use this working endpoint in your Flutter app:</p>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
    echo htmlspecialchars("
// Working login function
Future<Map<String, dynamic>> login(String email, String password) async {
  try {
    final response = await http.post(
      Uri.parse('{$working_url}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'email': email,
        'password': password,
      }),
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      
      if (data['status'] == 'success') {
        // Save token
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('auth_token', data['data']['token']);
        await prefs.setString('user_data', json.encode(data['data']['user']));
        
        return {'success': true, 'user': data['data']['user']};
      } else {
        return {'success': false, 'error': data['message']};
      }
    } else {
      return {'success': false, 'error': 'HTTP \${response.statusCode}'};
    }
  } catch (e) {
    return {'success': false, 'error': 'Network error: \$e'};
  }
}
");
    echo "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>API test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
