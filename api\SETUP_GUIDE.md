# 🛠️ Complete Setup Guide for Junior Developers

**Step-by-step instructions to get your API running in 10 minutes**

## 📋 What You Need

### Required Software
- **XAMPP** (includes Apache, MySQL, PHP) - [Download here](https://www.apachefriends.org/)
- **Text Editor** (VS Code recommended) - [Download here](https://code.visualstudio.com/)
- **Web Browser** (Chrome, Firefox, etc.)

### Knowledge Requirements
- Basic understanding of files and folders
- Basic PHP concepts (variables, arrays)
- Basic understanding of JSON format

---

## 🚀 Step 1: Install XAMPP

1. **Download XAMPP**
   - Go to https://www.apachefriends.org/
   - Download for your operating system (Windows/Mac/Linux)

2. **Install XAMPP**
   - Run the installer
   - Install to default location: `C:\xampp\` (Windows) or `/Applications/XAMPP/` (Mac)
   - Select Apache, MySQL, PHP (default selection is fine)

3. **Start XAMPP**
   - Open XAMPP Control Panel
   - Click "Start" for **Apache** and **MySQL**
   - Both should show green "Running" status

4. **Test XAMPP**
   - Open browser and go to: `http://localhost`
   - You should see XAMPP welcome page ✅

---

## 📁 Step 2: Setup Project Files

1. **Locate Web Directory**
   ```
   Windows: C:\xampp\htdocs\
   Mac: /Applications/XAMPP/htdocs/
   Linux: /opt/lampp/htdocs/
   ```

2. **Create Project Folder**
   ```
   📁 htdocs\
   └── 📁 ecom\          ← Create this folder
       └── 📁 api\       ← Place API files here
   ```

3. **Copy API Files**
   - Extract/copy all API files into `htdocs/ecom/api/`
   - Your structure should look like:
   ```
   📁 htdocs\ecom\api\
   ├── 📄 index.php
   ├── 📄 install.php
   ├── 📁 config\
   ├── 📁 endpoints\
   ├── 📁 utils\
   └── ...
   ```

---

## 🗄️ Step 3: Setup Database

### Option A: Automatic Setup (Recommended)
1. **Open Browser**
   - Go to: `http://localhost/ecom/api/install.php`

2. **Run Installation**
   - Click "Install API"
   - Wait for green checkmarks ✅
   - Installation creates database tables automatically

3. **Delete Install File**
   - After successful installation, delete `install.php` for security

### Option B: Manual Setup
1. **Open phpMyAdmin**
   - Go to: `http://localhost/phpmyadmin`
   - Username: `root`, Password: (leave empty)

2. **Create Database**
   - Click "New" on left sidebar
   - Database name: `ecom_db`
   - Click "Create"

3. **Import Database**
   - Select your database
   - Click "Import" tab
   - Choose your SQL file
   - Click "Go"

---

## 🧪 Step 4: Test Your API

1. **Test Installation**
   ```
   🌐 Visit: http://localhost/ecom/api/test.php
   ✅ All tests should show green checkmarks
   ```

2. **Test API Root**
   ```
   🌐 Visit: http://localhost/ecom/api/v1/
   📋 Should return JSON with API information
   ```

3. **Test Specific Endpoint**
   ```
   🌐 Visit: http://localhost/ecom/api/v1/products?limit=5
   📋 Should return JSON with product list
   ```

---

## 🔧 Step 5: Configure for Development

### Update Configuration (Optional)
Edit `api/config/config.php`:

```php
// Database settings
define('DB_HOST', 'localhost');
define('DB_NAME', 'ecom_db');
define('DB_USER', 'root');
define('DB_PASS', '');

// API settings
define('API_VERSION', '1.0.0');
define('JWT_SECRET', 'your-secret-key-here');

// Currency
define('DEFAULT_CURRENCY', 'TZS');
define('DEFAULT_INSTALLATION_FEE', 15000);
```

### Enable Error Reporting (For Development)
Add to top of `api/index.php`:
```php
// For development only - remove in production
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

---

## 📱 Step 6: Setup Flutter Project

### Add Dependencies
In your `pubspec.yaml`:
```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0
  shared_preferences: ^2.2.2
  json_annotation: ^4.8.1

dev_dependencies:
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
```

### Create API Service
Create `lib/services/api_service.dart`:
```dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const String baseUrl = 'http://localhost/ecom/api/v1';
  
  // Test connection
  static Future<bool> testConnection() async {
    try {
      final response = await http.get(Uri.parse(baseUrl));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
  
  // Get products
  static Future<List<dynamic>> getProducts() async {
    final response = await http.get(
      Uri.parse('$baseUrl/products?limit=10'),
      headers: {'Content-Type': 'application/json'},
    );
    
    final data = json.decode(response.body);
    if (data['status'] == 'success') {
      return data['data']['products'];
    }
    throw Exception(data['message']);
  }
}
```

### Test in Flutter
```dart
// In your widget
Future<void> testApi() async {
  try {
    final products = await ApiService.getProducts();
    print('Got ${products.length} products');
    // Update UI with products
  } catch (e) {
    print('Error: $e');
    // Show error message
  }
}
```

---

## 🐛 Troubleshooting

### Common Issues

**❌ "XAMPP won't start"**
```
✅ Check if port 80 is free (close Skype, IIS)
✅ Run XAMPP as Administrator
✅ Change Apache port to 8080 if needed
```

**❌ "Can't access localhost"**
```
✅ Make sure Apache is running (green in XAMPP)
✅ Try http://127.0.0.1 instead
✅ Check Windows firewall
```

**❌ "Database connection failed"**
```
✅ Make sure MySQL is running (green in XAMPP)
✅ Check database credentials in config.php
✅ Verify database exists in phpMyAdmin
```

**❌ "API returns errors"**
```
✅ Check PHP error logs in XAMPP
✅ Visit /api/test.php to see specific errors
✅ Verify file permissions
```

**❌ "Flutter can't connect"**
```
✅ Use http://********:80/ecom/api/v1 for Android emulator
✅ Use http://localhost/ecom/api/v1 for iOS simulator
✅ Check if API is accessible in browser first
```

### Getting Help

1. **Check API Status**
   ```
   🌐 Visit: http://localhost/ecom/api/test.php
   ```

2. **View Error Logs**
   ```
   📁 Check: C:\xampp\apache\logs\error.log
   ```

3. **Test Individual Components**
   ```
   🌐 Database: http://localhost/phpmyadmin
   🌐 API Root: http://localhost/ecom/api/v1/
   🌐 Products: http://localhost/ecom/api/v1/products
   ```

---

## ✅ Success Checklist

- [ ] XAMPP installed and running (Apache + MySQL green)
- [ ] API files in correct location (`htdocs/ecom/api/`)
- [ ] Database created and tables imported
- [ ] API test page shows all green checkmarks
- [ ] API root returns JSON response
- [ ] Flutter project can connect to API
- [ ] Products endpoint returns data

---

## 🎉 You're Ready!

Your API is now set up and ready for development. Next steps:

1. **Read the full documentation**: `README.md`
2. **Check the quick reference**: `QUICK_REFERENCE.md`
3. **Start building your Flutter app**
4. **Test different endpoints**
5. **Implement authentication**

**Happy coding! 🚀**
