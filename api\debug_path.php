<?php
/**
 * Debug Path Parsing
 * Shows how the API is parsing the request path
 */

echo "<h1>Path Parsing Debug</h1>";

echo "<h2>Server Variables</h2>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>PATH_INFO:</strong> " . ($_SERVER['PATH_INFO'] ?? 'Not set') . "</p>";
echo "<p><strong>QUERY_STRING:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "</p>";

$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

echo "<h2>Path Processing</h2>";
echo "<p><strong>Original Path:</strong> " . $path . "</p>";

// Remove common base paths
$base_patterns = [
    '/ecom/api/v1/',
    '/ecom/api/',
    '/api/v1/',
    '/api/'
];

echo "<p><strong>Base Patterns to Remove:</strong></p>";
echo "<ul>";
foreach ($base_patterns as $pattern) {
    echo "<li>" . $pattern . "</li>";
}
echo "</ul>";

$clean_path = $path;
$matched_pattern = 'None';

foreach ($base_patterns as $pattern) {
    if (strpos($path, $pattern) !== false) {
        $clean_path = str_replace($pattern, '', $path);
        $matched_pattern = $pattern;
        break;
    }
}

echo "<p><strong>Matched Pattern:</strong> " . $matched_pattern . "</p>";
echo "<p><strong>Path After Base Removal:</strong> " . $clean_path . "</p>";

// Remove filename if present
$filename_patterns = ['debug_path.php', 'test_api.php', 'index.php'];
foreach ($filename_patterns as $filename) {
    if (strpos($clean_path, $filename) !== false) {
        $clean_path = str_replace($filename, '', $clean_path);
        echo "<p><strong>Removed Filename:</strong> " . $filename . "</p>";
        break;
    }
}

$clean_path = trim($clean_path, '/');
echo "<p><strong>Final Clean Path:</strong> '" . $clean_path . "'</p>";

$path_parts = explode('/', $clean_path);
if (empty($path_parts[0])) {
    $path_parts[0] = '';
}

echo "<p><strong>Path Parts:</strong></p>";
echo "<ul>";
foreach ($path_parts as $index => $part) {
    echo "<li>[$index] = '" . $part . "'</li>";
}
echo "</ul>";

$endpoint = $path_parts[0] ?? '';
echo "<p><strong>Detected Endpoint:</strong> '" . $endpoint . "'</p>";

echo "<h2>Test Different URLs</h2>";
echo "<p>Try these URLs to test path parsing:</p>";
echo "<ul>";
echo "<li><a href='/ecom/api/debug_path.php' target='_blank'>Direct file access</a></li>";
echo "<li><a href='/ecom/api/debug_path.php/' target='_blank'>With trailing slash</a></li>";
echo "<li><a href='/ecom/api/debug_path.php/products' target='_blank'>With products path</a></li>";
echo "<li><a href='/ecom/api/debug_path.php/settings/app' target='_blank'>With settings/app path</a></li>";
echo "</ul>";

echo "<h2>Recommended Solution</h2>";
echo "<p>For the API to work correctly, you should:</p>";
echo "<ol>";
echo "<li>Access the API through the main index.php file</li>";
echo "<li>Use URL rewriting (.htaccess) to route requests</li>";
echo "<li>Or use the proper API endpoints like:</li>";
echo "<ul>";
echo "<li><a href='/ecom/api/v1/' target='_blank'>/ecom/api/v1/</a></li>";
echo "<li><a href='/ecom/api/v1/products' target='_blank'>/ecom/api/v1/products</a></li>";
echo "<li><a href='/ecom/api/v1/settings/app' target='_blank'>/ecom/api/v1/settings/app</a></li>";
echo "</ul>";
echo "</ol>";
?>
