<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Get category ID from URL
$category_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Fetch category details
$statement = $pdo->prepare("SELECT * FROM tbl_top_category WHERE tcat_id = ?");
$statement->execute([$category_id]);
$category = $statement->fetch(PDO::FETCH_ASSOC);

// Fetch sub-categories
$statement = $pdo->prepare("SELECT * FROM tbl_mid_category WHERE tcat_id = ? ORDER BY mcat_name ASC");
$statement->execute([$category_id]);
$subcategories = $statement->fetchAll(PDO::FETCH_ASSOC);

// Fetch products
$statement = $pdo->prepare("SELECT * FROM tbl_product WHERE tcat_id = ? AND p_is_active = 1 ORDER BY p_id DESC");
$statement->execute([$category_id]);
$products = $statement->fetchAll(PDO::FETCH_ASSOC);

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright']
    ?? "© 2025 SMART LIFE. All rights reserved.";
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><?= htmlspecialchars($category['tcat_name']) ?> | SMART LIFE</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 text-gray-800 category-page">

  <!-- Header -->
  <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
    <div class="container mx-auto px-4 flex items-center justify-between py-4">
      <a href="index.php" class="text-2xl font-bold text-gray-900">
        SMART LIFE TZ<span class="text-blue-600">.</span>
      </a>
      <nav class="hidden md:flex items-center space-x-6">
        <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
        <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
        <a href="index.php#products" class="hover:text-blue-600 transition">Products</a>
        <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
        <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>

        <!-- Search -->
        <div class="relative">
          <input id="searchInput" type="text" placeholder="Search products, categories..."
                 class="w-64 px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                 autocomplete="off">
          <div id="searchSuggestions"
               class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
            <!-- suggestions will appear here -->
          </div>
        </div>

        <!-- Cart -->
        <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
          🛒
          <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
        </a>
      </nav>
      <!-- Mobile Menu Button -->
      <button id="mobileMenuButton" class="md:hidden flex items-center">
        <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 8h16M4 16h16"/>
        </svg>
      </button>
    </div>
  </header>

  <!-- Mobile Menu -->
  <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
    <div class="flex flex-col h-full">
      <div class="flex justify-between items-center p-4 border-b">
        <a href="index.php" class="text-xl font-bold text-gray-900">
          SMART LIFE TZ<span class="text-[#00c2ff]">.</span>
        </a>
        <button id="closeMobileMenu" class="text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
        <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
        <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
        <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
        <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
        <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>

        <!-- Search in Mobile Menu -->
        <div class="relative mt-4">
          <input id="mobileSearchInput" type="text" placeholder="Search products, categories..."
                 class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                 autocomplete="off">
          <div id="mobileSearchSuggestions"
               class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
            <!-- suggestions will appear here -->
          </div>
        </div>

        <!-- Cart in Mobile Menu -->
        <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
          <span class="text-xl mr-2">🛒</span>
          <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
        </a>
      </nav>
    </div>
  </div>

  <!-- Backdrop for mobile menu -->
  <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

  <main class="pt-24 pb-12">
    <section class="container mx-auto px-4">
      <!-- Section Header -->
      <div class="text-center mb-8">
        <h2 class="text-4xl font-extrabold text-gray-900">
          <?= htmlspecialchars($category['tcat_name']) ?>
        </h2>
        <?php if ($subcategories): ?>
          <div class="mt-4 flex flex-wrap justify-center gap-2">
            <button class="subcategory-btn px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition"
                    data-subcat-id="all">All</button>
            <?php foreach ($subcategories as $subcat): ?>
              <button class="subcategory-btn px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition"
                      data-subcat-id="<?= $subcat['mcat_id'] ?>">
                <?= htmlspecialchars($subcat['mcat_name']) ?>
              </button>
            <?php endforeach; ?>
          </div>
        <?php endif; ?>
      </div>

      <!-- Category Sidebar and Products Grid -->
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- Category Sidebar -->
        <div class="lg:w-64 flex-shrink-0">
          <div class="bg-white rounded-lg shadow p-4 sticky top-24">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Categories</h3>
              <button id="toggleCategories" class="lg:hidden text-gray-500 hover:text-gray-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
            </div>
            <div id="categoriesList" class="lg:block hidden">
              <?php
              // Fetch all top categories
              $statement = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC");
              $statement->execute();
              $all_categories = $statement->fetchAll(PDO::FETCH_ASSOC);
              ?>
              <ul class="space-y-2">
                <?php foreach ($all_categories as $cat): ?>
                  <li>
                    <a href="category.php?id=<?= $cat['tcat_id'] ?>"
                       class="flex items-center justify-between px-3 py-2 rounded-md <?= $cat['tcat_id'] == $category_id ? 'bg-[#00c2ff] text-white' : 'text-gray-700 hover:bg-gray-100' ?>">
                      <span><?= htmlspecialchars($cat['tcat_name']) ?></span>
                      <?php if ($cat['tcat_id'] == $category_id): ?>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                      <?php endif; ?>
                    </a>
                  </li>
                <?php endforeach; ?>
              </ul>
            </div>
          </div>
        </div>

        <!-- Products Grid -->
        <div class="flex-1">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <?php if ($products): ?>
              <?php foreach ($products as $product):
                // Get sub-category ID
                $stmt2 = $pdo->prepare("SELECT mcat_id FROM tbl_product WHERE p_id = ?");
                $stmt2->execute([$product['p_id']]);
                $prodSub = $stmt2->fetch(PDO::FETCH_ASSOC);
                $subcat_id = $prodSub['mcat_id'] ?? 'all';
              ?>
                <div class="product-card bg-white shadow rounded-lg overflow-hidden cursor-pointer transform hover:-translate-y-1 transition w-full sm:max-w-[280px] mx-auto"
                     data-subcat-id="<?= $subcat_id ?>"
                     onclick="window.location.href='product_detail.php?id=<?= $product['p_id'] ?>'">
                  <img src="../assets/uploads/<?= htmlspecialchars($product['p_featured_photo']) ?>"
                       alt="<?= htmlspecialchars($product['p_name']) ?>"
                       class="h-48 sm:h-40 w-full object-cover">
                  <div class="p-4 sm:p-3">
                    <h3 class="text-lg sm:text-base font-semibold text-gray-800 line-clamp-2"><?= htmlspecialchars($product['p_name']) ?></h3>
                    <div class="text-[#00c2ff] font-bold mt-2 sm:mt-1">Tsh <?= htmlspecialchars($product['p_current_price']) ?></div>
                    <p class="text-gray-600 text-sm sm:text-xs mt-2 sm:mt-1 line-clamp-2">
                      <?php
                        $desc = strip_tags($product['p_short_description']);
                        echo strlen($desc) > 100 ? substr($desc, 0, 100) . '...' : $desc;
                      ?>
                    </p>
                    <button class="mt-4 sm:mt-2 w-full inline-flex items-center justify-center bg-[#00c2ff] hover:bg-[#00a8e0] text-white text-sm font-medium py-2 sm:py-1.5 rounded add-to-cart"
                            data-product-id="<?= $product['p_id'] ?>" onclick="event.stopPropagation()">
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2 sm:mr-1" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                      </svg>
                      Add to Cart
                    </button>
                  </div>
                </div>
              <?php endforeach; ?>
            <?php else: ?>
              <p class="col-span-full text-center text-gray-500">No products found in this category.</p>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-gray-800 text-gray-200 py-8">
    <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-4 gap-8">
      <div>
        <h4 class="text-xl font-semibold mb-4">SMART LIFE<span class="text-[#00c2ff]">.</span></h4>
        <p>Your Gateway to a Smarter Luxurious Home</p>
      </div>
      <div>
        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
        <ul class="space-y-2">
          <li><a href="index.php#home" class="hover:text-[#00c2ff] transition">Home</a></li>
          <li><a href="index.php#about" class="hover:text-[#00c2ff] transition">About Us</a></li>
          <li><a href="index.php#products" class="hover:text-[#00c2ff] transition">Products</a></li>
          <li><a href="index.php#gallery" class="hover:text-[#00c2ff] transition">Best Deals</a></li>
          <li><a href="index.php#contact" class="hover:text-[#00c2ff] transition">Contact</a></li>
        </ul>
      </div>
      <div>
        <h4 class="text-lg font-semibold mb-4">Information</h4>
        <ul class="space-y-2">
          
          
          <li><a href="#" class="hover:text-[#00c2ff] transition">Shipping Policy</a></li>
          <li><a href="#" class="hover:text-[#00c2ff] transition">Privacy Policy</a></li>
          <li><a href="#" class="hover:text-[#00c2ff] transition">Terms &amp; Conditions</a></li>
        </ul>
      </div>
      <div>
        <h4 class="text-lg font-semibold mb-4">Newsletter</h4>
        <p class="mb-4">Subscribe for exclusive offers and smart home insights.</p>
        <form action="subscribe.php" method="post" class="flex flex-col">
          <div class="flex">
            <input type="email" name="subscriber_email" required
                   class="w-full px-4 py-2 rounded-l-md bg-gray-700 text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent">
            <button type="submit"
                    class="px-4 bg-[#00c2ff] hover:bg-[#00a8e0] text-white font-medium rounded-r-md transition">
              →
            </button>
          </div>
          <div id="newsletter-message" class="mt-2"></div>
        </form>
      </div>
    </div>
    <div class="mt-8 border-t border-gray-700 pt-4 text-center text-sm">
      <?= $footer_copyright ?>
      <div class="mt-2 space-x-4">
        <a href="#" class="hover:text-[#00c2ff] transition">Privacy</a>
        <a href="#" class="hover:text-[#00c2ff] transition">Terms</a>
        <a href="#" class="hover:text-[#00c2ff] transition">Sitemap</a>
      </div>
    </div>
  </footer>

  <!-- External JavaScript -->
  <script src="js/script.js"></script>
  <script src="js/subcategory-filter.js"></script>

  <!-- Inline Page-Specific JavaScript -->
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      // Initialize cart if not exists
      if (!localStorage.getItem('cart')) {
        localStorage.setItem('cart', JSON.stringify([]));
      }

      // Update cart count display
      updateCartCount();

      // Add to cart buttons
      document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function(e) {
          e.stopPropagation(); // Prevent any card-level click events
          const productId = this.getAttribute('data-product-id');
          addToCart(productId);
        });
      });

      // Updated addToCart function to mimic product_detail.php processing
      function addToCart(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', 1);
        // If needed, you can append additional fields like a CSRF token here

        // Optionally add UI loading state here

        fetch('add_to_cart.php', {
          method: 'POST',
          body: formData
        })
        .then(response => {
          const contentType = response.headers.get("content-type");
          if (response.ok && contentType && contentType.includes("application/json")) {
            return response.json();
          } else {
            return response.text().then(text => {
              throw new Error(`Server error: ${text.substring(0,200)}`);
            });
          }
        })
        .then(data => {
          if (data && data.status === 'success' && data.added_item) {
            let cart = JSON.parse(localStorage.getItem('cart') || '[]');
            const productIdStr = String(data.added_item.product_id);
            const existingIndex = cart.findIndex(item => String(item.product_id) === productIdStr);
            if (existingIndex > -1) {
              cart[existingIndex].quantity = data.added_item.quantity;
            } else {
              cart.push(data.added_item);
            }
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            alert('Product added to cart!');
          } else {
            alert(data.message || 'Error adding product to cart.');
          }
        })
        .catch(error => {
          console.error("Add to Cart Error:", error);
          alert('Error adding product to cart.');
        });
      }

      function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart'));
        const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
        document.querySelector('.cart-count').textContent = totalItems;
      }

      // Get current category ID from URL
      const currentCategoryId = new URLSearchParams(window.location.search).get('id');

      // Store current category ID for potential use in other pages
      localStorage.setItem('selectedCategory', currentCategoryId);

      // Handle category links in sidebar
      document.querySelectorAll('#categoriesList a').forEach(link => {
        link.addEventListener('click', function(e) {
          const newCategoryId = new URLSearchParams(this.href.split('?')[1]).get('id');
          if (newCategoryId !== currentCategoryId) {
            localStorage.setItem('selectedCategory', newCategoryId);
            localStorage.setItem('selectedSubcat', 'all');
          }
        });
      });

      // Search functionality
      const searchInput = document.getElementById('searchInput');
      const searchSuggestions = document.getElementById('searchSuggestions');
      let searchTimeout;

      function highlightText(text, term) {
        if (!term) return text;
        const re = new RegExp(`(${term})`, 'gi');
        return text.replace(re, '<span class="bg-yellow-100 text-gray-900">$1</span>');
      }

      async function fetchSuggestions(q) {
        if (q.length < 2) {
          searchSuggestions.classList.add('hidden');
          return;
        }
        try {
          const res = await fetch(`search_suggestions.php?q=${encodeURIComponent(q)}`);
          const data = await res.json();
          if (data.length) {
            searchSuggestions.innerHTML = data.map(item => `
              <div class="suggestion-item flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                   data-type="${item.type}" data-id="${item.id}">
                <div class="icon mr-3 text-[#00c2ff]">${item.type==='product'?'🛍️':'📁'}</div>
                <div class="name flex-1 text-gray-700">${highlightText(item.name, q)}</div>
                <div class="ml-2 text-xs text-gray-400">${item.type}</div>
              </div>
            `).join('');
            searchSuggestions.classList.remove('hidden');
          } else {
            searchSuggestions.innerHTML = '<div class="px-4 py-3 text-gray-500 text-center">No results found</div>';
            searchSuggestions.classList.remove('hidden');
          }
        } catch (e) {
          console.error('Error fetching suggestions:', e);
        }
      }

      searchInput.addEventListener('input', e => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => fetchSuggestions(e.target.value), 300);
      });

      searchInput.addEventListener('focus', () => {
        if (searchInput.value.length >= 2) fetchSuggestions(searchInput.value);
      });

      document.addEventListener('click', e => {
        if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
          searchSuggestions.classList.add('hidden');
        }
      });

      searchSuggestions.addEventListener('click', e => {
        const item = e.target.closest('.suggestion-item');
        if (!item) return;
        const type = item.dataset.type, id = item.dataset.id;
        if (type === 'product') {
          window.location.href = `product_detail.php?id=${id}`;
        } else {
          window.location.href = `category.php?id=${id}`;
        }
      });

      // Mobile Menu Functionality
      const mobileMenuButton = document.getElementById('mobileMenuButton');
      const closeMobileMenu = document.getElementById('closeMobileMenu');
      const mobileMenu = document.getElementById('mobileMenu');
      const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');
      const mobileSearchInput = document.getElementById('mobileSearchInput');
      const mobileSearchSuggestions = document.getElementById('mobileSearchSuggestions');

      function toggleMobileMenu() {
        mobileMenu.classList.toggle('translate-x-full');
        mobileMenuBackdrop.classList.toggle('hidden');
        document.body.style.overflow = mobileMenu.classList.contains('translate-x-full') ? 'auto' : 'hidden';
      }

      mobileMenuButton.addEventListener('click', toggleMobileMenu);
      closeMobileMenu.addEventListener('click', toggleMobileMenu);
      mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

      // Mobile Search Functionality
      let mobileSearchTimeout;
      mobileSearchInput.addEventListener('input', e => {
        clearTimeout(mobileSearchTimeout);
        mobileSearchTimeout = setTimeout(() => fetchSuggestions(e.target.value, mobileSearchSuggestions), 300);
      });

      // Update fetchSuggestions function to work with both search inputs
      async function fetchSuggestions(q, suggestionsContainer) {
        if (q.length < 2) {
          suggestionsContainer.classList.add('hidden');
          return;
        }
        try {
          const res = await fetch(`search_suggestions.php?q=${encodeURIComponent(q)}`);
          const data = await res.json();
          if (data.length) {
            suggestionsContainer.innerHTML = data.map(item => `
              <div class="suggestion-item flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                   data-type="${item.type}" data-id="${item.id}">
                <div class="icon mr-3 text-[#00c2ff]">${item.type==='product'?'🛍️':'📁'}</div>
                <div class="name flex-1 text-gray-700">${highlightText(item.name, q)}</div>
                <div class="ml-2 text-xs text-gray-400">${item.type}</div>
              </div>
            `).join('');
            suggestionsContainer.classList.remove('hidden');
          } else {
            suggestionsContainer.innerHTML = '<div class="px-4 py-3 text-gray-500 text-center">No results found</div>';
            suggestionsContainer.classList.remove('hidden');
          }
        } catch (e) {
          console.error('Error fetching suggestions:', e);
        }
      }

      // Handle mobile search suggestions clicks
      mobileSearchSuggestions.addEventListener('click', e => {
        const item = e.target.closest('.suggestion-item');
        if (!item) return;
        const type = item.dataset.type, id = item.dataset.id;
        if (type === 'product') {
          window.location.href = `product_detail.php?id=${id}`;
        } else {
          window.location.href = `category.php?id=${id}`;
        }
      });

      // Category Sidebar Toggle
      const toggleCategories = document.getElementById('toggleCategories');
      const categoriesList = document.getElementById('categoriesList');

      if (toggleCategories && categoriesList) {
        toggleCategories.addEventListener('click', () => {
          categoriesList.classList.toggle('hidden');
          // Rotate the arrow icon
          const arrow = toggleCategories.querySelector('svg');
          arrow.classList.toggle('rotate-180');
        });
      }

      // Newsletter Form Handling
      const newsletterForm = document.querySelector('form[action="subscribe.php"]');
      const newsletterMessage = document.getElementById('newsletter-message');

      if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
          e.preventDefault();

          // Get form data
          const formData = new FormData(newsletterForm);

          // Show loading state
          const submitButton = newsletterForm.querySelector('button[type="submit"]');
          const originalButtonText = submitButton.innerHTML;
          submitButton.innerHTML = '...';
          submitButton.disabled = true;

          // Send form data
          fetch('subscribe.php', {
            method: 'POST',
            body: formData
          })
          .then(response => response.json())
          .then(data => {
            // Create message element
            const messageDiv = document.createElement('div');
            messageDiv.className = `px-4 py-2 rounded-md ${
              data.status === 'success'
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-red-100 text-red-800 border border-red-200'
            }`;
            messageDiv.textContent = data.message;

            // Clear previous messages and show new one
            newsletterMessage.innerHTML = '';
            newsletterMessage.appendChild(messageDiv);

            // Reset form if successful
            if (data.status === 'success') {
              newsletterForm.reset();
            }
          })
          .catch(error => {
            // Show error message
            const messageDiv = document.createElement('div');
            messageDiv.className = 'px-4 py-2 rounded-md bg-red-100 text-red-800 border border-red-200';
            messageDiv.textContent = 'An error occurred. Please try again.';
            newsletterMessage.innerHTML = '';
            newsletterMessage.appendChild(messageDiv);
          })
          .finally(() => {
            // Reset button state
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
          });
        });
      }
    });
  </script>
</body>
</html>
