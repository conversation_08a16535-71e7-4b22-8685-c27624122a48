<?php
ob_start();

// Include session configuration before starting session
include("session_config.php");
session_start();

include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Check if user is logged in
if (!isUserLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Get current user data
$customer_id = $_SESSION['customer']['cust_id'];

// Fetch fresh customer data from database to ensure we have current information
$stmt_customer = $pdo->prepare("SELECT * FROM tbl_customer WHERE cust_id = ?");
$stmt_customer->execute([$customer_id]);
$customer_data = $stmt_customer->fetch(PDO::FETCH_ASSOC);

// Fallback to session data if database fetch fails
if (!$customer_data) {
    $customer_data = $_SESSION['customer'];
}

// --- Define currency constants ---
define('CURRENCY_CODE', 'TZS');
define('CURRENCY_SYMBOL', 'TSH');
define('CURRENCY_FORMAT', 'en-TZ');
define('CURRENCY_SYMBOL_NEW', 'TSH');

// --- Fetch settings ---
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© " . date("Y") . " SMART LIFE. All rights reserved.";

// --- Fetch user orders ---
$stmt_orders = $pdo->prepare("
    SELECT o.*,
           COUNT(oi.id) as item_count,
           GROUP_CONCAT(oi.product_name SEPARATOR ', ') as product_names
    FROM orders o
    LEFT JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = ? OR o.email = ?
    GROUP BY o.id
    ORDER BY o.created_at DESC
");
$stmt_orders->execute([$customer_id, $customer_data['cust_email']]);
$user_orders = $stmt_orders->fetchAll(PDO::FETCH_ASSOC);

// --- Calculate dashboard statistics ---
$total_orders = count($user_orders);
// Only count successful orders for total spent
$total_spent = array_sum(array_column(array_filter($user_orders, function($order) {
    return $order['payment_status'] === 'success';
}), 'total_amount'));
$pending_orders = count(array_filter($user_orders, function($order) {
    return $order['payment_status'] === 'pending';
}));
$completed_orders = count(array_filter($user_orders, function($order) {
    return $order['payment_status'] === 'success';
}));

// --- Fetch recent products for recommendations ---
$stmt_products = $pdo->prepare("SELECT * FROM tbl_product WHERE p_is_active = 1 ORDER BY p_id DESC LIMIT 6");
$stmt_products->execute();
$recent_products = $stmt_products->fetchAll(PDO::FETCH_ASSOC);

// --- Fetch user messages ---
$stmt_messages = $pdo->prepare("SELECT * FROM tbl_customer_message WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
$stmt_messages->execute([$customer_id]);
$user_messages = $stmt_messages->fetchAll(PDO::FETCH_ASSOC);

// --- Fetch categories for quick navigation ---
$stmt_categories = $pdo->prepare("SELECT * FROM tbl_top_category WHERE show_on_menu = 1 ORDER BY tcat_name ASC");
$stmt_categories->execute();
$categories = $stmt_categories->fetchAll(PDO::FETCH_ASSOC);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'retry_order':
            $order_id = $_POST['order_id'];
            try {
                $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ? AND (user_id = ? OR email = ?)");
                $stmt->execute([$order_id, $customer_id, $customer_data['cust_email']]);
                $order = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($order && in_array($order['payment_status'], ['failed', 'pending'])) {
                    // Generate new tx_ref for retry using same format as dashboard copy.php
                    $new_tx_ref = 'RETRY_' . time() . '_' . $order_id;

                    $stmt = $pdo->prepare("UPDATE orders SET tx_ref = ?, payment_status = 'pending', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$new_tx_ref, $order_id]);

                    echo json_encode(['success' => true, 'tx_ref' => $new_tx_ref, 'order_id' => $order_id]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Order cannot be retried']);
                }
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Database error']);
            }
            exit;

        case 'update_profile':
            $firstName = trim($_POST['firstName'] ?? '');
            $lastName = trim($_POST['lastName'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');

            // Validation
            $errors = [];
            if (empty($firstName)) $errors[] = 'First name is required';
            if (empty($lastName)) $errors[] = 'Last name is required';
            if (empty($email)) $errors[] = 'Email is required';
            if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Invalid email format';
            }
            if (!empty($phone) && !preg_match('/^[0-9+\-\s()]+$/', $phone)) {
                $errors[] = 'Invalid phone number format';
            }

            if (!empty($errors)) {
                echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
                exit;
            }

            try {
                // Check if email already exists for another user
                if ($email !== $customer_data['cust_email']) {
                    $stmt = $pdo->prepare("SELECT cust_id FROM tbl_customer WHERE cust_email = ? AND cust_id != ?");
                    $stmt->execute([$email, $customer_id]);
                    if ($stmt->fetch()) {
                        echo json_encode(['success' => false, 'message' => 'Email already exists']);
                        exit;
                    }
                }

                // Check if any data has actually changed
                $hasChanges = false;
                $updateFields = [];
                $updateValues = [];

                if ($firstName !== ($customer_data['cust_fname'] ?? '')) {
                    $updateFields[] = 'cust_fname = ?';
                    $updateValues[] = $firstName;
                    $hasChanges = true;
                }
                if ($lastName !== ($customer_data['cust_lname'] ?? '')) {
                    $updateFields[] = 'cust_lname = ?';
                    $updateValues[] = $lastName;
                    $hasChanges = true;
                }
                if ($email !== ($customer_data['cust_email'] ?? '')) {
                    $updateFields[] = 'cust_email = ?';
                    $updateValues[] = $email;
                    $hasChanges = true;
                }
                if ($phone !== ($customer_data['cust_phone'] ?? '')) {
                    $updateFields[] = 'cust_phone = ?';
                    $updateValues[] = $phone;
                    $hasChanges = true;
                }
                if ($address !== ($customer_data['cust_address_street'] ?? '')) {
                    $updateFields[] = 'cust_address_street = ?';
                    $updateValues[] = $address;
                    $hasChanges = true;
                }

                if (!$hasChanges) {
                    echo json_encode(['success' => true, 'message' => 'No changes detected']);
                    exit;
                }

                // Update only changed fields
                $updateValues[] = $customer_id;
                $sql = "UPDATE tbl_customer SET " . implode(', ', $updateFields) . " WHERE cust_id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($updateValues);

                // Update session data
                $_SESSION['customer']['cust_fname'] = $firstName;
                $_SESSION['customer']['cust_lname'] = $lastName;
                $_SESSION['customer']['cust_email'] = $email;
                $_SESSION['customer']['cust_phone'] = $phone;
                $_SESSION['customer']['cust_address_street'] = $address;

                echo json_encode(['success' => true, 'message' => 'Profile updated successfully']);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Database error occurred']);
            }
            exit;

        case 'get_order_details':
            $tx_ref = $_POST['tx_ref'] ?? '';

            try {
                // Get order details
                $stmt = $pdo->prepare("SELECT * FROM orders WHERE tx_ref = ? AND (user_id = ? OR email = ?)");
                $stmt->execute([$tx_ref, $customer_id, $customer_data['cust_email']]);
                $order = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$order) {
                    echo json_encode(['success' => false, 'message' => 'Order not found']);
                    exit;
                }

                // Get order items with product details
                $stmt_items = $pdo->prepare("
                    SELECT oi.*, p.p_name as product_name
                    FROM order_items oi
                    LEFT JOIN tbl_product p ON oi.product_id = p.p_id
                    WHERE oi.order_id = ?
                ");
                $stmt_items->execute([$order['id']]);
                $order_items = $stmt_items->fetchAll(PDO::FETCH_ASSOC);

                echo json_encode([
                    'success' => true,
                    'order' => $order,
                    'items' => $order_items
                ]);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Database error occurred']);
            }
            exit;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dashboard | SMART LIFE</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <!-- Chart.js for analytics -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

  <style>
    /* Custom animations and styles */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    :root {
      --bg-primary: #f8f9fa;
      --bg-secondary: #ffffff;
      --bg-tertiary: #f3f4f6;
      --text-primary: #1f2937;
      --text-secondary: #6b7280;
      --text-tertiary: #9ca3af;
      --border-color: #e5e7eb;
      --shadow-color: rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] {
      --bg-primary: #111827;
      --bg-secondary: #1f2937;
      --bg-tertiary: #374151;
      --text-primary: #f9fafb;
      --text-secondary: #d1d5db;
      --text-tertiary: #9ca3af;
      --border-color: #374151;
      --shadow-color: rgba(0, 0, 0, 0.3);
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: var(--bg-primary);
      color: var(--text-primary);
      min-height: 100vh;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .glass-effect {
      background: var(--bg-secondary);
      border: 1px solid var(--border-color);
      box-shadow: 0 1px 3px var(--shadow-color);
      transition: all 0.3s ease;
    }

    .neon-glow {
      box-shadow: 0 4px 6px -1px var(--shadow-color);
    }

    .gradient-text {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .floating {
      animation: floating 3s ease-in-out infinite;
    }

    @keyframes floating {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    .pulse-glow {
      animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
      0%, 100% { box-shadow: 0 0 20px rgba(79, 70, 229, 0.3); }
      50% { box-shadow: 0 0 30px rgba(79, 70, 229, 0.6); }
    }

    .hover-lift {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-lift:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .status-pending { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
    .status-success { background: linear-gradient(135deg, #10b981, #059669); }
    .status-failed { background: linear-gradient(135deg, #ef4444, #dc2626); }

    .cyber-border {
      position: relative;
      overflow: hidden;
    }

    .cyber-border::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, #4f46e5, transparent);
      animation: scan 2s linear infinite;
    }

    @keyframes scan {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .metric-card {
      background: var(--bg-secondary);
      border: 1px solid var(--border-color);
      box-shadow: 0 1px 3px var(--shadow-color);
      transition: all 0.3s ease;
    }

    .metric-card:hover {
      transform: translateY(-5px) scale(1.02);
      box-shadow: 0 10px 25px var(--shadow-color);
    }

    /* Theme toggle button */
    .theme-toggle {
      position: relative;
      width: 60px;
      height: 30px;
      background: var(--bg-tertiary);
      border-radius: 15px;
      border: 1px solid var(--border-color);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .theme-toggle::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 24px;
      height: 24px;
      background: var(--bg-secondary);
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px var(--shadow-color);
    }

    [data-theme="dark"] .theme-toggle::before {
      transform: translateX(28px);
    }

    .theme-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .theme-icon.sun {
      left: 6px;
      color: #fbbf24;
    }

    .theme-icon.moon {
      right: 6px;
      color: #60a5fa;
    }

    [data-theme="dark"] .theme-icon.sun {
      opacity: 0.3;
    }

    [data-theme="light"] .theme-icon.moon {
      opacity: 0.3;
    }

    /* Mobile Menu Styles */
    .mobile-menu {
      position: fixed;
      top: 0;
      left: -100%;
      width: 280px;
      height: 100vh;
      background: var(--bg-secondary);
      border-right: 1px solid var(--border-color);
      transition: left 0.3s ease;
      z-index: 60;
      overflow-y: auto;
    }

    .mobile-menu.active {
      left: 0;
    }

    .mobile-menu-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 55;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    .mobile-menu-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      color: var(--text-primary);
      text-decoration: none;
      border-bottom: 1px solid var(--border-color);
      transition: background-color 0.3s ease;
    }

    .mobile-menu-item:hover {
      background: var(--bg-tertiary);
    }

    .mobile-menu-item i {
      width: 24px;
      margin-right: 12px;
      color: var(--text-secondary);
    }

    /* Mobile responsive improvements */
    @media (max-width: 768px) {
      .container {
        padding-left: 16px;
        padding-right: 16px;
      }

      .grid {
        gap: 16px;
      }

      .metric-card {
        padding: 16px;
      }

      .metric-card .text-3xl {
        font-size: 1.5rem;
      }

      .quick-action-btn {
        padding: 12px;
      }

      .quick-action-btn i {
        font-size: 1.25rem;
        margin-bottom: 4px;
      }

      .quick-action-btn span {
        font-size: 0.75rem;
      }

      .glass-effect {
        padding: 16px;
      }

      .text-4xl {
        font-size: 1.875rem;
      }

      .text-2xl {
        font-size: 1.25rem;
      }

      .order-item {
        padding: 12px;
      }

      .order-item .flex {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .order-item .text-right {
        align-self: flex-end;
      }
    }

    @media (max-width: 640px) {
      .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
      }

      .grid-cols-2.md\\:grid-cols-4.lg\\:grid-cols-6 {
        grid-template-columns: repeat(2, 1fr);
      }

      .lg\\:col-span-2 {
        grid-column: span 1;
      }

      .space-y-6 > * + * {
        margin-top: 16px;
      }
    }
  </style>
</head>
<body>
  <!-- Theme initialization script (must be before any content) -->
  <script>
    // Initialize theme before page renders
    (function() {
      const savedTheme = localStorage.getItem('theme');
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      const theme = savedTheme || systemTheme;

      document.documentElement.setAttribute('data-theme', theme);

      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        }
      });
    })();
  </script>
  <!-- Header -->
  <header class="fixed inset-x-0 top-0 z-50" style="background: var(--bg-secondary); box-shadow: 0 1px 3px var(--shadow-color);">
    <div class="container mx-auto px-4 flex items-center justify-between py-4">
      <!-- Mobile Menu Button -->
      <button class="md:hidden p-2 rounded-lg" style="color: var(--text-primary);" onclick="toggleMobileMenu()">
        <i class="fas fa-bars text-xl"></i>
      </button>

      <a href="index.php" class="text-2xl font-bold" style="color: var(--text-primary);">
        SMART LIFE<span class="text-blue-600">.</span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-6">
        <a href="index.php#home" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Home</a>
        <a href="index.php#about" class="hover:text-blue-600 transition" style="color: var(--text-primary);">About</a>
        <a href="index.php#products" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Products</a>
        <a href="all_products.php" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Shop</a>
        <a href="cart.php" class="relative text-xl hover:text-blue-600 transition" style="color: var(--text-primary);">
          🛒
          <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 min-w-[16px] h-4 flex items-center justify-center cart-count">0</span>
        </a>
        <div class="flex items-center space-x-4">
          <!-- Theme Toggle -->
          <div class="theme-toggle" onclick="toggleTheme()" title="Toggle dark/light mode">
            <i class="fas fa-sun theme-icon sun"></i>
            <i class="fas fa-moon theme-icon moon"></i>
          </div>
          <span style="color: var(--text-secondary);">Hi, <?php echo htmlspecialchars($customer_data['cust_fname']); ?>!</span>
          <a href="logout.php" class="text-red-600 hover:text-red-800 font-medium">Logout</a>
        </div>
      </nav>

      <!-- Mobile Cart & Theme Toggle -->
      <div class="md:hidden flex items-center space-x-2 mr-2">
        <div class="theme-toggle" onclick="toggleTheme()" title="Toggle dark/light mode">
          <i class="fas fa-sun theme-icon sun"></i>
          <i class="fas fa-moon theme-icon moon"></i>
        </div>
        <a href="cart.php" class="relative text-xl" style="color: var(--text-primary);">
          🛒
          <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 min-w-[16px] h-4 flex items-center justify-center mobile-cart-count">0</span>
        </a>
      </div>
    </div>
  </header>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-menu-overlay" onclick="closeMobileMenu()"></div>

  <!-- Mobile Menu -->
  <div class="mobile-menu">
    <div class="p-6 border-b" style="border-color: var(--border-color);">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-bold" style="color: var(--text-primary);">Menu</h3>
        <button onclick="closeMobileMenu()" style="color: var(--text-tertiary);">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>

    <!-- User Profile Section -->
    <div class="p-4 border-b" style="border-color: var(--border-color);">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <i class="fas fa-user text-white"></i>
        </div>
        <div>
          <h4 class="font-medium" style="color: var(--text-primary);"><?php echo htmlspecialchars($customer_data['cust_fname'] . ' ' . $customer_data['cust_lname']); ?></h4>
          <p class="text-sm" style="color: var(--text-secondary);"><?php echo htmlspecialchars($customer_data['cust_email']); ?></p>
        </div>
      </div>
    </div>

    <!-- Navigation Links -->
    <div class="py-2">
      <a href="index.php#home" class="mobile-menu-item">
        <i class="fas fa-home"></i>
        <span>Home</span>
      </a>
      <a href="all_products.php" class="mobile-menu-item">
        <i class="fas fa-shopping-bag"></i>
        <span>Shop</span>
      </a>
      <a href="cart.php" class="mobile-menu-item">
        <i class="fas fa-shopping-cart"></i>
        <span>Cart</span>
      </a>
      <button onclick="showProfileModal(); closeMobileMenu();" class="mobile-menu-item w-full text-left">
        <i class="fas fa-user-edit"></i>
        <span>Edit Profile</span>
      </button>
      <button onclick="showSupportModal(); closeMobileMenu();" class="mobile-menu-item w-full text-left">
        <i class="fas fa-headset"></i>
        <span>Support</span>
      </button>
      <a href="index.php#about" class="mobile-menu-item">
        <i class="fas fa-info-circle"></i>
        <span>About</span>
      </a>
      <a href="index.php#contact" class="mobile-menu-item">
        <i class="fas fa-envelope"></i>
        <span>Contact</span>
      </a>
      <a href="logout.php" class="mobile-menu-item text-red-600">
        <i class="fas fa-sign-out-alt"></i>
        <span>Logout</span>
      </a>
    </div>
  </div>

  <!-- Main Dashboard Content -->
  <main class="pt-24 pb-12 min-h-screen">
    <div class="container mx-auto px-4">
      <!-- Welcome Section -->
      <div class="mb-8" data-aos="fade-down">
        <div class="glass-effect rounded-2xl p-8 cyber-border" style="color: var(--text-primary);">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-4xl font-bold mb-2 floating" style="color: var(--text-primary);">
                Welcome back, <?php echo htmlspecialchars($customer_data['cust_fname']); ?>! 👋
              </h1>
              <p class="text-lg" style="color: var(--text-secondary);">Manage your orders, track shipments, and discover new products</p>
            </div>
            <div class="hidden lg:block">
              <div class="w-32 h-32 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center pulse-glow">
                <i class="fas fa-user text-4xl text-white"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="metric-card rounded-xl p-6 text-gray-800 hover-lift" data-aos="fade-up" data-aos-delay="100">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Total Orders</p>
              <p class="text-3xl font-bold"><?php echo $total_orders; ?></p>
            </div>
            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-shopping-bag text-xl text-white"></i>
            </div>
          </div>
        </div>

        <div class="metric-card rounded-xl p-6 text-gray-800 hover-lift" data-aos="fade-up" data-aos-delay="200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Total Spent</p>
              <p class="text-3xl font-bold"><?php echo CURRENCY_SYMBOL_NEW . ' ' . number_format($total_spent, 0); ?></p>
            </div>
            <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-dollar-sign text-xl text-white"></i>
            </div>
          </div>
        </div>

        <div class="metric-card rounded-xl p-6 text-gray-800 hover-lift" data-aos="fade-up" data-aos-delay="300">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Pending Orders</p>
              <p class="text-3xl font-bold"><?php echo $pending_orders; ?></p>
            </div>
            <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-clock text-xl text-white"></i>
            </div>
          </div>
        </div>

        <div class="metric-card rounded-xl p-6 text-gray-800 hover-lift" data-aos="fade-up" data-aos-delay="400">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600">Completed</p>
              <p class="text-3xl font-bold"><?php echo $completed_orders; ?></p>
            </div>
            <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-check-circle text-xl text-white"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions Panel -->
      <div class="mb-8" data-aos="fade-up">
        <div class="glass-effect rounded-2xl shadow-xl p-6">
          <h2 class="text-2xl font-bold mb-6 flex items-center" style="color: var(--text-primary);">
            <i class="fas fa-bolt text-yellow-500 mr-3"></i>
            Quick Actions
          </h2>
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <button onclick="window.location.href='all_products.php'" class="quick-action-btn bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl hover:scale-105 transition-all duration-300">
              <i class="fas fa-shopping-cart text-2xl mb-2"></i>
              <span class="text-sm font-medium">Shop Now</span>
            </button>
            <button onclick="window.location.href='cart.php'" class="quick-action-btn bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl hover:scale-105 transition-all duration-300">
              <i class="fas fa-shopping-bag text-2xl mb-2"></i>
              <span class="text-sm font-medium">View Cart</span>
            </button>
            <button onclick="showProfileModal()" class="quick-action-btn bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl hover:scale-105 transition-all duration-300">
              <i class="fas fa-user-edit text-2xl mb-2"></i>
              <span class="text-sm font-medium">Edit Profile</span>
            </button>
            <button onclick="showSupportModal()" class="quick-action-btn bg-gradient-to-r from-red-500 to-red-600 text-white p-4 rounded-xl hover:scale-105 transition-all duration-300">
              <i class="fas fa-headset text-2xl mb-2"></i>
              <span class="text-sm font-medium">Support</span>
            </button>
            <button onclick="showWishlistModal()" class="quick-action-btn bg-gradient-to-r from-pink-500 to-pink-600 text-white p-4 rounded-xl hover:scale-105 transition-all duration-300">
              <i class="fas fa-heart text-2xl mb-2"></i>
              <span class="text-sm font-medium">Wishlist</span>
            </button>
            <button onclick="downloadInvoices()" class="quick-action-btn bg-gradient-to-r from-indigo-500 to-indigo-600 text-white p-4 rounded-xl hover:scale-105 transition-all duration-300">
              <i class="fas fa-download text-2xl mb-2"></i>
              <span class="text-sm font-medium">Invoices</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
        <!-- Orders Section -->
        <div class="lg:col-span-2 order-2 lg:order-1">
          <div class="glass-effect rounded-2xl shadow-xl p-4 lg:p-6" data-aos="fade-right">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-2xl font-bold flex items-center" style="color: var(--text-primary);">
                <i class="fas fa-list-alt text-blue-500 mr-3"></i>
                Recent Orders
              </h2>
              <div class="flex space-x-2">
                <button onclick="filterOrders('all')" class="filter-btn active px-4 py-2 rounded-lg text-sm font-medium transition-all">All</button>
                <button onclick="filterOrders('pending')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all">Pending</button>
                <button onclick="filterOrders('success')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all">Completed</button>
              </div>
            </div>

            <div class="space-y-4 min-h-96 max-h-[600px] overflow-y-auto" id="ordersContainer">
              <?php if (empty($user_orders)): ?>
                <div class="text-center py-8">
                  <i class="fas fa-shopping-bag text-6xl text-gray-300 mb-4"></i>
                  <h3 class="text-xl font-semibold text-gray-600 mb-2">No Orders Yet</h3>
                  <p class="text-gray-500 mb-4">Start shopping to see your orders here</p>
                  <button onclick="window.location.href='all_products.php'" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    Start Shopping
                  </button>
                </div>
              <?php else: ?>
                <?php foreach ($user_orders as $order): ?>
                  <div class="order-item border rounded-xl p-4 hover:shadow-lg transition-all duration-300 cursor-pointer"
                       style="border-color: var(--border-color); background: var(--bg-secondary);"
                       data-status="<?php echo $order['payment_status']; ?>"
                       onclick="showOrderDetails('<?php echo $order['tx_ref']; ?>')">
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                          <span class="font-semibold" style="color: var(--text-primary);">#<?php echo $order['tx_ref']; ?></span>
                          <span class="status-badge <?php echo 'status-' . $order['payment_status']; ?> text-white px-3 py-1 rounded-full text-xs font-medium">
                            <?php echo ucfirst($order['payment_status']); ?>
                          </span>
                          <?php if ($order['payment_status'] === 'pending' || $order['payment_status'] === 'failed'): ?>
                            <button onclick="event.stopPropagation(); retryOrder(<?php echo $order['id']; ?>)"
                                    class="retry-btn bg-orange-500 text-white px-3 py-1 rounded-full text-xs hover:bg-orange-600 transition-colors">
                              <i class="fas fa-redo mr-1"></i>Retry Payment
                            </button>
                          <?php endif; ?>
                        </div>
                        <p class="text-sm mb-1" style="color: var(--text-secondary);"><?php echo $order['item_count']; ?> items • <?php echo CURRENCY_SYMBOL_NEW . ' ' . number_format($order['total_amount'], 0); ?></p>
                        <p class="text-xs" style="color: var(--text-tertiary);"><?php echo date('M d, Y', strtotime($order['created_at'])); ?></p>
                      </div>
                      <div class="text-right">
                        <i class="fas fa-chevron-right" style="color: var(--text-tertiary);"></i>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              <?php endif; ?>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-4 lg:space-y-6 order-1 lg:order-2">
          <!-- Profile Card -->
          <div class="glass-effect rounded-2xl shadow-xl p-4 lg:p-6" data-aos="fade-left">
            <div class="text-center">
              <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-user text-2xl text-white"></i>
              </div>
              <h3 class="text-xl font-bold" style="color: var(--text-primary);"><?php echo htmlspecialchars($customer_data['cust_fname'] . ' ' . $customer_data['cust_lname']); ?></h3>
              <p class="text-sm" style="color: var(--text-secondary);"><?php echo htmlspecialchars($customer_data['cust_email']); ?></p>
              <p class="text-xs mt-1" style="color: var(--text-tertiary);">Member since <?php echo isset($customer_data['cust_created_at']) && $customer_data['cust_created_at'] ? date('M Y', strtotime($customer_data['cust_created_at'])) : date('M Y'); ?></p>
              <button onclick="showProfileModal()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                <i class="fas fa-edit mr-2"></i>Edit Profile
              </button>
            </div>
          </div>

          <!-- Quick Categories -->
          <div class="glass-effect rounded-2xl shadow-xl p-4 lg:p-6" data-aos="fade-left" data-aos-delay="100">
            <h3 class="text-lg font-bold mb-4 flex items-center" style="color: var(--text-primary);">
              <i class="fas fa-th-large text-blue-500 mr-2"></i>
              Quick Categories
            </h3>
            <div class="grid grid-cols-2 lg:grid-cols-1 gap-2">
              <?php foreach ($categories as $category): ?>
                <a href="category.php?id=<?php echo $category['tcat_id']; ?>"
                   class="block p-2 lg:p-3 rounded-lg transition-colors border hover:border-blue-200 text-center lg:text-left"
                   style="border-color: var(--border-color); background: var(--bg-tertiary);">
                  <span class="hover:text-blue-600 text-sm lg:text-base" style="color: var(--text-primary);"><?php echo htmlspecialchars($category['tcat_name']); ?></span>
                </a>
              <?php endforeach; ?>
            </div>
          </div>

          <!-- Recent Products -->
          <div class="glass-effect rounded-2xl shadow-xl p-4 lg:p-6" data-aos="fade-left" data-aos-delay="200">
            <h3 class="text-lg font-bold mb-4 flex items-center" style="color: var(--text-primary);">
              <i class="fas fa-star text-yellow-500 mr-2"></i>
              Recommended
            </h3>
            <div class="space-y-3">
              <?php foreach (array_slice($recent_products, 0, 3) as $product): ?>
                <div class="flex items-center space-x-3 p-2 rounded-lg transition-colors cursor-pointer"
                     style="background: var(--bg-tertiary);"
                     onclick="window.location.href='product_detail.php?id=<?php echo $product['p_id']; ?>'">
                  <img src="../assets/uploads/<?php echo htmlspecialchars($product['p_featured_photo']); ?>"
                       alt="<?php echo htmlspecialchars($product['p_name']); ?>"
                       class="w-12 h-12 object-cover rounded-lg">
                  <div class="flex-1">
                    <h4 class="text-sm font-medium line-clamp-1" style="color: var(--text-primary);"><?php echo htmlspecialchars($product['p_name']); ?></h4>
                    <p class="text-xs text-blue-600 font-semibold"><?php echo CURRENCY_SYMBOL_NEW . ' ' . number_format($product['p_current_price'], 0); ?></p>
                  </div>
                </div>
              <?php endforeach; ?>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Features Section -->
      <div class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Order Tracking -->
        <div class="glass-effect rounded-2xl shadow-xl p-6" data-aos="fade-up">
          <h3 class="text-xl font-bold mb-4 flex items-center" style="color: var(--text-primary);">
            <i class="fas fa-truck text-blue-500 mr-3"></i>
            Order Tracking
          </h3>
          <div class="space-y-3">
            <div class="flex items-center justify-between p-3 rounded-lg" style="background: var(--bg-tertiary);">
              <span class="text-sm" style="color: var(--text-secondary);">Track by Order ID</span>
              <button onclick="showTrackingModal()" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-search"></i>
              </button>
            </div>
            <div class="flex items-center justify-between p-3 rounded-lg" style="background: var(--bg-tertiary);">
              <span class="text-sm" style="color: var(--text-secondary);">Delivery Notifications</span>
              <label class="switch">
                <input type="checkbox" id="deliveryNotifications" checked>
                <span class="slider"></span>
              </label>
            </div>
          </div>
        </div>

        <!-- Loyalty Program -->
        <div class="glass-effect rounded-2xl shadow-xl p-6" data-aos="fade-up" data-aos-delay="100">
          <h3 class="text-xl font-bold mb-4 flex items-center" style="color: var(--text-primary);">
            <i class="fas fa-gift text-purple-500 mr-3"></i>
            Loyalty Points
          </h3>
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600 mb-2"><?php echo rand(150, 850); ?></div>
            <p class="text-sm mb-4" style="color: var(--text-secondary);">Points Available</p>
            <div class="w-full rounded-full h-2 mb-4" style="background: var(--bg-tertiary);">
              <div class="bg-purple-600 h-2 rounded-full" style="width: <?php echo rand(30, 80); ?>%"></div>
            </div>
            <button onclick="showRewardsModal()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm">
              Redeem Points
            </button>
          </div>
        </div>

        <!-- Support Center -->
        <div class="glass-effect rounded-2xl shadow-xl p-6" data-aos="fade-up" data-aos-delay="200">
          <h3 class="text-xl font-bold mb-4 flex items-center" style="color: var(--text-primary);">
            <i class="fas fa-headset text-green-500 mr-3"></i>
            Support Center
          </h3>
          <div class="space-y-3">
            <button onclick="startLiveChat()" class="w-full bg-green-600 text-white p-3 rounded-lg hover:bg-green-700 transition-colors text-sm">
              <i class="fas fa-comments mr-2"></i>Live Chat
            </button>
            <button onclick="showFAQModal()" class="w-full bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition-colors text-sm">
              <i class="fas fa-question-circle mr-2"></i>FAQ
            </button>
            <button onclick="submitTicket()" class="w-full bg-orange-600 text-white p-3 rounded-lg hover:bg-orange-700 transition-colors text-sm">
              <i class="fas fa-ticket-alt mr-2"></i>Submit Ticket
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Modals -->
  <!-- Profile Modal -->
  <div id="profileModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="rounded-2xl max-w-md w-full p-6 transform transition-all" style="background: var(--bg-secondary);">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold" style="color: var(--text-primary);">Edit Profile</h3>
        <button onclick="closeModal('profileModal')" style="color: var(--text-tertiary);" class="hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
      <form id="profileForm" class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-1" style="color: var(--text-primary);">First Name <span class="text-red-500">*</span></label>
          <input type="text" id="firstName" name="firstName" required
                 value="<?php echo htmlspecialchars($customer_data['cust_fname'] ?? ''); ?>"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                 style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                 placeholder="Enter your first name">
        </div>
        <div>
          <label class="block text-sm font-medium mb-1" style="color: var(--text-primary);">Last Name <span class="text-red-500">*</span></label>
          <input type="text" id="lastName" name="lastName" required
                 value="<?php echo htmlspecialchars($customer_data['cust_lname'] ?? ''); ?>"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                 style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                 placeholder="Enter your last name">
        </div>
        <div>
          <label class="block text-sm font-medium mb-1" style="color: var(--text-primary);">Email <span class="text-red-500">*</span></label>
          <input type="email" id="email" name="email" required
                 value="<?php echo htmlspecialchars($customer_data['cust_email'] ?? ''); ?>"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                 style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                 placeholder="Enter your email address">
        </div>
        <div>
          <label class="block text-sm font-medium mb-1" style="color: var(--text-primary);">Phone</label>
          <input type="tel" id="phone" name="phone"
                 value="<?php echo htmlspecialchars($customer_data['cust_phone'] ?? ''); ?>"
                 class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                 style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                 placeholder="Enter your phone number (optional)">
        </div>
        <div>
          <label class="block text-sm font-medium mb-1" style="color: var(--text-primary);">Address</label>
          <textarea id="address" name="address" rows="3"
                    class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                    style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                    placeholder="Enter your address"><?php echo htmlspecialchars($customer_data['cust_address_street'] ?? ''); ?></textarea>
        </div>
        <button type="submit" id="updateProfileBtn" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
          <span class="btn-text">Update Profile</span>
          <span class="btn-loading hidden">
            <i class="fas fa-spinner fa-spin mr-2"></i>Updating...
          </span>
        </button>
      </form>
    </div>
  </div>

  <!-- Order Details Modal -->
  <div id="orderModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="rounded-2xl max-w-2xl w-full p-6 transform transition-all max-h-96 overflow-y-auto" style="background: var(--bg-secondary);">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold" style="color: var(--text-primary);">Order Details</h3>
        <button onclick="closeModal('orderModal')" style="color: var(--text-tertiary);" class="hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
      <div id="orderDetailsContent">
        <!-- Order details will be loaded here -->
      </div>
    </div>
  </div>

  <!-- Support Modal -->
  <div id="supportModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="rounded-2xl max-w-md w-full p-6 transform transition-all" style="background: var(--bg-secondary);">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold" style="color: var(--text-primary);">Contact Support</h3>
        <button onclick="closeModal('supportModal')" style="color: var(--text-tertiary);" class="hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
      <form id="supportForm" class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-1" style="color: var(--text-primary);">Subject</label>
          <select class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);">
            <option>Order Issue</option>
            <option>Payment Problem</option>
            <option>Product Question</option>
            <option>Technical Support</option>
            <option>Other</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium mb-1" style="color: var(--text-primary);">Message</label>
          <textarea rows="4" placeholder="Describe your issue..."
                    class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"></textarea>
        </div>
        <button type="submit" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors">
          Send Message
        </button>
      </form>
    </div>
  </div>

  <!-- Tracking Modal -->
  <div id="trackingModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-md w-full p-6 transform transition-all">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-gray-800">Track Order</h3>
        <button onclick="closeModal('trackingModal')" class="text-gray-400 hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Order ID</label>
          <input type="text" placeholder="Enter your order ID"
                 class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>
        <button onclick="trackOrder()" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
          Track Order
        </button>
      </div>
    </div>
  </div>

  <!-- Notification Toast -->
  <div id="toast" class="fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 transform translate-x-full transition-transform duration-300 z-50">
    <div class="flex items-center space-x-3">
      <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
        <i class="fas fa-check text-green-600"></i>
      </div>
      <div>
        <p class="text-gray-800 font-medium" id="toastTitle">Success!</p>
        <p class="text-gray-500 text-sm" id="toastMessage">Action completed successfully</p>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="bg-gray-900 text-gray-400 pt-12 pb-8 mt-16">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
        <!-- Company Info -->
        <div>
          <h3 class="text-xl font-bold text-white mb-4">SMART LIFE<span class="text-blue-500">.</span></h3>
          <p class="mb-4 text-gray-400 text-sm leading-relaxed">
            Your one-stop shop for all smart home and automation products. We provide quality products with excellent customer service.
          </p>
          <div class="flex space-x-4 mt-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-linkedin-in"></i>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="text-lg font-semibold text-white mb-4">Quick Links</h4>
          <ul class="space-y-2">
            <li><a href="index.php" class="text-gray-400 hover:text-white transition-colors text-sm">Home</a></li>
            <li><a href="all_products.php" class="text-gray-400 hover:text-white transition-colors text-sm">Shop</a></li>
            <li><a href="cart.php" class="text-gray-400 hover:text-white transition-colors text-sm">Cart</a></li>
            <li><a href="dashboard.php" class="text-gray-400 hover:text-white transition-colors text-sm">Dashboard</a></li>
          </ul>
        </div>

        <!-- Categories -->
        <div>
          <h4 class="text-lg font-semibold text-white mb-4">Categories</h4>
          <ul class="space-y-2">
            <?php foreach (array_slice($categories, 0, 4) as $cat): ?>
              <li>
                <a href="category.php?id=<?= $cat['tcat_id'] ?>" class="text-gray-400 hover:text-white transition-colors text-sm">
                  <?= htmlspecialchars($cat['tcat_name']) ?>
                </a>
              </li>
            <?php endforeach; ?>
          </ul>
        </div>

        <!-- Contact Info -->
        <div>
          <h4 class="text-lg font-semibold text-white mb-4">Contact Us</h4>
          <ul class="space-y-3">
            <li class="flex items-start">
              <i class="fas fa-map-marker-alt text-blue-500 mt-1 mr-3"></i>
              <span class="text-sm">123 Smart Street, Dar es Salaam, Tanzania</span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-phone-alt text-blue-500 mr-3"></i>
              <span class="text-sm">+255 123 456 789</span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-envelope text-blue-500 mr-3"></i>
              <span class="text-sm"><EMAIL></span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Copyright -->
      <div class="pt-8 border-t border-gray-800 text-center">
        <div class="text-sm">
          <?= htmlspecialchars($footer_copyright); ?>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Initialize AOS
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });

    // Dashboard Data
    const dashboardData = {
      orders: <?php echo json_encode($user_orders); ?>,
      customer: <?php echo json_encode($customer_data); ?>,
      currency: '<?php echo CURRENCY_SYMBOL_NEW; ?>'
    };

    // Theme Functions
    function toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);

      // Show toast notification
      showToast('Theme Changed', `Switched to ${newTheme} mode`, 'info');
    }

    function getSystemTheme() {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }

    function resetToSystemTheme() {
      localStorage.removeItem('theme');
      const systemTheme = getSystemTheme();
      document.documentElement.setAttribute('data-theme', systemTheme);
      showToast('Theme Reset', `Using system theme (${systemTheme} mode)`, 'info');
    }

    // Mobile Menu Functions
    function toggleMobileMenu() {
      const menu = document.querySelector('.mobile-menu');
      const overlay = document.querySelector('.mobile-menu-overlay');

      menu.classList.toggle('active');
      overlay.classList.toggle('active');

      // Prevent body scroll when menu is open
      if (menu.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }
    }

    function closeMobileMenu() {
      const menu = document.querySelector('.mobile-menu');
      const overlay = document.querySelector('.mobile-menu-overlay');

      menu.classList.remove('active');
      overlay.classList.remove('active');
      document.body.style.overflow = '';
    }

    // Close mobile menu when clicking outside or pressing escape
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeMobileMenu();
      }
    });

    // Auto-close mobile menu when resizing to desktop
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768) { // md breakpoint
        closeMobileMenu();
      }
    });

    // Modal Functions
    function showModal(modalId) {
      document.getElementById(modalId).classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }

    function closeModal(modalId) {
      document.getElementById(modalId).classList.add('hidden');
      document.body.style.overflow = 'auto';
    }

    function showProfileModal() {
      showModal('profileModal');
    }

    function showSupportModal() {
      showModal('supportModal');
    }

    function showTrackingModal() {
      showModal('trackingModal');
    }

    function showWishlistModal() {
      showToast('Info', 'Wishlist feature coming soon!', 'info');
    }

    function showRewardsModal() {
      showToast('Info', 'Rewards program coming soon!', 'info');
    }

    function showFAQModal() {
      showToast('Info', 'FAQ section coming soon!', 'info');
    }

    // Order Functions
    function filterOrders(status) {
      const orders = document.querySelectorAll('.order-item');
      const buttons = document.querySelectorAll('.filter-btn');

      // Update active button
      buttons.forEach(btn => btn.classList.remove('active', 'bg-blue-600', 'text-white'));
      event.target.classList.add('active', 'bg-blue-600', 'text-white');

      // Filter orders
      orders.forEach(order => {
        const orderStatus = order.dataset.status;
        if (status === 'all' || orderStatus === status) {
          order.style.display = 'block';
        } else {
          order.style.display = 'none';
        }
      });
    }

    function showOrderDetails(txRef) {
      // Show loading state
      document.getElementById('orderDetailsContent').innerHTML = `
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600">Loading order details...</p>
        </div>
      `;
      showModal('orderModal');

      // Fetch detailed order information including products
      fetch('dashboard.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_order_details&tx_ref=${encodeURIComponent(txRef)}`
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const orderDetails = data.order;
          const orderItems = data.items;

          let productsHtml = '';
          if (orderItems && orderItems.length > 0) {
            productsHtml = `
              <div class="mt-4">
                <h5 class="font-medium text-gray-700 mb-3">Products Ordered</h5>
                <div class="space-y-2">
                  ${orderItems.map(item => `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <div class="flex-1">
                        <h6 class="font-medium text-gray-800">${item.product_name || item.product_name || 'Product'}</h6>
                        <p class="text-sm text-gray-600">Quantity: ${item.quantity}</p>
                        ${item.color ? `<p class="text-xs text-gray-500">Color: ${item.color}</p>` : ''}
                        ${item.size ? `<p class="text-xs text-gray-500">Size: ${item.size}</p>` : ''}
                      </div>
                      <div class="text-right">
                        <p class="font-semibold text-gray-800">${dashboardData.currency} ${parseFloat(item.total || item.unit_price * item.quantity).toLocaleString()}</p>
                        <p class="text-xs text-gray-500">${dashboardData.currency} ${parseFloat(item.unit_price).toLocaleString()} each</p>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            `;
          }

          const content = `
            <div class="space-y-4">
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 class="font-semibold text-gray-800">Order #${orderDetails.tx_ref}</h4>
                  <p class="text-sm text-gray-600">Placed on ${new Date(orderDetails.created_at).toLocaleDateString()}</p>
                </div>
                <span class="status-badge status-${orderDetails.payment_status} text-white px-3 py-1 rounded-full text-sm">
                  ${orderDetails.payment_status.charAt(0).toUpperCase() + orderDetails.payment_status.slice(1)}
                </span>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 class="font-medium text-gray-700 mb-2">Customer Details</h5>
                  <p class="text-sm text-gray-600">${orderDetails.firstname} ${orderDetails.lastname}</p>
                  <p class="text-sm text-gray-600">${orderDetails.email}</p>
                  <p class="text-sm text-gray-600">${orderDetails.phone || 'N/A'}</p>
                  ${orderDetails.address ? `<p class="text-sm text-gray-600 mt-2">${orderDetails.address}</p>` : ''}
                </div>
                <div>
                  <h5 class="font-medium text-gray-700 mb-2">Order Summary</h5>
                  <p class="text-sm text-gray-600">Items: ${orderItems.length}</p>
                  <p class="text-sm text-gray-600">Subtotal: ${dashboardData.currency} ${parseFloat(orderDetails.total_amount - (orderDetails.shipping_fee || 0)).toLocaleString()}</p>
                  <p class="text-sm text-gray-600">Shipping: ${dashboardData.currency} ${parseFloat(orderDetails.shipping_fee || 0).toLocaleString()}</p>
                  <p class="text-sm font-semibold text-gray-800">Total: ${dashboardData.currency} ${parseFloat(orderDetails.total_amount).toLocaleString()}</p>
                </div>
              </div>

              ${productsHtml}

              ${orderDetails.payment_status === 'pending' || orderDetails.payment_status === 'failed' ? `
                <div class="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <p class="text-orange-800 text-sm mb-3">This order requires payment completion.</p>
                  <button onclick="retryOrder(${orderDetails.id})" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors text-sm">
                    <i class="fas fa-credit-card mr-2"></i>Complete Payment
                  </button>
                </div>
              ` : ''}
            </div>
          `;

          document.getElementById('orderDetailsContent').innerHTML = content;
        } else {
          document.getElementById('orderDetailsContent').innerHTML = `
            <div class="text-center py-8">
              <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
              <p class="text-gray-600">Failed to load order details</p>
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('Error:', error);
        document.getElementById('orderDetailsContent').innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <p class="text-gray-600">Error loading order details</p>
          </div>
        `;
      });
    }

    function retryOrder(orderId) {
      showToast('Info', 'Retrying order...', 'info');

      fetch('dashboard.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=retry_order&order_id=${orderId}`
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showToast('Success', 'Order retry initiated successfully!', 'success');
          // Redirect to payment
          setTimeout(() => {
            window.location.href = `payment.php?retry=1&order_id=${data.order_id}&tx_ref=${data.tx_ref}`;
          }, 1500);
        } else {
          showToast('Error', data.message || 'Failed to retry order', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showToast('Error', 'An error occurred while retrying the order', 'error');
      });
    }

    // Support Functions
    function startLiveChat() {
      showToast('Info', 'Live chat feature coming soon!', 'info');
    }

    function submitTicket() {
      showToast('Info', 'Support ticket system coming soon!', 'info');
    }

    function downloadInvoices() {
      showToast('Info', 'Downloading invoices...', 'info');
      // Simulate download
      setTimeout(() => {
        showToast('Success', 'Invoices downloaded successfully!', 'success');
      }, 2000);
    }

    function trackOrder() {
      const orderId = document.querySelector('#trackingModal input').value;
      if (!orderId) {
        showToast('Error', 'Please enter an order ID', 'error');
        return;
      }

      showToast('Info', 'Tracking order...', 'info');
      closeModal('trackingModal');

      // Simulate tracking
      setTimeout(() => {
        showToast('Success', 'Order tracking information sent to your email!', 'success');
      }, 2000);
    }

    // Toast Notification System
    function showToast(title, message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastTitle = document.getElementById('toastTitle');
      const toastMessage = document.getElementById('toastMessage');
      const icon = toast.querySelector('i');
      const iconContainer = toast.querySelector('.w-8');

      // Update content
      toastTitle.textContent = title;
      toastMessage.textContent = message;

      // Update styling based on type
      iconContainer.className = `w-8 h-8 rounded-full flex items-center justify-center`;
      icon.className = 'fas';

      switch(type) {
        case 'success':
          iconContainer.classList.add('bg-green-100');
          icon.classList.add('fa-check', 'text-green-600');
          break;
        case 'error':
          iconContainer.classList.add('bg-red-100');
          icon.classList.add('fa-times', 'text-red-600');
          break;
        case 'info':
          iconContainer.classList.add('bg-blue-100');
          icon.classList.add('fa-info', 'text-blue-600');
          break;
        case 'warning':
          iconContainer.classList.add('bg-yellow-100');
          icon.classList.add('fa-exclamation-triangle', 'text-yellow-600');
          break;
      }

      // Show toast
      toast.classList.remove('translate-x-full');

      // Auto hide after 4 seconds
      setTimeout(() => {
        toast.classList.add('translate-x-full');
      }, 4000);
    }

    // Form Handlers
    document.getElementById('profileForm').addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form data
      const firstName = document.getElementById('firstName').value.trim();
      const lastName = document.getElementById('lastName').value.trim();
      const email = document.getElementById('email').value.trim();
      const phone = document.getElementById('phone').value.trim();
      const address = document.getElementById('address').value.trim();

      // Client-side validation
      if (!firstName || !lastName || !email) {
        showToast('Error', 'Please fill in all required fields', 'error');
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        showToast('Error', 'Please enter a valid email address', 'error');
        return;
      }

      // Show loading state
      const submitBtn = document.getElementById('updateProfileBtn');
      const btnText = submitBtn.querySelector('.btn-text');
      const btnLoading = submitBtn.querySelector('.btn-loading');

      submitBtn.disabled = true;
      btnText.classList.add('hidden');
      btnLoading.classList.remove('hidden');

      // Send AJAX request
      fetch('dashboard.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=update_profile&firstName=${encodeURIComponent(firstName)}&lastName=${encodeURIComponent(lastName)}&email=${encodeURIComponent(email)}&phone=${encodeURIComponent(phone)}&address=${encodeURIComponent(address)}`
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          showToast('Success', data.message, 'success');
          closeModal('profileModal');

          // Update the displayed name in the UI
          setTimeout(() => {
            location.reload(); // Reload to show updated data
          }, 1000);
        } else {
          showToast('Error', data.message, 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showToast('Error', 'An error occurred while updating profile', 'error');
      })
      .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        btnText.classList.remove('hidden');
        btnLoading.classList.add('hidden');
      });
    });

    document.getElementById('supportForm').addEventListener('submit', function(e) {
      e.preventDefault();

      // Simulate support message
      showToast('Info', 'Sending message...', 'info');

      setTimeout(() => {
        showToast('Success', 'Support message sent! We\'ll get back to you soon.', 'success');
        closeModal('supportModal');
        this.reset();
      }, 1500);
    });



    // Initialize everything when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {

      // Set up filter buttons
      document.querySelectorAll('.filter-btn').forEach(btn => {
        if (btn.textContent.trim() === 'All') {
          btn.classList.add('active', 'bg-blue-600', 'text-white');
        } else {
          btn.classList.add('bg-gray-200', 'text-gray-700', 'hover:bg-gray-300');
        }
      });

      // Close modals when clicking outside
      document.querySelectorAll('[id$="Modal"]').forEach(modal => {
        modal.addEventListener('click', function(e) {
          if (e.target === this) {
            closeModal(this.id);
          }
        });
      });

      // Initialize cart count
      updateCartCount();

      // Show welcome message
      setTimeout(() => {
        showToast('Welcome!', 'Welcome to your dashboard, <?php echo htmlspecialchars($customer_data['cust_fname']); ?>!', 'success');
      }, 1000);
    });

    // Cart functionality
    function updateCartCount() {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      const totalItems = cart.reduce((total, item) => total + (item.quantity || 0), 0);

      // Update both desktop and mobile cart counts
      const desktopCartCount = document.querySelector('.cart-count');
      const mobileCartCount = document.querySelector('.mobile-cart-count');

      if (desktopCartCount) {
        desktopCartCount.textContent = totalItems;
      }
      if (mobileCartCount) {
        mobileCartCount.textContent = totalItems;
      }
    }

    // Additional CSS for switch toggle
    const switchStyles = `
      <style>
        .switch {
          position: relative;
          display: inline-block;
          width: 40px;
          height: 20px;
        }

        .switch input {
          opacity: 0;
          width: 0;
          height: 0;
        }

        .slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: .4s;
          border-radius: 20px;
        }

        .slider:before {
          position: absolute;
          content: "";
          height: 16px;
          width: 16px;
          left: 2px;
          bottom: 2px;
          background-color: white;
          transition: .4s;
          border-radius: 50%;
        }

        input:checked + .slider {
          background-color: #4f46e5;
        }

        input:checked + .slider:before {
          transform: translateX(20px);
        }

        .filter-btn.active {
          background-color: #4f46e5 !important;
          color: white !important;
        }
      </style>
    `;

    document.head.insertAdjacentHTML('beforeend', switchStyles);
  </script>
</body>
</html>
<?php ob_end_flush(); ?>