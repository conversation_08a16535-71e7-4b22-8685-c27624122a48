<?php
include 'inc/config.php';

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=subscriber_list_' . date('Y-m-d') . '.csv');

// Create output stream
$output = fopen("php://output", "w");

// Add CSV headers
fputcsv($output, array('ID', 'Email', 'Subscription Date', 'Status'));

// Fetch subscribers data
$statement = $pdo->prepare("SELECT * FROM tbl_subscriber ORDER BY created_at DESC");
$statement->execute();
$result = $statement->fetchAll(PDO::FETCH_ASSOC);

// Add data rows
foreach ($result as $row) {
    $status = ($row['status'] == 1) ? 'Active' : 'Inactive';
    fputcsv($output, array(
        $row['id'],
        $row['email'],
        date('Y-m-d H:i:s', strtotime($row['created_at'])),
        $status
    ));
}

fclose($output);
?>