<?php
// verify_otp_process.php

ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: cart.php');
    exit;
}

// Redirect if no registration data is available
if (!isset($_SESSION['registration_data']) || !isset($_SESSION['otp_data'])) {
    header('Location: register.php');
    exit;
}

// Only handle POST submissions
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: verify_otp.php');
    exit;
}

// Get OTP from form
$otp = filter_input(INPUT_POST, 'otp', FILTER_SANITIZE_NUMBER_INT);

// Validate OTP
if (empty($otp) || strlen($otp) !== 6) {
    $_SESSION['error_message'] = "Please enter a valid 6-digit OTP.";
    header('Location: verify_otp.php');
    exit;
}

// Verify OTP
if (!verifyOTP($otp)) {
    $_SESSION['error_message'] = "Invalid or expired OTP. Please try again or request a new OTP.";
    header('Location: verify_otp.php');
    exit;
}

// Get registration data from session
$registrationData = $_SESSION['registration_data'];

// Insert user into DB
try {
    $hashed = password_hash($registrationData['password'], PASSWORD_BCRYPT);
    $sql = "INSERT INTO tbl_customer
               (cust_fname, cust_lname, cust_email, cust_phone,
                cust_password, cust_address_street, cust_address_city, cust_address_region, cust_country,
                cust_photo, cust_status, cust_created_at)
            VALUES (?,?,?,?,?,?,?,?,?, ?,1,NOW())";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        $registrationData['fname'],
        $registrationData['lname'],
        $registrationData['email'],
        $registrationData['phone'],
        $hashed,
        $registrationData['address_street'],
        $registrationData['address_city'],
        $registrationData['address_region'],
        $registrationData['country'],
        $registrationData['photo_path']
    ]);

    // Auto-login
    $newId = $pdo->lastInsertId();
    session_regenerate_id(true);
    $_SESSION['customer'] = [
        'cust_id'   => $newId,
        'cust_fname'=> $registrationData['fname'],
        'cust_lname'=> $registrationData['lname'],
        'cust_email'=> $registrationData['email'],
        'cust_photo'=> $registrationData['photo_path']
    ];
    
    // Clean up session data
    unset($_SESSION['registration_data']);
    unset($_SESSION['otp_data']);
    
    $_SESSION['success_message'] = "Registration successful! Your email has been verified and you are now logged in.";
    header('Location: cart.php');
    exit;

} catch (PDOException $e) {
    error_log("Registration DB Error: " . $e->getMessage());
    $_SESSION['error_message'] = 'Registration failed. Please try again.';
    header('Location: register.php');
    exit;
}

ob_end_flush();
?>
