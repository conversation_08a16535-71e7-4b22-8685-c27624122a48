<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© 2025 SMART LIFE. All rights reserved.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Home Guides - SMART LIFE</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#00c2ff',
                        'primary-dark': '#00a8e0',
                        'primary-light': '#e0f7ff',
                    },
                    fontFamily: {
                        sans: ['Sora', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700&display=swap');

        .bg-gradient-radial {
            background-image: radial-gradient(circle at center, rgba(0, 194, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .card-hover-effect {
            transition: all 0.3s ease;
        }

        .card-hover-effect:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-sans">

    <!-- Header -->
    <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
        <div class="container mx-auto px-4 flex items-center justify-between py-4">
            <a href="index.php" class="text-2xl font-bold text-gray-900">
                SMART LIFE<span class="text-blue-600">.</span>
            </a>
            <nav class="hidden md:flex items-center space-x-6">
                <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
                <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
                <a href="index.php#products" class="hover:text-blue-600 transition">Products</a>
                <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
                <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>

                <!-- Search -->
                <div class="relative">
                    <input id="searchInput" type="text" placeholder="Search products, categories..."
                           class="w-64 px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="searchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart -->
                <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
                    🛒
                    <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
                </a>
            </nav>
            <!-- Mobile Menu Button -->
            <button id="mobileMenuButton" class="md:hidden flex items-center">
                <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 8h16M4 16h16"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
        <div class="flex flex-col h-full">
            <div class="flex justify-between items-center p-4 border-b">
                <a href="index.php" class="text-xl font-bold text-gray-900">
                    SMART LIFE<span class="text-[#00c2ff]">.</span>
                </a>
                <button id="closeMobileMenu" class="text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
                <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
                <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
                <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
                <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
                <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>

                <!-- Search in Mobile Menu -->
                <div class="relative mt-4">
                    <input id="mobileSearchInput" type="text" placeholder="Search products, categories..."
                           class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                           autocomplete="off">
                    <div id="mobileSearchSuggestions"
                         class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100">
                        <!-- suggestions will appear here -->
                    </div>
                </div>

                <!-- Cart in Mobile Menu -->
                <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
                    <span class="text-xl mr-2">🛒</span>
                    <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Backdrop for mobile menu -->
    <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

    <!-- Hero Section -->
    <section class="pt-32 pb-16 relative overflow-hidden bg-gradient-to-b from-white to-blue-50">
        <div class="absolute inset-0 bg-gradient-radial pointer-events-none"></div>
        <div class="absolute right-0 top-0 w-64 h-64 bg-primary-light rounded-full filter blur-3xl opacity-20 -mr-32 -mt-32"></div>
        <div class="absolute left-0 bottom-0 w-64 h-64 bg-primary-light rounded-full filter blur-3xl opacity-20 -ml-32 -mb-32"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-3xl mx-auto">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Smart Home <span class="text-primary">Guides</span></h1>
                <p class="text-lg md:text-xl text-gray-600 mb-8">Learn how to make the most of your smart home devices with our comprehensive guides and tutorials</p>

                <!-- Search Bar -->
                <div class="relative max-w-xl mx-auto mb-12">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="guideSearch" placeholder="Search guides by keyword..."
                           class="w-full pl-12 pr-4 py-4 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-sm transition-all duration-200">
                </div>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute right-10 top-40 text-5xl text-primary opacity-10 animate-pulse-slow">
            <i class="fas fa-lightbulb"></i>
        </div>
        <div class="absolute left-10 bottom-20 text-5xl text-primary opacity-10 animate-pulse-slow" style="animation-delay: 1s;">
            <i class="fas fa-home"></i>
        </div>
    </section>

    <main class="py-12">
        <div class="container mx-auto px-4">
            <!-- Categories -->
            <div class="flex flex-wrap justify-center gap-3 mb-12">
                <button class="category-btn bg-primary text-white px-6 py-3 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:bg-primary-dark">
                    All Guides
                </button>
                <button class="category-btn bg-white text-gray-700 px-6 py-3 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:bg-primary-light hover:text-primary-dark">
                    <i class="fas fa-tools mr-2"></i>Installation
                </button>
                <button class="category-btn bg-white text-gray-700 px-6 py-3 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:bg-primary-light hover:text-primary-dark">
                    <i class="fas fa-wrench mr-2"></i>Troubleshooting
                </button>
                <button class="category-btn bg-white text-gray-700 px-6 py-3 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:bg-primary-light hover:text-primary-dark">
                    <i class="fas fa-magic mr-2"></i>Automation
                </button>
                <button class="category-btn bg-white text-gray-700 px-6 py-3 rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:bg-primary-light hover:text-primary-dark">
                    <i class="fas fa-shield-alt mr-2"></i>Security
                </button>
            </div>

            <!-- Guides Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Guide Card 1 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover-effect">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                             alt="Smart Home Setup" class="w-full h-52 object-cover">
                        <div class="absolute top-4 right-4 bg-primary text-white text-xs font-bold px-3 py-1 rounded-full">
                            Installation
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Getting Started with Smart Home</h3>
                        <p class="text-gray-600 mb-4">A comprehensive guide to setting up your first smart home system, from choosing devices to installation.</p>
                        <div class="flex items-center justify-between mt-6">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="far fa-clock mr-2"></i>
                                <span>10 min read</span>
                            </div>
                            <a href="#" class="text-primary font-medium hover:text-primary-dark transition-colors">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 2 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover-effect">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                             alt="Security Setup" class="w-full h-52 object-cover">
                        <div class="absolute top-4 right-4 bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                            Security
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Smart Home Security Best Practices</h3>
                        <p class="text-gray-600 mb-4">Learn how to secure your smart home devices and protect your privacy with these essential tips.</p>
                        <div class="flex items-center justify-between mt-6">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="far fa-clock mr-2"></i>
                                <span>15 min read</span>
                            </div>
                            <a href="#" class="text-primary font-medium hover:text-primary-dark transition-colors">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 3 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover-effect">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1581092921461-7d65ca45393a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                             alt="Automation" class="w-full h-52 object-cover">
                        <div class="absolute top-4 right-4 bg-purple-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                            Automation
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Creating Smart Home Automations</h3>
                        <p class="text-gray-600 mb-4">Discover how to create powerful automations that make your home more convenient and efficient.</p>
                        <div class="flex items-center justify-between mt-6">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="far fa-clock mr-2"></i>
                                <span>12 min read</span>
                            </div>
                            <a href="#" class="text-primary font-medium hover:text-primary-dark transition-colors">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 4 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover-effect">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80"
                             alt="Troubleshooting" class="w-full h-52 object-cover">
                        <div class="absolute top-4 right-4 bg-amber-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                            Troubleshooting
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Common Smart Home Issues & Solutions</h3>
                        <p class="text-gray-600 mb-4">Troubleshoot common problems with your smart home devices and learn how to fix them.</p>
                        <div class="flex items-center justify-between mt-6">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="far fa-clock mr-2"></i>
                                <span>8 min read</span>
                            </div>
                            <a href="#" class="text-primary font-medium hover:text-primary-dark transition-colors">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 5 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover-effect">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1585399000684-d2f72660f092?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80"
                             alt="Voice Control" class="w-full h-52 object-cover">
                        <div class="absolute top-4 right-4 bg-green-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                            Voice Control
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Mastering Voice Control Systems</h3>
                        <p class="text-gray-600 mb-4">Learn how to set up and optimize voice assistants like Alexa, Google Assistant, and Siri for your smart home.</p>
                        <div class="flex items-center justify-between mt-6">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="far fa-clock mr-2"></i>
                                <span>14 min read</span>
                            </div>
                            <a href="#" class="text-primary font-medium hover:text-primary-dark transition-colors">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 6 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover-effect">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                             alt="Energy Saving" class="w-full h-52 object-cover">
                        <div class="absolute top-4 right-4 bg-teal-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                            Energy Saving
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">Smart Energy Saving Tips</h3>
                        <p class="text-gray-600 mb-4">Discover how to use smart home technology to reduce your energy consumption and save money on utility bills.</p>
                        <div class="flex items-center justify-between mt-6">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="far fa-clock mr-2"></i>
                                <span>11 min read</span>
                            </div>
                            <a href="#" class="text-primary font-medium hover:text-primary-dark transition-colors">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button class="px-8 py-3 bg-white text-primary border border-primary rounded-full font-medium shadow-sm hover:bg-primary hover:text-white transition-all duration-300">
                    Load More Guides <i class="fas fa-chevron-down ml-2"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Newsletter Section -->
    <section class="py-16 bg-gray-100">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="flex flex-col md:flex-row">
                    <div class="md:w-1/2 bg-primary p-8 text-white flex items-center">
                        <div>
                            <h3 class="text-2xl font-bold mb-4">Stay Updated</h3>
                            <p class="mb-6">Subscribe to our newsletter to receive the latest smart home guides, tips, and exclusive offers directly to your inbox.</p>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Weekly smart home tips</span>
                            </div>
                            <div class="flex items-center text-sm mt-2">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Exclusive subscriber discounts</span>
                            </div>
                            <div class="flex items-center text-sm mt-2">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>New product announcements</span>
                            </div>
                        </div>
                    </div>
                    <div class="md:w-1/2 p-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Join Our Newsletter</h3>
                        <form class="space-y-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                                <input type="text" id="name" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                <input type="email" id="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <button type="submit" class="w-full bg-primary hover:bg-primary-dark text-white font-medium py-3 rounded-lg transition-colors duration-300">
                                Subscribe Now
                            </button>
                            <p class="text-xs text-gray-500 text-center mt-4">
                                By subscribing, you agree to our Privacy Policy and consent to receive updates from our company.
                            </p>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-200 py-8">
        <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
                <h4 class="text-xl font-semibold mb-4">SMART LIFE<span class="text-[#00c2ff]">.</span></h4>
                <p>Your Gateway to a Smarter Luxurious Home</p>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="index.php#home" class="hover:text-[#00c2ff] transition">Home</a></li>
                    <li><a href="index.php#about" class="hover:text-[#00c2ff] transition">About Us</a></li>
                    <li><a href="index.php#products" class="hover:text-[#00c2ff] transition">Products</a></li>
                    <li><a href="index.php#gallery" class="hover:text-[#00c2ff] transition">Best Deals</a></li>
                    <li><a href="index.php#contact" class="hover:text-[#00c2ff] transition">Contact</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Information</h4>
                <ul class="space-y-2">
                    <li><a href="subscription_details.php" class="hover:text-[#00c2ff] transition">FAQs & Details</a></li>
                    <li><a href="guides.php" class="hover:text-[#00c2ff] transition">Guides</a></li>
                    <li><a href="shipping_policy.php" class="hover:text-[#00c2ff] transition">Shipping Policy</a></li>
                    <li><a href="privacy_policy.php" class="hover:text-[#00c2ff] transition">Privacy Policy</a></li>
                    <li><a href="#" class="hover:text-[#00c2ff] transition">Terms &amp; Conditions</a></li>
                </ul>
            </div>
            <div>
                <h4 class="text-lg font-semibold mb-4">Newsletter</h4>
                <p class="mb-4">Subscribe for exclusive offers and smart home insights.</p>
                <form action="subscribe.php" method="post" class="flex flex-col">
                    <div class="flex">
                        <input type="email" name="subscriber_email" required
                               class="w-full px-4 py-2 rounded-l-md bg-gray-700 text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent">
                        <button type="submit"
                                class="px-4 bg-[#00c2ff] hover:bg-[#00a8e0] text-white font-medium rounded-r-md transition">
                            →
                        </button>
                    </div>
                    <div id="newsletter-message" class="mt-2"></div>
                </form>
            </div>
        </div>
        <div class="mt-8 border-t border-gray-700 pt-4 text-center text-sm">
            <?= $footer_copyright ?>
            <div class="mt-2 space-x-4">
                <a href="#" class="hover:text-[#00c2ff] transition">Privacy</a>
                <a href="#" class="hover:text-[#00c2ff] transition">Terms</a>
                <a href="#" class="hover:text-[#00c2ff] transition">Sitemap</a>
            </div>
        </div>
    </footer>

    <!-- External JavaScript -->
    <script src="js/script.js"></script>

    <!-- Inline Page-Specific JavaScript -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Initialize cart if not exists
            if (!localStorage.getItem('cart')) {
                localStorage.setItem('cart', JSON.stringify([]));
            }

            // Update cart count display
            updateCartCount();

            function updateCartCount() {
                const cart = JSON.parse(localStorage.getItem('cart'));
                const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
                document.querySelectorAll('.cart-count').forEach(el => {
                    el.textContent = totalItems;
                });
            }

            // Mobile Menu Functionality
            const mobileMenuButton = document.getElementById('mobileMenuButton');
            const closeMobileMenu = document.getElementById('closeMobileMenu');
            const mobileMenu = document.getElementById('mobileMenu');
            const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

            function toggleMobileMenu() {
                mobileMenu.classList.toggle('translate-x-full');
                mobileMenuBackdrop.classList.toggle('hidden');
                document.body.style.overflow = mobileMenu.classList.contains('translate-x-full') ? 'auto' : 'hidden';
            }

            mobileMenuButton.addEventListener('click', toggleMobileMenu);
            closeMobileMenu.addEventListener('click', toggleMobileMenu);
            mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

            // Guide Search Functionality
            const guideSearch = document.getElementById('guideSearch');
            const guideCards = document.querySelectorAll('.card-hover-effect');

            if (guideSearch) {
                guideSearch.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase().trim();

                    guideCards.forEach(card => {
                        const title = card.querySelector('h3').textContent.toLowerCase();
                        const description = card.querySelector('p').textContent.toLowerCase();
                        const category = card.querySelector('.absolute').textContent.toLowerCase().trim();

                        const isMatch = title.includes(searchTerm) ||
                                       description.includes(searchTerm) ||
                                       category.includes(searchTerm);

                        card.style.display = isMatch ? 'block' : 'none';
                    });

                    // Show/hide "no results" message
                    const visibleCards = [...guideCards].filter(card => card.style.display !== 'none');
                    const noResultsMsg = document.getElementById('noResultsMessage');

                    if (visibleCards.length === 0 && searchTerm !== '') {
                        if (!noResultsMsg) {
                            const msg = document.createElement('div');
                            msg.id = 'noResultsMessage';
                            msg.className = 'text-center py-12 text-gray-500';
                            msg.innerHTML = `
                                <i class="fas fa-search text-4xl mb-4 text-gray-300"></i>
                                <p class="text-xl">No guides found matching "${searchTerm}"</p>
                                <p class="mt-2">Try different keywords or browse by category</p>
                            `;
                            document.querySelector('.grid').after(msg);
                        }
                    } else if (noResultsMsg) {
                        noResultsMsg.remove();
                    }
                });
            }

            // Category Filter Functionality
            const categoryButtons = document.querySelectorAll('.category-btn');

            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active state
                    categoryButtons.forEach(btn => {
                        btn.classList.remove('bg-primary', 'text-white');
                        btn.classList.add('bg-white', 'text-gray-700');
                    });

                    this.classList.remove('bg-white', 'text-gray-700');
                    this.classList.add('bg-primary', 'text-white');

                    // Filter cards
                    const category = this.textContent.trim().toLowerCase();

                    guideCards.forEach(card => {
                        if (category === 'all guides') {
                            card.style.display = 'block';
                        } else {
                            const cardCategory = card.querySelector('.absolute').textContent.toLowerCase().trim();
                            card.style.display = cardCategory === category ? 'block' : 'none';
                        }
                    });

                    // Remove any "no results" message
                    const noResultsMsg = document.getElementById('noResultsMessage');
                    if (noResultsMsg) noResultsMsg.remove();
                });
            });

            // Load More Button Animation
            const loadMoreBtn = document.querySelector('.mt-12 button');
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', function() {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Loading...';
                    this.disabled = true;

                    // Simulate loading delay
                    setTimeout(() => {
                        this.innerHTML = 'No More Guides <i class="fas fa-check ml-2"></i>';
                        this.classList.add('bg-gray-200', 'text-gray-500', 'border-gray-300');
                        this.classList.remove('bg-white', 'text-primary', 'border-primary', 'hover:bg-primary', 'hover:text-white');
                    }, 1500);
                });
            }

            // Newsletter Form Handling
            const newsletterForm = document.querySelector('section.py-16 form');

            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const nameInput = this.querySelector('#name');
                    const emailInput = this.querySelector('#email');
                    const submitButton = this.querySelector('button[type="submit"]');

                    // Basic validation
                    if (!nameInput.value.trim()) {
                        showFormMessage(this, 'Please enter your name', 'error');
                        nameInput.focus();
                        return;
                    }

                    if (!emailInput.value.trim() || !isValidEmail(emailInput.value)) {
                        showFormMessage(this, 'Please enter a valid email address', 'error');
                        emailInput.focus();
                        return;
                    }

                    // Show loading state
                    const originalButtonText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Subscribing...';
                    submitButton.disabled = true;

                    // Simulate form submission
                    setTimeout(() => {
                        showFormMessage(this, 'Thank you for subscribing! Check your email for confirmation.', 'success');
                        this.reset();
                        submitButton.innerHTML = originalButtonText;
                        submitButton.disabled = false;
                    }, 1500);
                });
            }

            function showFormMessage(form, message, type) {
                // Remove any existing message
                const existingMessage = form.querySelector('.form-message');
                if (existingMessage) existingMessage.remove();

                // Create new message
                const messageDiv = document.createElement('div');
                messageDiv.className = `form-message px-4 py-3 rounded-lg mt-4 ${
                    type === 'success'
                        ? 'bg-green-100 text-green-800 border border-green-200'
                        : 'bg-red-100 text-red-800 border border-red-200'
                }`;
                messageDiv.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
                        <span>${message}</span>
                    </div>
                `;

                // Add to form
                form.appendChild(messageDiv);

                // Auto-remove after delay if success
                if (type === 'success') {
                    setTimeout(() => {
                        messageDiv.remove();
                    }, 5000);
                }
            }

            function isValidEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(String(email).toLowerCase());
            }

            // Card Hover Animation Enhancement
            guideCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    const readMoreLink = this.querySelector('a');
                    if (readMoreLink) {
                        readMoreLink.innerHTML = 'Read More <i class="fas fa-arrow-right ml-1 animate-pulse"></i>';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    const readMoreLink = this.querySelector('a');
                    if (readMoreLink) {
                        readMoreLink.innerHTML = 'Read More <i class="fas fa-arrow-right ml-1"></i>';
                    }
                });
            });
        });
    </script>
</body>
</html>