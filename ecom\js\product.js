<?php
// product_detail.php
ob_start(); // Start output buffering
session_start(); // Start session

// --- Configuration & Includes ---
// Adjust path if needed
require_once("../admin/inc/config.php");
require_once("../admin/inc/functions.php");
require_once("../admin/inc/CSRF_Protect.php");

$csrf = new CSRF_Protect();

// --- File Paths & Settings ---
$upload_path_featured = "../assets/uploads/";
$upload_path_variations = "../assets/uploads/product_variations/";
$upload_path_gallery = "../assets/uploads/product_photos/";
$installation_fee = 15000; // Example fixed installation fee, ensure it's numeric

// --- Input Validation ---
if (!isset($_GET['id']) || !filter_var($_GET['id'], FILTER_VALIDATE_INT) || (int)$_GET['id'] <= 0) {
    // Consider logging this attempt before redirecting
    error_log("Invalid or missing product ID accessed: " . ($_GET['id'] ?? 'Not Set'));
    header("Location: index.php");
    exit();
}
$product_id = (int)$_GET['id'];

// --- Initialize variables to avoid potential PHP notices ---
$product = null;
$variations_data = [];
$product_photos = [];
$settings = [];
$footer_copyright = ('© ' . date('Y') . ' SMART LIFE. All rights reserved.'); // Default copyright
$countries = [];
$shipping_costs_map = [];
$default_shipping_cost = 0;
$initial_shipping_cost = 0;
$variations_json = [];
$available_variation_names = [];
$variation_images_map = [];
$initial_variation = null;
$initial_price = 0;
$initial_quantity = 0;
$initial_sku = 'N/A';
$initial_image = ''; // Default empty, set later
$initial_description = '';
$initial_variation_id = null;
$initial_variation_name = '';
$initial_color_name = null;
$initial_size_name = null;
$initial_color_code = null; // Added for initial swatch
$in_cart = false;
$cart_button_text = 'Out of Stock';
$cart_button_disabled = true;
$initial_total = 0;
$base_product_description = '';
$base_product_short_description = '';
$product_category_name = ''; // Initialize category name
$has_variations = false; // Initialize

// --- Database Operations ---
try {
    // --- Fetch Base Product Details ---
    $stmt_product = $pdo->prepare("
        SELECT p.*, tcat.tcat_name
        FROM tbl_product p
        LEFT JOIN tbl_top_category tcat ON p.tcat_id = tcat.tcat_id
        WHERE p.p_id = ? AND p.p_is_active = 1
    ");
    $stmt_product->execute([$product_id]);
    $product = $stmt_product->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        // Product not found or inactive
        error_log("Product ID {$product_id} not found or inactive.");
        header("Location: index.php");
        exit();
    }

    // Set base product details from fetched data
    $initial_price = is_numeric($product['p_current_price']) ? (float)$product['p_current_price'] : 0;
    $initial_quantity = (int)$product['p_qty']; // Base quantity used if no variations
    $initial_sku = htmlspecialchars($product['p_id']); // Fallback SKU - Keep escaping for SKU
    $initial_image = !empty($product['p_featured_photo']) ? $upload_path_featured . htmlspecialchars($product['p_featured_photo']) : 'images/default_product.png'; // Provide a default image path - Keep escaping for image path
    
    // --- *** MODIFICATION 1: Remove htmlspecialchars and nl2br for descriptions *** ---
    // Assuming the stored HTML includes necessary formatting like <p>, <br> etc.
    // If your stored text relies *only* on database newlines, you might keep nl2br() but remove htmlspecialchars()
    $base_product_description = $product['p_description'] ?? ''; // Output raw HTML
    $base_product_short_description = $product['p_short_description'] ?? ''; // Output raw HTML
    // --- *** END MODIFICATION 1 *** ---
    
    $initial_description = $base_product_description; // Start with base description (now raw HTML)
    $product_category_name = htmlspecialchars($product['tcat_name'] ?? ''); // Get category name - Keep escaping

    // --- Fetch Product Variations ---
    $stmt_variations = $pdo->prepare("
        SELECT
            pv.variation_id, pv.p_id,
            pv.variation_name, pv.variation_description,
            pv.variation_color as color_id, c.color_name, c.color_code,
            pv.variation_size as size_id, s.size_name,
            pv.variation_price, /* ABSOLUTE PRICE */
            pv.variation_qty, pv.variation_sku, pv.variation_image
        FROM tbl_product_variation pv
        LEFT JOIN tbl_color c ON pv.variation_color = c.color_id
        LEFT JOIN tbl_size s ON pv.variation_size = s.size_id
        WHERE pv.p_id = ?
        ORDER BY pv.variation_name ASC, c.color_name ASC, s.size_name ASC /* Adjust ordering as needed */
    ");
    $stmt_variations->execute([$product_id]);
    $variations_data = $stmt_variations->fetchAll(PDO::FETCH_ASSOC);

    // --- Fetch Additional Gallery Photos (CORRECTED QUERY) ---
    $stmt_photos = $pdo->prepare("SELECT photo FROM tbl_product_photo WHERE p_id = ?"); // Select only 'photo' column
    $stmt_photos->execute([$product_id]);
    $product_photos = $stmt_photos->fetchAll(PDO::FETCH_ASSOC);

    // --- Fetch Settings ---
    $stmt_settings = $pdo->prepare("SELECT footer_copyright FROM tbl_settings WHERE id=1");
    $stmt_settings->execute();
    $settings = $stmt_settings->fetch(PDO::FETCH_ASSOC);
    if ($settings && !empty($settings['footer_copyright'])) {
         $footer_copyright = htmlspecialchars($settings['footer_copyright']); // Escape copyright text - Keep escaping
    }

    // --- Fetch Shipping Data ---
    $stmt_countries = $pdo->prepare("SELECT country_id, country_name FROM tbl_country ORDER BY country_name ASC");
    $stmt_countries->execute();
    $countries = $stmt_countries->fetchAll(PDO::FETCH_ASSOC);

    $stmt_shipping_costs = $pdo->prepare("SELECT country_id, amount FROM tbl_shipping_cost");
    $stmt_shipping_costs->execute();
    $shipping_costs_raw = $stmt_shipping_costs->fetchAll(PDO::FETCH_ASSOC);
    foreach ($shipping_costs_raw as $cost) {
        $shipping_costs_map[(int)$cost['country_id']] = is_numeric($cost['amount']) ? (float)$cost['amount'] : 0;
    }

    $stmt_shipping_all = $pdo->prepare("SELECT amount FROM tbl_shipping_cost_all LIMIT 1");
    $stmt_shipping_all->execute();
    $shipping_all_data = $stmt_shipping_all->fetch(PDO::FETCH_ASSOC);
    $default_shipping_cost = ($shipping_all_data && is_numeric($shipping_all_data['amount'])) ? (float)$shipping_all_data['amount'] : 0;
    // Let JS determine initial shipping cost based on dropdown default selection ("Select Area") -> 0 cost initially
    $initial_shipping_cost = 0;

} catch (PDOException $e) {
    error_log("Database Error in product_detail.php for product ID {$product_id}: " . $e->getMessage());
    // Display a more user-friendly error in production
    // exit('Sorry, there was an error loading product details. Please try again later.');
    // During development, show more details:
    exit('Database Error: ' . $e->getMessage() . ' (Check PHP error log for more details)');
}

// --- Prepare Variation Data for JavaScript & Determine Initial Variation ---
if (!empty($variations_data)) {
    $has_variations = true;
    foreach ($variations_data as $var) {
        // Price is absolute, ensure it's a float
        $variation_actual_price = is_numeric($var['variation_price']) ? (float)$var['variation_price'] : 0;
        $variation_image_path = $var['variation_image'] ? $upload_path_variations . htmlspecialchars($var['variation_image']) : null; // Keep escaping for image path

        $js_var_data = [
            'id' => (int)$var['variation_id'],
            'name' => htmlspecialchars($var['variation_name'] ?? 'Unnamed Variation'), // Keep escaping for name
            // --- *** MODIFICATION 2: Remove htmlspecialchars and nl2br for variation description *** ---
            'description' => $var['variation_description'] ?? '', // Output raw HTML for JS
            // --- *** END MODIFICATION 2 *** ---
            'color_id' => $var['color_id'] ? (int)$var['color_id'] : null,
            'color_name' => $var['color_name'] ? htmlspecialchars($var['color_name']) : null, // Keep escaping
            'color_code' => $var['color_code'] ? htmlspecialchars($var['color_code']) : null, // Keep escaping
            'size_id' => $var['size_id'] ? (int)$var['size_id'] : null,
            'size_name' => $var['size_name'] ? htmlspecialchars($var['size_name']) : null, // Keep escaping
            'price' => $variation_actual_price,
            'quantity' => (int)$var['variation_qty'],
            'sku' => $var['variation_sku'] ? htmlspecialchars($var['variation_sku']) : 'N/A', // Keep escaping for SKU
            'image' => $variation_image_path
        ];
        $variations_json[$var['variation_id']] = $js_var_data;

        if (!isset($available_variation_names[$var['variation_id']])) {
             $available_variation_names[$var['variation_id']] = $js_var_data['name']; // Already escaped above
        }
        if ($variation_image_path) {
            $variation_images_map[$variation_image_path] = $var['variation_id'];
        }
    }

    // Select the first variation as the default IF variations exist
    $first_variation_key = array_key_first($variations_json);
    if ($first_variation_key !== null) {
        $initial_variation = $variations_json[$first_variation_key];
        $initial_variation_id = $initial_variation['id'];
        $initial_variation_name = $initial_variation['name']; // Already escaped
        $initial_price = $initial_variation['price'];
        $initial_quantity = $initial_variation['quantity']; // Now use variation quantity
        $initial_sku = $initial_variation['sku'] !== 'N/A' && !empty($initial_variation['sku']) ? $initial_variation['sku'] : $initial_sku; // Already escaped
        
        // --- *** MODIFICATION 3: Ensure initial description uses raw HTML *** ---
        $initial_description = !empty($initial_variation['description']) ? $initial_variation['description'] : $base_product_description; // Variation desc (raw) or base (raw)
        // --- *** END MODIFICATION 3 *** ---
        
        $initial_color_name = $initial_variation['color_name']; // Already escaped
        $initial_size_name = $initial_variation['size_name'];   // Already escaped
        $initial_color_code = $initial_variation['color_code']; // Already escaped
        if ($initial_variation['image']) {
             $initial_image = $initial_variation['image']; // Already escaped
        }
    }
} else {
    // No variations, use base product quantity already set
    $has_variations = false;
    $initial_price = is_numeric($product['p_current_price']) ? (float)$product['p_current_price'] : 0; // ensure base price is set
    $initial_quantity = (int)$product['p_qty'];
    // Initial description is already the base description (raw HTML)
}


// --- Check Cart Status for Initial Variation (Check session first, JS will sync with localStorage) ---
if ($initial_variation_id && isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
    foreach ($_SESSION['cart'] as $item) {
        if (isset($item['product_id']) && $item['product_id'] == $product_id &&
            isset($item['variation_id']) && $item['variation_id'] == $initial_variation_id) {
                $in_cart = true;
                break;
        }
    }
}

// Determine initial button state (Revised for clarity)
if ($in_cart) {
    $cart_button_text = 'Already in Cart';
    $cart_button_disabled = true;
} elseif ($has_variations && !$initial_variation_id && !empty($variations_data)) { // Check variations_data isn't empty
    // Variations exist, but initial selection failed somehow (should be rare)
    $cart_button_text = 'Select Option';
    $cart_button_disabled = true;
} elseif ($initial_quantity > 0) {
    // Either base product or selected variation has stock
    $cart_button_text = 'Add to Cart';
    $cart_button_disabled = false;
} else {
    // Out of stock (either base product or selected variation)
    $cart_button_text = 'Out of Stock';
    $cart_button_disabled = true;
}

// Calculate initial total price (Base price only, shipping/installation added via JS interaction)
$initial_total = $initial_price; // Start with variation price (or base price if no variations)
// $initial_total += $initial_shipping_cost; // Shipping cost starts at 0 until selected

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <title><?php echo htmlspecialchars($product['p_name'] ?? 'Product Details'); ?> <?php echo $initial_variation_name ? '| ' . $initial_variation_name : ''; ?> | SMART Security</title>
  
  <link rel="stylesheet" href="css/style.css" />
  <link rel="stylesheet" href="css/product.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    /* Add any specific styles for rendered HTML content if needed */
    .product-description-container h1,
    .product-description-container h2,
    .product-description-container h3 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        line-height: 1.3;
    }
    .product-description-container p {
        margin-bottom: 1em;
        line-height: 1.6;
    }
    .product-description-container ul,
    .product-description-container ol {
        margin-left: 2em;
        margin-bottom: 1em;
    }
    .product-description-container li {
        margin-bottom: 0.5em;
    }

    /* Ensure initial description display elements handle raw HTML */
     #baseDescription, #variationDescription {
       word-wrap: break-word; /* Prevent long strings from breaking layout */
     }

  </style>
</head>
<body>

<header>
    <div class="container">
        <nav>
          <a href="index.php" class="logo">SMART<span>.</span></a>
          <ul class="nav-links" id="navLinks">
            <li><a href="index.php">Home</a></li>
            <li><a href="index.php#about">About</a></li>
            <li><a href="index.php#products">Products</a></li>
            <li><a href="index.php#gallery">Gallery</a></li>
            <li><a href="index.php#contact">Contact</a></li>
            <li class="cart-icon">
              <a href="cart.php" aria-label="View Shopping Cart">
                  <i class="fas fa-shopping-cart"></i>
                  <span class="cart-count" id="cartCount">0</span>
              </a>
            </li>
          </ul>
          <div class="mobile-menu" id="mobileMenuBtn">
             <div class="menu-btn"><span></span><span></span><span></span></div>
          </div>
        </nav>
    </div>
</header>

<main>
    <div class="container">
        <?php if ($product): ?>
        <div class="product-detail-container">
            <div class="product-gallery">
                <div class="main-image-container">
                    <img id="mainImage" src="<?php echo $initial_image; ?>" alt="<?php echo htmlspecialchars($product['p_name']); ?>" class="main-image" onerror="this.onerror=null; this.src='images/default_product.png';">
                </div>
                <div class="thumbnail-container">
                    <?php
                    $displayed_thumb_images = [];
                    // Base image path already escaped
                    $base_featured_img_path = !empty($product['p_featured_photo']) ? $upload_path_featured . htmlspecialchars($product['p_featured_photo']) : null;

                    // 1. Base Featured Photo (if exists and not associated with a specific variation image)
                    if ($base_featured_img_path && !in_array($base_featured_img_path, $displayed_thumb_images) && !array_key_exists($base_featured_img_path, $variation_images_map) ) {
                        $is_active = ($initial_image == $base_featured_img_path);
                        $data_variation_id_attr = '';
                        // Link to initial variation only if initial variation HAS NO image itself
                        if ($initial_variation && !$initial_variation['image'] && $is_active) {
                             $data_variation_id_attr = ' data-variation-id="' . $initial_variation['id'] . '"';
                        }
                        // Path already escaped, alt text escaped
                        echo '<img src="' . $base_featured_img_path . '" alt="Featured Thumbnail" class="thumbnail' . ($is_active ? ' active' : '') . '" data-image="' . $base_featured_img_path . '"' . $data_variation_id_attr . ' onerror="this.style.display=\'none\'">';
                        $displayed_thumb_images[] = $base_featured_img_path;
                    }

                    // 2. Variation Images
                    foreach ($variations_json as $var_id => $var_data):
                        // Image path already escaped
                        if ($var_data['image'] && !in_array($var_data['image'], $displayed_thumb_images)):
                            $is_active = ($initial_image == $var_data['image']);
                            // Alt text variation name escaped
                            echo '<img src="' . $var_data['image'] . '" alt="Variation ' . htmlspecialchars($var_data['name']) . '" class="thumbnail' . ($is_active ? ' active' : '') . '" data-image="' . $var_data['image'] . '" data-variation-id="' . $var_id . '" onerror="this.style.display=\'none\'">';
                            $displayed_thumb_images[] = $var_data['image'];
                        endif;
                    endforeach;

                    // 3. Gallery Photos
                    foreach ($product_photos as $index => $photo):
                        if (!empty($photo['photo'])) {
                            // Path escaped
                            $gallery_img_path = $upload_path_gallery . htmlspecialchars($photo['photo']);
                            if (!in_array($gallery_img_path, $displayed_thumb_images)):
                                // Alt text simple, path escaped
                                echo '<img src="' . $gallery_img_path . '" alt="Gallery Thumbnail ' . ($index + 1) . '" class="thumbnail" data-image="' . $gallery_img_path . '" onerror="this.style.display=\'none\'">';
                                $displayed_thumb_images[] = $gallery_img_path;
                            endif;
                        }
                    endforeach;
                    ?>
                </div>
            </div>

            <div class="product-info">
                <h1 class="product-title"><?php echo htmlspecialchars($product['p_name']); ?></h1>
                <div class="product-price" id="productPrice">Tsh <?php echo number_format($initial_price, 0); ?></div>

                <div class="product-meta">
                    <span>Availability: <strong id="productAvailability" style="color: <?php echo ($initial_quantity > 0) ? '#1cc88a' : '#e74a3b'; ?>"><?php echo ($initial_quantity > 0) ? 'In Stock' : 'Out of Stock'; ?></strong></span>
                    <span id="productSku">SKU: <?php echo $initial_sku; ?></span>
                    <?php if (!empty($product_category_name)): ?>
                        <span>Category: <?php echo $product_category_name; ?></span>
                    <?php endif; ?>
                </div>

               <div class="variation-attributes">
                   <span id="variationColorDisplay">
                       Color: <?php if($initial_color_code): ?><span class="color-swatch" style="background-color: <?php echo $initial_color_code; ?>;"></span><?php endif; ?>
                       <strong id="variationColorName"><?php echo $initial_color_name ?? ''; ?></strong>
                   </span>
                   <span id="variationSizeDisplay">
                       Size: <strong id="variationSizeName"><?php echo $initial_size_name ?? ''; ?></strong>
                   </span>
               </div>

                <div class="product-description-container">
                    <?php if (!empty($base_product_short_description)): ?>
                        <div class="short-description"><?php echo $base_product_short_description; ?></div>
                        <?php endif; ?>
                    <?php if (!empty($base_product_description)): ?>
                        <div class="base-description" id="baseDescription">
                              <?php echo $base_product_description; ?>
                          </div>
                        <?php endif; ?>
                    <div id="variationDescription">
                        <?php echo $initial_variation ? ($initial_variation['description'] ?? '') : ''; ?>
                    </div>
                    </div>

                <input type="hidden" id="selectedVariationId" name="variation_id" value="<?php echo $initial_variation_id ?? ''; ?>">

                <div class="options-section">
                     <div class="variation-selector-group variation-name-container">
                          <?php if ($has_variations): ?>
                              <label class="group-label">Select Option:</label>
                              <div class="variation-name-selector" id="variationNameSelector">
                                   <?php foreach ($available_variation_names as $var_id => $var_name): ?>
                                       <div class="variation-name-option">
                                           <input type="radio" id="variation-<?php echo $var_id; ?>"
                                                  name="variation_id_selector" value="<?php echo $var_id; ?>"
                                                  class="variation-name-radio"
                                                  <?php echo ($initial_variation_id == $var_id) ? 'checked' : ''; ?>
                                                  >
                                           <label for="variation-<?php echo $var_id; ?>" class="variation-name-label">
                                               <?php echo $var_name; ?>
                                           </label>
                                       </div>
                                   <?php endforeach; ?>
                              </div>
                          <?php endif; ?>
                     </div>

                    <div class="quantity-selector">
                        <label for="quantity">Quantity:</label>
                        <input type="number" id="quantity" name="quantity" value="1" min="1"
                               max="<?php echo max(1, $initial_quantity); ?>" class="quantity-input"
                               <?php echo ($initial_quantity <= 0) ? 'disabled' : ''; ?>
                               aria-label="Product Quantity">
                    </div>

                    <div class="additional-options">
                        <h4>Additional Services & Shipping:</h4>
                         <div class="shipping-option">
                             <label for="shippingCountry">Shipping Area:</label>
                             <select id="shippingCountry" name="shipping_country" class="shipping-select">
                                 <option value="">-- Select Area --</option>
                                 <?php foreach ($countries as $country): ?>
                                     <option value="<?php echo $country['country_id']; ?>">
                                         <?php echo htmlspecialchars($country['country_name']); ?>
                                     </option>
                                 <?php endforeach; ?>
                                 <option value="0">Other (Default Rate)</option>
                             </select>
                             <span id="shippingCostDisplay"></span> </div>

                        <?php if($installation_fee > 0): ?>
                        <div class="fee-option">
                            <input type="checkbox" id="installation" name="installation">
                            <label for="installation">Professional Installation</label>
                            <span class="fee-amount" data-fee="<?php echo $installation_fee; ?>">+ Tsh <?php echo number_format($installation_fee, 0); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="price-action-section">
                    <div class="total-price">
                        <span class="label">Total:</span>
                        <span class="amount" id="totalPrice">
                            Tsh <?php echo number_format($initial_total, 0); ?>
                        </span>
                    </div>

                    <button class="add-to-cart-btn" id="addToCartBtn" <?php echo $cart_button_disabled ? 'disabled' : ''; ?>>
                        <span class="btn-icon">
                            <?php
                            if ($in_cart) { echo '<i class="fas fa-check"></i>'; }
                            elseif ($cart_button_disabled && $cart_button_text !== 'Select Option') { echo '<i class="fas fa-times-circle"></i>'; }
                            elseif ($cart_button_text === 'Select Option') { echo '<i class="fas fa-info-circle"></i>'; }
                            else { echo '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>'; }
                            ?>
                        </span>
                        <span class="btn-text"><?php echo $cart_button_text; ?></span>
                    </button>

                    <a href="cart.php" class="return-to-cart">View Cart & Checkout →</a>
                </div>
            </div> </div> <?php else: ?>
            <div style="text-align: center; padding: 50px; background: #fff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2>Product Not Found</h2>
                <p>Sorry, the product you are looking for is not available or could not be loaded.</p>
                <a href="index.php" style="color: var(--secondary); text-decoration: none; font-weight: 600;">← Return to Home</a>
            </div>
        <?php endif; ?>
    </div> </main>

<footer>
   <div class="container">
     <div class="footer-bottom">
       <div class="copyright"><?php echo $footer_copyright; ?></div>
     </div>
   </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="js/product-detail.js"></script> <script>
    // Make PHP data available to JS (ensure variations_json descriptions are raw HTML)
    const variationsData = <?php echo json_encode($variations_json, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT); ?>;
    const shippingCostsMap = <?php echo json_encode($shipping_costs_map); ?>;
    const defaultShippingCost = <?php echo json_encode($default_shipping_cost); ?>;
    const installationFee = <?php echo json_encode($installation_fee); ?>;
    const baseProductId = <?php echo json_encode($product_id); ?>;
    const baseProductDescription = <?php echo json_encode($base_product_description); ?>; // Pass raw base description
    const initialVariationId = <?php echo json_encode($initial_variation_id); ?>;
    // Add other necessary variables...

    // --- Basic JS structure outline (adapt to your existing product-detail.js) ---
    document.addEventListener('DOMContentLoaded', () => {
        // --- Elements ---
        const mainImage = document.getElementById('mainImage');
        const thumbnails = document.querySelectorAll('.thumbnail');
        const productPriceEl = document.getElementById('productPrice');
        const availabilityEl = document.getElementById('productAvailability');
        const skuEl = document.getElementById('productSku');
        const variationColorDisplayEl = document.getElementById('variationColorDisplay');
        const variationColorNameEl = document.getElementById('variationColorName');
        const variationSizeDisplayEl = document.getElementById('variationSizeDisplay');
        const variationSizeNameEl = document.getElementById('variationSizeName');
        const baseDescriptionEl = document.getElementById('baseDescription'); // Might be hidden/shown
        const variationDescriptionEl = document.getElementById('variationDescription');
        const quantityInput = document.getElementById('quantity');
        const shippingCountrySelect = document.getElementById('shippingCountry');
        const shippingCostDisplayEl = document.getElementById('shippingCostDisplay');
        const installationCheckbox = document.getElementById('installation');
        const totalPriceEl = document.getElementById('totalPrice');
        const addToCartBtn = document.getElementById('addToCartBtn');
        const cartBtnText = addToCartBtn.querySelector('.btn-text');
        const cartBtnIcon = addToCartBtn.querySelector('.btn-icon');
        const selectedVariationIdInput = document.getElementById('selectedVariationId');
        const variationNameRadios = document.querySelectorAll('.variation-name-radio');

        // --- State ---
        let currentVariation = variationsData[initialVariationId] || null;
        let currentQuantity = parseInt(quantityInput.value) || 1;
        let currentShippingCost = 0; // Starts at 0 until selected
        let installationSelected = false;
        let currentBasePrice = parseFloat(productPriceEl.textContent.replace(/[^0-9.]/g, '')) || 0;

        // --- Functions ---
        function formatPrice(amount) {
            return `Tsh ${amount.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
        }

        function updateUI(variation) {
            if (!variation && !<?php echo json_encode(!$has_variations); ?>) { // Handle case where no valid variation is selected but variations exist
                 productPriceEl.textContent = formatPrice(<?php echo json_encode($initial_price); ?>); // Show base or initial price?
                 availabilityEl.textContent = 'Select Option';
                 availabilityEl.style.color = '#ffc107'; // warning color
                 skuEl.textContent = `SKU: <?php echo htmlspecialchars($product['p_id']); ?>`; // Base SKU
                 if (variationDescriptionEl) variationDescriptionEl.innerHTML = ''; // Clear variation description
                 if (baseDescriptionEl) baseDescriptionEl.style.display = 'block'; // Show base description
                 if (variationColorNameEl) variationColorNameEl.textContent = '';
                 if (variationColorDisplayEl) variationColorDisplayEl.querySelector('.color-swatch')?.remove();
                 if (variationSizeNameEl) variationSizeNameEl.textContent = '';

                 quantityInput.max = 1;
                 quantityInput.value = 1;
                 quantityInput.disabled = true;
                 addToCartBtn.disabled = true;
                 cartBtnText.textContent = 'Select Option';
                 cartBtnIcon.innerHTML = '<i class="fas fa-info-circle"></i>';
                 selectedVariationIdInput.value = '';

                 // Reset thumbnails - deselect variation-specific ones
                 thumbnails.forEach(thumb => {
                     thumb.classList.remove('active');
                     if (thumb.dataset.variationId) { // Only deselect thumbs tied to variations
                         // Optionally, activate the base featured image if it exists
                         const baseFeatured = document.querySelector('.thumbnail[data-image="<?php echo $base_featured_img_path; ?>"]');
                         if (baseFeatured) {
                             baseFeatured.classList.add('active');
                             mainImage.src = baseFeatured.dataset.image;
                         } else {
                             mainImage.src = 'images/default_product.png'; // Fallback
                         }
                     }
                 });

            } else if (variation) { // Update based on selected variation
                currentBasePrice = variation.price;
                productPriceEl.textContent = formatPrice(variation.price);
                availabilityEl.textContent = variation.quantity > 0 ? 'In Stock' : 'Out of Stock';
                availabilityEl.style.color = variation.quantity > 0 ? '#1cc88a' : '#e74a3b';
                skuEl.textContent = `SKU: ${variation.sku || 'N/A'}`;

                // *** USE innerHTML for descriptions ***
                if (variationDescriptionEl) {
                    variationDescriptionEl.innerHTML = variation.description || ''; // Render variation HTML description
                }
                // Decide whether to show/hide base description based on variation description presence
                if (baseDescriptionEl) {
                   baseDescriptionEl.style.display = variation.description ? 'none' : 'block';
                }
                // *** END innerHTML change ***

                if (variationColorNameEl) variationColorNameEl.textContent = variation.color_name || '';
                if (variationColorDisplayEl) {
                   // Remove old swatch if exists
                    variationColorDisplayEl.querySelector('.color-swatch')?.remove();
                    if(variation.color_code) {
                        const swatch = document.createElement('span');
                        swatch.className = 'color-swatch';
                        swatch.style.backgroundColor = variation.color_code;
                        variationColorDisplayEl.insertBefore(swatch, variationColorNameEl); // Insert before the name
                    }
                }
                if (variationSizeNameEl) variationSizeNameEl.textContent = variation.size_name || '';

                quantityInput.max = Math.max(1, variation.quantity); // Allow 1 even if 0 stock, but button disabled
                quantityInput.value = 1; // Reset quantity on variation change
                currentQuantity = 1;
                quantityInput.disabled = variation.quantity <= 0;

                // Update main image if variation has one
                if (variation.image) {
                    mainImage.src = variation.image;
                    // Update active thumbnail
                    thumbnails.forEach(thumb => {
                        thumb.classList.toggle('active', thumb.dataset.image === variation.image);
                    });
                } else {
                    // If variation has NO image, try to show the base featured image
                     const baseFeaturedThumb = document.querySelector('.thumbnail[data-image="<?php echo $base_featured_img_path; ?>"]');
                     if (baseFeaturedThumb) {
                         mainImage.src = baseFeaturedThumb.dataset.image;
                         thumbnails.forEach(thumb => thumb.classList.remove('active'));
                         baseFeaturedThumb.classList.add('active');
                     } else {
                         mainImage.src = 'images/default_product.png'; // Absolute fallback
                         thumbnails.forEach(thumb => thumb.classList.remove('active'));
                     }
                }

                selectedVariationIdInput.value = variation.id;
                // Check cart status for THIS variation
                checkCartStatus(baseProductId, variation.id);


            } else { // No variations exist for the product
                currentBasePrice = <?php echo json_encode($initial_price); ?>; // Base product price
                 productPriceEl.textContent = formatPrice(currentBasePrice);
                 const baseQuantity = <?php echo json_encode($initial_quantity); ?>;
                 availabilityEl.textContent = baseQuantity > 0 ? 'In Stock' : 'Out of Stock';
                 availabilityEl.style.color = baseQuantity > 0 ? '#1cc88a' : '#e74a3b';
                 skuEl.textContent = `SKU: <?php echo $initial_sku; ?>`; // Base SKU
                  // Show base description (already rendered via PHP)
                 if (variationDescriptionEl) variationDescriptionEl.innerHTML = '';
                 if (baseDescriptionEl) baseDescriptionEl.style.display = 'block';
                 if (variationColorNameEl) variationColorNameEl.textContent = '';
                 if (variationColorDisplayEl) variationColorDisplayEl.querySelector('.color-swatch')?.remove();
                 if (variationSizeNameEl) variationSizeNameEl.textContent = '';


                 quantityInput.max = Math.max(1, baseQuantity);
                 quantityInput.disabled = baseQuantity <= 0;
                 addToCartBtn.disabled = baseQuantity <= 0;
                 cartBtnText.textContent = baseQuantity <= 0 ? 'Out of Stock' : 'Add to Cart';
                 cartBtnIcon.innerHTML = baseQuantity <= 0 ? '<i class="fas fa-times-circle"></i>' : '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>';
                 selectedVariationIdInput.value = ''; // No variation ID
            }

            updateTotalPrice(); // Recalculate total whenever UI changes
        }

        function checkCartStatus(productId, variationId) {
             // Check localStorage cart first for responsiveness
            let cart = JSON.parse(localStorage.getItem('cart') || '[]');
            let itemInCart = cart.find(item =>
                item.product_id == productId && item.variation_id == variationId
            );

            const variationAvailable = variationId ? (variationsData[variationId]?.quantity > 0) : (<?php echo json_encode($initial_quantity); ?> > 0) ;

            if (itemInCart) {
                addToCartBtn.disabled = true;
                cartBtnText.textContent = 'Already in Cart';
                cartBtnIcon.innerHTML = '<i class="fas fa-check"></i>';
            } else if (!variationAvailable) {
                 addToCartBtn.disabled = true;
                 cartBtnText.textContent = 'Out of Stock';
                 cartBtnIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            } else {
                 addToCartBtn.disabled = false;
                 cartBtnText.textContent = 'Add to Cart';
                 cartBtnIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" class="cart-icon-svg"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>';
            }

            // Also disable quantity input if already in cart? Optional UX choice.
            // quantityInput.disabled = itemInCart || variationQuantity <= 0;
             quantityInput.disabled = itemInCart || !variationAvailable;
        }

        function updateTotalPrice() {
            const variation = variationsData[selectedVariationIdInput.value] || null;
            const basePrice = variation ? variation.price : (<?php echo json_encode(!$has_variations ? $initial_price : 0); ?>) ; // Use base price only if no variations *exist*

            let total = basePrice * currentQuantity;
            total += currentShippingCost;
            if (installationSelected) {
                total += installationFee;
            }
            totalPriceEl.textContent = formatPrice(total);
        }

        // --- Event Listeners ---
        thumbnails.forEach(thumb => {
            thumb.addEventListener('click', () => {
                const newImageSrc = thumb.dataset.image;
                const linkedVariationId = thumb.dataset.variationId;

                mainImage.src = newImageSrc;
                thumbnails.forEach(t => t.classList.remove('active'));
                thumb.classList.add('active');

                // If thumbnail is explicitly linked to a variation, select that variation
                if (linkedVariationId && variationsData[linkedVariationId]) {
                    // Find the corresponding radio button and check it
                    const radioToSelect = document.getElementById(`variation-${linkedVariationId}`);
                    if (radioToSelect && !radioToSelect.checked) {
                        radioToSelect.checked = true;
                        // Trigger the change event manually to update everything
                        radioToSelect.dispatchEvent(new Event('change'));
                    } else if (radioToSelect.checked) {
                         // Already selected, maybe just update price/total (though change event should handle)
                         updateUI(variationsData[linkedVariationId]);
                    }
                }
                // If thumbnail is NOT linked (e.g., gallery or base image when variation has no image)
                // do NOT change the selected variation radio. The UI update handles showing the correct description/price.
            });
        });

        variationNameRadios.forEach(radio => {
             radio.addEventListener('change', () => {
                 const selectedId = radio.value;
                 currentVariation = variationsData[selectedId] || null;
                 updateUI(currentVariation);
             });
        });


        quantityInput.addEventListener('change', () => {
            let qty = parseInt(quantityInput.value);
            const maxQty = parseInt(quantityInput.max);
            if (isNaN(qty) || qty < 1) {
                qty = 1;
            }
            if (qty > maxQty) {
                qty = maxQty;
            }
            quantityInput.value = qty; // Correct the input field
            currentQuantity = qty;
            updateTotalPrice();
        });

        shippingCountrySelect.addEventListener('change', () => {
            const countryId = shippingCountrySelect.value;
            if (countryId && shippingCostsMap[countryId] !== undefined) {
                currentShippingCost = shippingCostsMap[countryId];
                shippingCostDisplayEl.textContent = `+ ${formatPrice(currentShippingCost)}`;
            } else if (countryId === "0") { // "Other" selected
                currentShippingCost = defaultShippingCost;
                shippingCostDisplayEl.textContent = `+ ${formatPrice(currentShippingCost)} (Default)`;
            } else { // "-- Select Area --"
                currentShippingCost = 0;
                shippingCostDisplayEl.textContent = '';
            }
            updateTotalPrice();
        });

        if (installationCheckbox) {
            installationCheckbox.addEventListener('change', () => {
                installationSelected = installationCheckbox.checked;
                updateTotalPrice();
            });
        }

        addToCartBtn.addEventListener('click', () => {
            const variationId = selectedVariationIdInput.value;
            const quantity = parseInt(quantityInput.value);

            // Double check selection if variations exist
            if (<?php echo json_encode($has_variations); ?> && !variationId) {
                 Swal.fire({
                    title: 'Option Required',
                    text: 'Please select a product option before adding to cart.',
                    icon: 'warning',
                    confirmButtonColor: '#3085d6',
                  });
                 return;
            }
            // Double check quantity available
            const availableQty = variationId ? (variationsData[variationId]?.quantity || 0) : <?php echo json_encode($initial_quantity); ?>;
             if (quantity > availableQty) {
                  Swal.fire({
                    title: 'Insufficient Stock',
                    text: `Only ${availableQty} item(s) available. Please adjust the quantity.`,
                    icon: 'warning',
                    confirmButtonColor: '#3085d6',
                  });
                 return;
             }
             if (quantity < 1) {
                 Swal.fire({
                    title: 'Invalid Quantity',
                    text: 'Please enter a quantity of 1 or more.',
                    icon: 'warning',
                    confirmButtonColor: '#3085d6',
                  });
                 return;
             }


            // Add to LocalStorage Cart (or send to server via AJAX)
             let cart = JSON.parse(localStorage.getItem('cart') || '[]');
             let existingItemIndex = cart.findIndex(item =>
                 item.product_id == baseProductId && item.variation_id == (variationId || null) // Handle null if no variations
             );

             if (existingItemIndex > -1) {
                 // Optionally update quantity if already in cart, or just show message
                 // cart[existingItemIndex].quantity += quantity; // Example: Increase quantity
                 Swal.fire('Already Added', 'This item is already in your cart.', 'info');
             } else {
                  const itemToAdd = {
                      product_id: baseProductId,
                      variation_id: variationId || null, // Store null if no variation selected/exists
                      quantity: quantity,
                      // Add other details needed for the cart page (name, price, image)
                      // Fetch these from the current state or variationsData
                      name: <?php echo json_encode(htmlspecialchars($product['p_name'])); ?> + (currentVariation ? ` - ${currentVariation.name}` : ''),
                      price: currentVariation ? currentVariation.price : <?php echo json_encode($initial_price); ?>,
                      image: currentVariation ? currentVariation.image : <?php echo json_encode($initial_image); ?>,
                      sku: currentVariation ? currentVariation.sku : <?php echo json_encode($initial_sku); ?>
                  };
                  cart.push(itemToAdd);
                  localStorage.setItem('cart', JSON.stringify(cart));

                  // Update UI
                   addToCartBtn.disabled = true;
                   cartBtnText.textContent = 'Added to Cart';
                   cartBtnIcon.innerHTML = '<i class="fas fa-check"></i>';
                   quantityInput.disabled = true; // Disable quantity after adding

                   // Update header cart count (implement this function globally)
                   updateCartCount();

                   Swal.fire({
                      title: 'Added!',
                      text: 'Product added to your cart.',
                      icon: 'success',
                      timer: 1500, // Automatically close after 1.5 seconds
                      showConfirmButton: false
                    });

                    // Optional: Send to server session via AJAX here if needed
             }
        });

        // --- Initial Setup ---
        updateUI(currentVariation || null); // Update based on initial variation (or null if none/no variations)
         // Initial check if item is in cart (after UI is initially set)
        checkCartStatus(baseProductId, selectedVariationIdInput.value || null);
        updateTotalPrice(); // Calculate initial total price based on selections

         // Example function - place this appropriately in your global JS or header script
         function updateCartCount() {
             const cart = JSON.parse(localStorage.getItem('cart') || '[]');
             const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
             const cartCountEl = document.getElementById('cartCount');
             if (cartCountEl) {
                 cartCountEl.textContent = totalItems;
                 cartCountEl.style.display = totalItems > 0 ? 'flex' : 'none'; // Show/hide badge
             }
         }
         updateCartCount(); // Initial cart count on page load

    });


</script>


</body>
</html>
<?php
ob_end_flush(); // Send output buffer
?>