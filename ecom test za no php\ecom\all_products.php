<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Fetch all categories
$statement = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC");
$statement->execute();
$all_categories = $statement->fetchAll(PDO::FETCH_ASSOC);

// Fetch all products
$statement = $pdo->prepare("SELECT * FROM tbl_product WHERE p_is_active = 1 ORDER BY p_id DESC");
$statement->execute();
$all_products = $statement->fetchAll(PDO::FETCH_ASSOC);

// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = $settings['footer_copyright'] ?? "© " . date("Y") . " SMART LIFE. All rights reserved.";
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>All Products | SMART</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    /* Custom Checkbox Styles */
    .custom-checkbox {
      appearance: none;
      -webkit-appearance: none;
      height: 1.125rem; /* 18px */
      width: 1.125rem; /* 18px */
      border: 2px solid #d1d5db; /* Tailwind: border-gray-300 */
      border-radius: 0.25rem; /* Tailwind: rounded-sm */
      display: inline-block;
      position: relative;
      cursor: pointer;
      transition: background-color 0.2s, border-color 0.2s;
      flex-shrink: 0; /* Prevent shrinking in flex container */
      margin-top: 2px; /* Align better with text */
    }
    .custom-checkbox:checked {
      background-color: #4f46e5; /* Tailwind: bg-indigo-600 */
      border-color: #4f46e5; /* Tailwind: border-indigo-600 */
    }
    .custom-checkbox:checked::after {
      content: "";
      position: absolute;
      left: 5px;
      top: 2px;
      width: 4px;
      height: 8px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
    .custom-checkbox:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.4); /* Tailwind: ring-2 ring-indigo-500 ring-opacity-40 */
    }

    /* Custom Scrollbar for Filter Panel */
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #f1f5f9; /* Tailwind: bg-slate-100 */
      border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #94a3b8; /* Tailwind: bg-slate-400 */
      border-radius: 10px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #64748b; /* Tailwind: bg-slate-500 */
    }

    /* Fix for filter panel content overflow */
    #filtersPanel .overflow-y-auto {
      max-height: calc(100vh - 12rem);
      overflow-y: auto;
    }

    /* Ensure price range filter stays within container */
    .price-filter-container {
      width: 100%;
      overflow-x: hidden;
      word-break: break-word;
    }

    /* Improve filter label display */
    .price-filter-container label {
      display: flex;
      align-items: flex-start;
      flex-wrap: nowrap;
    }

    .price-filter-container label span {
      flex: 1;
      min-width: 0;
      overflow-wrap: break-word;
      word-wrap: break-word;
      hyphens: auto;
    }

    .product-preview {
      position: fixed;
      z-index: 1000;
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      width: 320px;
      padding: 1rem;
      opacity: 0;
      transform: translateY(10px);
      transition: opacity 0.2s, transform 0.2s;
      pointer-events: none;
      --arrow-direction: down;
    }
    .product-preview.show {
      opacity: 1;
      transform: translateY(0);
    }
    .product-preview::after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%) rotate(45deg);
      width: 16px;
      height: 16px;
      background: white;
      box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
    }
    .product-preview[style*="--arrow-direction: down"]::after {
      top: -8px;
    }
    .product-preview[style*="--arrow-direction: up"]::after {
      bottom: -8px;
      box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Custom Radio Styles */
    .custom-radio {
      appearance: none;
      -webkit-appearance: none;
      height: 1.125rem; /* 18px */
      width: 1.125rem; /* 18px */
      border: 2px solid #d1d5db; /* Tailwind: border-gray-300 */
      border-radius: 50%; /* Rounded for radio */
      display: inline-block;
      position: relative;
      cursor: pointer;
      transition: background-color 0.2s, border-color 0.2s;
      flex-shrink: 0;
      margin-top: 2px;
    }
    .custom-radio:checked {
      border-color: #4f46e5; /* Tailwind: border-indigo-600 */
      background-color: white; /* Keep background white */
    }
    .custom-radio:checked::after {
      content: "";
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 0.625rem; /* 10px */
      height: 0.625rem; /* 10px */
      border-radius: 50%;
      background-color: #4f46e5; /* Tailwind: bg-indigo-600 */
    }
    .custom-radio:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.4); /* Tailwind: ring-2 ring-indigo-500 ring-opacity-40 */
    }

    /* Compare Button Styles */
    .compare-btn {
      position: absolute;
      top: 8px;
      right: 8px;
      background-color: rgba(255, 255, 255, 0.8);
      border: 1px solid #e5e7eb;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      opacity: 0.7;
      z-index: 10; /* Ensure it's above image */
    }
    .compare-btn:hover {
      background-color: white;
      border-color: #4f46e5;
      color: #4f46e5;
      opacity: 1;
      transform: scale(1.1);
    }
    .compare-btn.selected {
      background-color: #4f46e5;
      border-color: #4f46e5;
      color: white;
      opacity: 1;
    }
    .compare-btn i {
      font-size: 14px;
    }

    /* Compare Bar Styles */
    #compareBar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #1f2937; /* Tailwind: bg-gray-800 */
      color: white;
      padding: 0.75rem 1rem; /* Tailwind: p-3 px-4 */
      box-shadow: 0 -4px 10px rgba(0,0,0,0.2);
      display: flex;
      align-items: center;
      justify-content: space-between;
      transform: translateY(100%);
      transition: transform 0.3s ease-in-out;
      z-index: 900;
    }
    #compareBar.show {
      transform: translateY(0);
    }
    #compareItemsContainer {
      display: flex;
      gap: 0.75rem; /* Tailwind: gap-3 */
      align-items: center;
      overflow-x: auto; /* Allow scrolling if many items */
      flex-grow: 1;
      padding-right: 1rem; /* Space before button */
    }
    .compare-item-thumbnail {
      width: 40px; /* Tailwind: w-10 */
      height: 40px; /* Tailwind: h-10 */
      object-fit: cover;
      border-radius: 0.25rem; /* Tailwind: rounded-sm */
      border: 1px solid #4b5563; /* Tailwind: border-gray-600 */
      flex-shrink: 0; /* Prevent shrinking */
    }
    #compareNowBtn {
      background-color: #4f46e5; /* Tailwind: bg-indigo-600 */
      color: white;
      padding: 0.5rem 1rem; /* Tailwind: px-4 py-2 */
      border-radius: 0.375rem; /* Tailwind: rounded-md */
      font-size: 0.875rem; /* Tailwind: text-sm */
      font-weight: 500; /* Tailwind: font-medium */
      transition: background-color 0.2s;
      white-space: nowrap; /* Prevent button text wrapping */
      flex-shrink: 0;
    }
     #compareNowBtn:hover {
       background-color: #4338ca; /* Tailwind: bg-indigo-700 */
     }
     #compareNowBtn:disabled {
       background-color: #6b7280; /* Tailwind: bg-gray-500 */
       cursor: not-allowed;
       opacity: 0.7;
     }
     #compareCountBadge {
        background-color: #dc2626; /* Tailwind: bg-red-600 */
        border-radius: 50%;
        padding: 0 6px;
        font-size: 11px;
        font-weight: bold;
        margin-left: 4px;
        line-height: 16px;
        min-width: 16px;
        text-align: center;
        display: inline-block; /* Ensure badge displays correctly */
     }
  </style>
</head>
<body class="bg-gray-50 text-gray-800">
  <!-- Header (from category.php) -->
  <header class="fixed inset-x-0 top-0 bg-white shadow z-50">
    <div class="container mx-auto px-4 flex items-center justify-between py-4">
      <a href="index.php" class="text-2xl font-bold text-gray-900">
        SMART LIFE<span class="text-blue-600">.</span>
      </a>
      <nav class="hidden md:flex items-center space-x-6">
        <a href="index.php#home" class="hover:text-blue-600 transition">Home</a>
        <a href="index.php#about" class="hover:text-blue-600 transition">About</a>
        <a href="index.php#products" class="hover:text-blue-600 transition">Products</a>
        <a href="index.php#gallery" class="hover:text-blue-600 transition">Best Deals</a>
        <a href="index.php#contact" class="hover:text-blue-600 transition">Contact</a>
        <!-- Search -->
        <div class="relative">
          <input id="searchInput" type="text" placeholder="Search products, categories..."
                 class="w-64 px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                 autocomplete="off">
          <div id="searchSuggestions"
               class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100"></div>
        </div>
        <!-- Cart -->
        <a href="cart.php" class="relative text-xl hover:text-blue-600 transition">
          🛒
          <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
        </a>
      </nav>
      <!-- Mobile Menu Button -->
      <button id="mobileMenuButton" class="md:hidden flex items-center">
        <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
        </svg>
      </button>
    </div>
  </header>
  <!-- Mobile Menu -->
  <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 bg-white z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg">
    <div class="flex flex-col h-full">
      <div class="flex justify-between items-center p-4 border-b">
        <a href="index.php" class="text-xl font-bold text-gray-900">
          SMART LIFE<span class="text-[#00c2ff]">.</span>
        </a>
        <button id="closeMobileMenu" class="text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
        <a href="index.php#home" class="block text-gray-700 hover:text-[#00c2ff] transition">Home</a>
        <a href="index.php#about" class="block text-gray-700 hover:text-[#00c2ff] transition">About</a>
        <a href="index.php#products" class="block text-gray-700 hover:text-[#00c2ff] transition">Products</a>
        <a href="index.php#gallery" class="block text-gray-700 hover:text-[#00c2ff] transition">Best Deals</a>
        <a href="index.php#contact" class="block text-gray-700 hover:text-[#00c2ff] transition">Contact</a>
        <!-- Search in Mobile Menu -->
        <div class="relative mt-4">
          <input id="mobileSearchInput" type="text" placeholder="Search products, categories..."
                 class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                 autocomplete="off">
          <div id="mobileSearchSuggestions"
               class="absolute inset-x-0 mt-1 bg-white rounded-lg shadow-xl overflow-hidden hidden z-50 border border-gray-100"></div>
        </div>
        <!-- Cart in Mobile Menu -->
        <a href="cart.php" class="flex items-center text-gray-700 hover:text-[#00c2ff] transition">
          <span class="text-xl mr-2">🛒</span>
          <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
        </a>
      </nav>
    </div>
  </div>
  <!-- Backdrop for mobile menu -->
  <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>
  <main class="pt-24 pb-12">
    <section class="container mx-auto px-4 flex flex-col lg:flex-row gap-8">
      <!-- Mobile Filter Button -->
      <button id="mobileFilterButton" class="lg:hidden fixed bottom-4 right-4 bg-blue-600 text-white p-3 rounded-full shadow-lg z-40">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
        </svg>
      </button>

      <!-- Sidebar Filters -->
      <aside id="filtersPanel" class="fixed lg:sticky lg:top-24 inset-y-0 right-0 lg:inset-auto lg:w-72 w-4/5 bg-white rounded-xl shadow-2xl p-0 lg:block flex-shrink-0 transform translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out z-50 lg:z-auto border border-gray-200/70 flex flex-col max-h-[calc(100vh-2rem)] lg:max-h-[calc(100vh-6rem)] my-2 lg:my-0 overflow-hidden">
        <div class="flex items-center justify-between p-5 border-b border-gray-200">
          <h3 class="text-xl font-semibold text-gray-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
            </svg>
            Filters
          </h3>
          <div class="flex items-center gap-3">
            <button id="clearFilters" class="text-xs text-indigo-600 hover:text-indigo-500 font-semibold transition-colors uppercase tracking-wider">Clear all</button>
            <button id="closeFilters" class="lg:hidden text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div class="flex-grow overflow-y-auto p-5 custom-scrollbar" style="max-height: calc(100vh - 10rem);">
          <div class="space-y-6">
            <div>
              <h4 class="text-xs font-bold text-gray-400 uppercase tracking-wider mb-3">Categories</h4>
              <ul class="space-y-1">
                <?php foreach ($all_categories as $cat): ?>
                  <li>
                    <label class="flex items-start gap-2.5 cursor-pointer p-2 rounded-lg hover:bg-indigo-50 transition-colors group">
                      <input type="checkbox" class="custom-checkbox category-filter" value="<?= $cat['tcat_id'] ?>">
                      <span class="text-black group-hover:text-indigo-700 font-medium text-sm leading-tight" style="color: black !important;"><?= htmlspecialchars($cat['tcat_name']) ?></span>
                    </label>
                    <?php
                      // Fetch subcategories for this top category
                      $stmt_sub = $pdo->prepare("SELECT * FROM tbl_mid_category WHERE tcat_id = ? ORDER BY mcat_name ASC");
                      $stmt_sub->execute([$cat['tcat_id']]);
                      $subcategories = $stmt_sub->fetchAll(PDO::FETCH_ASSOC);
                    ?>
                    <?php if (!empty($subcategories)): ?>
                      <ul class="ml-[calc(1.125rem+0.625rem-2px)] mt-1.5 space-y-1 border-l-2 border-gray-200/70 pl-3.5">
                        <?php foreach ($subcategories as $subcat): ?>
                          <li>
                            <label class="flex items-start gap-2.5 cursor-pointer py-1 px-1.5 rounded-md hover:bg-indigo-50 transition-colors group">
                              <input type="checkbox" class="custom-checkbox subcategory-filter" value="<?= $subcat['mcat_id'] ?>" data-parent-category-id="<?= $cat['tcat_id'] ?>">
                              <span class="text-black group-hover:text-indigo-700 text-xs leading-tight" style="color: black !important;"><?= htmlspecialchars($subcat['mcat_name']) ?></span>
                            </label>
                          </li>
                        <?php endforeach; ?>
                      </ul>
                    <?php endif; ?>
                  </li>
                <?php endforeach; ?>
              </ul>
            </div>

            <div class="pt-5 border-t border-gray-200">
              <h4 class="text-xs font-bold text-gray-400 uppercase tracking-wider mb-3">Availability</h4>
              <label class="flex items-start gap-2.5 cursor-pointer p-2 rounded-lg hover:bg-indigo-50 transition-colors group">
                <input type="checkbox" class="custom-checkbox in-stock-filter">
                <span class="text-black group-hover:text-indigo-700 font-medium text-sm leading-tight" style="color: black !important;">In Stock Only</span>
              </label>
            </div>

            <!-- Price Filter Section -->
            <div class="pt-5 border-t border-gray-200 price-filter-container">
              <h4 class="text-xs font-bold text-gray-400 uppercase tracking-wider mb-3">Price Range (TSH)</h4>
              <ul class="space-y-1">
                <li><label class="flex items-start gap-2.5 cursor-pointer p-1.5 rounded-lg hover:bg-indigo-50 group"><input type="radio" name="price_filter" value="all" class="custom-radio price-filter" checked><span class="text-black font-medium text-sm leading-tight break-words" style="color: black !important;">All Prices</span></label></li>
                <li><label class="flex items-start gap-2.5 cursor-pointer p-1.5 rounded-lg hover:bg-indigo-50 group"><input type="radio" name="price_filter" value="0-50000" class="custom-radio price-filter"><span class="text-black font-medium text-sm leading-tight break-words" style="color: black !important;">Under 50,000</span></label></li>
                <li><label class="flex items-start gap-2.5 cursor-pointer p-1.5 rounded-lg hover:bg-indigo-50 group"><input type="radio" name="price_filter" value="50000-100000" class="custom-radio price-filter"><span class="text-black font-medium text-sm leading-tight break-words" style="color: black !important;">50,000 - 100,000</span></label></li>
                <li><label class="flex items-start gap-2.5 cursor-pointer p-1.5 rounded-lg hover:bg-indigo-50 group"><input type="radio" name="price_filter" value="100000-250000" class="custom-radio price-filter"><span class="text-black font-medium text-sm leading-tight break-words" style="color: black !important;">100,000 - 250,000</span></label></li>
                <li><label class="flex items-start gap-2.5 cursor-pointer p-1.5 rounded-lg hover:bg-indigo-50 group"><input type="radio" name="price_filter" value="250000-" class="custom-radio price-filter"><span class="text-black font-medium text-sm leading-tight break-words" style="color: black !important;">Over 250,000</span></label></li>
              </ul>
            </div>
            <!-- End Price Filter Section -->

          </div>
        </div>
      </aside>

      <!-- Backdrop for mobile filters -->
      <div id="filtersBackdrop" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

      <!-- Products Grid -->
      <div class="flex-1">
        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6" id="productsGrid">
          <?php foreach ($all_products as $product): ?>
            <?php
              $stock = (int)$product['p_qty'];
              $is_out = $stock <= 0;
            ?>
            <div class="relative bg-white rounded-lg shadow overflow-hidden flex flex-col group cursor-pointer product-card"
                 data-category-id="<?= $product['tcat_id'] ?>"
                 data-subcategory-id="<?= $product['mcat_id'] ?? '' ?>"
                 data-price="<?= $product['p_current_price'] ?>"
                 data-product-id="<?= $product['p_id'] ?>"
                 data-product-name="<?= htmlspecialchars($product['p_name']) ?>"
                 data-product-image="../assets/uploads/<?= htmlspecialchars($product['p_featured_photo']) ?>"
                 data-description="<?= htmlspecialchars($product['p_short_description']) ?>"
                 onclick="window.location.href='product_detail.php?id=<?= $product['p_id'] ?>'">
              <!-- Compare Button -->
              <button class="compare-btn z-10" data-product-id="<?= $product['p_id'] ?>" onclick="event.stopPropagation(); toggleCompareItem(this);">
                <i class="fas fa-exchange-alt"></i> <!-- Font Awesome icon -->
              </button>
              <div class="h-32 sm:h-40 lg:h-48 bg-gray-100 flex items-center justify-center relative overflow-hidden">
                <?php if (!empty($product['p_featured_photo'])): ?>
                  <img src="../assets/uploads/<?= htmlspecialchars($product['p_featured_photo']) ?>" alt="<?= htmlspecialchars($product['p_name']) ?>" class="h-full w-full object-cover group-hover:scale-105 transition-transform duration-300" />
                <?php else: ?>
                  <span class="text-gray-400">No Image</span>
                <?php endif; ?>
              </div>
              <div class="p-2 sm:p-3 lg:p-4 flex-1 flex flex-col">
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900 text-sm sm:text-base lg:text-lg leading-tight mb-1 line-clamp-2"><?= htmlspecialchars($product['p_name']) ?></h3>
                  <div class="flex items-center gap-1 sm:gap-2 mb-1">
                    <span class="text-yellow-400 text-sm"><i class="fa fa-star"></i></span>
                    <span class="font-medium text-gray-700 text-xs sm:text-sm">4.<?= rand(3,9) ?></span>
                  </div>
                  <div class="font-bold text-gray-800 text-sm sm:text-base lg:text-lg mb-2">Tsh <?= number_format($product['p_current_price'], 2) ?></div>
                </div>
                <button class="mt-2 sm:mt-3 lg:mt-4 w-full flex items-center justify-center gap-1 sm:gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white text-xs sm:text-sm font-medium py-1.5 sm:py-2 px-2 sm:px-3 lg:px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5 add-to-cart"
                        data-product-id="<?= $product['p_id'] ?>" onclick="event.stopPropagation()">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 sm:w-5 sm:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Add to Cart
                </button>
              </div>
              <?php if ($is_out): ?>
                <div class="absolute inset-0 bg-gray-700 bg-opacity-70 flex items-center justify-center">
                  <span class="text-white font-bold text-sm sm:text-base lg:text-lg">Out of Stock</span>
                </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        </div>
        <!-- No Products Found Message -->
        <div id="noProductsMessage" class="hidden text-center py-12">
          <div class="max-w-md mx-auto">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Products Found</h3>
            <p class="text-gray-600 mb-6">We couldn't find any products matching your filters. Try checking out these popular categories instead:</p>
            <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
              <?php
              // Get 6 random categories to suggest
              $suggested_categories = [];
              if (count($all_categories) > 0) {
                  $suggested_categories = array_rand(array_flip(array_column($all_categories, 'tcat_id')), min(6, count($all_categories)));
                  // array_rand returns a single key if only one item is available, wrap it in an array
                  if (!is_array($suggested_categories)) {
                      $suggested_categories = [$suggested_categories];
                  }
              }
              foreach ($suggested_categories as $cat_id):
                $cat = array_filter($all_categories, function($c) use ($cat_id) { return $c['tcat_id'] == $cat_id; });
                $cat = reset($cat);
                if ($cat): // Ensure category exists
              ?>
                <a href="category.php?id=<?= $cat['tcat_id'] ?>"
                   class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <h4 class="font-medium text-gray-900"><?= htmlspecialchars($cat['tcat_name']) ?></h4>
                  <p class="text-sm text-gray-500 mt-1">Browse products</p>
                </a>
              <?php
                endif;
              endforeach;
              ?>
            </div>
            <button id="clearFiltersButton" class="mt-8 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
              Clear All Filters
            </button>
          </div>
        </div>
      </div>
    </section>
  </main>
  <!-- Modern Footer -->
  <footer class="bg-gray-900 text-gray-400 pt-12 pb-8 mt-16">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
        <!-- Company Info -->
        <div>
          <h3 class="text-xl font-bold text-white mb-4">SMART LIFE<span class="text-blue-500">.</span></h3>
          <p class="mb-4 text-gray-400 text-sm leading-relaxed">
            Your one-stop shop for all smart home and automation products. We provide quality products with excellent customer service.
          </p>
          <div class="flex space-x-4 mt-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <i class="fab fa-linkedin-in"></i>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="text-lg font-semibold text-white mb-4">Quick Links</h4>
          <ul class="space-y-2">
            <li><a href="index.php" class="text-gray-400 hover:text-white transition-colors text-sm">Home</a></li>
            <li><a href="all_products.php" class="text-gray-400 hover:text-white transition-colors text-sm">Shop</a></li>
            <li><a href="about.php" class="text-gray-400 hover:text-white transition-colors text-sm">About Us</a></li>
            <li><a href="contact.php" class="text-gray-400 hover:text-white transition-colors text-sm">Contact</a></li>
            <li><a href="faq.php" class="text-gray-400 hover:text-white transition-colors text-sm">FAQs</a></li>
          </ul>
        </div>

        <!-- Categories -->
        <div>
          <h4 class="text-lg font-semibold text-white mb-4">Categories</h4>
          <ul class="space-y-2">
            <?php
            // Display top 5 categories
            $stmt = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC LIMIT 5");
            $stmt->execute();
            $footer_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($footer_categories as $cat):
            ?>
              <li>
                <a href="category.php?id=<?= $cat['tcat_id'] ?>" class="text-gray-400 hover:text-white transition-colors text-sm">
                  <?= htmlspecialchars($cat['tcat_name']) ?>
                </a>
              </li>
            <?php endforeach; ?>
          </ul>
        </div>

        <!-- Contact Info -->
        <div>
          <h4 class="text-lg font-semibold text-white mb-4">Contact Us</h4>
          <ul class="space-y-3">
            <li class="flex items-start">
              <i class="fas fa-map-marker-alt text-blue-500 mt-1 mr-3"></i>
              <span class="text-sm">123 Smart Street, Dar es Salaam, Tanzania</span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-phone-alt text-blue-500 mr-3"></i>
              <span class="text-sm">+255 123 456 789</span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-envelope text-blue-500 mr-3"></i>
              <span class="text-sm"><EMAIL></span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-clock text-blue-500 mr-3"></i>
              <span class="text-sm">Mon-Sat: 9AM - 6PM</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Copyright & Payment Methods -->
      <div class="pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
        <div class="text-sm text-center md:text-left mb-4 md:mb-0">
          <?= htmlspecialchars($footer_copyright); ?>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm">Payment Methods:</span>
          <div class="flex space-x-2">
            <i class="fab fa-cc-visa text-xl text-gray-400 hover:text-white transition-colors"></i>
            <i class="fab fa-cc-mastercard text-xl text-gray-400 hover:text-white transition-colors"></i>
            <i class="fab fa-cc-paypal text-xl text-gray-400 hover:text-white transition-colors"></i>
            <i class="fab fa-cc-apple-pay text-xl text-gray-400 hover:text-white transition-colors"></i>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Back to Top Button -->
  <button id="backToTop" class="fixed bottom-8 right-8 bg-blue-600 text-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg transform transition-transform duration-300 scale-0 hover:bg-blue-700">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
  </button>
  <!-- Toast Notification -->
  <div id="toast" class="fixed bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 transform translate-y-full transition-transform duration-300 hidden">
    <div class="flex items-center gap-3">
      <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <div>
        <p class="text-gray-800 font-medium">Product Added!</p>
        <p class="text-gray-500 text-sm">Item has been added to your cart</p>
      </div>
      <button onclick="hideToast()" class="ml-4 text-gray-400 hover:text-gray-600">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>
  <div id="productPreview" class="product-preview">
    <div class="flex flex-col gap-2">
      <h3 id="previewName" class="font-semibold text-gray-900 text-lg"></h3>
      <div id="previewDescription" class="text-gray-600 text-sm line-clamp-4"></div>
    </div>
  </div>
  <!-- Compare Bar -->
  <div id="compareBar" class="fixed bottom-0 left-0 right-0 bg-gray-800 text-white p-3 px-4 shadow-lg flex items-center justify-between transform translate-y-full transition-transform duration-300 ease-in-out z-[900]">
    <div class="flex items-center gap-3 flex-grow overflow-x-auto mr-4">
      <span class="text-sm font-medium flex-shrink-0">Compare (<span id="compareCount">0</span>/4):</span>
      <div id="compareItemsContainer" class="flex gap-2">
        <!-- Thumbnails will be added here by JS -->
      </div>
    </div>
    <button id="compareNowBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition disabled:bg-gray-500 disabled:cursor-not-allowed flex-shrink-0" disabled>
      Compare Now <span id="compareCountBadge" class="ml-1 bg-red-600 rounded-full px-1.5 text-xs">0</span>
    </button>
  </div>

  <script>
    // --- Filter Logic ---
    const categoryCheckboxes = document.querySelectorAll('.category-filter');
    const subcategoryCheckboxes = document.querySelectorAll('.subcategory-filter');
    const priceRadios = document.querySelectorAll('.price-filter'); // New
    const inStockCheckbox = document.querySelector('.in-stock-filter');
    const productsGrid = document.getElementById('productsGrid');
    const allCards = Array.from(productsGrid.children);
    const clearFiltersBtn = document.getElementById('clearFilters');
    const clearFiltersNoProductsBtn = document.getElementById('clearFiltersButton');
    const noProductsMessage = document.getElementById('noProductsMessage');

    // Load saved filter states
    function loadFilterStates() {
      const savedFilters = JSON.parse(localStorage.getItem('productFilters') || '{}');
      if (savedFilters.categories) {
        categoryCheckboxes.forEach(cb => {
          cb.checked = savedFilters.categories.includes(cb.value);
        });
      }
      if (savedFilters.subcategories) {
        subcategoryCheckboxes.forEach(cb => {
          cb.checked = savedFilters.subcategories.includes(cb.value);
        });
      }
      if (savedFilters.price) { // New
        const selectedPriceRadio = document.querySelector(`.price-filter[value="${savedFilters.price}"]`);
        if (selectedPriceRadio) selectedPriceRadio.checked = true;
      } else {
         // Default to "All Prices" if nothing saved
         const allPricesRadio = document.querySelector('.price-filter[value="all"]');
         if (allPricesRadio) allPricesRadio.checked = true;
      }
      if (savedFilters.inStock !== undefined) {
        inStockCheckbox.checked = savedFilters.inStock;
      }
      filterProducts(); // Apply loaded filters
    }

    // Save filter states
    function saveFilterStates() {
      const selectedCategories = Array.from(categoryCheckboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);
      const selectedSubcategories = Array.from(subcategoryCheckboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);
      const selectedPrice = document.querySelector('.price-filter:checked')?.value || 'all'; // New

      const filters = {
        categories: selectedCategories,
        subcategories: selectedSubcategories,
        price: selectedPrice, // New
        inStock: inStockCheckbox.checked
      };

      localStorage.setItem('productFilters', JSON.stringify(filters));
    }

    // Clear filters function
    function clearAllFilters() {
        categoryCheckboxes.forEach(cb => cb.checked = false);
        subcategoryCheckboxes.forEach(cb => cb.checked = false);
        const allPricesRadio = document.querySelector('.price-filter[value="all"]'); // New
        if (allPricesRadio) allPricesRadio.checked = true; // New
        inStockCheckbox.checked = false;
        localStorage.removeItem('productFilters');
        filterProducts();
    }

    // Add event listeners for clear buttons
    if (clearFiltersBtn) clearFiltersBtn.onclick = clearAllFilters;
    if (clearFiltersNoProductsBtn) clearFiltersNoProductsBtn.onclick = clearAllFilters;


    // Add event listeners for filters
    categoryCheckboxes.forEach(cb => cb.addEventListener('change', () => { filterProducts(); saveFilterStates(); }));
    subcategoryCheckboxes.forEach(cb => cb.addEventListener('change', () => { filterProducts(); saveFilterStates(); }));
    priceRadios.forEach(radio => radio.addEventListener('change', () => { filterProducts(); saveFilterStates(); })); // New
    inStockCheckbox.addEventListener('change', () => { filterProducts(); saveFilterStates(); });

    // Main filter function
    function filterProducts() {
      const selectedCats = Array.from(categoryCheckboxes).filter(cb => cb.checked).map(cb => cb.value);
      const selectedSubcats = Array.from(subcategoryCheckboxes).filter(cb => cb.checked).map(cb => cb.value);
      const selectedPriceRange = document.querySelector('.price-filter:checked')?.value || 'all'; // New
      const inStockOnly = inStockCheckbox.checked;

      let visibleCount = 0;
      allCards.forEach(card => {
        const catId = card.getAttribute('data-category-id');
        const subcatId = card.getAttribute('data-subcategory-id');
        const price = parseFloat(card.getAttribute('data-price') || 0); // New
        const isOut = card.querySelector('.absolute') !== null;
        let show = true;

        // Category/Subcategory Filter
        if (selectedSubcats.length > 0) {
          if (!subcatId || !selectedSubcats.includes(subcatId)) show = false;
        } else if (selectedCats.length > 0) {
          if (!selectedCats.includes(catId)) show = false;
        }

        // Price Filter (New) - only apply if a specific range is selected
        if (show && selectedPriceRange !== 'all') {
            const [minPriceStr, maxPriceStr] = selectedPriceRange.split('-');
            const minPrice = parseFloat(minPriceStr);
            const maxPrice = maxPriceStr ? parseFloat(maxPriceStr) : Infinity; // Handle ranges like "250000-"

            if (price < minPrice || price >= maxPrice) { // Note: >= maxPrice to exclude upper bound
                show = false;
            }
        }

        // Availability Filter
        if (show && inStockOnly && isOut) {
          show = false;
        }

        card.style.display = show ? '' : 'none';
        if (show) visibleCount++;
      });

      // Show/hide no products message
      if (noProductsMessage) {
        noProductsMessage.classList.toggle('hidden', visibleCount > 0);
      }
    }

    // --- Compare Feature Logic ---
    const compareBar = document.getElementById('compareBar');
    const compareItemsContainer = document.getElementById('compareItemsContainer');
    const compareCountSpan = document.getElementById('compareCount');
    const compareCountBadge = document.getElementById('compareCountBadge'); // Added
    const compareNowBtn = document.getElementById('compareNowBtn');
    const MAX_COMPARE_ITEMS = 4;
    let compareItems = JSON.parse(localStorage.getItem('compareItems') || '[]');

    function updateCompareBar() {
        // Ensure elements exist before proceeding
        if (!compareBar || !compareItemsContainer || !compareCountSpan || !compareNowBtn || !compareCountBadge) {
            console.error("Compare bar elements not found!");
            return;
        }

        // Clear existing thumbnails except the initial span
        compareItemsContainer.querySelectorAll('.compare-item-thumbnail').forEach(el => el.remove());

        compareItems.forEach(productId => {
            const productCard = document.querySelector(`.product-card[data-product-id="${productId}"]`);
            if (productCard) {
                const imgSrc = productCard.dataset.productImage;
                const productName = productCard.dataset.productName;
                const img = document.createElement('img');
                img.src = imgSrc || '../assets/uploads/placeholder.png'; // Fallback image
                img.alt = `Comparing ${productName}`;
                img.title = productName; // Tooltip
                img.classList.add('compare-item-thumbnail');
                img.dataset.productId = productId;
                compareItemsContainer.appendChild(img);
            } else {
                // Handle case where product card might not be on the current filtered view
                // Maybe remove the item from compareItems if its card isn't found?
                console.warn(`Product card for ID ${productId} not found in current view.`);
            }
        });

        const count = compareItems.length;
        compareCountSpan.textContent = count;
        compareCountBadge.textContent = count; // Update badge count
        compareNowBtn.disabled = count < 2; // Need at least 2 to compare

        // Show/hide bar
        compareBar.classList.toggle('show', count > 0);

        // Update button states on cards
        document.querySelectorAll('.compare-btn').forEach(btn => {
            const btnProductId = btn.dataset.productId;
            btn.classList.toggle('selected', compareItems.includes(btnProductId));
            // Disable adding more if max reached and this item isn't selected
            btn.disabled = count >= MAX_COMPARE_ITEMS && !compareItems.includes(btnProductId);
            // Re-enable if count is less than max OR if this item IS selected (so it can be deselected)
            if (count < MAX_COMPARE_ITEMS || compareItems.includes(btnProductId)) {
                btn.disabled = false;
            }
        });
    }


    function toggleCompareItem(buttonElement) {
        const productId = buttonElement.dataset.productId;
        const index = compareItems.indexOf(productId);

        if (index > -1) { // Item is selected, remove it
            compareItems.splice(index, 1);
        } else { // Item not selected, add it if space allows
            if (compareItems.length < MAX_COMPARE_ITEMS) {
                compareItems.push(productId);
            } else {
                // Optional: Show a message that max items reached
                alert(`You can only compare up to ${MAX_COMPARE_ITEMS} items.`);
                return; // Don't update state if max reached
            }
        }

        localStorage.setItem('compareItems', JSON.stringify(compareItems));
        updateCompareBar();
    }

    // Event listener for the Compare Now button
    if (compareNowBtn) {
        compareNowBtn.addEventListener('click', () => {
            if (compareItems.length >= 2) {
                const ids = compareItems.join(',');
                // **Important:** Create compare.php to handle this
                window.location.href = `compare.php?ids=${ids}`;
            }
        });
    }

    // Initial setup on page load
    document.addEventListener('DOMContentLoaded', () => {
        loadFilterStates(); // Load filters first
        updateCompareBar(); // Then update compare bar based on loaded state
    });


    // --- Add to cart logic ---
    document.querySelectorAll('.add-to-cart').forEach(button => {
      button.addEventListener('click', function(e) {
        e.stopPropagation(); // Prevent any card-level click events

        // Don't proceed if button is disabled
        if (this.disabled) {
          return;
        }

        const productId = this.getAttribute('data-product-id');
        addToCart(productId);
      });
    });
    // Search logic (demo only)
    document.getElementById('searchInput').addEventListener('input', function() {
      const q = this.value.toLowerCase();
      allCards.forEach(card => {
        const name = card.querySelector('h3').textContent.toLowerCase();
        card.style.display = name.includes(q) ? '' : 'none';
      });
    });
    document.addEventListener("DOMContentLoaded", function() {
      // Initialize cart if not exists
      if (!localStorage.getItem('cart')) {
        localStorage.setItem('cart', JSON.stringify([]));
      }

      // Update cart count display and check product status
      updateCartCount();
      checkCartStatus();

      // Add to cart buttons
      document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function(e) {
          e.stopPropagation(); // Prevent any card-level click events

          // Don't proceed if button is disabled
          if (this.disabled) {
            return;
          }

          const productId = this.getAttribute('data-product-id');
          addToCart(productId);
        });
      });

      // Function to check if product is in cart and update button state
      function checkCartStatus() {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]');
        document.querySelectorAll('.add-to-cart').forEach(button => {
          const productId = button.getAttribute('data-product-id');
          const isInCart = cart.some(item => String(item.product_id) === productId);

          if (isInCart) {
            button.disabled = true;
            button.classList.add('opacity-50', 'cursor-not-allowed');
            button.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Added to Cart
            `;
          }
        });
      }

      // Updated addToCart function to mimic product_detail.php processing
      function addToCart(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', 1);

        fetch('add_to_cart.php', {
          method: 'POST',
          body: formData
        })
        .then(response => {
          const contentType = response.headers.get("content-type");
          if (response.ok && contentType && contentType.includes("application/json")) {
            return response.json();
          } else {
            return response.text().then(text => {
              throw new Error(`Server error: ${text.substring(0,200)}`);
            });
          }
        })
        .then(data => {
          if (data && data.status === 'success' && data.added_item) {
            let cart = JSON.parse(localStorage.getItem('cart') || '[]');
            const productIdStr = String(data.added_item.product_id);
            const existingIndex = cart.findIndex(item => String(item.product_id) === productIdStr);
            if (existingIndex > -1) {
              cart[existingIndex].quantity = data.added_item.quantity;
            } else {
              cart.push(data.added_item);
            }
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            checkCartStatus(); // Update button states after adding to cart
            showToast();
          } else {
            showToast('error', data.message || 'Error adding product to cart.');
          }
        })
        .catch(error => {
          console.error("Add to Cart Error:", error);
          showToast('error', 'Error adding product to cart.');
        });
      }

      function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('cart') || '[]'); // Ensure cart is parsed correctly
        const totalItems = cart.reduce((total, item) => total + (item.quantity || 0), 0); // Sum quantities

        // Update header cart count
        const headerCartCount = document.querySelector('header .cart-count');
        if(headerCartCount) headerCartCount.textContent = totalItems;

        // Update mobile menu cart count
        const mobileCartCount = document.querySelector('#mobileMenu .cart-count');
        if(mobileCartCount) mobileCartCount.textContent = totalItems;
      }


      function showToast(type = 'success', message = 'Product added to cart!') {
        const toast = document.getElementById('toast');
        if (!toast) return; // Exit if toast element doesn't exist
        const iconContainer = toast.querySelector('div:first-child'); // More robust selector
        const icon = iconContainer ? iconContainer.querySelector('svg') : null;
        const title = toast.querySelector('p:first-child');
        const description = toast.querySelector('p:last-child');

        if (type === 'error') {
          toast.classList.remove('bg-white');
          toast.classList.add('bg-red-50');
          if (iconContainer) {
            iconContainer.classList.remove('bg-green-100');
            iconContainer.classList.add('bg-red-100');
          }
          if (icon) {
            icon.classList.remove('text-green-600');
            icon.classList.add('text-red-600');
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>'; // Error icon
          }
          if (title) title.textContent = 'Error!';
          if (description) description.textContent = message;
        } else {
          toast.classList.remove('bg-red-50');
          toast.classList.add('bg-white');
           if (iconContainer) {
            iconContainer.classList.remove('bg-red-100');
            iconContainer.classList.add('bg-green-100');
          }
          if (icon) {
            icon.classList.remove('text-red-600');
            icon.classList.add('text-green-600');
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>'; // Success icon
          }
          if (title) title.textContent = 'Product Added!';
          if (description) description.textContent = 'Item has been added to your cart';
        }

        toast.classList.remove('hidden', 'translate-y-full');
        // Force reflow / repaint before adding class for transition
        void toast.offsetWidth;
        toast.classList.add('translate-y-0'); // Assuming this class triggers the slide-in

        setTimeout(() => {
          hideToast();
        }, 3000);
      }

      function hideToast() {
        const toast = document.getElementById('toast');
         if (!toast) return;
        toast.classList.remove('translate-y-0'); // Slide out
        toast.classList.add('translate-y-full');
        setTimeout(() => {
          toast.classList.add('hidden');
        }, 300); // Match transition duration
      }
    });

    // Mobile Menu Functionality
    const mobileMenuButton = document.getElementById('mobileMenuButton');
    const closeMobileMenu = document.getElementById('closeMobileMenu');
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');
    const mobileSearchInput = document.getElementById('mobileSearchInput');
    const mobileSearchSuggestions = document.getElementById('mobileSearchSuggestions');

    // Mobile Filters Functionality
    const mobileFilterButton = document.getElementById('mobileFilterButton');
    const filtersPanel = document.getElementById('filtersPanel');
    const filtersBackdrop = document.getElementById('filtersBackdrop');
    const closeFilters = document.getElementById('closeFilters');

    // Back to Top Button Functionality
    const backToTopButton = document.getElementById('backToTop');

    window.addEventListener('scroll', function() {
      if (window.pageYOffset > 300) {
        backToTopButton.classList.remove('scale-0');
        backToTopButton.classList.add('scale-100');
      } else {
        backToTopButton.classList.remove('scale-100');
        backToTopButton.classList.add('scale-0');
      }
    });

    backToTopButton.addEventListener('click', function() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });

    function toggleFilters() {
      filtersPanel.classList.toggle('translate-x-full');
      filtersBackdrop.classList.toggle('hidden');
      document.body.style.overflow = filtersPanel.classList.contains('translate-x-full') ? 'auto' : 'hidden';
    }

    if (mobileFilterButton && filtersPanel && filtersBackdrop && closeFilters) {
      mobileFilterButton.addEventListener('click', toggleFilters);
      closeFilters.addEventListener('click', toggleFilters);
      filtersBackdrop.addEventListener('click', toggleFilters);
    }

    // Function to update cart count in both header and mobile menu
    function updateCartCount() {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      const totalItems = cart.reduce((total, item) => total + (item.quantity || 0), 0); // Sum quantities

      // Update header cart count
      const headerCartCount = document.querySelector('header .cart-count');
      if(headerCartCount) headerCartCount.textContent = totalItems;

      // Update mobile menu cart count
      const mobileCartCount = document.querySelector('#mobileMenu .cart-count');
      if(mobileCartCount) mobileCartCount.textContent = totalItems;
    }


    // Initialize cart count on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize cart if not exists
      if (!localStorage.getItem('cart')) {
        localStorage.setItem('cart', JSON.stringify([]));
      }
      updateCartCount();
    });

    function toggleMobileMenu() {
      mobileMenu.classList.toggle('translate-x-full');
      mobileMenuBackdrop.classList.toggle('hidden');
      document.body.style.overflow = mobileMenu.classList.contains('translate-x-full') ? 'auto' : 'hidden';
    }

    if (mobileMenuButton && closeMobileMenu && mobileMenu && mobileMenuBackdrop) {
      mobileMenuButton.addEventListener('click', toggleMobileMenu);
      closeMobileMenu.addEventListener('click', toggleMobileMenu);
      mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);
    }

    // Mobile Search Functionality
    if (mobileSearchInput && mobileSearchSuggestions) {
      let mobileSearchTimeout;
      mobileSearchInput.addEventListener('input', e => {
        clearTimeout(mobileSearchTimeout);
        mobileSearchTimeout = setTimeout(() => fetchSuggestions(e.target.value, mobileSearchSuggestions), 300);
      });

      function highlightText(text, term) {
        if (!term) return text;
        const re = new RegExp(`(${term})`, 'gi');
        return text.replace(re, '<span class="bg-yellow-100 text-gray-900">$1</span>');
      }

      async function fetchSuggestions(q, suggestionsContainer) {
        if (q.length < 2) {
          suggestionsContainer.classList.add('hidden');
          return;
        }
        try {
          const res = await fetch(`search_suggestions.php?q=${encodeURIComponent(q)}`);
          const data = await res.json();
          if (data.length) {
            suggestionsContainer.innerHTML = data.map(item => `
              <div class="suggestion-item flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                   data-type="${item.type}" data-id="${item.id}">
                <div class="icon mr-3 text-[#00c2ff]">${item.type==='product'?'🛍️':'📁'}</div>
                <div class="name flex-1 text-gray-700">${highlightText(item.name, q)}</div>
                <div class="ml-2 text-xs text-gray-400">${item.type}</div>
              </div>
            `).join('');
            suggestionsContainer.classList.remove('hidden');
          } else {
            suggestionsContainer.innerHTML = '<div class="px-4 py-3 text-gray-500 text-center">No results found</div>';
            suggestionsContainer.classList.remove('hidden');
          }
        } catch (e) {
          console.error('Error fetching suggestions:', e);
        }
      }

      mobileSearchSuggestions.addEventListener('click', e => {
        const item = e.target.closest('.suggestion-item');
        if (!item) return;
        const type = item.dataset.type, id = item.dataset.id;
        if (type === 'product') {
          window.location.href = `product_detail.php?id=${id}`;
        } else {
          window.location.href = `category.php?id=${id}`;
        }
      });
    }

    // Product preview functionality (assuming it exists)
    const preview = document.getElementById('productPreview');
    if (preview) {
        let previewTimeout;
        let currentProductCard = null;

        document.querySelectorAll('.product-card').forEach(card => {
          card.addEventListener('mouseenter', () => {
            currentProductCard = card;
            previewTimeout = setTimeout(() => {
              if (currentProductCard === card) {
                showProductPreview(card);
              }
            }, 1000); // Delay before showing preview
          });

          card.addEventListener('mouseleave', () => {
            currentProductCard = null;
            clearTimeout(previewTimeout);
            hideProductPreview();
          });
        });

        function showProductPreview(card) {
          const productName = card.querySelector('h3').textContent;
          const productDescription = card.getAttribute('data-description') || 'No description available';

          // Strip HTML tags from description
          const cleanDescription = productDescription.replace(/<[^>]*>/g, '');

          // Update preview content
          document.getElementById('previewName').textContent = productName;
          document.getElementById('previewDescription').textContent = cleanDescription;

          // Position the preview
          const rect = card.getBoundingClientRect();
          const previewWidth = preview.offsetWidth;
          const previewHeight = preview.offsetHeight;

          let left = rect.left + (rect.width / 2) - (previewWidth / 2);
          let top = rect.bottom + 10; // Default below card
          let arrowDirection = 'down';

          // Adjust position if preview would go off screen horizontally
          if (left + previewWidth > window.innerWidth) {
            left = window.innerWidth - previewWidth - 10;
          }
          if (left < 0) {
            left = 10;
          }

          // Check if preview would go below viewport or above header (adjust 72px if header height changes)
          if (top + previewHeight > window.innerHeight && rect.top - previewHeight - 10 > 72) {
             // If it goes off bottom AND there's space above, position above
             top = rect.top - previewHeight - 10;
             arrowDirection = 'up';
          } else if (top < 72) {
             // If it goes above header (unlikely with default position, but check)
             top = 72 + 10; // Position just below header
             arrowDirection = 'down'; // Keep arrow down if forced below header
          }


          // Update arrow direction and position
          preview.style.setProperty('--arrow-direction', arrowDirection);
          preview.style.left = `${left}px`;
          preview.style.top = `${top}px`;
          preview.classList.add('show');
        }

        function hideProductPreview() {
          preview.classList.remove('show');
        }

        // Add to cart from preview (assuming button exists in preview)
        const addToCartPreviewBtn = document.querySelector('.add-to-cart-preview');
        if (addToCartPreviewBtn) {
            addToCartPreviewBtn.addEventListener('click', function(e) {
              e.stopPropagation();
              if (this.disabled) return;
              const productId = this.getAttribute('data-product-id'); // Ensure preview button has this
              addToCart(productId);
              hideProductPreview();
            });
        }
    } else {
        console.warn("Product preview element not found.");
    }

  </script>
</body>
</html>
