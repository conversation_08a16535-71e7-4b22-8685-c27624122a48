<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Redirect if no password reset data is available
if (!isset($_SESSION['password_reset_data'])) {
    header('Location: forgot_password.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $otp = filter_input(INPUT_POST, 'otp', FILTER_SANITIZE_NUMBER_INT);
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate OTP
    if (empty($otp) || strlen($otp) !== 6) {
        $_SESSION['error_message'] = "Please enter a valid 6-digit verification code.";
        header('Location: reset_password.php');
        exit;
    }

    // Validate passwords
    if (empty($new_password) || empty($confirm_password)) {
        $_SESSION['error_message'] = "Both password fields are required.";
        header('Location: reset_password.php');
        exit;
    }

    if ($new_password !== $confirm_password) {
        $_SESSION['error_message'] = "Passwords do not match.";
        header('Location: reset_password.php');
        exit;
    }

    // Password strength validation
    if (strlen($new_password) < 8) {
        $_SESSION['error_message'] = "Password must be at least 8 characters long.";
        header('Location: reset_password.php');
        exit;
    }

    if (!preg_match('/[A-Z]/', $new_password)) {
        $_SESSION['error_message'] = "Password must contain at least one uppercase letter.";
        header('Location: reset_password.php');
        exit;
    }

    if (!preg_match('/[a-z]/', $new_password)) {
        $_SESSION['error_message'] = "Password must contain at least one lowercase letter.";
        header('Location: reset_password.php');
        exit;
    }

    if (!preg_match('/[0-9]/', $new_password)) {
        $_SESSION['error_message'] = "Password must contain at least one number.";
        header('Location: reset_password.php');
        exit;
    }

    // Verify OTP
    if (!verifyOTP($otp)) {
        $_SESSION['error_message'] = "Invalid or expired verification code. Please try again.";
        header('Location: reset_password.php');
        exit;
    }

    try {
        // Get password reset data from session
        $resetData = $_SESSION['password_reset_data'];
        $customer_id = $resetData['customer_id'];
        $email = $resetData['email'];

        // Hash the new password
        $hashed_password = password_hash($new_password, PASSWORD_BCRYPT);

        // Update password in database
        $stmt = $pdo->prepare("UPDATE tbl_customer SET cust_password = ? WHERE cust_id = ? AND cust_email = ?");
        $stmt->execute([$hashed_password, $customer_id, $email]);

        if ($stmt->rowCount() > 0) {
            // Clean up session data
            unset($_SESSION['password_reset_data']);
            unset($_SESSION['otp_data']);

            // Send confirmation email
            $subject = "Password Reset Successful - SMART LIFE";
            $message = "
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                    .header { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }
                    .content { padding: 20px; }
                    .success-box { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; }
                    .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
                    .security-tips { background-color: #e2e3e5; border: 1px solid #d6d8db; color: #383d41; padding: 15px; border-radius: 5px; margin: 15px 0; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>SMART LIFE Password Reset</h2>
                    </div>
                    <div class='content'>
                        <p>Hello " . htmlspecialchars($resetData['customer_name']) . ",</p>
                        <div class='success-box'>
                            <h3>✓ Password Reset Successful</h3>
                            <p>Your password has been successfully updated.</p>
                        </div>
                        <p>You can now log in to your account using your new password.</p>
                        <div class='security-tips'>
                            <h4>Security Tips:</h4>
                            <ul>
                                <li>Keep your password secure and don't share it with anyone</li>
                                <li>Use a unique password for your SMART LIFE account</li>
                                <li>Consider enabling two-factor authentication if available</li>
                                <li>If you didn't make this change, contact us immediately</li>
                            </ul>
                        </div>
                        <p>If you have any questions or concerns, please don't hesitate to contact our support team.</p>
                    </div>
                    <div class='footer'>
                        <p>&copy; " . date('Y') . " SMART LIFE. All rights reserved.</p>
                    </div>
                </div>
            </body>
            </html>
            ";

            // Send confirmation email (optional - don't fail if email fails)
            sendEmailWithPHPMailer($email, $subject, $message);

            $_SESSION['success_message'] = "Your password has been successfully reset. You can now log in with your new password.";
            header('Location: login.php');
            exit;

        } else {
            $_SESSION['error_message'] = "Failed to update password. Please try again.";
            header('Location: reset_password.php');
            exit;
        }

    } catch (PDOException $e) {
        error_log("Password Reset Error: " . $e->getMessage());
        $_SESSION['error_message'] = 'An error occurred while resetting your password. Please try again.';
        header('Location: reset_password.php');
        exit;
    }

} else {
    header('Location: reset_password.php');
    exit;
}

ob_end_flush();
?>
