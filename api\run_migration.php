<?php
/**
 * Manual Migration Runner
 * Run this file directly to execute database migrations
 */

// Include configuration
require_once __DIR__ . '/config/config.php';

echo "<h1>Database Migration</h1>";
echo "<p>Running database migrations for the API...</p>";

try {
    // Set flag to allow web execution
    $_GET['web_install'] = true;

    // Run migration
    $result = include __DIR__ . '/migrations/migrate.php';

    if ($result && $result['success']) {
        echo "<div style='color: green; padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h2>✅ Migration Successful!</h2>";
        echo "<p>Database migrations completed successfully.</p>";
        echo "<p>Migrations run: {$result['migrations_run']}</p>";
        echo "</div>";

        // Detect correct paths
        $script_dir = dirname($_SERVER['SCRIPT_NAME']);
        $monitor_token = md5(JWT_SECRET . date('Y-m-d'));

        echo "<h3>Next Steps:</h3>";
        echo "<ol>";
        echo "<li><a href='{$script_dir}/test.php'>Test the API</a></li>";
        echo "<li><a href='{$script_dir}/v1/docs'>View API Documentation</a></li>";
        echo "<li><a href='{$script_dir}/v1/'>Access API Root</a></li>";
        echo "<li><a href='{$script_dir}/monitor/dashboard.php?token={$monitor_token}'>Monitor API</a></li>";
        echo "</ol>";

    } else {
        echo "<div style='color: red; padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h2>❌ Migration Failed!</h2>";
        echo "<p>Error: " . ($result['error'] ?? 'Unknown error') . "</p>";
        echo "</div>";
    }

} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Migration Failed!</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>You can safely delete this file after successful migration.</small></p>";
