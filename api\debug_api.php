<?php
/**
 * Debug API
 * Shows detailed debugging information about API requests
 */

// Start output buffering to capture any unexpected output
ob_start();

// Include configuration
require_once __DIR__ . '/config/config.php';

// Get any captured output
$captured_output = ob_get_clean();

// Start fresh output buffering
ob_start();

echo "=== API DEBUG INFORMATION ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "\n";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "\n";

if ($captured_output) {
    echo "Captured Output: " . $captured_output . "\n";
}

// Parse request
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
echo "Parsed Path: " . $path . "\n";

// Test path cleaning
$base_patterns = [
    '/ecom/api/v1',
    '/api/v1',
    dirname($_SERVER['SCRIPT_NAME']) . '/v1'
];

echo "Base Patterns: " . implode(', ', $base_patterns) . "\n";

$clean_path = $path;
foreach ($base_patterns as $pattern) {
    if (strpos($path, $pattern) === 0) {
        $clean_path = substr($path, strlen($pattern));
        echo "Matched Pattern: " . $pattern . "\n";
        break;
    }
}

$clean_path = trim($clean_path, '/');
echo "Clean Path: '" . $clean_path . "'\n";

$segments = $clean_path ? explode('/', $clean_path) : [];
$endpoint = $segments[0] ?? '';

echo "Segments: " . implode(', ', $segments) . "\n";
echo "Endpoint: '" . $endpoint . "'\n";

// Test database connection
try {
    $test_query = $pdo->query("SELECT 1")->fetchColumn();
    echo "Database: Connected\n";
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
}

// Test Response class
try {
    echo "Testing Response class...\n";
    
    // Capture the response
    ob_start();
    Response::success(['test' => 'data'], 'Test response');
    $response_output = ob_get_clean();
    
    echo "Response Output Length: " . strlen($response_output) . "\n";
    echo "Response Preview: " . substr($response_output, 0, 100) . "...\n";
    
    // Validate JSON
    $json_data = json_decode($response_output, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "JSON Validation: Valid\n";
    } else {
        echo "JSON Validation: Invalid - " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    echo "Response Class Error: " . $e->getMessage() . "\n";
}

// Get all debug output
$debug_output = ob_get_clean();

// Now output the actual API response or debug info
if (isset($_GET['debug'])) {
    // Show debug information
    header('Content-Type: text/plain');
    echo $debug_output;
} else {
    // Try to output a proper API response
    header('Content-Type: application/json');
    
    try {
        $api_response = [
            'status' => 'success',
            'message' => 'Debug API is working',
            'timestamp' => date('c'),
            'api_version' => API_VERSION,
            'debug_info' => [
                'endpoint' => $endpoint,
                'clean_path' => $clean_path,
                'segments' => $segments,
                'method' => $_SERVER['REQUEST_METHOD']
            ],
            'data' => [
                'name' => 'Ecommerce API Debug',
                'version' => API_VERSION
            ]
        ];
        
        echo json_encode($api_response, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Debug API error: ' . $e->getMessage(),
            'debug_output' => $debug_output
        ], JSON_PRETTY_PRINT);
    }
}
?>
