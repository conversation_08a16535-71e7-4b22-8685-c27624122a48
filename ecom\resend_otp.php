<?php
// resend_otp.php

ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: cart.php');
    exit;
}

// Redirect if no registration data is available
if (!isset($_SESSION['registration_data'])) {
    header('Location: register.php');
    exit;
}

// Get registration data
$registrationData = $_SESSION['registration_data'];
$email = $registrationData['email'];
$fname = $registrationData['fname'];

// Generate new OTP
$otp = generateOTP();
storeOTP($email, $otp);

// Prepare email content
$subject = "Your New OTP for SMARTLIFE TZ Registration";
$message = "
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .header { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; }
        .otp-box { font-size: 24px; font-weight: bold; text-align: center; padding: 15px; background-color: #f1f1f1; border-radius: 5px; margin: 20px 0; letter-spacing: 5px; }
        .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>SMART LIFE Registration</h2>
        </div>
        <div class='content'>
            <p>Hello $fname,</p>
            <p>You requested a new OTP. To complete your registration, please use the following One-Time Password (OTP):</p>
            <div class='otp-box'>$otp</div>
            <p>This OTP is valid for 10 minutes. If you did not request this registration, please ignore this email.</p>
        </div>
        <div class='footer'>
            <p>&copy; " . date('Y') . " SMART LIFE TZ. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
";

// Send OTP via email
$emailSent = sendEmailWithPHPMailer($email, $subject, $message);

if (!$emailSent) {
    $_SESSION['error_message'] = "Failed to send OTP email. Please try again.";
} else {
    $_SESSION['success_message'] = "A new OTP has been sent to your email address.";
}

// Redirect back to OTP verification page
header('Location: verify_otp.php');
exit;

ob_end_flush();
?>
