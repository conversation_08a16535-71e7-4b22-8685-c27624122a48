/**
 * Subcategory filtering functionality
 * This script handles the filtering of products by subcategory
 * Works for both index.php and category.php
 */

document.addEventListener("DOMContentLoaded", function () {
  // Function to apply filtering
  function filterProducts(container, subcatId) {
    console.log("Filtering products in container:", container);
    console.log("Filtering by subcategory ID:", subcatId);

    // Find all product cards in the container
    const productCards = container.querySelectorAll(".product-card");
    console.log("Found", productCards.length, "product cards");

    // Apply filtering
    productCards.forEach((card) => {
      const cardSubcatId = card.getAttribute("data-subcat-id");
      const productName = card.getAttribute("data-product-name") || "Unknown";
      console.log("Card:", productName, "subcategory ID:", cardSubcatId);

      // Convert to strings for comparison
      const cardSubcatIdStr = String(cardSubcatId);
      const subcatIdStr = String(subcatId);

      if (subcatIdStr === "all") {
        // Show all products
        card.style.display = "";
        card.classList.remove("hidden");
        console.log("Showing card (all):", productName);
      } else if (cardSubcatIdStr === subcatIdStr) {
        // Show matching products
        card.style.display = "";
        card.classList.remove("hidden");
        console.log(
          "Showing card (match):",
          productName,
          "with subcatId:",
          cardSubcatIdStr
        );
      } else {
        // Hide non-matching products
        card.style.display = "none";
        card.classList.add("hidden");
        console.log(
          "Hiding card:",
          productName,
          "with subcatId:",
          cardSubcatIdStr,
          "because it doesn't match",
          subcatIdStr
        );
      }
    });
  }

  // Function to update button styles
  function updateButtonStyles(container, activeButton) {
    // Reset all buttons in this container
    container.querySelectorAll(".subcategory-btn").forEach((btn) => {
      // For index.php style
      btn.classList.remove("active");

      // For category.php style (if these classes exist)
      if (btn.classList.contains("bg-[#00c2ff]")) {
        btn.classList.remove("bg-[#00c2ff]", "text-white");
        btn.classList.add("bg-gray-100", "text-gray-700");
      }
    });

    // Set active button
    if (activeButton) {
      // For index.php style
      activeButton.classList.add("active");

      // For category.php style (if these classes are used in this page)
      if (document.body.classList.contains("category-page")) {
        activeButton.classList.add("bg-[#00c2ff]", "text-white");
        activeButton.classList.remove("bg-gray-100", "text-gray-700");
      }
    }
  }

  // Initialize all category sections (for index.php)
  document.querySelectorAll(".category-section").forEach((section) => {
    // Set 'All' button as active by default
    const allButton = section.querySelector(
      '.subcategory-btn[data-subcat-id="all"]'
    );
    if (allButton) {
      updateButtonStyles(section, allButton);
    }
  });

  // Initialize main section (for category.php)
  const mainSection = document.querySelector("main");
  if (mainSection) {
    const allButton = mainSection.querySelector(
      '.subcategory-btn[data-subcat-id="all"]'
    );
    if (allButton) {
      updateButtonStyles(mainSection, allButton);
    }
  }

  // Add click handlers for sub-category buttons
  document.querySelectorAll(".subcategory-btn").forEach((button) => {
    button.addEventListener("click", function (e) {
      // Only prevent default if it's a link
      if (button.tagName === "A") {
        e.preventDefault();
      }

      // Get subcategory ID
      const subcatId = this.getAttribute("data-subcat-id");
      console.log("Button clicked with subcatId:", subcatId);

      // Find container (works for both index.php and category.php)
      const container =
        this.closest(".category-section") || this.closest("main");
      console.log("Container found:", container);

      if (!container) {
        console.error("No container found for this button");
        return;
      }

      // Update button styles
      updateButtonStyles(container, this);

      // Filter products
      filterProducts(container, subcatId);

      // Store selected subcategory in localStorage
      localStorage.setItem("selectedSubcat", subcatId);

      // Debug: Log all product cards in this container and their visibility
      console.log("--- After filtering ---");
      container.querySelectorAll(".product-card").forEach((card) => {
        const isHidden =
          card.classList.contains("hidden") || card.style.display === "none";
        console.log(
          "Product:",
          card.getAttribute("data-product-name") || "Unknown",
          "| Subcategory:",
          card.getAttribute("data-subcat-id"),
          "| Hidden:",
          isHidden
        );
      });
    });
  });

  // Apply stored filter if on category page
  const currentUrl = window.location.href;
  if (currentUrl.includes("category.php")) {
    const storedSubcat = localStorage.getItem("selectedSubcat");
    if (storedSubcat) {
      const button = document.querySelector(
        `.subcategory-btn[data-subcat-id="${storedSubcat}"]`
      );
      if (button) {
        // Simulate click on the stored subcategory button
        button.click();
      }
    }
  }
});
