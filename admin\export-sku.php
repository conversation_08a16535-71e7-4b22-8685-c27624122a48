<?php
// Start session first
session_start();
require_once('inc/config.php');

// Check if user is logged in
if(!isset($_SESSION['user'])) {
    header('location: login.php');
    exit;
}

// Get filter parameters
$status = $_GET['status'] ?? '';
$stock_filter = $_GET['stock_filter'] ?? '';
$product_id = $_GET['product_id'] ?? '';

// Build query
$sql = "SELECT
    s.sku_code,
    p.p_name as product_name,
    s.variant_details,
    s.price,
    s.cost_price,
    s.quantity,
    s.reorder_level,
    s.barcode,
    CASE WHEN s.status = 1 THEN 'Active' ELSE 'Inactive' END as status,
    s.created_at,
    s.updated_at
FROM tbl_sku s
LEFT JOIN tbl_product p ON s.product_id = p.p_id
WHERE 1=1";

$params = [];

if($status !== '') {
    $sql .= " AND s.status = ?";
    $params[] = $status;
}

if($product_id) {
    $sql .= " AND s.product_id = ?";
    $params[] = $product_id;
}

if($stock_filter) {
    switch($stock_filter) {
        case 'low':
            $sql .= " AND s.quantity <= s.reorder_level AND s.quantity > 0";
            break;
        case 'out':
            $sql .= " AND s.quantity = 0";
            break;
        case 'in_stock':
            $sql .= " AND s.quantity > s.reorder_level";
            break;
    }
}

$sql .= " ORDER BY s.sku_id DESC";

$statement = $pdo->prepare($sql);
$statement->execute($params);
$results = $statement->fetchAll(PDO::FETCH_ASSOC);

// Set headers for CSV download
$filename = 'sku_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Open output stream
$output = fopen('php://output', 'w');

// Write CSV headers
$headers = [
    'SKU Code',
    'Product Name',
    'Variant Details',
    'Selling Price',
    'Cost Price',
    'Quantity',
    'Reorder Level',
    'Barcode',
    'Status',
    'Created Date',
    'Last Updated'
];

fputcsv($output, $headers);

// Write data rows
foreach($results as $row) {
    $csvRow = [
        $row['sku_code'],
        $row['product_name'],
        $row['variant_details'],
        number_format($row['price'], 0),
        number_format($row['cost_price'], 0),
        $row['quantity'],
        $row['reorder_level'],
        $row['barcode'],
        $row['status'],
        date('Y-m-d H:i:s', strtotime($row['created_at'])),
        date('Y-m-d H:i:s', strtotime($row['updated_at']))
    ];

    fputcsv($output, $csvRow);
}

// Close output stream
fclose($output);
exit;
?>
