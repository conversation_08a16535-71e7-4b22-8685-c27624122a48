<?php
ob_start();
session_start();
include("../admin/inc/config.php");
include("../admin/inc/functions.php");

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Fetch settings for footer
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 Your Company. All rights reserved.";

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register | SMART LIFE</title>
    <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />

    <style>
        header {
    position: fixed;
    width: 100%;
    z-index: 1000;
    transition: all 0.4s ease;
    padding: 10px 0;
}

    </style>
</head>
<body class="auth-page">
    <header style="background-color: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
         <div class="container">
          <a href="index.php" class="logo">SMART LIFE TZ<span>.</span></a>
          <div class="nav-links">
            <a href="index.php">Home</a>
             <a href="index.php#about">About</a>
             <a href="index.php#products">Products</a>
             <a href="index.php#gallery">SPECIAL OFFER</a>
             <a href="index.php#contact">Contact</a>
             <div class="cart-icon">
               <a href="cart.php"><i class="fas fa-shopping-cart"></i> <span class="cart-count">0</span></a>
             </div>
            </div>
            <div class="mobile-menu">
               <div class="menu-btn"><span></span><span></span><span></span></div>
             </div>
         </div>
     </header>

    <main style="padding-top: 150px !important;">
        <div class="container">
            <div class="auth-container">
                <h2>Register</h2>
                <?php
                // Display messages
                if (isset($_SESSION['error_message'])) {
                    echo '<div class="error-message">' . $_SESSION['error_message'] . '</div>';
                    unset($_SESSION['error_message']);
                    // Optional: Repopulate form fields here using $_SESSION['form_data'] if you stored it
                }
                ?>
                <form action="register_process.php" method="POST" enctype="multipart/form-data">
                     <div class="form-row">
                        <div class="form-group">
                            <label for="fname">First Name *</label>
                            <input type="text" id="fname" name="fname" required class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="lname">Last Name *</label>
                            <input type="text" id="lname" name="lname" required class="form-control">
                        </div>
                    </div>
                    <div class="form-row">
                         <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" required class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone *</label>
                            <input type="tel" id="phone" name="phone" required class="form-control">
                        </div>
                     </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" required class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">Confirm Password *</label>
                            <input type="password" id="confirm_password" name="confirm_password" required class="form-control">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="address_city">City *</label>
                            <input type="text" id="address_city" name="address_city" required class="form-control">
                        </div>

                    </div>
                     <div class="form-row">

                         <div class="form-group">
                            <label for="country">Country *</label>
                            <input type="text" id="country" name="country" required class="form-control">
                            </div>
                     </div>
                     <div class="form-group">
                        <label for="photo">Profile Photo (Optional)</label>
                        <input type="file" id="photo" name="photo" accept="image/*" class="form-control">
                     </div>

                    <button type="submit" class="btn">Register</button>
                </form>
                <p>Already have an account? <a href="login.php">Login here</a></p>
            </div>
        </div>
    </main>

    <footer>
         <div class="container">
             <div class="footer-bottom">
                 <div class="copyright"><?php echo htmlspecialchars($footer_copyright); ?></div>
             </div>
         </div>
     </footer>
      <script>
         // Mobile Menu Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const menuBtn = document.querySelector(".menu-btn");
            const navLinks = document.querySelector(".nav-links");
            const navOverlay = document.querySelector(".nav-overlay");

            if (menuBtn && navLinks) {
                menuBtn.addEventListener("click", function() {
                    menuBtn.classList.toggle("active");
                    navLinks.classList.toggle("active");
                });

                // Close menu when clicking overlay
                if (navOverlay) {
                    navOverlay.addEventListener("click", function() {
                        menuBtn.classList.remove("active");
                        navLinks.classList.remove("active");
                    });
                }
            }

            // Update cart count
            let cartCount = <?php
                $count = 0;
                if(isset($_SESSION['cart'])) {
                    foreach($_SESSION['cart'] as $item) {
                        $count += $item['quantity'];
                    }
                }
                echo $count;
            ?>;
            const countElem = document.querySelector('.cart-count');
            if(countElem) {
                countElem.textContent = cartCount;
                countElem.style.display = cartCount > 0 ? 'inline-block' : 'none';
            }
        });
     </script>
</body>
</html>