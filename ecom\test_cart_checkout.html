<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cart Checkout</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .btn { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 5px; 
            cursor: pointer;
            border: none;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            max-height: 400px;
            overflow-y: auto;
        }
        .status { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Cart Checkout</h1>
        
        <h2>Current Cart Status</h2>
        <div id="cart-status">Loading...</div>
        
        <h2>Test Actions</h2>
        <button class="btn btn-success" onclick="addTestItem()">Add Test Item to Cart</button>
        <button class="btn" onclick="testCheckout()">Test Checkout Process</button>
        <button class="btn btn-danger" onclick="clearCart()">Clear Cart</button>
        
        <h2>Test Results</h2>
        <div id="results"></div>
        
        <h2>Debug Information</h2>
        <pre id="debug-info">Loading...</pre>
        
        <p><a href="cart.php" class="btn">Go to Cart Page</a></p>
    </div>
    
    <script>
        function updateCartStatus() {
            const cart = getCartFromLocalStorage();
            const statusDiv = document.getElementById('cart-status');
            
            if (cart.length > 0) {
                statusDiv.innerHTML = `<div class="status success">✅ Cart has ${cart.length} items</div>`;
            } else {
                statusDiv.innerHTML = `<div class="status error">❌ Cart is empty</div>`;
            }
            
            // Update debug info
            const debugDiv = document.getElementById('debug-info');
            debugDiv.textContent = JSON.stringify({
                cart: cart,
                cartLength: cart.length,
                localStorage: localStorage.getItem('cart')
            }, null, 2);
        }
        
        function getCartFromLocalStorage() {
            try {
                const cartData = localStorage.getItem('cart');
                return cartData ? JSON.parse(cartData) : [];
            } catch (e) {
                console.error("Error reading cart from localStorage:", e);
                return [];
            }
        }
        
        function saveCartToLocalStorage(cart) {
            try {
                localStorage.setItem('cart', JSON.stringify(cart));
                updateCartStatus();
            } catch (e) {
                console.error("Error saving cart to localStorage:", e);
            }
        }
        
        function addTestItem() {
            const cart = getCartFromLocalStorage();
            const testItem = {
                id: 1,
                product_id: 1,
                name: 'Test Product',
                price: 1000,
                quantity: 1,
                photo: 'test.jpg',
                color_id: null,
                color_name: null,
                variation_id: null,
                installation: false
            };
            
            // Check if item already exists
            const existingIndex = cart.findIndex(item => 
                (item.id === testItem.id || item.product_id === testItem.product_id)
            );
            
            if (existingIndex >= 0) {
                cart[existingIndex].quantity += 1;
                addResult('success', 'Test item quantity increased to ' + cart[existingIndex].quantity);
            } else {
                cart.push(testItem);
                addResult('success', 'Test item added to cart');
            }
            
            saveCartToLocalStorage(cart);
        }
        
        function testCheckout() {
            const cart = getCartFromLocalStorage();
            
            if (cart.length === 0) {
                addResult('error', 'Cannot test checkout - cart is empty. Add a test item first.');
                return;
            }
            
            // Convert cart to PHP session format
            const cartForPhpSession = {};
            cart.forEach((item, index) => {
                const key = `${item.product_id || item.id}-${item.variation_id || 0}`;
                cartForPhpSession[key] = {
                    product_id: item.product_id || item.id,
                    variation_id: item.variation_id || null,
                    quantity: Number(item.quantity || 1),
                    price: parseFloat(item.price || 0),
                    name: item.name || '',
                    photo: item.photo || '',
                    color_id: item.color_id || null,
                    color_name: item.color_name || null,
                    installation: item.installation ? 1 : 0
                };
            });
            
            const testData = {
                products_subtotal: 1000,
                shipping_fee: 0,
                installation_fee: 0,
                final_total: 1000,
                country_id: 1,
                cart: cartForPhpSession
            };
            
            addResult('info', 'Testing checkout with data: ' + JSON.stringify(testData, null, 2));
            
            fetch('checkout_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Checkout response:', data);
                
                if (data.status === 'success') {
                    addResult('success', `✅ Checkout successful! Order ID: ${data.order_id}, TX Ref: ${data.tx_ref}`);
                } else {
                    addResult('error', `❌ Checkout failed: ${data.message}`);
                    if (data.debug_info) {
                        addResult('info', 'Debug info: ' + JSON.stringify(data.debug_info, null, 2));
                    }
                }
            })
            .catch(error => {
                console.error('Error during checkout:', error);
                addResult('error', `❌ Network error: ${error.message}`);
            });
        }
        
        function clearCart() {
            localStorage.setItem('cart', JSON.stringify([]));
            updateCartStatus();
            addResult('success', 'Cart cleared');
        }
        
        function addResult(type, message) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            
            // Scroll to bottom
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCartStatus();
            addResult('info', 'Test page loaded');
        });
    </script>
</body>
</html>
