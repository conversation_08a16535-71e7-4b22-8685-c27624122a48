# 🛒 Complete Ecommerce API Guide for Junior Developers

**A Comprehensive REST API for Mobile Ecommerce Applications**

This guide is written specifically for junior developers who want to understand and implement a complete ecommerce API. We'll cover everything from basic concepts to advanced implementation with real examples.

## 📋 Table of Contents

1. [🚀 Quick Start (5 Minutes)](#-quick-start-5-minutes)
2. [🧠 Understanding APIs](#-understanding-apis)
3. [🔐 Authentication System](#-authentication-system)
4. [📚 Complete API Reference](#-complete-api-reference)
5. [📱 Flutter Integration](#-flutter-integration)
6. [🐛 Error Handling](#-error-handling)
7. [🧪 Testing & Debugging](#-testing--debugging)
8. [🚀 Deployment Guide](#-deployment-guide)

---

## 🚀 Quick Start (5 Minutes)

### What You Need
- **XAMPP/WAMP** (local web server)
- **Basic PHP knowledge** (variables, arrays, functions)
- **Basic understanding of JSON**
- **Flutter development environment** (for mobile app)

### Installation Steps

1. **Download & Place Files**
   ```
   📁 C:\xampp\htdocs\
   └── 📁 ecom\
       └── 📁 api\          ← Place API files here
           ├── 📄 index.php
           ├── 📁 config\
           ├── 📁 endpoints\
           └── ...
   ```

2. **Run Auto-Installation**
   ```
   🌐 Visit: http://localhost/ecom/api/install.php
   ✅ Click "Install API"
   ⏳ Wait for green checkmarks
   🗑️ Delete install.php when done
   ```

3. **Test Your API**
   ```
   🌐 Visit: http://localhost/ecom/api/test.php
   ✅ All tests should pass (green checkmarks)
   ```

4. **Your API is Ready!**
   ```
   📍 Base URL: http://localhost/ecom/api/v1
   📖 Documentation: http://localhost/ecom/api/v1/docs
   ```

---

## 🧠 Understanding APIs

### What is a REST API?

Think of an API like a **restaurant**:
- **You (Flutter app)** = Customer who orders food
- **API** = Waiter who takes orders and brings food
- **Database** = Kitchen where food is prepared

```
Flutter App  →  "I want products"  →  API  →  Database
Flutter App  ←  "Here are products" ←  API  ←  Database
```

### HTTP Methods (Verbs)
- **GET** = "Give me data" (like reading a book)
- **POST** = "Create new data" (like writing a new page)
- **PUT** = "Update existing data" (like editing a page)
- **DELETE** = "Remove data" (like tearing out a page)

### JSON Response Format
Every API response looks like this:

```json
{
  "status": "success",           // "success" or "error"
  "message": "Data retrieved",   // What happened
  "timestamp": "2024-01-15T10:30:00Z",
  "api_version": "1.0.0",
  "data": {                      // Your actual data
    "products": [...],
    "total": 25
  }
}
```

### HTTP Status Codes
- **200** ✅ OK - Everything worked perfectly
- **201** ✅ Created - New item was created
- **400** ❌ Bad Request - You sent wrong data
- **401** ❌ Unauthorized - You need to login first
- **404** ❌ Not Found - Item doesn't exist
- **500** ❌ Server Error - Something broke on server

---

## 🔐 Authentication System

### How Login Works (Step by Step)

**Step 1: User Enters Credentials**
```dart
// In your Flutter app
String email = "<EMAIL>";
String password = "password123";
```

**Step 2: Send Login Request**
```dart
final response = await http.post(
  Uri.parse('$baseUrl/auth/login'),
  headers: {'Content-Type': 'application/json'},
  body: json.encode({
    'email': email,
    'password': password
  }),
);
```

**Step 3: API Checks Database**
```php
// API checks if user exists and password is correct
// If valid, creates a JWT token
```

**Step 4: API Returns Token**
```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

**Step 5: Store Token in Flutter**
```dart
// Save token for future use
SharedPreferences prefs = await SharedPreferences.getInstance();
await prefs.setString('auth_token', data['token']);
```

**Step 6: Use Token for Protected Requests**
```dart
// Add token to every protected request
final token = await prefs.getString('auth_token');
final response = await http.get(
  Uri.parse('$baseUrl/users/profile'),
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $token',  // ← This is the key!
  },
);
```

### Token Lifecycle
```
Login → Get Token → Use Token (24 hours) → Token Expires → Login Again
```

---

## 📚 Complete API Reference

### 🔑 Authentication Endpoints

#### POST /auth/login
**What it does**: Logs in a user and returns authentication token

**When to use**: When user wants to sign in to their account

**Request Example**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Success Response**:
```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "phone": "+************"
    }
  }
}
```

**Flutter Implementation**:
```dart
class AuthService {
  static const String baseUrl = 'http://localhost/ecom/api/v1';

  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        // Save token
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('auth_token', data['data']['token']);
        await prefs.setString('user_data', json.encode(data['data']['user']));
      }

      return data;
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Network error: $e'
      };
    }
  }
}
```

#### POST /auth/register
**What it does**: Creates a new user account

**When to use**: When someone wants to create a new account

**Request Example**:
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+************"
}
```

**Validation Rules**:
- Email must be unique and valid format
- Password must be at least 6 characters
- Phone number should include country code
- All fields are required

### 🛍️ Product Endpoints

#### GET /products
**What it does**: Gets a list of products with pagination

**When to use**: To display products in your app (home screen, category pages)

**Query Parameters**:
```
limit     - How many products per page (default: 20, max: 100)
page      - Which page to get (default: 1)
category  - Filter by category ID
featured  - Get only featured products (true/false)
search    - Search products by name
```

**Example Requests**:
```
GET /products                           // First 20 products
GET /products?limit=10                  // First 10 products
GET /products?page=2                    // Second page
GET /products?category=5                // Products in category 5
GET /products?featured=true             // Only featured products
GET /products?search=phone              // Search for "phone"
GET /products?limit=5&category=1&page=2 // Complex query
```

**Success Response**:
```json
{
  "status": "success",
  "message": "Products retrieved successfully",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "iPhone 15 Pro",
        "price": 1200000,           // Price in TZS (Tanzanian Shillings)
        "old_price": 1350000,       // Original price (for discounts)
        "image": "/uploads/iphone15.jpg",
        "description": "Latest iPhone with amazing camera",
        "category_id": 1,
        "category_name": "Electronics",
        "in_stock": true,
        "stock_quantity": 25,
        "is_featured": true,
        "installation_fee": 15000   // Additional installation cost
      },
      {
        "id": 2,
        "name": "Samsung Galaxy S24",
        "price": 950000,
        "old_price": null,          // No discount
        "image": "/uploads/galaxy-s24.jpg",
        "description": "Powerful Android smartphone",
        "category_id": 1,
        "category_name": "Electronics",
        "in_stock": true,
        "stock_quantity": 15,
        "is_featured": false,
        "installation_fee": 15000
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_items": 150,
      "total_pages": 8,
      "has_next_page": true,
      "has_previous_page": false
    }
  }
}
```

**Flutter Implementation**:
```dart
class ProductService {
  static const String baseUrl = 'http://localhost/ecom/api/v1';

  static Future<ProductResponse> getProducts({
    int page = 1,
    int limit = 20,
    int? categoryId,
    bool? featured,
    String? search,
  }) async {
    // Build query parameters
    Map<String, String> queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (categoryId != null) queryParams['category'] = categoryId.toString();
    if (featured != null) queryParams['featured'] = featured.toString();
    if (search != null && search.isNotEmpty) queryParams['search'] = search;

    final uri = Uri.parse('$baseUrl/products').replace(queryParameters: queryParams);

    try {
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      final data = json.decode(response.body);

      if (data['status'] == 'success') {
        return ProductResponse.fromJson(data['data']);
      } else {
        throw Exception(data['message']);
      }
    } catch (e) {
      throw Exception('Failed to load products: $e');
    }
  }
}

// Data models
class ProductResponse {
  final List<Product> products;
  final Pagination pagination;

  ProductResponse({required this.products, required this.pagination});

  factory ProductResponse.fromJson(Map<String, dynamic> json) {
    return ProductResponse(
      products: (json['products'] as List)
          .map((item) => Product.fromJson(item))
          .toList(),
      pagination: Pagination.fromJson(json['pagination']),
    );
  }
}

class Product {
  final int id;
  final String name;
  final int price;
  final int? oldPrice;
  final String image;
  final String description;
  final int categoryId;
  final String categoryName;
  final bool inStock;
  final int stockQuantity;
  final bool isFeatured;
  final int installationFee;

  Product({
    required this.id,
    required this.name,
    required this.price,
    this.oldPrice,
    required this.image,
    required this.description,
    required this.categoryId,
    required this.categoryName,
    required this.inStock,
    required this.stockQuantity,
    required this.isFeatured,
    required this.installationFee,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      name: json['name'],
      price: json['price'],
      oldPrice: json['old_price'],
      image: json['image'],
      description: json['description'],
      categoryId: json['category_id'],
      categoryName: json['category_name'],
      inStock: json['in_stock'],
      stockQuantity: json['stock_quantity'],
      isFeatured: json['is_featured'],
      installationFee: json['installation_fee'],
    );
  }

  // Helper methods
  bool get hasDiscount => oldPrice != null && oldPrice! > price;
  int get discountAmount => hasDiscount ? oldPrice! - price : 0;
  double get discountPercentage => hasDiscount ? (discountAmount / oldPrice!) * 100 : 0;
  String get formattedPrice => 'TZS ${price.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';
}

class Pagination {
  final int currentPage;
  final int perPage;
  final int totalItems;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPreviousPage;

  Pagination({
    required this.currentPage,
    required this.perPage,
    required this.totalItems,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      currentPage: json['current_page'],
      perPage: json['per_page'],
      totalItems: json['total_items'],
      totalPages: json['total_pages'],
      hasNextPage: json['has_next_page'],
      hasPreviousPage: json['has_previous_page'],
    );
  }
}
```

---

## 🐛 Error Handling

### Common Error Scenarios

#### 1. Network Errors
```dart
try {
  final response = await EcommerceApiService.getProducts();
  // Handle success
} catch (e) {
  if (e.toString().contains('SocketException')) {
    // No internet connection
    showError('Please check your internet connection');
  } else if (e.toString().contains('TimeoutException')) {
    // Request timeout
    showError('Request timed out. Please try again');
  } else {
    // Other network errors
    showError('Network error occurred');
  }
}
```

#### 2. Authentication Errors
```dart
// Check if user is logged in before making protected requests
if (await EcommerceApiService.isLoggedIn()) {
  final response = await EcommerceApiService.getCart();
  // Handle response
} else {
  // Redirect to login
  Navigator.pushReplacementNamed(context, '/login');
}
```

#### 3. Validation Errors
```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": {
    "email": ["Email is required", "Email format is invalid"],
    "password": ["Password must be at least 6 characters"]
  }
}
```

```dart
// Handle validation errors in Flutter
if (response.data['errors'] != null) {
  Map<String, List<String>> errors = Map<String, List<String>>.from(
    response.data['errors'].map((key, value) => MapEntry(key, List<String>.from(value)))
  );

  // Show specific field errors
  if (errors['email'] != null) {
    emailError = errors['email']!.first;
  }
  if (errors['password'] != null) {
    passwordError = errors['password']!.first;
  }
}
```

### Error Response Format
```json
{
  "status": "error",
  "message": "Human readable error message",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z",
  "api_version": "1.0.0"
}
```

### HTTP Status Code Meanings
- **400 Bad Request**: Your request data is invalid
- **401 Unauthorized**: You need to login or your token expired
- **403 Forbidden**: You don't have permission for this action
- **404 Not Found**: The resource doesn't exist
- **422 Unprocessable Entity**: Validation failed
- **429 Too Many Requests**: You're making requests too quickly
- **500 Internal Server Error**: Something went wrong on the server

---

## 🧪 Testing & Debugging

### 1. Test Your API Installation
```
🌐 Visit: http://localhost/ecom/api/test.php
✅ All tests should show green checkmarks
```

### 2. Test Individual Endpoints
```
🌐 Visit: http://localhost/ecom/api/v1/
📋 Should return API information in JSON format
```

### 3. Debug API Responses
```dart
// Add logging to see API responses
print('API Response: ${response.body}');

// Check response status
print('HTTP Status: ${response.statusCode}');

// Check headers
print('Headers: ${response.headers}');
```

### 4. Common Issues & Solutions

**Issue**: "Failed to connect"
```
✅ Solution: Check if XAMPP is running
✅ Solution: Verify the URL is correct
✅ Solution: Check firewall settings
```

**Issue**: "401 Unauthorized"
```
✅ Solution: Check if token is being sent correctly
✅ Solution: Verify token hasn't expired (24 hours)
✅ Solution: Re-login to get new token
```

**Issue**: "429 Too Many Requests"
```
✅ Solution: Wait a few minutes before trying again
✅ Solution: Reduce request frequency in your app
```

### 5. API Monitoring
```
🌐 Visit: http://localhost/ecom/api/monitor/dashboard.php
📊 View real-time API statistics and errors
```

---

## 🚀 Deployment Guide

### For Production Deployment

#### 1. Security Checklist
- [ ] Change JWT secret in `config/config.php`
- [ ] Remove `install.php` and test files
- [ ] Set proper file permissions (755 for directories, 644 for files)
- [ ] Configure HTTPS/SSL
- [ ] Update CORS origins for your domain
- [ ] Enable error logging
- [ ] Set up database backups

#### 2. Performance Optimization
- [ ] Enable PHP OPcache
- [ ] Use database connection pooling
- [ ] Implement Redis for session storage
- [ ] Set up CDN for images
- [ ] Configure proper caching headers
- [ ] Monitor and optimize slow queries

#### 3. Environment Configuration
```php
// Production config changes
error_reporting(0);
ini_set('display_errors', 0);

// Update database credentials
define('DB_HOST', 'your-production-host');
define('DB_NAME', 'your-production-db');
define('DB_USER', 'your-production-user');
define('DB_PASS', 'your-secure-password');

// Update JWT secret
define('JWT_SECRET', 'your-super-secure-secret-key');

// Update CORS origins
define('ALLOWED_ORIGINS', [
    'https://yourdomain.com',
    'https://www.yourdomain.com'
]);
```

#### 4. Flutter App Configuration
```dart
class ApiConfig {
  static const String baseUrl = kDebugMode
    ? 'http://localhost/ecom/api/v1'  // Development
    : 'https://yourdomain.com/api/v1'; // Production
}
```

---

## 📞 Support & Resources

### Getting Help
- **Documentation**: `/api/v1/docs`
- **API Test**: `/api/test.php`
- **Monitoring**: `/api/monitor/dashboard.php`

### Useful Resources
- [Flutter HTTP Package](https://pub.dev/packages/http)
- [SharedPreferences Package](https://pub.dev/packages/shared_preferences)
- [JSON Annotation Package](https://pub.dev/packages/json_annotation)

### Best Practices
1. **Always handle errors gracefully**
2. **Cache API responses when possible**
3. **Use pagination for large datasets**
4. **Implement retry logic for failed requests**
5. **Show loading states to users**
6. **Validate user input before sending to API**
7. **Use secure storage for sensitive data**

---

**🎉 Congratulations! You now have a complete understanding of the Ecommerce API and how to integrate it with Flutter. Start building your amazing mobile app!**
