function updateCartCount(count) {
    // Update cart count in all cart count elements across pages
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count;
    });
}

function removeCartItem(key, callback) {
    const formData = new URLSearchParams();
    formData.append('action', 'remove_item');
    formData.append('cart_key', key);

    fetch('update_cart_session.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.status === 'success') {
            // Update cart count globally
            if (result.cart_count !== undefined) {
                updateCartCount(result.cart_count);
            }
            // Execute callback if provided
            if (typeof callback === 'function') {
                callback(result);
            }
        } else {
            throw new Error(result.message || 'Could not remove item');
        }
    })
    .catch(error => {
        console.error('Remove Item Error:', error);
        alert('Failed to remove item: ' + error.message);
    });
}