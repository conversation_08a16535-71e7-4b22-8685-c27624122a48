<?php
/**
 * Automated Cleanup Dashboard
 * Monitor and manage the fully automated cleanup system
 */

// Include necessary files
include("session_config.php");
include("auto_cleanup.php");
session_start();
include("../admin/inc/config.php");

// Handle manual actions
$message = '';
$message_type = '';

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'force_cleanup':
            $result = performScheduledCleanup($pdo);
            if ($result['success']) {
                $message = "Manual cleanup completed. Removed {$result['deleted_count']} expired tokens.";
                $message_type = 'success';
            } else {
                $message = "Cleanup failed: " . ($result['error'] ?? 'Unknown error');
                $message_type = 'error';
            }
            break;

        case 'emergency_cleanup':
            $deleted = emergencyCleanup($pdo);
            $message = "Emergency cleanup completed. Removed $deleted very old tokens (60+ days).";
            $message_type = 'success';
            break;

        case 'test_auto':
            initAutoCleanup($pdo);
            $message = "Automatic cleanup system tested successfully.";
            $message_type = 'success';
            break;
    }
}

// Get current statistics
$stats = getCleanupStats($pdo);
if ($stats === null) {
    $stats = [
        'active_tokens' => 0,
        'expired_tokens' => 0,
        'last_cleanup' => 'Error',
        'last_cleanup_count' => 0,
        'next_cleanup' => 'Error',
        'cleanup_due' => false
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Cleanup Dashboard - SMART LIFE</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4f46e5, #06b6d4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #4f46e5;
            transition: transform 0.2s;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #4f46e5;
            font-size: 1.1rem;
        }
        .status-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin: 10px 0;
        }
        .status-card .label {
            color: #6b7280;
            font-size: 0.9rem;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #4f46e5, #06b6d4);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 70, 229, 0.3);
        }
        .btn-danger { background: linear-gradient(135deg, #dc3545, #c82333); }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .btn-success { background: linear-gradient(135deg, #28a745, #20c997); }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #4f46e5;
        }
        .auto-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .auto-active { background: #10b981; }
        .auto-inactive { background: #ef4444; }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4f46e5, #06b6d4);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s;
        }
        .refresh-btn:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Automated Cleanup Dashboard</h1>
            <p>Fully Automated Remember Token Management System</p>
        </div>

        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?= $message_type ?>">
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>

            <div class="status-grid">
                <div class="status-card">
                    <h3>🟢 Active Tokens</h3>
                    <div class="value"><?= $stats['active_tokens'] ?? 0 ?></div>
                    <div class="label">Currently valid remember tokens</div>
                </div>

                <div class="status-card">
                    <h3>🔴 Expired Tokens</h3>
                    <div class="value"><?= $stats['expired_tokens'] ?? 0 ?></div>
                    <div class="label">Awaiting automatic cleanup</div>
                </div>

                <div class="status-card">
                    <h3>🕒 Last Cleanup</h3>
                    <div class="value" style="font-size: 1.2rem;">
                        <?= $stats['last_cleanup'] !== 'Never' ? date('M j, H:i', strtotime($stats['last_cleanup'])) : 'Never' ?>
                    </div>
                    <div class="label">Removed <?= $stats['last_cleanup_count'] ?? 0 ?> tokens</div>
                </div>

                <div class="status-card">
                    <h3>⏰ Next Cleanup</h3>
                    <div class="value" style="font-size: 1.2rem;">
                        <?php if ($stats['cleanup_due']): ?>
                            <span style="color: #ef4444;">Due Now</span>
                        <?php else: ?>
                            <?= date('M j, H:i', strtotime($stats['next_cleanup'])) ?>
                        <?php endif; ?>
                    </div>
                    <div class="label">Automatic scheduling</div>
                </div>
            </div>

            <div class="alert info">
                <strong>🤖 Automation Status:</strong>
                <span class="auto-indicator auto-active"></span>
                <strong>Fully Automated System Active</strong><br>
                The cleanup system runs automatically using multiple methods:
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Time-based:</strong> Runs once every 24 hours</li>
                    <li><strong>Probabilistic:</strong> 1% chance on each page load</li>
                    <li><strong>Lightweight:</strong> Triggers when 100+ expired tokens exist</li>
                    <li><strong>Emergency:</strong> Removes tokens older than 60 days</li>
                </ul>
                No manual intervention required!
            </div>

            <h2>🛠 Manual Controls</h2>
            <div style="margin: 20px 0;">
                <a href="?action=force_cleanup" class="btn btn-success">🧹 Force Cleanup Now</a>
                <a href="?action=emergency_cleanup" class="btn btn-warning">⚡ Emergency Cleanup (60+ days)</a>
                <a href="?action=test_auto" class="btn">🧪 Test Auto System</a>
                <a href="test_persistent_login.php" class="btn">📊 Full Diagnostics</a>
            </div>

            <h2>📈 Cleanup History</h2>
            <?php
            try {
                $stmt = $pdo->prepare("
                    SELECT last_cleanup, tokens_deleted
                    FROM tbl_cleanup_log
                    WHERE cleanup_type = 'remember_tokens'
                    ORDER BY last_cleanup DESC
                    LIMIT 10
                ");
                $stmt->execute();
                $cleanup_history = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (count($cleanup_history) > 0) {
                    echo '<table>';
                    echo '<thead>';
                    echo '<tr>';
                    echo '<th>Cleanup Time</th>';
                    echo '<th>Tokens Removed</th>';
                    echo '<th>Type</th>';
                    echo '</tr>';
                    echo '</thead>';
                    echo '<tbody>';

                    foreach ($cleanup_history as $entry) {
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($entry['last_cleanup']) . '</td>';
                        echo '<td>' . htmlspecialchars($entry['tokens_deleted']) . '</td>';
                        echo '<td>';
                        if ($entry['tokens_deleted'] > 100) {
                            echo '<span style="color: #dc3545;">🔥 Emergency</span>';
                        } elseif ($entry['tokens_deleted'] > 10) {
                            echo '<span style="color: #ffc107;">⚡ Scheduled</span>';
                        } else {
                            echo '<span style="color: #28a745;">🧹 Routine</span>';
                        }
                        echo '</td>';
                        echo '</tr>';
                    }

                    echo '</tbody>';
                    echo '</table>';
                } else {
                    echo '<div class="alert info">';
                    echo 'No cleanup history available yet. The system will start logging after the first automatic cleanup.';
                    echo '</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert error">';
                echo 'Error loading cleanup history: ' . htmlspecialchars($e->getMessage());
                echo '</div>';
            }
            ?>

            <h2>⚙️ System Information</h2>
            <table>
                <tr>
                    <th>Feature</th>
                    <th>Status</th>
                    <th>Details</th>
                </tr>
                <tr>
                    <td>Automatic Cleanup</td>
                    <td><span class="auto-indicator auto-active"></span>Active</td>
                    <td>Runs on every authentication check</td>
                </tr>
                <tr>
                    <td>Time-based Cleanup</td>
                    <td><span class="auto-indicator auto-active"></span>Active</td>
                    <td>Every 24 hours</td>
                </tr>
                <tr>
                    <td>Probabilistic Cleanup</td>
                    <td><span class="auto-indicator auto-active"></span>Active</td>
                    <td>1% chance per page load</td>
                </tr>
                <tr>
                    <td>Emergency Cleanup</td>
                    <td><span class="auto-indicator auto-active"></span>Active</td>
                    <td>Tokens older than 60 days</td>
                </tr>
                <tr>
                    <td>Cleanup Logging</td>
                    <td><span class="auto-indicator auto-active"></span>Active</td>
                    <td>All activities logged</td>
                </tr>
            </table>
        </div>
    </div>

    <button class="refresh-btn" onclick="window.location.reload()" title="Refresh Dashboard">
        🔄
    </button>

    <script>
        // Auto-refresh every 30 seconds
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
