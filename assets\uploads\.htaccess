# Enable CORS for image files
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, OPTIONS"
    Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
    
    # Cache settings for better performance
    Header set Cache-Control "max-age=2592000, public"
</IfModule>

# Handle OPTIONS requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Allow image files
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Header set Access-Control-Allow-Origin "*"
</FilesMatch>
