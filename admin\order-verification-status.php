<?php
require_once('header.php');

if(isset($_GET['id']) && isset($_GET['status'])) {
    $id = $_GET['id'];
    $status = $_GET['status'];
    
    // Validate status
    $valid_statuses = ['verified', 'waiting', 'not_verified'];
    if(!in_array($status, $valid_statuses)) {
        header('location: order.php');
        exit;
    }
    
    // Update the verification status
    $statement = $pdo->prepare("UPDATE orders SET verification_status=? WHERE id=?");
    $statement->execute(array($status, $id));
    
    // Redirect back to orders page
    header('location: order.php');
    exit;
} else {
    header('location: order.php');
    exit;
}
?> 